#!/usr/bin/env python3

import glob
import os
import re


def fix_httpcontext_ambiguity(file_path):
    """Fix HttpContext CS0104 ambiguity by adding alias"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Check if file already has HttpContext alias
        if "using HttpContext = Microsoft.AspNetCore.Http.HttpContext;" in content:
            return False

        # Check if file has both System.Web and Microsoft.AspNetCore.Http imports
        has_system_web = "using System.Web;" in content
        has_aspnet_core = "using Microsoft.AspNetCore.Http;" in content

        if not has_aspnet_core:
            return False

        # Find the last using statement
        using_pattern = r"^using\s+[^=;]+;"
        using_statements = re.findall(using_pattern, content, re.MULTILINE)

        if not using_statements:
            return False

        # Find the position after the last using statement
        last_using = using_statements[-1]
        last_using_pos = content.rfind(last_using)

        if last_using_pos == -1:
            return False

        # Insert the HttpContext alias after the last using statement
        insert_pos = last_using_pos + len(last_using)
        alias_line = "\nusing HttpContext = Microsoft.AspNetCore.Http.HttpContext;"

        new_content = content[:insert_pos] + alias_line + content[insert_pos:]

        # Remove System.Web import if it exists
        if has_system_web:
            new_content = re.sub(
                r"^using System\.Web;\s*\n", "", new_content, flags=re.MULTILINE
            )

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(new_content)

        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    # List of files with CS0104 HttpContext errors
    files_to_fix = [
        "./Cerebrum30/API/Appointments/AppointmentsAPI.cs",
        "./Cerebrum30/DAL/DataAccess/Infrastructure/GenericRepository.cs",
        "./Cerebrum30/Areas/WebBooking/DataAccess/WebBookingRepository.cs",
        "./Cerebrum30/Areas/Daysheet/DataAccess/AppointmentStatusLogRepository.cs",
        "./Cerebrum30/Utility/Helper.cs",
        "./Cerebrum30/Global.asax.cs",
        "./Cerebrum30/Areas/EForms/Controllers/BLL/EformsBLL.cs",
        "./Cerebrum30/Models/HomeViewModels.cs",
        "./Cerebrum30/DAL/TransferC2Data/TransterTable.cs",
        "./Cerebrum30/DAL/DataAccess/Infrastructure/RadGenericRepository.cs",
        "./Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkPdfConversion.cs",
        "./Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkPatientChart.cs",
        "./Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkHealthCardService.cs",
        "./Cerebrum30/Areas/Schedule/Mappers/ScheduleMapper.cs",
    ]

    fixed_count = 0

    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_httpcontext_ambiguity(file_path):
                print(f"Fixed HttpContext ambiguity in: {file_path}")
                fixed_count += 1
            else:
                print(f"No changes needed for: {file_path}")
        else:
            print(f"File not found: {file_path}")

    print(f"\nFixed HttpContext ambiguity in {fixed_count} files")


if __name__ == "__main__":
    main()
