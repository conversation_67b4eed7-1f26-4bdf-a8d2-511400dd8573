#!/usr/bin/env python3

import os
import re
import sys


def fix_httpcontext_current_in_file(file_path):
    """
    Replace HttpContext.Current with HttpContextProvider.Current in a C# file
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content

        # Replace HttpContext.Current with HttpContextProvider.Current
        # This handles the basic pattern
        content = re.sub(
            r"\bHttpContext\.Current\b", "HttpContextProvider.Current", content
        )

        # Check if we made changes and if we need to add the using statement
        if content != original_content:
            # Check if the file already has the necessary using statement
            if "using Cerebrum30.Utility;" not in content:
                # Find the last using statement and add after it
                using_pattern = r"(using [^;]+;[\r\n]+)"
                using_matches = list(re.finditer(using_pattern, content))
                if using_matches:
                    # Insert after the last using statement
                    last_using = using_matches[-1]
                    insert_pos = last_using.end()
                    content = (
                        content[:insert_pos]
                        + "using Cerebrum30.Utility;\n"
                        + content[insert_pos:]
                    )
                else:
                    # No using statements found, add at the beginning
                    content = "using Cerebrum30.Utility;\n" + content

            # Write the modified content back
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)

            return True

        return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    # List of files that use HttpContext.Current
    files_to_fix = [
        "Cerebrum30/Areas/Measurements/Controllers/MeasurementController.cs",
        "Cerebrum30/Areas/Measurements/DataAccess/MeasurementRepository.cs",
        "Cerebrum30/Areas/Daysheet/DataAccess/AppointmentStatusLogRepository.cs",
        "Cerebrum30/Areas/WebBooking/DataAccess/WebBookingRepository.cs",
        "Cerebrum30/Areas/Schedule/Mappers/ScheduleMapper.cs",
        "Cerebrum30/Areas/PdfConversions/Controllers/PdfTestController.cs",
        "Cerebrum30/Areas/Labs/Controllers/OLISController.cs",
        "Cerebrum30/Areas/VP/Controllers/VPController.cs",
        "Cerebrum30/Areas/VP/DataAccess/VPRepository.cs",
        "Cerebrum30/Areas/EForms/Controllers/BLL/EformsBLL.cs",
        "Cerebrum30/Areas/VirtualVisit/Controllers/VirtualVisitController.cs",
        "Cerebrum30/Areas/ExternalDocument/DataAccess/IUnitOfWorkExternalDocument.cs",
        "Cerebrum30/Utility/PDF_Writer.cs",
        "Cerebrum30/Utility/Helper.cs",
        "Cerebrum30/Global.asax.cs",
        "Cerebrum30/Controllers/ContactManagersWebAPIController.cs",
        "Cerebrum30/Security/CerebrumUser.cs",
        "Cerebrum30/API/Demographics/DemographicsAPI.cs",
        "Cerebrum30/API/OneIdToken/OneIdTokenController.cs",
        "Cerebrum30/API/Appointments/AppointmentsAPI.cs",
        "Cerebrum30/Helpers/CustomHelpers.cs",
        "Cerebrum30/DAL/TransferC2Data/TransterTable.cs",
        "Cerebrum30/DAL/DataAccess/Infrastructure/RadGenericRepository.cs",
        "Cerebrum30/DAL/DataAccess/Infrastructure/GenericRepository.cs",
        "Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkEMRRecords.cs",
        "Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkPdfConversion.cs",
        "Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkHealthCardService.cs",
        "Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkPatientChart.cs",
        "Cerebrum30/Models/HomeViewModels.cs",
    ]

    fixed_files = []

    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_httpcontext_current_in_file(file_path):
                fixed_files.append(file_path)
                print(f"Fixed: {file_path}")
            else:
                print(f"No changes needed: {file_path}")
        else:
            print(f"File not found: {file_path}")

    print(f"\nSummary: Fixed {len(fixed_files)} files")
    for file_path in fixed_files:
        print(f"  - {file_path}")


if __name__ == "__main__":
    main()
