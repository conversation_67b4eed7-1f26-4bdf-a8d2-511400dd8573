#!/usr/bin/env python3

import os
import re


def fix_httpcontext_ambiguity(file_path):
    """Fix HttpContext CS0104 ambiguity by adding alias and replacing System.Web"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Check if file already has HttpContext alias
        if "using HttpContext = Microsoft.AspNetCore.Http.HttpContext;" in content:
            return False

        # Check if file has System.Web import
        has_system_web = "using System.Web;" in content

        if not has_system_web:
            return False

        # Replace System.Web import with ASP.NET Core imports and alias
        new_content = content.replace(
            "using System.Web;",
            "using Microsoft.AspNetCore.Http;\nusing HttpContext = Microsoft.AspNetCore.Http.HttpContext;",
        )

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(new_content)

        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    # List of files with CS0104 HttpContext errors
    files_to_fix = [
        "./Cerebrum30/API/Appointments/AppointmentsAPI.cs",
        "./Cerebrum30/DAL/DataAccess/Infrastructure/GenericRepository.cs",
        "./Cerebrum30/Areas/WebBooking/DataAccess/WebBookingRepository.cs",
        "./Cerebrum30/Areas/Daysheet/DataAccess/AppointmentStatusLogRepository.cs",
        "./Cerebrum30/Utility/Helper.cs",
        "./Cerebrum30/Global.asax.cs",
        "./Cerebrum30/Areas/EForms/Controllers/BLL/EformsBLL.cs",
        "./Cerebrum30/Models/HomeViewModels.cs",
        "./Cerebrum30/DAL/TransferC2Data/TransterTable.cs",
        "./Cerebrum30/DAL/DataAccess/Infrastructure/RadGenericRepository.cs",
        "./Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkPdfConversion.cs",
        "./Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkPatientChart.cs",
        "./Cerebrum30/DAL/DataAccessGB/UnitOfWorks/UnitOfWorkHealthCardService.cs",
        "./Cerebrum30/Areas/Schedule/Mappers/ScheduleMapper.cs",
    ]

    fixed_count = 0

    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_httpcontext_ambiguity(file_path):
                print(f"Fixed HttpContext ambiguity in: {file_path}")
                fixed_count += 1
            else:
                print(f"No changes needed for: {file_path}")
        else:
            print(f"File not found: {file_path}")

    print(f"\nFixed HttpContext ambiguity in {fixed_count} files")


if __name__ == "__main__":
    main()
