#!/usr/bin/env python3

import os
import re


def fix_isauthenticated_references(file_path):
    """
    Replace HttpRequest.IsAuthenticated with HttpContext.User.Identity.IsAuthenticated
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content

        # Replace HttpContextProvider.Current.Request.IsAuthenticated with HttpContextProvider.Current.User.Identity.IsAuthenticated
        content = re.sub(
            r"HttpContextProvider\.Current\.Request\.IsAuthenticated",
            "HttpContextProvider.Current.User.Identity.IsAuthenticated",
            content,
        )

        # Also handle direct context.Request.IsAuthenticated patterns
        content = re.sub(
            r"(\w+)\.Request\.IsAuthenticated",
            r"\1.User.Identity.IsAuthenticated",
            content,
        )

        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return True

        return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    # Find all files that might have IsAuthenticated references
    files_to_check = []

    # Walk through the Cerebrum30 directory
    for root, dirs, files in os.walk("Cerebrum30"):
        for file in files:
            if file.endswith(".cs"):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        if "IsAuthenticated" in content:
                            files_to_check.append(file_path)
                except:
                    pass

    fixed_files = []

    for file_path in files_to_check:
        if fix_isauthenticated_references(file_path):
            fixed_files.append(file_path)
            print(f"Fixed: {file_path}")

    print(f"\nSummary: Fixed {len(fixed_files)} files")
    for file_path in fixed_files:
        print(f"  - {file_path}")


if __name__ == "__main__":
    main()
