# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Cerebrum is a cardiology Electronic Medical Record (EMR) system currently being migrated from ASP.NET Framework 4.8 to ASP.NET Core 8.0. The application is designed for healthcare practices and includes comprehensive patient management, appointment scheduling, diagnostic testing, and billing functionality.

## Architecture

### Current State
- **Main Application**: Cerebrum30 (ASP.NET Framework 4.8 MVC application)
- **Core Migration Target**: Cerebrum30Core (ASP.NET Core 8.0 - currently minimal setup)
- **Database**: SQL Server with Entity Framework 6.x
- **Frontend**: Mix of server-rendered Razor views and Angular 2.0 components
- **Deployment**: Docker containers with Traefik reverse proxy

### Key Components
- **Areas**: Modular feature organization (Admin, Schedule, Measurements, Medications, Labs, etc.)
- **BLL Layer**: Business Logic Layer projects (Cerebrum.BLL, Cerebrum.Labs, etc.)
- **Data Layer**: Entity Framework models in C2/ directory and various Data Access repositories
- **API Controllers**: RESTful APIs for frontend consumption
- **Background Services**: Console applications for data processing (HRM imports, reminders, etc.)

### Areas Structure
The application uses ASP.NET Areas for feature organization:
- **Admin**: System administration and configuration
- **Schedule**: Appointment scheduling and daysheet management  
- **Measurements**: Diagnostic test results and reporting
- **Medications**: Prescription management and drug interactions
- **Labs**: HL7 lab results and OLIS integration
- **VP**: Visit Page for patient encounters
- **Bills**: Billing and claims processing
- **ContactManagers**: Patient communication and recalls

## Development Commands

### Build and Run
```powershell
# Clean build
./build.ps1

# Development with Docker (recommended)
./dev.ps1

# Development with shared database
./dev.ps1 -SharedDb

# Clean build before running
./dev.ps1 -CleanBuild

# Update hosts file for local development
./dev.ps1 -UpdateHost
```

### Database Operations
```powershell
# Run migrations
./migrate.ps1

# Create new migration
./new-migration.ps1 <MigrationName>

# Rollback migration
./rollback.ps1
```

### Testing
```powershell
# Basic build test
./test-basic-build.sh

# Run with build bypass (for faster iteration)
./run-with-build-bypass.sh
```

### Access Points
- Main Application: http://cerebrum.c3.localhost:81/
- Traefik Dashboard: http://traefik.localhost:8080/dashboard/
- Voice Reminder API: http://voicereminder.c3.localhost/
- Radiology API: http://rad.c3.localhost/

## Migration Context

This codebase is actively being migrated from .NET Framework to .NET Core 8.0 using an incremental approach with YARP reverse proxy. See `INCREMENTAL_MIGRATION_PLAN.md` for detailed migration strategy.

### Migration Status Indicators
- Files with `// TODO: ASP.NET Core migration` comments need conversion
- Controllers ending in `WebAPIController.cs` are API endpoints
- Views may have compatibility issues with Razor syntax changes
- Many dependencies need updating for .NET Core compatibility

### Common Migration Tasks
- Converting `System.Web.Mvc` to `Microsoft.AspNetCore.Mvc`
- Updating Entity Framework 6.x to Entity Framework Core
- Converting Web.config settings to appsettings.json
- Updating authentication/authorization middleware
- Converting custom ActionFilters and Attributes

## Code Patterns

### Controllers
- Use area-based organization with `{Area}AreaRegistration.cs` files
- API controllers typically inherit from `BaseController`
- Authentication handled through custom `CerebrumAuthorizeAttribute`
- Extensive use of ViewModels for data transfer

### Data Access
- Repository pattern with Unit of Work
- Interface-based repositories (I{Feature}Repository)
- Entity Framework with DbContext per area/feature
- Connection strings configured per environment

### Views and Frontend
- Razor views with shared layouts
- Bootstrap 3.x for styling (needs updating)
- jQuery and Angular 2.0 (legacy, needs modernization)
- Extensive use of partial views for modularity

### Configuration
- Multiple Web.config transforms for different environments
- Docker configuration in docker-compose.yml
- Environment-specific settings in configs.env

## Development Guidelines

### When Making Changes
1. Always test both .NET Framework and Core versions if applicable
2. Update corresponding tests when modifying business logic
3. Maintain backwards compatibility during migration phase
4. Document breaking changes in migration notes
5. Use the repository pattern for new data access code
6. Follow existing naming conventions for consistency

### Security Considerations
- Patient data requires strict access controls
- All user input must be validated and sanitized
- Authentication required for all non-public endpoints
- Audit logging required for sensitive operations
- HIPAA compliance considerations for patient data

### Performance Notes
- Large datasets common (thousands of patients/appointments)
- Reports can be resource-intensive
- Database queries should be optimized with proper indexing
- Consider caching for frequently accessed reference data

## Common Issues

### Build Issues
- Docker Desktop must be running for development
- Visual Studio may have issues with .dll copying - stop Docker containers and retry
- NuGet package restore may require Azure DevOps credentials
- Multiple .NET Framework versions required (see README.md)

### Database Access
- Use `sqlcmd open ads` to launch Azure Data Studio with auto-login
- Database credentials stored in `~/.sqlcmd/sqlconfig`
- Multiple database contexts may conflict during migration

### Migration-Specific Issues
- Ambiguous references between System.Web and Microsoft.AspNetCore namespaces
- Configuration differences between Web.config and appsettings.json
- Dependency injection container differences
- Middleware pipeline ordering in ASP.NET Core

## Key Files and Locations

### Configuration
- `Cerebrum30/Web.Base.config` - Base web configuration
- `docker-compose.yml` - Development environment setup
- `appsettings.json` - ASP.NET Core configuration
- `build-pipeline.yaml` - CI/CD configuration

### Migration Utilities
- `fix_*.py` and `fix_*.sh` - Automated migration scripts
- `INCREMENTAL_MIGRATION_PLAN.md` - Migration strategy
- `TESTING_STRATEGY.md` - Testing approach for migration

### Critical Business Logic
- `Cerebrum.BLL/` - Core business logic layer
- `Cerebrum30/Areas/*/Controllers/` - Feature controllers
- `Cerebrum30/C2/` - Entity Framework models
- `Cerebrum30/Areas/*/DataAccess/` - Repository implementations