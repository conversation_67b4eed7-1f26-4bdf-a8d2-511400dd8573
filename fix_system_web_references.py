#!/usr/bin/env python3

import os
import re


def fix_system_web_references(file_path):
    """
    Replace System.Web.HttpContextProvider references with HttpContextProvider
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content

        # Replace System.Web.HttpContextProvider with HttpContextProvider
        content = re.sub(
            r"\bSystem\.Web\.HttpContextProvider\b", "HttpContextProvider", content
        )

        # Replace System.Web.HttpContext with var (since we're using HttpContextProvider.Current)
        content = re.sub(
            r"\bSystem\.Web\.HttpContext\s+(\w+)\s*=\s*", r"var \1 = ", content
        )

        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return True

        return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    files_to_fix = [
        "Cerebrum30/Areas/Measurements/Controllers/MeasurementController.cs",
        "Cerebrum30/Areas/PdfConversions/Controllers/PdfTestController.cs",
        "Cerebrum30/Areas/Labs/Controllers/OLISController.cs",
        "Cerebrum30/Areas/VP/Controllers/VPController.cs",
        "Cerebrum30/Controllers/ContactManagersWebAPIController.cs",
    ]

    fixed_files = []

    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_system_web_references(file_path):
                fixed_files.append(file_path)
                print(f"Fixed: {file_path}")
            else:
                print(f"No changes needed: {file_path}")
        else:
            print(f"File not found: {file_path}")

    print(f"\nSummary: Fixed {len(fixed_files)} files")


if __name__ == "__main__":
    main()
