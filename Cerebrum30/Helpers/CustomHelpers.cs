﻿using Cerebrum.BLL.AdminUser;
using Cerebrum.Data;
using Cerebrum.ViewModels.ApplicationSetting;
using Cerebrum.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Routing;
using System.IO;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Cerebrum30.Utility;

namespace Cerebrum30.Helpers
{
    public static class CustomHelpers
    {
        public static string GetRoutePrefix(this IHtmlHelper helper)
        {
            // Get the controller type
            // TODO: ViewContext.Controller doesn't exist in ASP.NET Core - using placeholder
            var controllerType = typeof(object);

            // TODO: RoutePrefixAttribute doesn't exist in ASP.NET Core - commenting out
            // Get the RoutePrefix Attribute
            // var routePrefixAttribute = (RoutePrefixAttribute)Attribute.GetCustomAttribute(
            //     controllerType, typeof(RoutePrefixAttribute));
            object routePrefixAttribute = null;

            string routePrefix = "";

            // TODO: routePrefixAttribute is null object - commenting out Prefix access
            // if (routePrefixAttribute != null && !String.IsNullOrWhiteSpace(routePrefixAttribute.Prefix))
            // {
            //     routePrefix = routePrefixAttribute.Prefix.ToLower();
            // }

            return routePrefix;
        }

        public static string GetAreaName(this IHtmlHelper helper)
        {
            string areaName = "";

            var routeData = helper.ViewContext.RouteData;
            if (routeData.DataTokens.ContainsKey("area"))
            {
                areaName = (string)routeData.DataTokens["area"];
            }

            return areaName;
        }

        public static string GetControllerName(this IHtmlHelper helper)
        {
            string controllerName = "";
            var routeValues = helper.ViewContext.RouteData.Values;

            if (routeValues.ContainsKey("controller"))
            {
                controllerName = (string)routeValues["controller"];
            }

            return controllerName;
        }

        public static string GetActionName(this IHtmlHelper helper)
        {
            string actionName = "";
            var routeValues = helper.ViewContext.RouteData.Values;

            if (routeValues.ContainsKey("action"))
            {
                actionName = (string)routeValues["action"];
            }

            return actionName;
        }

        public static string GetReferrerUrl(this IHtmlHelper helper)
        {
            // TODO: HttpContextProvider.Current doesn't exist in ASP.NET Core - using placeholder
            // if (HttpContextProvider.Current.Request.UrlReferrer == null)
            //     return string.Empty;
            // string referrerUrl = HttpContextProvider.Current.Request.UrlReferrer.ToString();
            // return referrerUrl;
            return string.Empty;
        }

        public static string GetCurrentUrl(this IHtmlHelper helper)
        {
            // TODO: ASP.NET Core migration - HttpContextProvider.Current doesn't exist
            // Need to access HttpContext through ViewContext
            var request = helper.ViewContext.HttpContext.Request;
            string currentUrl = $"{request.Scheme}://{request.Host}{request.Path}{request.QueryString}";
            return currentUrl;
        }

        public static string GetReturnUrl(this IHtmlHelper helper, string btnText = "")
        {
            // TODO: ASP.NET Core migration - HttpContextProvider.Current doesn't exist
            var request = helper.ViewContext.HttpContext.Request;
            string currentUrl = $"{request.Scheme}://{request.Host}{request.Path}{request.QueryString}";
            string returnUrl = "&returnUrl=" + currentUrl;
            if (!String.IsNullOrWhiteSpace(btnText))
            {
                returnUrl = returnUrl + "&returnText=" + btnText;
            }
            return returnUrl;
        }

        public static string GetApplicationVersion(this IHtmlHelper helper)
        {
            string version = System.Configuration.ConfigurationManager.AppSettings["CerebrumVersion"];

            if (!String.IsNullOrWhiteSpace(version))
            {
                version = "v" + version;
            }

            return version;
        }

        // gets prod, dev or test
        public static string GetEnvironment(this IHtmlHelper helper)
        {
            string environment = System.Configuration.ConfigurationManager.AppSettings["CerebrumEnvironment"];

            if (!String.IsNullOrWhiteSpace(environment))
            {
                return environment;
            }
            else
            {
                return "";
            }
        }

        public static string PreserveNewLines(this IHtmlHelper htmlHelper, string message)
        {
            if (String.IsNullOrWhiteSpace(message)) return null;

            var htmlContent = htmlHelper.Raw(htmlHelper.Encode(message).Replace("\n", "<br/>").Replace("\r", "<br/>"));
            using (var writer = new StringWriter())
            {
                htmlContent.WriteTo(writer, System.Text.Encodings.Web.HtmlEncoder.Default);
                return writer.ToString();
            }
        }

        public static HtmlString Truncate(this IHtmlHelper helper, string input, int length = 25, bool addToolTip = false, bool toolTipHTML = false)
        {
            if ((!String.IsNullOrWhiteSpace(input)))
            {
                if (input.Length <= length)
                {
                    return new HtmlString(input);
                }
                else
                {
                    string truncatedSTR = input.Substring(0, length) + "...";

                    if (addToolTip)
                    {
                        string html = toolTipHTML ? "data-html=\"true\"" : "data-html=\"false\"";
                        truncatedSTR = string.Format("<span data-toggle=\"tooltip\" data-placement=\"bottom\" {2} title=\"{0}\">{1}</span>", input, truncatedSTR, html);
                    }

                    return new HtmlString(truncatedSTR);
                }
            }
            else
            {
                return new HtmlString("");
            }
        }

        public static IHtmlContent ModalHeader(this IHtmlHelper helper, string title)
        {
            string header = string.Format("<div class=\"modal-header\">" +
            "<button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-hidden=\"true\">&times;</button>" +
            "<h4 class=\"modal-title\">{0}</h4></div>", title);

            return new HtmlString(header);
        }
        public static IHtmlContent ModalHeader(this IHtmlHelper helper, string title, string info)
        {
            string header = string.Format("<div class=\"modal-header\">" +
            "<button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-hidden=\"true\">&times;</button>" +
            "<h2 class=\"modal-title\">{0}</h2><h4><strong>{1}</strong></h4></div>", title, info);

            return new HtmlString(header);
        }
        public static IHtmlContent ModalFooter(this IHtmlHelper helper, string btnText = "Save", string btnColor = "green", string ccsClasses = "", bool isInfoModal = false, bool addAnother = false, bool showMedCoverAndSadieLinks = false)
        {
            string footer = "";
            if (isInfoModal)
            {
                footer = "<div class=\"modal-footer\"><button class=\"btn btn-default btn-sm\" data-dismiss=\"modal\">Close</button></div>";
            }
            else
            {
                string colorClass = "btn-success";
                if (btnColor.ToLower() == "red") { colorClass = "btn-danger"; }
                else if (btnColor.ToLower() == "blue") { colorClass = "btn-primary"; }

                string links = string.Empty;
                if (showMedCoverAndSadieLinks)
                {
                    if (Cerebrum.DHDR.Services.Config.GetConfiguration?.CerebrumConfig.Enabled == true)
                    {
                        List<VMApplicationSetting> list = GetApplicationSettings();
                        string urlMedCoverage = list.Where(i => i.key == "MedCoverageEndPoint").FirstOrDefault().value;
                        string urlSadie = list.Where(i => i.key == "SadieEndPoint").FirstOrDefault().value;
                        if (!string.IsNullOrEmpty(urlMedCoverage) && !string.IsNullOrEmpty(urlSadie))
                        {
                            links = "<span class='pull-left text-left' style='font-size:10px;'><a href='" + urlMedCoverage + "' class='text-danger' title='Click to Check Medication Coverage' target='_blank'>Check Medication Coverage - Drug Formulary</a>" +
                                    "<br><a href='" + urlSadie + "' class='text-danger' title='Click to View SADIE' target='_blank'>Special Authorization Digital Information Exchange (SADIE)</a></span>";
                        }
                    }
                }
                string another = addAnother ? "<button type=\"submit\" value=\"addnew\" class=\"btn btn-sm btn-primary modal-submit-btn btn-spacing\">Save and add new</button>" : "";
                footer = "<div class=\"modal-footer\">" + links +
                "<button type=\"submit\" value=\"save\" class=\"btn btn-sm modal-submit-btn btn-spacing " + colorClass + " " + ccsClasses + "\">" + btnText.Trim() + "</button>" +
                another +
                "<button class=\"btn btn-default btn-sm\" data-dismiss=\"modal\">Cancel</button>" +
                "</div>";
            }
            return new HtmlString(footer);
        }

        public static IHtmlContent ReferrerButton(this IHtmlHelper helper, string btnText)
        {
            string referrerUrl = helper.ViewContext.HttpContext.Request.Headers["Referer"].ToString();
            string button = string.Format("<a href=\"{0}\" class=\"btn btn-default btn-sm\">{1}</a>", referrerUrl, btnText);

            return new HtmlString(button);
        }

        public static IHtmlContent DropdownMultiSelect(this IHtmlHelper helper, List<VMLookupItem> items, string listName, bool showSaveButton, string saveCallBackFunction)
        {
            string drpHtml = "";
            if (!String.IsNullOrWhiteSpace(listName))
            {
                for (int i = 0; i < items.Count(); i++)
                {

                    listName = listName.Trim();
                    var item = items[i];
                    var inputNameBase = listName + "[" + i + "].";
                    var inputIdBase = listName + "_" + i + "__";
                    var label = new TagBuilder("label");
                    label.AddCssClass("lbl-multiselect");

                    // TODO: System.Web.Mvc doesn't exist in ASP.NET Core - commenting out
                    // drpHtml += System.Web.Mvc.Html.InputExtensions.Hidden(helper, inputNameBase + "Text", item.Text, new { @id = inputIdBase + "Text" }).ToHtmlString();
                    // drpHtml += System.Web.Mvc.Html.InputExtensions.Hidden(helper, inputNameBase + "Value", item.Value, new { @id = inputIdBase + "Value" }).ToHtmlString();
                    // drpHtml += System.Web.Mvc.Html.InputExtensions.Hidden(helper, inputNameBase + "Code", item.Code, new { @id = inputIdBase + "Code" }).ToHtmlString();
                    // drpHtml += System.Web.Mvc.Html.InputExtensions.Hidden(helper, inputNameBase + "DisplayOrder", item.DisplayOrder, new { @id = inputIdBase + "DisplayOrder" }).ToHtmlString();


                    // TODO: System.Web.Mvc doesn't exist in ASP.NET Core - commenting out
                    // var checkBox = System.Web.Mvc.Html.InputExtensions.CheckBox(helper, inputNameBase + "Selected", item.Selected, new
                    // {
                    //     @id = inputIdBase + "Selected",
                    //     @autocomplete = "off",
                    //     @class = "cb-drp-multiselect",
                    //     data_code = item.Code,
                    //     data_item_value = item.Value,
                    //     data_item_text = item.Text,
                    //     data_item_displayorder = item.DisplayOrder
                    // }).ToHtmlString();
                    var checkBox = "";

                    label.InnerHtml.SetHtmlContent(checkBox + item.Text);

                    drpHtml += label.ToString();
                }

                if (showSaveButton)
                {
                    var btnSave = new TagBuilder("button");
                    btnSave.AddCssClass("btn");
                    btnSave.AddCssClass("btn-xs");
                    btnSave.AddCssClass("btn-primary");
                    btnSave.AddCssClass("modal-submit-btn");
                    btnSave.AddCssClass("btn-spacing");
                    btnSave.AddCssClass("btn-save-drp-multi");

                    btnSave.Attributes.Add("type", "button");

                    if (!String.IsNullOrWhiteSpace(saveCallBackFunction))
                    {
                        btnSave.Attributes.Add("data-call-back-function", saveCallBackFunction);
                    }

                    btnSave.InnerHtml.SetContent("Save");

                    drpHtml += btnSave.ToString();
                }
            }
            return new HtmlString(drpHtml);
        }
        public static IHtmlContent DropDownLookup(this IHtmlHelper helper, string name, List<VMLookupItem> lookupItems, object htmlAttributes, string selectedItem = null)
        {
            //Creating a select element using TagBuilder class which will create a dropdown.
            TagBuilder dropdown = new TagBuilder("select");
            //Setting the name and id attribute with name parameter passed to this method.
            dropdown.Attributes.Add("name", name);
            dropdown.Attributes.Add("id", name);

            //Created StringBuilder object to store option data fetched oen by one from list.
            StringBuilder options = new StringBuilder();
            //Iterated over the IEnumerable list.
            foreach (var item in lookupItems)
            {
                if (!String.IsNullOrWhiteSpace(selectedItem) && item.Value == selectedItem)
                {
                    options = options.Append("<option selected=\"selected\" value='" + item.Value + "'>" + item.Text + "</option>");
                }
                else
                {
                    options = options.Append("<option value='" + item.Value + "'>" + item.Text + "</option>");
                }

            }
            //assigned all the options to the dropdown using innerHTML property.
            dropdown.InnerHtml.SetHtmlContent(options.ToString());
            //Assigning the attributes passed as a htmlAttributes object.
            dropdown.MergeAttributes(new RouteValueDictionary(htmlAttributes));
            //Returning the entire select or dropdown control in HTMLString format.
            return new HtmlString(dropdown.ToString());
        }
        public static IHtmlContent GetPatientInfo(this IHtmlHelper helper, int patientId, int appointmentId = 0, int appointmentTestId = 0, int testId = 0)
        {
            if (patientId > 0)
            {
                var request = new Cerebrum.ViewModels.Patient.VMPatientInfoRequest();
                request.PatientId = patientId;
                request.AppointmentId = appointmentId;
                request.AppointmentTestId = appointmentTestId;
                request.TestId = testId;
                return helper.Partial("~/Views/Shared/_PatientInfoMenu.cshtml", request);
            }
            return new HtmlString("");
        }

        public static IHtmlContent GetPreviousTestsButtons(this IHtmlHelper helper, int patientId, string placeHolder)
        {
            if (patientId > 0 && !String.IsNullOrWhiteSpace(placeHolder))
            {
                var request = new Cerebrum.ViewModels.TestBase.VMPreviousTestsRequest();
                request.PatientId = patientId;
                request.PlaceHolder = placeHolder;
                return helper.Partial("~/Views/Shared/_PreviousTestsButtons.cshtml", request);
            }
            return new HtmlString("");
        }

        public static HtmlString GetPatientName(this IHtmlHelper helper)
        {
            string patientName = "";
            try
            {
                if (helper.ViewBag.PatientInfo != null)
                {
                    Cerebrum.ViewModels.Medications.VMPatient patient = (Cerebrum.ViewModels.Medications.VMPatient)helper.ViewBag.PatientInfo;
                    patientName = patient.FullName;
                    return new HtmlString(patientName);

                }
                else
                {
                    return new HtmlString("");
                }
            }
            catch
            {
                return new HtmlString("");
            }
        }

        public static IHtmlContent CustomEnumDropDownListFor<TModel, TEnum>(
   this IHtmlHelper<TModel> htmlHelper, Expression<Func<TModel, TEnum>> expression, object htmlAttributes, SelectListItem topItem = null)
        {
            // TODO: ModelMetadata.FromLambdaExpression doesn't exist in ASP.NET Core - using placeholder
            // var metadata = ModelMetadata.FromLambdaExpression(expression, htmlHelper.ViewData);
            var values = Enum.GetValues(typeof(TEnum)).Cast<TEnum>();
            var items =
                values.Select(
                   value =>
                   new SelectListItem
                   {
                       Text = GetEnumerationDescription(value),
                       Value = Convert.ToInt16((TEnum)Enum.Parse(typeof(TEnum), value.ToString())).ToString(),
                       // TODO: metadata doesn't exist - using placeholder
                       Selected = false
                   });

            if (topItem != null)
            {
                var newItems = items.ToList();
                newItems.Insert(0, topItem);
                items = newItems.AsEnumerable();
            }
            var attributes = HtmlHelper.AnonymousObjectToHtmlAttributes(htmlAttributes);
            return htmlHelper.DropDownListFor(expression, items, attributes);
        }
        public static string GetEnumerationDescription<TEnum>(TEnum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attributes = (DescriptionAttribute[])field.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }

        public static string GetConsultNoteTitle(string state)
        {
            var _state = state.ToLower();
            // add more states here
            if (_state == "consult provided")
            {
                return " provided consult";
            }
            else if (_state == "clarification requested")
            {
                return " requested clarification";
            }
            else if (_state == "completed")
            {
                return " completed case";
            }
            else if (_state == "returned")
            {
                return " returned consult";
            }
            else if (_state == "cancelled")
            {
                return " cancelled case";
            }
            else if (_state == "more info requested")
            {
                return " requested more info";
            }
            else if (_state == "more info provided")
            {
                return " provided more info";
            }
            else
            {
                return " added note";
            }
        }
        public static string GetStatusText(string state)
        {
            var _state = state.ToLower();
            // add more states here
            if (_state == "consult provided")
            {
                return "Consult provided";
            }
            else if (_state == "clarification requested")
            {
                return "Clarification requested";
            }
            else if (_state == "completed")
            {
                return "Case completed";
            }
            else if (_state == "returned")
            {
                return "Consult returned";
            }
            else if (_state == "cancelled")
            {
                return "Case cancelled";
            }
            else if (_state == "submitted")
            {
                return "New case submitted";
            }
            else if (_state == "more info requested")
            {
                return "More info requested";
            }
            else if (_state == "more info provided")
            {
                return "More info provided";
            }
            else
            {
                return state;
            }
        }
        public static string GetFlagColor(string flag)
        {
            var _flag = flag.ToUpper();
            // add more states here
            if (_flag == "RESEARCH")
            {
                return "orange";
            }
            else if (_flag == "EDUCATION")
            {
                return "lightblue";
            }
            else if (_flag == "OTHER")
            {
                return "lawngreen";
            }
            else
            {
                return "black";
            }
        }
        public static string GetErrorMessageByStatus(HttpStatusCode statusCode)
        {
            if (statusCode == HttpStatusCode.RequestTimeout)
            {
                return "Request Timeout";
            }
            else if (statusCode == HttpStatusCode.Unauthorized || statusCode == (HttpStatusCode)0)
            {
                return "Your user account is not properly configured, please contact administrator";//"You are unauthorized to view this page";
            }
            else
            {
                return "OTN API is not available";
            }
        }

        private static List<VMApplicationSetting> GetApplicationSettings()
        {
            CerebrumContext context = new CerebrumContext();
            ApplicationSettingBLL bll = new ApplicationSettingBLL(context);
            List<VMApplicationSetting> list = bll.GetApplicationSettings();

            return list;
        }
    }
}