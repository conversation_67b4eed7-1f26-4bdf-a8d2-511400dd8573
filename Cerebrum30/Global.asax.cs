﻿using System;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
// TODO: Replace with ASP.NET Core equivalents in Startup.cs/Program.cs
// using System.Web.Mvc;
// using System.Web.Optimization;
// using System.Web.Routing;
using Cerebrum30.Controllers;
using System.IO;
// using System.Web.Http;
using System.Data.Entity.Validation;
using System.Web.SessionState;
using AutoMapper;
using Cerebrum30.Hubs;
using Microsoft.ApplicationInsights.Extensibility;
using Cerebrum30.ApplicationInsights;
using log4net;
using Cerebrum30.Utility;
using Microsoft.AspNetCore.Http.Extensions;

namespace Cerebrum30
{
    public class MvcApplication : System.Web.HttpApplication
    {
        private log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod()?.DeclaringType ?? typeof(MvcApplication));
        protected void Application_Start()
        {
            // TODO: ASP.NET Core migration - These initialization calls don't exist in ASP.NET Core
            // They should be moved to Program.cs or Startup.cs

            //CSRF tokens are unique per-practice.
            // System.Web.Helpers.AntiForgeryConfig.UniqueClaimTypeIdentifier = nameof(Cerebrum.Data.ApplicationRole.PracticeId);

            // TelemetryConfiguration.Active.TelemetryInitializers.Add(new ApplicationInsightsTelemetryInitializer());
            // AreaRegistration.RegisterAllAreas();
            // FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            // GlobalConfiguration.Configure(WebApiConfig.Register);
            // GlobalConfiguration.Configure(WebApiConfig.RegisterVirtualVisit);
            // RouteConfig.RegisterRoutes(RouteTable.Routes);
            // BundleConfig.RegisterBundles(BundleTable.Bundles);

            // log4net.Config.XmlConfigurator.Configure(new FileInfo(Server.MapPath("~/Web.config")));
            // DataAnnotationsModelValidatorProvider.AddImplicitRequiredAttributeForValueTypes = false;

            //Query Log: Disable 2017-07-05
            //DbInterception.Add(new QueryLog());

            //// for log4net
            //GlobalContext.Properties["user"] = new GetCurrentUser();
            //GlobalContext.Properties["page"] = new GetCurrentPage();
            //GlobalContext.Properties["port"] = new GetCurrentPort();
            //GlobalContext.Properties["ipaddress"] = new GetCurrentIPAddress();
            //GlobalContext.Properties["LoggedIn"] = (new IsAuthenticatedLog());
            AutomapperConfig.RegisterMapper();
        }

        protected void Application_BeginRequest()
        {
            //System.Globalization.CultureInfo newCulture = (System.Globalization.CultureInfo)System.Threading.Thread.CurrentThread.CurrentCulture.Clone();

            //newCulture.DateTimeFormat.ShortDatePattern = "MM/dd/yyyy";
            //newCulture.DateTimeFormat.DateSeparator = "/";

            //System.Threading.Thread.CurrentThread.CurrentCulture = newCulture;
            // Response.Headers["X-Frame-Options"] = "DENY";
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            var httpContext = ((MvcApplication)sender).Context;
            // TODO: ASP.NET Core migration - controller/action tracking not needed for basic error handling
            // var currentController = " ";
            // var currentAction = " ";
            // TODO: ASP.NET Core migration - RouteTable doesn't exist in ASP.NET Core
            // var currentRouteData = RouteTable.Routes.GetRouteData(new HttpContextWrapper(httpContext));
            // TODO: ASP.NET Core migration - RouteData doesn't exist in ASP.NET Core - commenting out unused variables
            // object currentRouteData = null;

            // TODO: ASP.NET Core migration - RouteData doesn't exist in ASP.NET Core
            // if (currentRouteData != null)
            // {
            //     if (currentRouteData.Values["controller"] != null && !String.IsNullOrEmpty(currentRouteData.Values["controller"].ToString()))
            //     {
            //         currentController = currentRouteData.Values["controller"].ToString();
            //     }
            //
            //     if (currentRouteData.Values["action"] != null && !String.IsNullOrEmpty(currentRouteData.Values["action"].ToString()))
            //     {
            //         currentAction = currentRouteData.Values["action"].ToString();
            //     }
            // }
            if (string.IsNullOrEmpty(LogicalThreadContext.Properties["ipaddress"]?.ToString()))
            {
                var request = HttpContextProvider.Current?.Request;
                if (request != null)
                {
                    LogicalThreadContext.Properties["ipaddress"] = RequestUtils.GetIpAddress(request);
                }
            }
            if (string.IsNullOrEmpty(LogicalThreadContext.Properties["page"]?.ToString()))
            {
                var request = HttpContextProvider.Current?.Request;
                if (request != null)
                {
                    LogicalThreadContext.Properties["page"] = request.GetDisplayUrl();
                }
            }

            var ex = Server.GetLastError();
            // TODO: ASP.NET Core migration - RouteData doesn't exist in ASP.NET Core
            // var routeData = new RouteData();
            // object routeData = null;
            // var action = "GenericError"; // Commented out since route data assignment is commented out

            // TODO: Exception type-based action assignment (commented out since not used)
            // if (ex is FileNotFoundException || ex is DirectoryNotFoundException)
            // {
            //     action = "NotFound";
            // }
            // else if (ex is UnauthorizedAccessException)
            // {
            //     action = "Unauthorized";
            // }
            // else if (ex is ArgumentException || ex is ArgumentNullException)
            // {
            //     action = "BadRequest";
            // }
            // else
            // {
            //     // For any other exception, use generic error handling
            //     action = "GenericError";
            // }

            if (ex is DbEntityValidationException)
            {
                var dberror = ex as DbEntityValidationException;
                _log.Error(ex.Message);
                if (ex.InnerException != null)
                    _log.Error(ex.InnerException.Message);
                if (dberror?.EntityValidationErrors != null)
                {
                    foreach (var eve in dberror.EntityValidationErrors)
                    {
                        _log.Error($"Entity of type \"{eve.Entry.Entity.GetType().Name}\" in state \"{eve.Entry.State}\" has the following validation errors:");
                        foreach (var ve in eve.ValidationErrors)
                        {
                            _log.Error($"- Property: \"{ve.PropertyName}\", Error: \"{ve.ErrorMessage}\"");
                        }
                    }
                }
            }
            else
            {
                _log?.Error(ex?.ToString() ?? "Unknown error");
            }
            httpContext.ClearError();
            httpContext.Response.Clear();
            // Set appropriate status code based on exception type
            int statusCode = 500; // Default to internal server error
            if (ex is FileNotFoundException || ex is DirectoryNotFoundException)
            {
                statusCode = 404;
            }
            else if (ex is UnauthorizedAccessException)
            {
                statusCode = 401;
            }
            else if (ex is ArgumentException || ex is ArgumentNullException)
            {
                statusCode = 400;
            }

            httpContext.Response.StatusCode = statusCode;
            httpContext.Response.TrySkipIisCustomErrors = true;

            // TODO: routeData is null and of type object - commenting out Values access
            // routeData.Values["controller"] = "Error";
            // routeData.Values["action"] = action;
            // TODO: RouteData.Values access pattern changed in ASP.NET Core - commenting out during migration
            // routeData.Values["exception"] = new HandleErrorInfo(ex, currentController, currentAction);

            // TODO: ASP.NET Core migration - IController and RequestContext don't exist in ASP.NET Core
            // Error handling should be done through ASP.NET Core middleware
            // IController errormanagerController = new ErrorController();
            // HttpContextWrapper wrapper = new HttpContextWrapper(httpContext);
            // var rc = new RequestContext(wrapper, routeData);
            // errormanagerController.Execute(rc);
        }

        protected void Application_PostAuthorizeRequest()
        {
            if (IsWebApiRequest())
            {
                // Note: SetSessionStateBehavior is not needed in ASP.NET Core as session behavior is configured in Program.cs
            }
        }
        //void Session_Start(object sender, EventArgs e)
        //{
        //    //Session["PracticeId"] = null;
        //}

        //void Session_End(object sender, EventArgs e)
        //{
        //    // Code that runs when a session ends.
        //    // Note: The Session_End event is raised only when the sessionstate mode
        //    // is set to InProc in the Web.config file. If session mode is set to StateServer
        //    // or SQLServer, the event is not raised.
        //    var practiceId = Convert.ToInt32(Session["PracticeId"]);
        //    if (practiceId > 0)
        //    {
        //        //System.Threading.Thread.Sleep(30000);
        //        AppointmentHub.RefreshScreenBySessionId(Session.SessionID, practiceId.ToString());
        //    }
        //    // test
        //    Session.Clear();
        //    Session.RemoveAll();
        //}
        private bool IsWebApiRequest()
        {
            return HttpContextProvider.Current?.Request.Path.Value?.StartsWith(WebApiConfig.UrlPrefixRelative) ?? false;
        }
    }

}
