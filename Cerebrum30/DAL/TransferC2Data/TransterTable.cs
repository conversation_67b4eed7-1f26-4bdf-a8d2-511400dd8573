﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum30.Utility;
using System.Data.Entity;
using Cerebrum30;
using Cerebrum30.C2;
using Cerebrum.Data;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Entities.VisitPage;

namespace Cerebrum30.DAL.TransferTbData
{
    public class TransterTable
    {
        public static bool TransferTableExtDoctors()
        {
            int pulledDocCount = 0;
            int inseretedDocCount = 0;
            int groupSize = Helper.GetTransferGroupSize();
            int tblRowCount = GetTableRowNumber("doctors");
            int numOfGroups = GetNumberGroup(groupSize, tblRowCount);
            for (int i = 0; i < numOfGroups; i++)
            {//
                if (i != 0)
                { break; }

                List<doctor1> oldDocList = GetExternalDoctorsList(i, groupSize);
                pulledDocCount += oldDocList.Count;
                if (oldDocList.Count > 0)
                {
                    List<doctor1> newDocList = InsertExternalDocList(oldDocList, 1);
                    inseretedDocCount = +newDocList.Count;
                }
            }


            return false;
        }


        #region Import Measurement lookup tables

        public static void LoadAllMeasurements()
        {

            try
            {

                #region import from old db

                //RepMCats IDs  in cerbrum2 DO NOT start from 1. 
                //Had to take off ID as Identity column from 
                //class 'MeasurementCategory' for the import
                using (cm2Entities context = new cm2Entities())
                {
                    //var cats = from c in context.RepMCats
                    //           join m in context.RepMs on c.id equals m.cat
                    //           select c;


                    //.OrderBy(x => x.id)
                    //var cats = context.RepMCats
                    //.Select(x =>

                    //        new {
                    //                x.id,
                    //                x.test,
                    //                x.par,
                    //                x.cat,
                    //                x.ord,
                    //                x.standardnum,
                    //                x.status,
                    //                x.add_date
                    //            }

                    //        ).ToList();

                    //Only get categories with a valid testID
                    var cats = context.RepMCats.Where(r => r.test != 0).ToList();


                    foreach (var cat in cats.ToList())
                    {
                        MeasurementCategory newCategory = null;
                        using (CerebrumContext c3 = new CerebrumContext())
                        {
                            var categoryEntity = new MeasurementCategory()
                                                                  {
                                                                      //Id = cat.id,
                                                                      //Test = cat.test,
                                                                      OLDID = cat.id,
                                                                      par = cat.par,
                                                                      name = cat.cat,
                                                                      order = cat.ord,
                                                                      categoryCode = cat.standardnum,
                                                                      //status = Int32.Parse(cat.status),
                                                                      status = cat.status,
                                                                      dateAdded = cat.add_date
                                                                  };
                            c3.MeasurementCategory.Add(categoryEntity);
                            newCategory = categoryEntity;


                            //c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                            c3.SaveChanges();
                        }

                        var measures = from m in context.RepMs
                                       join c in context.RepMCats on m.cat equals c.id
                                       where m.cat == cat.id
                                       orderby m.id
                                       select m;

                        foreach (var measure in measures.ToList())
                        {
                            Measurement measurement = null;
                            using (CerebrumContext c3 = new CerebrumContext())
                            {
                                var measurementEntity = new Measurement()
                                                            {
                                                                //Id = measure.id,

                                                                OLDID = measure.id,

                                                                categoryCode = newCategory.Id,

                                                                MeasurementCategoryID = newCategory.Id,

                                                                name = measure.mes,                                   // name = measure.cat,

                                                                order = measure.ord,

                                                                units = measure.units,

                                                                measurementCode = measure.mes_code,

                                                                status = measure.status,

                                                                mask = measure.mask,

                                                                dateAdded = measure.add_date,

                                                                isCompulsory = measure.iscompulsory,

                                                                visibleOnWorkSheet = measure.VisOnMeas,

                                                            };
                                c3.Measurement.Add(measurementEntity);
                                measurement = measurementEntity;

                                //c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                                c3.SaveChanges();
                            }

                            var mappings = from i in context.RepMinputs
                                           join m in context.RepMs on i.repmId equals m.id
                                           where i.repmId == measure.id
                                           orderby i.id
                                           select i;
                            //&& i.measureCode == measurement.measurementCode && i.categoryCode == newCategory.categoryCode

                            foreach (var mapping in mappings.ToList())
                            {

                                using (CerebrumContext c3 = new CerebrumContext())
                                {
                                    c3.MeasurementMapping.Add(
                                                                new MeasurementMapping()
                                                                {
                                                                    measureName = mapping.mes,
                                                                    measureCode = mapping.mes_code,
                                                                    status = mapping.status,
                                                                    dateAdded = mapping.add_date,
                                                                    categoryCode = mapping.cat_code,
                                                                    MeasurementID = measurement.Id
                                                                });

                                    //c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                                    c3.SaveChanges();
                                }

                            }
                        }

                    }
                }

                #endregion


            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);



            }

        }

        public static List<MeasurementCategory> GetMeasurementsCategories()
        {
            List<MeasurementCategory> lstCats = new List<MeasurementCategory>();
            try
            {

                #region import from old db

                //RepMCats IDs  in cerbrum2 DO NOT start from 1. 
                //Had to take off ID as Identity column from 
                //class 'MeasurementCategory' for the import
                using (cm2Entities context = new cm2Entities())
                {
                    var cats = context.RepMCats.Select(x => x).ToList();

                    foreach (var cat in cats)
                    {
                        lstCats.Add(
                                new MeasurementCategory()
                                {
                                    Id = cat.id,
                                    //Test = cat.test,
                                    par = cat.par,
                                    name = cat.cat,
                                    order = cat.ord,
                                    categoryCode = cat.standardnum,
                                    //status = Int32.Parse(cat.status),
                                    status = cat.status,
                                    dateAdded = cat.add_date
                                }
                            );
                    }
                }

                #endregion


            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);



            }
            return lstCats;
        }

        public static List<Measurement> GetMeasurements()
        {
            List<Measurement> lstmeasures = new List<Measurement>();
            try
            {

                #region import from old db
                //had to take off identity column from Masurements
                //because cerebrum 2 had random ids
                using (cm2Entities context = new cm2Entities())
                {
                    var measures =
                                    (
                                        from c in context.RepMCats
                                        from m in context.RepMs
                                        where c.id == m.cat
                                        select m
                                    ).ToList();

                    //var measures = context.RepMs.ToList();

                    foreach (var measure in measures)
                    {
                        lstmeasures.Add(

                            new Measurement()
                            {
                                Id = measure.id,

                                categoryCode = measure.cat,

                                MeasurementCategoryID = measure.cat,

                                name = measure.mes,                                   // name = measure.cat,

                                order = measure.ord,

                                units = measure.units,

                                measurementCode = measure.mes_code,

                                status = measure.status,

                                mask = measure.mask,

                                dateAdded = measure.add_date,

                                isCompulsory = measure.iscompulsory,

                                visibleOnWorkSheet = measure.VisOnMeas,

                            }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lstmeasures;
        }

        public static List<MeasurementRangeType> GetMeasurementRangeTypes()
        {
            List<MeasurementRangeType> lst = new List<MeasurementRangeType>();
            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    var types = context.RepMrangeTypes.ToList();
                    foreach (var item in types)
                    {
                        lst.Add(

                                new MeasurementRangeType()
                                {
                                    name = item.Name,
                                    color = item.Color
                                }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;
        }

        public static List<MeasurementRange> GetMeasurementRange()
        {
            List<MeasurementRange> lst = new List<MeasurementRange>();
            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    var ranges = context.RepMranges.ToList();
                    foreach (var range in ranges)
                    {
                        lst.Add(

                                new MeasurementRange()
                                {
                                    //Id =  range.id  ,
                                    categoryCode = range.CatCode,
                                    measurementCode = range.Code,
                                    gender = range.Gender,
                                    Range1 = range.Range1,
                                    Range2 = range.Range2,
                                    misc = range.misc,
                                    age1 = range.Age1,
                                    age2 = range.Age2,
                                    units = range.Units,
                                    MeasurementRangeTypeId = range.Type ?? 0
                                }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;
        }

        public static List<MeasurementBSARange> GetMeasurementBSARange()
        {
            List<MeasurementBSARange> lst = new List<MeasurementBSARange>();
            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    var ranges = context.RepMbsaIndexRanges.ToList();
                    foreach (var range in ranges)
                    {
                        lst.Add(

                                new MeasurementBSARange()
                                {
                                    //Id = range.id,
                                    categoryCode = range.CatCode,
                                    measurementCode = range.Code,
                                    gender = range.Gender.ToUpper() ,
                                    IndexedRange1 = range.IndexedRange1,
                                    IndexedRange2 = range.IndexedRange2,
                                    units = range.Units,
                                    MeasurementRangeTypeId = range.Type ?? 0
                                }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;
        }

        public static List<Practice> GetPractices()
        {
            List<Practice> lst = new List<Practice>();

            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    var practices = context.ugroups.ToList();
                    foreach (var practice in practices)
                    {
                        lst.Add(
                                new Practice()
                                {
                                    PracticeNumber = (short)practice.id,
                                    PracticeName = practice.name,
                                    MasterId = 1
                                }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;

        }

        public static List<MeasurementByPractice> GetMeasurementsByPractice()
        {
            List<MeasurementByPractice> lst = new List<MeasurementByPractice>();

            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    var measures = context.RepmDisplays.ToList();
                    foreach (var measure in measures)
                    {
                        lst.Add(
                                new MeasurementByPractice()
                                {
                                    MeasurementID = measure.measurementID,
                                    PracticeID = measure.grp,
                                    Visible = measure.isvisible
                                }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;

        }

        public static List<MeasurementMapping> GetMeasurementMapping()
        {
            List<MeasurementMapping> lst = new List<MeasurementMapping>();
            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    //had to take the join because certain
                    //mappings dont have valid MeasurementID
                    //var mappings = context.RepMinputs.ToList();
                    var mappings = (
                                        from i in context.RepMinputs
                                        from m in context.RepMs
                                        from c in context.RepMCats
                                        where i.repmId == m.id &&
                                              c.id == m.cat
                                        select i
                                    ).ToList();

                    //  context.RepMinputsToList();

                    foreach (var mapping in mappings)
                    {
                        lst.Add(

                                new MeasurementMapping()
                                {
                                    measureName = mapping.mes,
                                    measureCode = mapping.mes_code,
                                    status = mapping.status,
                                    dateAdded = mapping.add_date,
                                    categoryCode = mapping.cat_code,
                                    MeasurementID = mapping.repmId
                                }
                            );
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;
        }

        public static List<ReportPhrase> GetReportPhrases()
        {
            List<ReportPhrase> lst = new List<ReportPhrase>();
            List<Test> lstC3Test = new List<Test>();

            try
            {

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    lstC3Test.AddRange(c3.Tests.ToList());
                }

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {
                    int counter = 1;
                    var phrases = context.repops.ToList();
                    foreach (var phrase in phrases)
                    {
                        lst.Add(

                                new ReportPhrase()
                                {
                                    // Id = phrase.id,  //TODO take this off
                                    Index = counter++,
                                    OldId = phrase.id,
                                    name = phrase.name,
                                    value = phrase.value,
                                    parent = phrase.parent,
                                    root = phrase.root,
                                    ordernumber = phrase.ordernumber,
                                    OLDTESTID = phrase.test,
                                    GroupID = phrase.test,
                                    type = phrase.typ,
                                    field = phrase.field,
                                    status = phrase.status,

                                    grp = phrase.grp,
                                    dr = phrase.dr ?? new int?()
                                }
                            );
                    }

                    foreach (var phrase in lst)
                    {
                        phrase.parent = GetProperWSRPID(lst, phrase.parent);
                        phrase.root = GetProperWSRPID(lst, phrase.root);
                        phrase.TestID = GetProperTestID(lstC3Test, phrase.OLDTESTID);
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;
        }

        protected static void AddReportPhrasesLookUpTables()
        {

            using (CerebrumContext context = new CerebrumContext())
            {
                var phrases = TransterTable.GetReportPhrases();
                context.ReportPhrase.AddRange(phrases);
                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                context.SaveChanges();
            }

        }

        protected static void AddTestGroups()
        {

            List<Test> lstc3Test = new List<Test>();
            List<MeasurementCategory> lstC3Cats = new List<MeasurementCategory>();
            List<ReportPhrase> lstC3Phrases = new List<ReportPhrase>();

            List<TestGroupDetail> lstTestGroupDetail = new List<TestGroupDetail>();

            using (CerebrumContext context = new CerebrumContext())
            {
                lstc3Test.AddRange(context.Tests.ToList());

                lstC3Cats.AddRange(context.MeasurementCategory.ToList());

                lstC3Phrases.AddRange(context.ReportPhrase.ToList());
            }

            List<Group> lstGroup = new List<Group>();
            List<TestGroup> lstTestGroup = new List<TestGroup>();
            using (cm2Entities context = new cm2Entities())
            {
                var c2tests = context.tests.ToList();
                var c2Groups = c2tests.Select(t => t.testGroup).Distinct().ToList();

                #region Adding groups

                for(int i=0;i<50;i++)
                {
                    lstGroup.Add(new Group() { Id = i , Name = "Group" + i.ToString() });
                }


                //c2Groups.ForEach(g =>
                //{
                //    if (g.HasValue)
                //    {
                //        lstGroup.Add(new Group() { Id = g.Value, Name = "Group" + g.Value.ToString() });
                //    }
                //});


                #endregion

                #region Adding Test groups

                c2tests.ForEach(c2test =>
                {
                    if (c2test.testGroup.HasValue)
                    {
                        var c3test = lstc3Test.Where(t => t.OLDID == c2test.id).FirstOrDefault();

                        if (c3test != null)
                        {
                            lstTestGroup.Add(new TestGroup()
                            {
                                GroupId = c2test.testGroup.Value,
                                TestId = c3test.Id
                            });
                        }
                    }
                });

                #endregion

            }

            using (CerebrumContext context = new CerebrumContext())
            {
                context.Group.AddRange(lstGroup);

                context.TestGroup.AddRange(lstTestGroup);

                context.SaveChanges();
            }


            List<TestGroup> lstC3TestGroup = new List<TestGroup>();

            using (CerebrumContext context = new CerebrumContext())
            {
                lstC3TestGroup.AddRange(context.TestGroup.ToList());

                // lstc3Test.AddRange(context.Tests.ToList());
            }


            #region parsing repops to retrieve test ids

            using (CerebrumContext context = new CerebrumContext())
            {
                context.TestGroupDetail.AddRange(lstTestGroupDetail);

                context.SaveChanges();
            }

            #endregion

            lstTestGroupDetail.Clear();

            #region parsing TestRepMCats to retrieve categories

            using (cm2Entities context = new cm2Entities())
            {
                var lstTestRepMCats = context.TestRepMCats.ToList();

                foreach (var itemTestRepMCat in lstTestRepMCats)
                {
                    //find c3 cat using OLDID
                    var c3Cat = lstC3Cats.Where(c => c.OLDID == itemTestRepMCat.RepMCatId).FirstOrDefault();
                    //find c3 test
                    var c3Test = lstc3Test.Where(t => t.OLDID == itemTestRepMCat.TestId).FirstOrDefault();

                    if (c3Cat != null && c3Test != null)
                    {

                        var exists = lstTestGroupDetail.Where(x => x.MeasurementCategoryId == c3Cat.Id && x.TestID == c3Test.Id).FirstOrDefault();
                        if (exists == null)
                        {
                            lstTestGroupDetail.Add(new TestGroupDetail()
                            {
                                MeasurementCategoryId = c3Cat.Id,
                                TestID = c3Test.Id,
                                Visible = true,
                            });
                        }
                    }
                }
            }

            using (CerebrumContext context = new CerebrumContext())
            {
                context.TestGroupDetail.AddRange(lstTestGroupDetail);

                context.SaveChanges();
            }

            #endregion


        }

        protected static void AddReportPhrasMeasurementsScrollTables()
        {
            using (CerebrumContext context = new CerebrumContext())
            {


                context.ReportPhraseMeasurmentCategoryScroll.Add(new ReportPhraseMeasurmentCategoryScroll()
                {
                    ReportPhraseID = 829,
                    MeasurementCategoryID = 33
                });
                
                context.ReportPhraseMeasurmentCategoryScroll.Add(new ReportPhraseMeasurmentCategoryScroll()
                {
                    ReportPhraseID = 2339,
                    MeasurementCategoryID = 36

                });
                context.ReportPhraseMeasurmentCategoryScroll.Add(new ReportPhraseMeasurmentCategoryScroll()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 40

                });
                context.ReportPhraseMeasurmentCategoryScroll.Add(new ReportPhraseMeasurmentCategoryScroll()
                {
                    ReportPhraseID = 827,
                    MeasurementCategoryID = 52

                });
                context.ReportPhraseMeasurmentCategoryScroll.Add(new ReportPhraseMeasurmentCategoryScroll()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 55

                });
               
                context.ReportPhraseMeasurmentCategoryScroll.Add(new ReportPhraseMeasurmentCategoryScroll()
                {
                    ReportPhraseID = 833,
                    MeasurementCategoryID = 57

                });



                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                context.SaveChanges();
            }
        }


        protected static void AddReportPhrasMeasurementsCategoriesTables()
        {
            using (CerebrumContext context = new CerebrumContext())
            {

                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 829,
                    MeasurementCategoryID = 33

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 2339,
                    MeasurementCategoryID = 36

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 2339,
                    MeasurementCategoryID = 38

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 40

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 827,
                    MeasurementCategoryID = 52

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 55

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 56

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 833,
                    MeasurementCategoryID = 57

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 1

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 829,
                    MeasurementCategoryID = 3

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 827,
                    MeasurementCategoryID = 11

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 71

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 13

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 14

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 15

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 16

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 829,
                    MeasurementCategoryID = 69

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 93

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 17

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 2339,
                    MeasurementCategoryID = 18

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 21

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 829,
                    MeasurementCategoryID = 145

                });
                
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 147

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 148

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 149

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 834,
                    MeasurementCategoryID = 177

                });
                context.ReportPhraseMeasurmentCategory.Add(new ReportPhraseMeasurmentCategory()
                {
                    ReportPhraseID = 832,
                    MeasurementCategoryID = 178

                });


                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                context.SaveChanges();
            }

        }

        protected static void AddProstaticData()
        {

            var lstProstatic = GetProstaticValveData();

            var lstPatientProstatic = GetPatientProstaticValveData();

            int index = 0;

            using (CerebrumContext context = new CerebrumContext())
            {
                context.ProstaticValve.AddRange(lstProstatic);

                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                context.PatientProstaticValve.AddRange(lstPatientProstatic);

                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                var lstRange = context.MeasurementRange.Take(10).ToList();

                foreach (var item in lstRange)
                {
                    item.ProstaticValveID = lstPatientProstatic[index++].Id;
                }

                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                context.SaveChanges();

            }

        }

        protected static List<ProstaticValve> GetProstaticValveData()
        {

            List<ProstaticValve> lst = new List<ProstaticValve>();
            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {

                    var valves = context.prosthvalves.ToList();
                    foreach (var valve in valves)
                    {
                        lst.Add(
                            new ProstaticValve()
                            {
                                // Id = valve.id,
                                Name = valve.name,
                                Type = valve.type,
                                Typeid = valve.typeid,
                                Gentype = valve.gentype
                            });

                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;
        }

        #endregion 

        protected static List<PatientProstaticValve> GetPatientProstaticValveData()
        {
            List<PatientProstaticValve> lst = new List<PatientProstaticValve>();
            try
            {

                #region import from old db

                using (cm2Entities context = new cm2Entities())
                {

                    var valves = context.pat_prosthvalve.ToList();
                    foreach (var valve in valves)
                    {
                        lst.Add(
                            new PatientProstaticValve()
                            {
                                Id = valve.id,
                                PatientRecordId = valve.patientid,
                                ProstaticValveId = valve.valveid ?? -1,
                                Size = valve.size,
                                Addedby = valve.addedby,
                                StatusChangeBy = valve.statuschangeby,
                                AddedDate = valve.addedat,
                                EndDate = valve.statuschangedat,
                                InActComment = valve.inactcomment,
                                InActreason = valve.inactreason,
                                IsActive = valve.isactive
                            });

                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);

            }
            return lst;

        }

        protected static void AddMeasurementRangeText()
        {
            using (CerebrumContext context = new CerebrumContext())
            {

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error",
                    MeasurementId = 589,
                    MeasurementRangeId = 4
                });

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "Error occurred in Calculations 5",
                    MeasurementId = 925,
                    MeasurementRangeId = 5
                });

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "Error occurred in Calculations 4",
                    MeasurementId = 925,
                    MeasurementRangeId = 4
                });

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "Error occurred in Calculations 3",
                    MeasurementId = 925,
                    MeasurementRangeId = 3
                });

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "Error occurred in Calculations 2",
                    MeasurementId = 925,
                    MeasurementRangeId = 2
                });

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "Error occurred in Calculations",
                    MeasurementId = 925,
                    MeasurementRangeId = 1
                });


                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "Yes This is working",
                    MeasurementId = 513,
                    MeasurementRangeId = 1
                });

                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error",
                    MeasurementId = 589,
                    MeasurementRangeId = 4
                });



                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error 1",
                    MeasurementId = 140,
                    MeasurementRangeId = 1
                });
                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error 2",
                    MeasurementId = 140,
                    MeasurementRangeId = 2
                });
                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error 3",
                    MeasurementId = 140,
                    MeasurementRangeId = 3
                });
                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error 4",
                    MeasurementId = 140,
                    MeasurementRangeId = 4
                });
                context.MeasurementRangeText.Add(new MeasurementRangeText()
                {
                    Text = "This is a Test Error 5",
                    MeasurementId = 140,
                    MeasurementRangeId = 5
                });

                for (int i = 1; i < 6; i++)
                {

                    context.MeasurementRangeText.Add(new MeasurementRangeText()
                    {
                        Text = "This is a Test Error " + i.ToString(),
                        MeasurementId = 1805,
                        MeasurementRangeId = i
                    });


                    context.MeasurementRangeText.Add(new MeasurementRangeText()
                    {
                        Text = "This is a Test Error " + i.ToString(),
                        MeasurementId = 1817,
                        MeasurementRangeId = i
                    });

                    context.MeasurementRangeText.Add(new MeasurementRangeText()
                    {
                        Text = "This is a Test Error " + i.ToString(),
                        MeasurementId = 1989,
                        MeasurementRangeId = i
                    });

                    context.MeasurementRangeText.Add(new MeasurementRangeText()
                    {
                        Text = "This is a Test Error " + i.ToString(),
                        MeasurementId = 1419,
                        MeasurementRangeId = i
                    });

                }







                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            }
        }

        protected static void AddReportPhrasesNormalTables()
        {
            using (CerebrumContext context = new CerebrumContext())
            {

                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 735,
                    NormalReportPhraseID = 2479,
                    TestID = 1

                });


                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 875,
                    NormalReportPhraseID = 2618,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 773,
                    NormalReportPhraseID = 2777,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 775,
                    NormalReportPhraseID = 1910,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 810,
                    NormalReportPhraseID = 2515,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 778,
                    NormalReportPhraseID = 2595,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2296,
                    NormalReportPhraseID = 777,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2868,
                    NormalReportPhraseID = 2870,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 779,
                    NormalReportPhraseID = 1430,
                    TestID = 1

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 1923,
                    NormalReportPhraseID = 1981,
                    TestID = 1

                });

                //for test 2 
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2105,
                    NormalReportPhraseID = 2107,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2013,
                    NormalReportPhraseID = 2120,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2014,
                    NormalReportPhraseID = 2299,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 1922,
                    NormalReportPhraseID = 1990,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2589,
                    NormalReportPhraseID = 2773,
                    TestID = 2

                });

                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 1920,
                    NormalReportPhraseID = 2011,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2101,
                    NormalReportPhraseID = 2197,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 1919,
                    NormalReportPhraseID = 1977,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 1921,
                    NormalReportPhraseID = 4504,
                    TestID = 2

                });
                context.ReportPhraseNormal.Add(new ReportPhraseNormal()
                {
                    ReportPhraseID = 2588,
                    NormalReportPhraseID = 2614,
                    TestID = 2

                });



                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            }

        }
        protected static void AddMeasurementLookUpTables()
        {

            TransterTable.LoadAllMeasurements();

            using (CerebrumContext context = new CerebrumContext())
            {

                //var categories = TransterTable.GetMeasurementsCategories();
                //context.MeasurementCategory.AddRange(categories);
                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                //var measurements  = TransterTable.GetMeasurements();
                //context.Measurement.AddRange(measurements);
                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                var lstMeas = context.Measurement.ToList();

                var lstRangeTypes = TransterTable.GetMeasurementRangeTypes();
                context.MeasurementRangeType.AddRange(lstRangeTypes);
                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                var lstRanges = TransterTable.GetMeasurementRange();
                context.MeasurementRange.AddRange(lstRanges);
                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                var lstRangesBSA = TransterTable.GetMeasurementBSARange();
                context.MeasurementBSARange.AddRange(lstRangesBSA);
                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                //var lstPractices = TransterTable.GetPractices();
                //context.Practices.AddRange(lstPractices);
                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                var lstPractice = context.Practices.ToList();
                var lstMeasByPratice = TransterTable.GetMeasurementsByPractice();
                List<MeasurementByPractice> FinallstMeasByPratice = new List<MeasurementByPractice>();
                //meas by PRacticeID
                lstMeasByPratice.ForEach(e =>
                {
                    bool practiceFound = false;
                    bool measFound = false;

                    var practice = lstPractice.Where(p => p.PracticeNumber == e.PracticeID).FirstOrDefault();
                    if (practice != null)
                    {
                        practiceFound = true;
                        e.PracticeID = practice.Id;
                    }

                    var measure = lstMeas.Where(p => p.OLDID == e.MeasurementID).FirstOrDefault();
                    if (measure != null)
                    {
                        measFound = true;
                        e.MeasurementID = measure.Id;
                    }
                    if (practiceFound && measFound)
                    {
                        FinallstMeasByPratice.Add(e);
                    }
                });
                context.MeasurementByPractice.AddRange(FinallstMeasByPratice);
                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                //var lstMapping = TransterTable.GetMeasurementMapping();
                //context.MeasurementMapping.AddRange(lstMapping);
                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                var lstOperators = new List<MeasurementOperator>()
                {
                     new MeasurementOperator() { name="---" },
                     new MeasurementOperator() { name="MAX" },
                     new MeasurementOperator() { name="MIN" },
                     new MeasurementOperator() { name="MEAN" },
                };

                lstOperators.ForEach(x => context.MeasurementOperator.Add(x));
                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            }

        }

        public static void LoadWorkSheetData()
        {

            AddReportPhrasesLookUpTables();

            AddReportPhrasesNormalTables();

            AddReportPhrasMeasurementsScrollTables();

            AddReportPhrasMeasurementsCategoriesTables();

            AddMeasurementLookUpTables();

            AddMeasurementRangeText();

            AddProstaticData();

            AddTestGroups();
        }

        public static int GetProperVPRPID(List<VPReportPhrase> lst, int Id)
        {
            int properID = Id;

            var entry = lst.Where(p => p.OldId == Id).FirstOrDefault();

            if (entry != null)
            {
                properID = entry.Index;
            }

            return properID;
        }

        public static int? GetProperWSRPID(List<ReportPhrase> lst, int? Id)
        {
            int? properID = Id;

            var entry = lst.Where(p => p.OldId == Id).FirstOrDefault();

            if (entry != null)
            {
                properID = entry.Index;
            }

            return properID;
        }


        public static int? GetProperTestID(List<Test> lst, int? Id)
        {
            int? properID = Id;

            var entry = lst.Where(p => p.OLDID == Id).FirstOrDefault();

            if (entry != null)
            {
                properID = entry.Id;
            }

            return properID;
        }
        

        public static void LoadVPData()
        {

            List<VPOption> lstOptions = new List<VPOption>();
            List<VPCategory> lstCategory = new List<VPCategory>();
            List<VPCategoryOption> lstCategoryOptions = new List<VPCategoryOption>();
            List<VPMeasurement> lstMeasurements = new List<VPMeasurement>();
            List<VPMeasurementOption> lstMeasurementOptions = new List<VPMeasurementOption>();
            List<VPReportPhrase> lstPhrases = new List<VPReportPhrase>();
            List<VPReportPhraseOption> lstPhraseOptions = new List<VPReportPhraseOption>();
            List<VPReportPhraseByDoctor> lstPhraseByPractice = new List<VPReportPhraseByDoctor>();
            List<VP_CPP_Category> lstCpp = new List<VP_CPP_Category>();
            List<VPTemplateField> lstTemplateItem = new List<VPTemplateField>();
            List<VP_Template> lstTemplate = new List<VP_Template>();
            List<VP_Template_Detail> lstVP_Template_Detail = new List<VP_Template_Detail>();
            List<ConsultCode> lstConsultCodes = new List<ConsultCode>();
            List<DiagnoseCode> lstDiagnoseCodes = new List<DiagnoseCode>();
            List<VP_CPP_ImmunizationType> lstImmunizationType = new List<VP_CPP_ImmunizationType>();
            List<VP_CPP_ImmunizationStatus> lstImuinizationStatus = new List<VP_CPP_ImmunizationStatus>();

            using (cm2Entities context = new cm2Entities())
            {
                #region load options
                var options = context.vipoptions.ToList();
                foreach (var option in options)
                {
                    lstOptions.Add(

                            new VPOption()
                            {
                                Id = option.id,
                                Spec = option.spec,
                                Name = option.name,
                                DrID = option.dr ?? 0,
                                Status = option.status,
                                Grp = option.grp
                            }
                        );
                }
                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPOption.AddRange(lstOptions);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }
                #endregion

                #region load categories

                var cats = context.vpMCats.ToList();
                foreach (var cat in cats)
                {
                    lstCategory.Add(

                            new VPCategory()
                            {
                                //id = cat.id,
                                OldId = cat.id,
                                Name = cat.cat,
                                Order = cat.ord,
                                Dt = cat.dt,
                                Options = cat.opt,
                                DrID = cat.dr,
                                Status = cat.status

                            }
                        );
                }

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPCategory.AddRange(lstCategory);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }


                //load category options
                foreach (var item in lstCategory)
                {
                    if (!string.IsNullOrEmpty(item.Options))
                    {
                        foreach (var str in item.Options.Split(",".ToCharArray()))
                        {
                            int optionID = 0;
                            Int32.TryParse(str, out optionID);

                            if (optionID != 0)
                            {
                                lstCategoryOptions.Add(new VPCategoryOption()
                                {
                                    VPCategoryId = item.id,
                                    VPOptionId = optionID
                                });
                            };
                        }
                    }

                }
                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPCategoryOption.AddRange(lstCategoryOptions);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }
                //load category options
                #endregion

                #region load measurements
                //var measures = context.vpMs.ToList();
                var measures = context.vpMs.ToList();
                foreach (var m in measures)
                {
                    int c3CatID = 0;
                    var entry = lstCategory.Where(c => c.OldId == m.cat).FirstOrDefault();
                    if (entry != null)
                    {
                        c3CatID = entry.id;
                    }

                    if (c3CatID > 0)
                    {
                        lstMeasurements.Add(

                                        new VPMeasurement()
                                        {
                                            //Id = m.id,
                                            OLDID = m.id,
                                            Name = m.mes,
                                            Order = m.ord,
                                            Units = m.units,
                                            Normal = m.normal,
                                            Range1 = !string.IsNullOrEmpty(m.normal) && m.normal.Contains("-") ? decimal.Parse(m.normal.Split("-".ToCharArray())[0]) : new decimal?(),
                                            Range2 = !string.IsNullOrEmpty(m.normal) && m.normal.Contains("-") ? decimal.Parse(m.normal.Split("-".ToCharArray())[1]) : new decimal?(),
                                            Spec = m.spec,
                                            Type = m.typ,
                                            DrID = m.dr,
                                            Status = m.status,
                                            Options = m.opts,
                                            Testcode = m.testcode,
                                            Cdid = m.cdid,

                                            VPCategoryID = c3CatID
                                        });
                    }

                }
                //load measurements
                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPMeasurement.AddRange(lstMeasurements);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }
                //load measurement options
                foreach (var item in lstMeasurements)
                {
                    if (!string.IsNullOrEmpty(item.Options))
                    {
                        foreach (var str in item.Options.Split(",".ToCharArray()))
                        {
                            int optionID = 0;
                            Int32.TryParse(str, out optionID);

                            if (optionID != 0)
                            {
                                lstMeasurementOptions.Add(new VPMeasurementOption()
                                {
                                    VPMeasurementId = item.Id,
                                    VPOptionId = optionID
                                });

                            };
                        }
                    }
                }
                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPMeasurementOption.AddRange(lstMeasurementOptions);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }
                //load measurement options
                #endregion

                #region  Report phrases

                var phrases = context.vpops.ToList();
                int counter = 1;
                foreach (var i in phrases)
                {
                    lstPhrases.Add(new VPReportPhrase()
                    {
                        //Id = i.id,
                        Index = counter++,
                        OldId = i.id,
                        Name = i.name,
                        Value = i.value,
                        //Parent = GetProperVPRPID(phrases,i.parent),
                        //Root = GetProperVPRPID(phrases,i.root),
                        Parent = i.parent,
                        Root = i.root,
                        Order = i.ordernumber,
                        Type = i.typ,
                        Options = i.opt,
                        Spec = i.spec,
                        DrID = i.dr,
                        Status = i.status,
                        Grp = i.grp
                    });
                }
                //opening statement
                lstPhrases.Add(new VPReportPhrase()
                {
                    //Id = i.id,
                    Index = counter++,
                    OldId = 0,
                    Name = "Opening Statement",
                    Value = "Opening Statement",
                    //Parent = GetProperVPRPID(phrases,i.parent),
                    //Root = GetProperVPRPID(phrases,i.root),
                    Parent = -1,
                    Root = -1,
                    Spec = 0,
                    Status = 2,
                    Order = 0
                });

                foreach (var phrase in lstPhrases)
                {
                    phrase.Parent = GetProperVPRPID(lstPhrases, phrase.Parent);
                    phrase.Root = GetProperVPRPID(lstPhrases, phrase.Root);
                }

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPReportPhrase.AddRange(lstPhrases);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }
                //report phrases

                //load report phrase options
                foreach (var item in lstPhrases)
                {
                    if (!string.IsNullOrEmpty(item.Options))
                    {
                        foreach (var str in item.Options.Split(",".ToCharArray()))
                        {
                            int optionID = 0;
                            Int32.TryParse(str, out optionID);

                            if (optionID != 0)
                            {
                                lstPhraseOptions.Add(
                                    new VPReportPhraseOption()
                                    {
                                        //VPVPReportPhraseId = properID,
                                        VPVPReportPhraseId = item.Id,
                                        VPOptionId = optionID
                                    });
                            };
                        }
                    }
                }
                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPReportPhraseOption.AddRange(lstPhraseOptions);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }


                var customPhrases = context.vpvals.ToList();
                foreach (var pi in customPhrases)
                {
                    var properID = lstPhrases.Where(p => p.OldId == pi.vpid).FirstOrDefault().Id;
                    lstPhraseByPractice.Add(new VPReportPhraseByDoctor()
                    {
                        DrID = pi.dr,
                        Text = pi.val,
                        //VPReportPhraseID = pi.vpid
                        VPReportPhraseID = properID
                    });
                }

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VPReportPhraseByDoctor.AddRange(lstPhraseByPractice);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }


                #endregion

                #region Load Cpp

                lstCpp.Add(new VP_CPP_Category() { Text = "FAMILY HISTORY" });
                lstCpp.Add(new VP_CPP_Category() { Text = "PAST HEALTH" });
                lstCpp.Add(new VP_CPP_Category() { Text = "PROBLEM LIST" });
                lstCpp.Add(new VP_CPP_Category() { Text = "RISK FACTORS" });
                lstCpp.Add(new VP_CPP_Category() { Text = "IMMUNIZATION AND PREVENTATIVE CARE" });
                lstCpp.Add(new VP_CPP_Category() { Text = "ALERTS AND SPECIAL NEEDS" });

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.VP_CPP_Category.AddRange(lstCpp);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }

                #endregion

                #region IC10

                List<ICD10> lstIC = new List<ICD10>();

                var lst = context.icd10_short.Take(100).ToList();
                //var lst = context.icd10_short.ToList();

                foreach (var item in lst)
                {
                    lstIC.Add(new ICD10()
                    {
                        Code = item.code,
                        Name = item.name
                    });
                }

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    c3.ICD10.AddRange(lstIC);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }

                #endregion

                #region VP_CPP_Items
                //using (Cerebrum3Context c3 = new Cerebrum3Context())
                //{
                //    c3.VP_CPP_Item.Add(new VP_CPP_Item() { Name="Alert" });
                //    c3.VP_CPP_Item.Add(new VP_CPP_Item() { Name = "Family History" });
                //    c3.VP_CPP_Item.Add(new VP_CPP_Item() { Name = "Immunization" });
                //    c3.VP_CPP_Item.Add(new VP_CPP_Item() { Name = "Problem List" });
                //    c3.VP_CPP_Item.Add(new VP_CPP_Item() { Name = "Risk Factor" });

                //    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                //}
                #endregion

                #region  CDF
                //VPTemplateField
                var lstc2Def = context.cm_def.ToList();
                lstc2Def.ForEach(x =>
                {
                    lstTemplateItem.Add(new VPTemplateField()
                    {

                        OLDId = x.id,
                        Name = x.name,
                        Order = x.ord,
                        Grp = x.grp,
                        Type = x.typ,

                        NH = x.nh.HasValue ? decimal.Parse(x.nh.Value.ToString()) : new decimal?(),
                        NL = x.nl.HasValue ? decimal.Parse(x.nl.Value.ToString()) : new decimal?(),

                        OLDVPMID = x.vpmid

                    });
                });

                var lstc2tmp = context.cm_temp.ToList();

                lstc2tmp.ForEach(x =>
                {
                    lstTemplate.Add(new VP_Template()
                    {
                        Name = x.name
                    });
                });

                using (CerebrumContext c3 = new CerebrumContext())
                {
                    //lstTemplateItem
                    //lstTemplate
                    c3.VPTemplateField.AddRange(lstTemplateItem);
                    c3.VP_Template.AddRange(lstTemplate);

                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }


                lstc2tmp.ForEach(x =>
                {

                    int c3templateFieldItemID = 0;
                    int c3templateID = 0;
                    List<int> lstTemplateitemID = new List<int>();

                    foreach (var oldTemplateID in x.def.Split(",".ToCharArray()))
                    {
                        lstTemplateitemID.Add(Int32.Parse(oldTemplateID));
                    }

                    foreach (var oldTemplateID in lstTemplateitemID)
                    {
                        var c3TemplateItem = lstTemplateItem.Where(y => y.OLDId == oldTemplateID).FirstOrDefault();
                        var c3Template = lstTemplate.Where(y => y.Name == x.name).FirstOrDefault();

                        if (c3TemplateItem != null)
                        {
                            c3templateFieldItemID = c3TemplateItem.Id;
                        }
                        if (c3Template != null)
                        {
                            c3templateID = c3Template.Id;
                        }

                        lstVP_Template_Detail.Add(new VP_Template_Detail()
                        {
                            Value = string.Empty,
                            VPTemplateField = c3templateFieldItemID,
                            VP_TemplateId = c3templateID
                        });
                    }

                });

                using (CerebrumContext c3 = new CerebrumContext())
                {

                    c3.VP_Template_Detail.AddRange(lstVP_Template_Detail);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }

                using (CerebrumContext c3 = new CerebrumContext())
                {

                    c3.VPCategory.Add(new VPCategory()
                    {
                        Name = "CDF",
                        Order = 1,
                        Dt = 1,
                        Status = 0
                    });

                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                    var cdfCat = c3.VPCategory.Where(c => c.Name == "CDF").FirstOrDefault();

                    var allFields = c3.VPTemplateField.ToList();

                    var lstCommon = (from v in c3.VPMeasurement
                                     join f in c3.VPTemplateField on v.Name.Trim().ToLower() equals f.Name.Trim().ToLower()
                                     select f).ToList();

                    var lstNotCommon = (from a in allFields
                                        join b in lstCommon on a.Id equals b.Id into ab
                                        from c in ab.DefaultIfEmpty()
                                        where c == null
                                        select a).ToList();

                    //var lstNotCommon = (from t in c3.VPTemplateField
                    //                    where !(from c in lstCommon select c.Id).Contains(t.Id)
                    //                    select t);


                    lstCommon.ForEach(i => 
                    {
                        var fieldName = (from f in c3.VPTemplateField where f.Id == i.Id select f.Name).FirstOrDefault();

                        var mes = (from m in c3.VPMeasurement
                                   where m.Name.Trim().ToLower() == fieldName.Trim().ToLower()
                                   select m).ToList().FirstOrDefault();

                        if(mes!= null)
                        {
                            mes.IsCFD = true;
                            mes.NH = i.NH;
                            mes.NL = i.NL;
                            mes.TH = i.TH;
                            mes.TL = i.TL;
                            mes.Frequency = i.Frequency;
                            mes.OLD_TemplateFieldID = i.Id;
                        }
                    });

                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");


                    lstNotCommon.ForEach(i =>
                    {
                        c3.VPMeasurement.Add(new VPMeasurement()
                        {
                            IsCFD = true,
                            Name = i.Name,
                            NH = i.NH,
                            NL = i.NL,
                            TH = i.TH,
                            TL = i.TL,
                            Frequency = i.Frequency,
                            OLDID = 999,
                            Order = 1,
                            Spec = 0,
                            Status = 0,
                            VPCategoryID = cdfCat.id,
                            OLD_TemplateFieldID = i.Id

                        });
                    });

                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                    var tempDetail =  c3.VP_Template_Detail.ToList();

                    tempDetail.ForEach(t => 
                    {
                        var c3mes = c3.VPMeasurement.Where(v => v.OLD_TemplateFieldID == t.VPTemplateField).FirstOrDefault();
                        if (c3mes != null)
                        {
                            t.MeasurementID = c3mes.Id;
                        }
                       
                    });
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                }


                #endregion

                #region Diagnose and consult code

                context.DiagnoseRares.ToList().ForEach(d =>
                {
                    lstDiagnoseCodes.Add(new DiagnoseCode()
                    {

                        Code = d.id.ToString() ,
                        OLDID = d.id,
                        Diagnosis = d.diagnose,
                        Type = d.type
                    });
                });

                context.consultrares.ToList().ForEach(d =>
                {
                    lstConsultCodes.Add(new ConsultCode()
                    {
                        OLDID = d.id,
                        Code = d.code,
                        Spec = d.spec,
                        Name = d.name,
                        Tech = d.tech,
                        Pro = d.pro,
                        Fee = d.fee,
                        Note = d.note,
                        SortOrder = d.sortorder
                    });
                });

                lstConsultCodes.ForEach(x =>
               {

                   if (x.Code.Length == 4)
                   {
                       x.Code = x.Code + "A";
                   }
               });


                lstDiagnoseCodes.ForEach(d =>
               {
                   switch (d.Type)
                   {
                       case 0:

                           d.SpecialityID = 60;
                           break;

                       case 1:

                           d.SpecialityID = 15;
                           break;

                       case 3:

                           d.SpecialityID = 47;
                           break;
                   }
               });

                #endregion

                #region Imunization 

                //status
                context.cpp_immunization_status.ToList().ForEach(s =>
                {
                    lstImuinizationStatus.Add(new VP_CPP_ImmunizationStatus()
                    {
                        Status = s.immunization_status,
                        Color = s.color
                    });
                });

                //types
                context.cpp_Immunization_Type.ToList().ForEach(s =>
                {
                    lstImmunizationType.Add(new VP_CPP_ImmunizationType()
                    {
                        Name = s.Immunization_name,
                        Agecategory = s.Age_category,
                        Period = s.Period,
                        Submite_date = s.Submite_date
                    });
                });

                #endregion

                #region Extracting unqiue VP meas

                using (CerebrumContext c3 = new CerebrumContext())
                {

                    List<VPUniqueMeasurement> c3Meas = new List<VPUniqueMeasurement>();

                    var c3meas = c3.VPMeasurement.OrderBy(x => x.Name).OrderByDescending( y => y.Testcode).ToList();

                    bool isDuplicate = false;

                    c3meas.ForEach(x =>
                    {

                        var duplicate = c3Meas.Where(d => d.Testcode == (string.IsNullOrEmpty(x.Testcode) ? string.Empty : x.Testcode) ||
                                                          d.Name.Trim().ToLower() == x.Name.Trim().ToLower()).ToList().FirstOrDefault();
                        isDuplicate = !(duplicate == null);

                        if (!isDuplicate)
                        {
                            c3Meas.Add(new VPUniqueMeasurement()
                            {

                                OLDID = x.OLDID,
                                OLD_TemplateFieldID = x.OLD_TemplateFieldID,
                                Name = x.Name,
                                Order = x.Order,
                                Units = x.Units,
                                Normal = x.Normal,
                                Range1 = x.Range1,
                                Range2 = x.Range2,
                                Spec = x.Spec,
                                Type = x.Type,
                                DrID = x.DrID,
                                Status = x.Status,
                                Options = x.Options,
                                Testcode = x.Testcode,
                                Cdid = x.Cdid,
                                NH = x.NH,
                                NL = x.NL,
                                TH = x.TH,
                                TL = x.TL,
                                Frequency = x.Frequency
                            });
                        }
                    });

                    c3.VPUniqueMeasurement.AddRange(c3Meas);
                    c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                }
                #endregion

            }

            #region Add c3 data
            using (CerebrumContext c3 = new CerebrumContext())
            {

                c3.VP_CPP_ImmunizationStatus.AddRange(lstImuinizationStatus);
                c3.VP_CPP_ImmunizationType.AddRange(lstImmunizationType);


                c3.DiagnoseCode.AddRange(lstDiagnoseCodes);
                c3.ConsultCode.AddRange(lstConsultCodes);
                c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                /*
                #region Add Loinc code
                //add loinc codes that are mapped to Vp measnuremts 
                List<OLISTestResultNomenclature> lstLoincLst = new List<OLISTestResultNomenclature>();

                #region Adding LOIN codes found in VP_Measurements 
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14569-8",
                    LOINCComponent = "17-hydroxyprogesterone",
                    LOINCProperty = "SCnc",
                    Units = "nmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "17OHP SerPl-sCnc",
                    LOINCFullySpecifiedName = "17-HYDROXYPROGESTERONE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "17-Hydroxyprogesterone",
                    resultAlternateName2 = "17-ALPHA-HYDROXYPROGESTERONE;",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.505.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:00.7,
                    //updatedOn = 12:00.7,
                    isActive = true
                });


                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14603-5",
                    LOINCComponent = "Androstenedione",
                    LOINCProperty = "SCnc",
                    Units = "nmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Androst SerPl-sCnc",
                    LOINCFullySpecifiedName = "ANDROSTENEDIONE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Androstenedione",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.306.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13 , 
                    //createdOn = 12:01.8,
                    //updatedOn = 12:01.8,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14635-7",
                    LOINCComponent = "Calcidiol",
                    LOINCProperty = "SCnc",
                    Units = "nmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Vit D25 SerPl-sCnc",
                    LOINCFullySpecifiedName = "CALCIDIOL:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Vitamin D, 25-Hydroxy",
                    resultAlternateName2 = "25-HYDROXYVITAMIN D;",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.325.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13 , 
                    //createdOn = 12:02.8,
                    //updatedOn = 12:02.8,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14637-3",
                    LOINCComponent = "Calcium",
                    LOINCProperty = "SRat",
                    Units = "mmolL/24hr",
                    LOINCTime = "24H",
                    LOINCSystem = "Urine",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Calcium 24H Ur-sRate",
                    LOINCFullySpecifiedName = "CALCIUM:SRAT:24H:URINE:QN",
                    resultAlternateName1 = "Calcium; 24h Urine",
                    resultAlternateName2 = "CA",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.189.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13,
                    //createdOn = 12:02.9,
                    //updatedOn = 12:02.9,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14646-4",
                    LOINCComponent = "Cholesterol.in HDL",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "HDLc SerPl-sCnc",
                    LOINCFullySpecifiedName = "CHOLESTEROL.IN HDL:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Cholesterol In HDL",
                    resultAlternateName2 = "HDL",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.260.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:03.2,
                    //updatedOn = 12:03.2,
                    isActive =true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14647-2",
                    LOINCComponent = "Cholesterol",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Cholest SerPl-sCnc",
                    LOINCFullySpecifiedName = "CHOLESTEROL:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Cholesterol",
                    resultAlternateName2 = "CHOL",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.119.000",
                    OLISTestResultCategoryId = 1,
                    //        OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:03.2,
                    //updatedOn = 12:03.2,
                    isActive =true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14682-9",
                    LOINCComponent = "Creatinine",
                    LOINCProperty = "SCnc",
                    Units = "umol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Creat SerPl-sCnc",
                    LOINCFullySpecifiedName = "CREATININE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Creatinine",
                    resultAlternateName2 = "CR",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.134.000",
                    OLISTestResultCategoryId = 1,
                    //         OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:04.4,
                    //updatedOn = 12:04.4,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14715-7",
                    LOINCComponent = "Estradiol",
                    LOINCProperty = "SCnc",
                    Units = "pmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Estradiol SerPl-sCnc",
                    LOINCFullySpecifiedName = "ESTRADIOL:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Estradiol",
                    resultAlternateName2 = "E2",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "	100000.300.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13 ,
                    //createdOn = 12:05.7,
                    //updatedOn = 12:05.7,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14771-0",
                    LOINCComponent = "Glucose^post CFst",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Glucose p fast SerPl-sCnc",
                    LOINCFullySpecifiedName = "GLUCOSE^POST CFST:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Glucose Fasting",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.102.000",
                    OLISTestResultCategoryId = 1,
                    //     OLISTestResultCategoryId = 16 ,
                    //createdOn = 12:07.6,
                    //updatedOn = 12:07.6,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14866-8",
                    LOINCComponent = "Parathyrin.intact",
                    LOINCProperty = "SCnc",
                    Units = "pmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "PTH-Intact SerPl-sCnc",
                    LOINCFullySpecifiedName = "PARATHYRIN.INTACT:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Parathyroid Hormone [PTH] Intact",
                    resultAlternateName2 = "PARATHYROID HORMONE INTACT;",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.324.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13 ,
                    //createdOn = 12:11.1,
                    //updatedOn = 12:11.1,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14914-6",
                    LOINCComponent = "Testosterone.free",
                    LOINCProperty = "SCnc",
                    Units = string.Empty,
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Testost Free SerPl-sCnc",
                    LOINCFullySpecifiedName = "TESTOSTERONE.FREE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Testosterone, Free",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.287.000",
                    OLISTestResultCategoryId = 1,
                    //       OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:12.8,
                    //updatedOn = 12:12.8,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14920-3",
                    LOINCComponent = "Thyroxine.free",
                    LOINCProperty = "SCnc",
                    Units = "pmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "T4 Free SerPl-sCnc",
                    LOINCFullySpecifiedName = "THYROXINE.FREE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Thyroxine, Free",
                    resultAlternateName2 = "FREE T4",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.267.000",
                    OLISTestResultCategoryId = 1,
                    //       OLISTestResultCategoryId = 13 ,
                    //createdOn = 12:13.0,
                    //updatedOn = 12:13.0,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14927-8",
                    LOINCComponent = "Triglyceride",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Trigl SerPl-sCnc",
                    LOINCFullySpecifiedName = "TRIGLYCERIDE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Triglyceride",
                    resultAlternateName2 = "TRIG",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.122.000",
                    OLISTestResultCategoryId = 1,
                    //     OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:13.3,
                    //updatedOn = 12:13.3,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14928-6",
                    LOINCComponent = "Triiodothyronine.free",
                    LOINCProperty = "SCnc",
                    Units = "pmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "T3Free SerPl-sCnc",
                    LOINCFullySpecifiedName = "TRIIODOTHYRONINE.FREE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Triiodothyronine, Free",
                    resultAlternateName2 = "T3 FREE",
                    resultAlternateName3 = "FT3;",
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.266.100",
                    OLISTestResultCategoryId = 1,
                    //       OLISTestResultCategoryId = 13 ,
                    //createdOn = 12:13.3,
                    //updatedOn = 12:13.3,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "14957-5",
                    LOINCComponent = "Albumin",
                    LOINCProperty = "MCnc",
                    Units = "mg/L;mcg/mL; mg/dL",
                    LOINCTime = "Pt",
                    LOINCSystem = "Urine",
                    LOINCMethod = "Detection limit = 20 mg/L",
                    LOINCShortName = "Microalbumin Ur Qn",
                    LOINCFullySpecifiedName = "ALBUMIN:MCNC:PT:URINE:QN:DETECTION LIMIT = 20 MG/L",
                    resultAlternateName1 = "Albumin; Urine",
                    resultAlternateName2 = "MICROALBUMIN;",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.190.000",
                    OLISTestResultCategoryId = 1,
                    //  OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:14.4,
                    //updatedOn = 12:14.4,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "15054-0",
                    LOINCComponent = "Dehydroepiandrosterone",
                    LOINCProperty = "SCnc",
                    Units = "umol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "DHEA SerPl-sCnc",
                    LOINCFullySpecifiedName = "DEHYDROEPIANDROSTERONE:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Dehydroepiandrosterone [DHEA]",
                    resultAlternateName2 = "DHEA",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.305.000",
                    OLISTestResultCategoryId = 1,
                    //    OLISTestResultCategoryId = 13  ,
                    //createdOn = 12:17.7,
                    //updatedOn = 12:17.7,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "1751-7",
                    LOINCComponent = "Albumin",
                    LOINCProperty = "MCnc",
                    Units = "g/L;g/dL; mg/dL",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Alb SerPl-mCnc",
                    LOINCFullySpecifiedName = "ALBUMIN:MCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Albumin",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.163.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13  ,
                    //createdOn = 14:05.3,
                    //updatedOn = 14:05.3,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "1920-8",
                    LOINCComponent = "Aspartate aminotransferase",
                    LOINCProperty = "CCnc",
                    Units = "U/L;units/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "AST SerPl-cCnc",
                    LOINCFullySpecifiedName = "ASPARTATE AMINOTRANSFERASE:CCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Aspartate Aminotransferase",
                    resultAlternateName2 = "AST",
                    resultAlternateName3 = "SGOT",
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.175.000",
                    OLISTestResultCategoryId = 1,
                    //       OLISTestResultCategoryId = 13  ,
                    //createdOn = 15:16.9,
                    //updatedOn = 15:16.9,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "1995-0",
                    LOINCComponent = "Calcium.ionized",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Ca-I SerPl-sCnc",
                    LOINCFullySpecifiedName = "CALCIUM.IONIZED:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Calcium Ionized",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.133.000",
                    OLISTestResultCategoryId = 1,
                    //         OLISTestResultCategoryId = 13  ,
                    //createdOn = 15:53.7,
                    //updatedOn = 15:53.7,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "2157-6",
                    LOINCComponent = "Creatine kinase",
                    LOINCProperty = "CCnc",
                    Units = "U/L;units/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "CK SerPl-cCnc",
                    LOINCFullySpecifiedName = "CREATINE KINASE:CCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Creatine Kinase",
                    resultAlternateName2 = "CK",
                    resultAlternateName3 = "CPK",
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.178.000",
                    OLISTestResultCategoryId = 1,
                    //            OLISTestResultCategoryId = 13 ,
                    //createdOn = 17:19.2,
                    //updatedOn = 17:19.2,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "2286-3",
                    LOINCComponent = "Follitropin",
                    LOINCProperty = "SCnc",
                    Units = string.Empty,
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "FSH SerPl-sCnc",
                    LOINCFullySpecifiedName = "FOLLITROPIN:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Follicle Stimulating Hormone [FSH]",
                    resultAlternateName2 = "FOLLICLE STIMULATING HORMONE;",
                    resultAlternateName3 = "FSH;",
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.285.200",
                    OLISTestResultCategoryId = 1,
                    //           OLISTestResultCategoryId = 13 ,
                    //createdOn = 19:01.4,
                    //updatedOn = 19:01.4,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "2601-3",
                    LOINCComponent = "Magnesium",
                    LOINCProperty = "SCnc",
                    Units = string.Empty,
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Magnesium SerPl-sCnc",
                    LOINCFullySpecifiedName = "MAGNESIUM:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Magnesium",
                    resultAlternateName2 = "MG",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.132.000",
                    OLISTestResultCategoryId = 1,
                    //          OLISTestResultCategoryId = 13  ,
                    //createdOn = 22:52.6,
                    //updatedOn = 22:52.6,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "2823-3",
                    LOINCComponent = "Potassium",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L	",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Potassium SerPl-sCnc",
                    LOINCFullySpecifiedName = "POTASSIUM:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Potassium",
                    resultAlternateName2 = "K",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.157.000",
                    OLISTestResultCategoryId = 1,
                    //         OLISTestResultCategoryId = 13 ,
                    //createdOn = 25:30.4,
                    //updatedOn = 25:30.4,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "2842-3",
                    LOINCComponent = "Prolactin",
                    LOINCProperty = "MCnc",
                    Units = "ug/L;ng/mL",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Prolactin SerPl-mCnc",
                    LOINCFullySpecifiedName = "PROLACTIN:MCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Prolactin",
                    resultAlternateName2 = "MAMMOTROPIN",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.308.000",
                    OLISTestResultCategoryId = 1,
                    //         OLISTestResultCategoryId = 13 ,
                    //createdOn = 25:32.4,
                    //updatedOn = 25:32.4,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "2951-2",
                    LOINCComponent = "Sodium",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Sodium SerPl-sCnc",
                    LOINCFullySpecifiedName = "SODIUM:SCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Sodium",
                    resultAlternateName2 = "NA;",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.152.000",
                    OLISTestResultCategoryId = 1,
                    //     OLISTestResultCategoryId = 13  ,
                    //createdOn = 26:17.3,
                    //updatedOn = 26:17.3,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "3016-3",
                    LOINCComponent = "Thyrotropin",
                    LOINCProperty = "ACnc",
                    Units = "mU/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "TSH SerPl-aCnc",
                    LOINCFullySpecifiedName = "THYROTROPIN:ACNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Thyroid Stimulating Hormone [TSH]",
                    resultAlternateName2 = "THYROID STIMULATING HORMONE",
                    resultAlternateName3 = "TSH",
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.265.000",
                    OLISTestResultCategoryId = 1,
                    //      OLISTestResultCategoryId = 13 ,
                    //createdOn = 27:36.2,
                    //updatedOn = 27:36.2,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "30522-7",
                    LOINCComponent = "C reactive protein",
                    LOINCProperty = "MCnc",
                    Units = "mcg/mL; mg/dL; mg/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = "High sensitivity",
                    LOINCShortName = "CRP SerPl High Sens-mCnc",
                    LOINCFullySpecifiedName = "C REACTIVE PROTEIN:MCNC:PT:SER/PLAS:QN:HIGH SENSITIVITY",
                    resultAlternateName1 = "C Reactive Protein",
                    resultAlternateName2 = "CRP HIGH SENSITIVITY",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "300000.072.300",
                    OLISTestResultCategoryId = 1,
                    //         OLISTestResultCategoryId = 13 ,
                    //createdOn = 28:23.6,
                    //updatedOn = 28:23.6,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "3167-4",
                    LOINCComponent = "Specimen volume",
                    LOINCProperty = "Vol",
                    Units = string.Empty,
                    LOINCTime = "24H",
                    LOINCSystem = "Urine",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Spec Vol 24H Ur",
                    LOINCFullySpecifiedName = "SPECIMEN VOLUME:VOL:24H:URINE:QN",
                    resultAlternateName1 = "Specimen Volume; 24h Urine",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "000000.151.000",
                    OLISTestResultCategoryId = 1,
                    //         OLISTestResultCategoryId = 17  ,
                    //createdOn = 30:08.7,
                    //updatedOn = 30:08.7,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "33914-3",
                    LOINCComponent = "Glomerular filtration rate.predicted",
                    LOINCProperty = "VRat",
                    Units = "mL/min; ml/min/1.73m2",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = "MDRD formula",
                    LOINCShortName = "Pred GFR SerPl MDRD-vRate",
                    LOINCFullySpecifiedName = "GLOMERULAR FILTRATION RATE.PREDICTED:VRAT:PT:SER/PLAS:QN:MDRD FORMULA",
                    resultAlternateName1 = "Glomerular Filtration Rate Predicted",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.140.000",
                    OLISTestResultCategoryId = 1,
                    //       OLISTestResultCategoryId = 13 ,
                    //createdOn = 34:35.9,
                    //updatedOn = 34:35.9,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "39469-2",
                    LOINCComponent = "Cholesterol.in LDL",
                    LOINCProperty = "SCnc",
                    Units = "mmol/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas	",
                    LOINCMethod = "Calculated",
                    LOINCShortName = "LDLc SerPl Calc-sCnc",
                    LOINCFullySpecifiedName = "CHOLESTEROL.IN LDL:SCNC:PT:SER/PLAS:QN:CALCULATED",
                    resultAlternateName1 = "Cholesterol In LDL; Calculated",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.259.100",
                    OLISTestResultCategoryId = 1,
                    //       OLISTestResultCategoryId = 13 ,
                    //createdOn = 41:00.8,
                    //updatedOn = 41:00.8,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "4548-4",
                    LOINCComponent = "Hemoglobin A1c/Hemoglobin.total",
                    LOINCProperty = "MFr",
                    Units = "%; % Hgb",
                    LOINCTime = "Pt",
                    LOINCSystem = "Bld",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "Hgb A1c fr Bld",
                    LOINCFullySpecifiedName = "HEMOGLOBIN A1C/HEMOGLOBIN.TOTAL:MFR:PT:BLD:QN",
                    resultAlternateName1 = "Hemoglobin A1C/Total Hemoglobin; Blood",
                    resultAlternateName2 = string.Empty,
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.251.000",
                    OLISTestResultCategoryId = 1,
                    //  OLISTestResultCategoryId = 15  ,
                    //createdOn = 54:19.3,
                    //updatedOn = 54:19.3,
                    isActive = true
                });
                lstLoincLst.Add(new OLISTestResultNomenclature()
                {
                    LOINCCode = "6768-6",
                    LOINCComponent = "Alkaline phosphatase",
                    LOINCProperty = "CCnc",
                    Units = "U/L;units/L",
                    LOINCTime = "Pt",
                    LOINCSystem = "Ser/Plas",
                    LOINCMethod = string.Empty,
                    LOINCShortName = "ALP SerPl-cCnc",
                    LOINCFullySpecifiedName = "ALKALINE PHOSPHATASE:CCNC:PT:SER/PLAS:QN",
                    resultAlternateName1 = "Alkaline Phosphatase",
                    resultAlternateName2 = "ALK PHOS",
                    resultAlternateName3 = string.Empty,
                    LOINCAnswerList = string.Empty,
                    sortKey = "100000.174.000",
                    OLISTestResultCategoryId = 1,
                    //   OLISTestResultCategoryId = 13  ,
                    //createdOn = 47:40.7,
                    //updatedOn = 47:40.7,
                    isActive = true
                });
                #endregion 
                
                c3.OLISTestResultNomenclature.AddRange(lstLoincLst);
                c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
               
               
                    //get Loinc codes from VPMeasurement 
                //(type=1, measn labs)and copy them to VPTemplateField based on name 
               
                // var testCodes = (from m in c3.VPMeasurement
                                 //join t in c3.VPTemplateField on
                                 // m.Name.Trim().ToLower() equals t.Name.Trim().ToLower()
                                 // where !string.IsNullOrEmpty(m.Testcode) &&
                                 //       m.Type == 1  
                                 //select new { TestCode=m.Testcode , TemplateItem = t }).ToList();


                var testCodes = (from m in c3.VPMeasurement
                                 join t in c3.VPTemplateField on
                                  m.OLDID equals t.OLDVPMID
                                 //where //!string.IsNullOrEmpty(m.Testcode) &&
                                       //m.Type == 1 //for labs
                                 select new { TestCode = m.Testcode, TemplateItem = t }).ToList();

                testCodes.ForEach(t =>
                {

                    t.TemplateItem.TestCode = t.TestCode;
                });

                c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

                #endregion
                 */

                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "not arrived" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "arrived" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "test started" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "test completed" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "images/data transferred" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "ready for doctor" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "trainee report ready" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "ready for sending" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "sent" });
                //c3.AppointmentTestStatus.Add(new AppointmentTestStatus() { Status = "amended" });


                c3.SendType.Add(new Cerebrum.Data.SendType() { Name = "HRM", Color = "Red" });
                c3.SendType.Add(new Cerebrum.Data.SendType() { Name = "Fax", Color = "Blue" });
                c3.SendType.Add(new Cerebrum.Data.SendType() { Name = "Email", Color = "Brown" });
                c3.SendType.Add(new Cerebrum.Data.SendType() { Name = "Mail", Color = "Green" });

                c3.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            }
            #endregion
        }

        public static void LoadHL7Coding()
        {
            
            using (var context = new cm2Entities())
            {
                var codinglst = new List<Cerebrum.Data.HL7Coding>();
                var c3 = new CerebrumContext();
                var hl7 = context.HL7Coding.Where(w => (!w.LOINC.StartsWith("000")));
                foreach (var h in hl7)
                {
                    if (codinglst.Count() == 0 || (!codinglst.Any(a => a.labName.Equals(h.LabName) && a.labCode.Equals(h.LabCode))))
                    {
                        var c3hl7 = new Cerebrum.Data.HL7Coding { labName = h.LabName, labCode = h.LabCode, LOINC = h.LOINC, description = h.Description, createdDate = DateTime.Now, updatedDate = DateTime.Now };
                        codinglst.Add(c3hl7);
                    }
                }
                c3.HL7Codings.AddRange(codinglst);
                c3.SaveChanges();
            }
        }
        private static List<doctor1> InsertExternalDocList(List<doctor1> oldDocList, int mastreId)
        {
            List<doctor1> faiedDocList = new List<doctor1>();
            using (CerebrumContext ent = new CerebrumContext())
            {
                for (int i = 0; i < oldDocList.Count; i++)
                {
                    ExternalDoctor extDoc = new ExternalDoctor();
                    extDoc.OHIPPhysicianId = oldDocList[i].BCode;
                    extDoc.firstName = oldDocList[i].FName;
                    extDoc.middleName = null;
                    extDoc.lastName = oldDocList[i].LName;
                    extDoc.CPSO = oldDocList[i].cpso;
                    extDoc.HRMId = null;
                    extDoc.description = null;
                    extDoc.comment = oldDocList[i].comment;
                    extDoc.locked = oldDocList[i].locked == null ? false : Convert.ToBoolean(oldDocList[i].locked);
                    extDoc.active = oldDocList[i].Status == 0 ? true : false;
                    extDoc.MasterId = mastreId;
                    ent.ExternalDoctors.Add(extDoc);

                    try
                    {
                        ent.SaveChanges();

                        if (oldDocList[i].Phone1 != null)
                        {
                            ExternalDoctorPhoneNumber ph_num1 = new ExternalDoctorPhoneNumber();
                            ph_num1.phoneNumber = oldDocList[i].Phone1;
                            //ph_num1.choiceType = PhoneType.phoneNumber;
                            ph_num1.typeOfPhoneNumber = PhoneNumberType.R;
                            ph_num1.ExternalDoctorId = extDoc.Id;
                            ent.ExternalDoctorPhoneNumbers.Add(ph_num1);
                        }
                        if (oldDocList[i].Phone2 != null)
                        {
                            ExternalDoctorPhoneNumber ph_num2 = new ExternalDoctorPhoneNumber();
                            ph_num2.phoneNumber = oldDocList[i].Phone2;
                            //ph_num2.choiceType = PhoneType.phoneNumber;
                            ph_num2.typeOfPhoneNumber = PhoneNumberType.R;
                            ph_num2.ExternalDoctorId = extDoc.Id;
                            ent.ExternalDoctorPhoneNumbers.Add(ph_num2);
                        }
                        if (oldDocList[i].Phone3 != null)
                        {
                            ExternalDoctorPhoneNumber ph_num3 = new ExternalDoctorPhoneNumber();
                            ph_num3.phoneNumber = oldDocList[i].Phone3;
                            //ph_num3.choiceType = PhoneType.faxNumber;
                            ph_num3.typeOfPhoneNumber = PhoneNumberType.R;
                            ph_num3.ExternalDoctorId = extDoc.Id;
                            ent.ExternalDoctorPhoneNumbers.Add(ph_num3);
                        }
                        if (oldDocList[i].Phone4 != null)
                        {
                            ExternalDoctorPhoneNumber ph_num4 = new ExternalDoctorPhoneNumber();
                            ph_num4.phoneNumber = oldDocList[i].Phone4;
                            //ph_num4.choiceType = PhoneType.phoneNumber;
                            ph_num4.typeOfPhoneNumber = PhoneNumberType.R;
                            ph_num4.ExternalDoctorId = extDoc.Id;
                            ent.ExternalDoctorPhoneNumbers.Add(ph_num4);
                        }

                        ent.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        faiedDocList.Add(oldDocList[i]);
                        System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                        string methodName = st.GetFrame(0).GetMethod().Name;

                        string Msg = methodName + " ### " + ex.Message + " ### ";
                        if (ex.InnerException != null)
                            Msg += ex.InnerException.Message;

                        Helper.WriteToLog(Msg);
                        //errLable.Text = "There is connection problem try again! ";
                    }
                }
            }

            return faiedDocList;
        }

        private static List<doctor1> GetExternalDoctorsList(int i, int groupSize)
        {
            List<doctor1> doctorsList = null;
            try
            {
                using (cm2Entities ent = new cm2Entities())
                {
                    doctorsList = ent.doctors1.OrderBy(t => t.ID).Select(t => t).ToList();
                }
                doctorsList = doctorsList.Skip(i * groupSize).Take(groupSize).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);
                //errLable.Text = "There is connection problem try again! ";
            }

            return doctorsList;
        }

        public static int GetTableRowNumber(string tblName)
        {
            int rowNum = 0;
            try
            {
                using (cm2Entities ent = new cm2Entities())
                {
                    if (tblName == "doctors")
                    {
                        rowNum = ent.doctors1.OrderBy(t => t.ID).Select(t => t.ID).Count();
                    }
                    else
                    {
                        //
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message;

                Helper.WriteToLog(Msg);
                //errLable.Text = "There is connection problem try again! ";
            }

            return rowNum;
        }

        public static int GetNumberGroup(int groupSize, int tblRowCount)
        {
            int numOfGroups = 0;
            int dec = tblRowCount % groupSize;
            if (dec > 0)
            {
                numOfGroups = (tblRowCount - dec) / groupSize + 1;
            }
            else
            {
                numOfGroups = tblRowCount / groupSize;
            }

            return numOfGroups;
        }

    }

    public class ID
    {
        public int IdOld { get; set; }
        public int IdNew { get; set; }
    }
}