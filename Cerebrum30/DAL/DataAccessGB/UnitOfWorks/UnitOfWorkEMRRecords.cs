﻿using Cerebrum.Data;
using System.Web;
using Cerebrum30.Utility;
namespace Cerebrum30.DAL.DataAccessGB
{
    public class UnitOfWorkEMRRecords : CerebrumContext, IUnitOfWorkEMRRecords
    {
        private readonly ServicesGenericRepositoryGB<Appointment> _AppointmentsRepro;
        private readonly ServicesGenericRepositoryGB<Demographic> _DemographicsReprosRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctor> _ExternalDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<PracticeDoctor> _PracticeDoctorsReprosRepro;

        private readonly ServicesGenericRepositoryGB<ReportReceived> _ReportReceivedsRepro;
        private readonly ServicesGenericRepositoryGB<HL7Report> _HL7ReportsRepro;
        private readonly ServicesGenericRepositoryGB<HL7ReportDoctor> _HL7ReportDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<HL7ReportVersion> _HL7ReportVersionsRepro;
        private readonly ServicesGenericRepositoryGB<HL7MarkedSeen> _HL7MarkedSeensRepro;
        private readonly ServicesGenericRepositoryGB<CM_TaskMessageRecipient> _CM_TaskMessageRecipientsRepro;
        private readonly ServicesGenericRepositoryGB<BillDetail> _BillDetailsRepro;

        public UnitOfWorkEMRRecords()
        {
            _AppointmentsRepro = new ServicesGenericRepositoryGB<Appointment>(Appointments);
            _DemographicsReprosRepro = new ServicesGenericRepositoryGB<Demographic>(Demographics);
            _ExternalDoctorsRepro = new ServicesGenericRepositoryGB<ExternalDoctor>(ExternalDoctors);
            _PracticeDoctorsReprosRepro = new ServicesGenericRepositoryGB<PracticeDoctor>(PracticeDoctors);
            _ReportReceivedsRepro = new ServicesGenericRepositoryGB<ReportReceived>(ReportsReceived);
            _HL7ReportsRepro = new ServicesGenericRepositoryGB<HL7Report>(HL7Reports);
            _HL7ReportDoctorsRepro = new ServicesGenericRepositoryGB<HL7ReportDoctor>(HL7ReportDoctors);
            _HL7ReportVersionsRepro = new ServicesGenericRepositoryGB<HL7ReportVersion>(HL7ReportVersions);
            _HL7MarkedSeensRepro = new ServicesGenericRepositoryGB<HL7MarkedSeen>(HL7MarkedSeens);
            _CM_TaskMessageRecipientsRepro = new ServicesGenericRepositoryGB<CM_TaskMessageRecipient>(CM_TaskMessageRecipients);
            _BillDetailsRepro = new ServicesGenericRepositoryGB<BillDetail>(BillDetails);
        }

        public IGenericRepository<BillDetail> BillDetailsRepro
        {
            get
            {
                return _BillDetailsRepro;
            }
        }

        public IGenericRepository<ReportReceived> ReportReceivedsRepro
        {
            get
            {
                return _ReportReceivedsRepro;
            }
        }
        public IGenericRepository<HL7Report> HL7ReportsRepro
        {
            get
            {
                return _HL7ReportsRepro;
            }
        }
        public IGenericRepository<HL7ReportDoctor> HL7ReportDoctorsRepro
        {
            get
            {
                return _HL7ReportDoctorsRepro;
            }
        }
        public IGenericRepository<HL7ReportVersion> HL7ReportVersionsRepro
        {
            get
            {
                return _HL7ReportVersionsRepro;
            }
        }
        public IGenericRepository<HL7MarkedSeen> HL7MarkedSeensRepro
        {
            get
            {
                return _HL7MarkedSeensRepro;
            }
        }
        public IGenericRepository<CM_TaskMessageRecipient> CM_TaskMessageRecipientsRepro
        {
            get
            {
                return _CM_TaskMessageRecipientsRepro;
            }
        }


        public IGenericRepository<Appointment> AppointmentsRepro
        {
            get
            {
                return _AppointmentsRepro;
            }
        }

        public IGenericRepository<Demographic> DemographicsRepro
        {
            get
            {
                return _DemographicsReprosRepro;
            }
        }

        public IGenericRepository<ExternalDoctor> ExternalDoctorsRepro
        {
            get
            {
                return _ExternalDoctorsRepro;
            }
        }

        public IGenericRepository<PracticeDoctor> PracticeDoctorsRepro
        {
            get
            {
                return _PracticeDoctorsReprosRepro;
            }
        }

        public void Commit()
        {
            //SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            SaveChanges();
        }
    }
}