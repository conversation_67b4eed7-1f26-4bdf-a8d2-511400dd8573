﻿using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum.Data;
using Cerebrum30.Utility;
namespace Cerebrum30.DAL.DataAccessGB
{
    public class UnitOfWorkHealthCardService : CerebrumContext, IUnitOfWorkHealthCardService
    {
        private readonly ServicesGenericRepositoryGB<OHIPTimeLimitedFeeCode> _OHIPTimeLimitedFeeCodesRepo;
        private readonly ServicesGenericRepositoryGB<ExternalDoctor> _ExternalDoctorsRepo;
        private readonly ServicesGenericRepositoryGB<PracticeDoctor> _PracticeDoctorsRepo;


        public UnitOfWorkHealthCardService()
        {
            _OHIPTimeLimitedFeeCodesRepo = new ServicesGenericRepositoryGB<OHIPTimeLimitedFeeCode>(OHIPTimeLimitedFeeCodes);
            _ExternalDoctorsRepo = new ServicesGenericRepositoryGB<ExternalDoctor>(ExternalDoctors);
            _PracticeDoctorsRepo = new ServicesGenericRepositoryGB<PracticeDoctor>(PracticeDoctors);
        }

        public IGenericRepository<OHIPTimeLimitedFeeCode> OHIPTimeLimitedFeeCodeRepro
        {
            get
            {
                return _OHIPTimeLimitedFeeCodesRepo;
            }
        }

        public IGenericRepository<ExternalDoctor> ExternalDoctorRepro
        {
            get
            {
                return _ExternalDoctorsRepo;
            }
        }

        public IGenericRepository<PracticeDoctor> PracticeDoctorRepro  
        {
            get
            {
                return _PracticeDoctorsRepo;
            }
        }


        public void Commit()
        {
            SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
        }
    }
}