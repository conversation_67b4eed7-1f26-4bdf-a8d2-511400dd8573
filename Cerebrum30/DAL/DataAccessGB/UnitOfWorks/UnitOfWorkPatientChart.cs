﻿using Cerebrum.Data;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum30.Utility;
namespace Cerebrum30.DAL.DataAccessGB
{
    public class UnitOfWorkPatientChart : CerebrumContext, IUnitOfWorkPatientChart
    {//<> 
        private readonly ServicesGenericRepositoryGB<Demographic> _DemographicsRepro;

        private readonly ServicesGenericRepositoryGB<DemographicsFamilyDoctor> _DemographicsFamilyDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsMainResponsiblePhysician> _DemographicsMainResponsiblePhysiciansRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsContactPhoneNumber> _DemographicsContactPhoneNumbersRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsEnrollment> _DemographicsEnrollmentsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsHealthCard> _DemographicsHealthCardsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsNextOfKin> _DemographicsNextOfKinsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsPhoneNumber> _DemographicsPhoneNumbersRepro;
        private readonly ServicesGenericRepositoryGB<PatientMRN> _PatientMRNsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsAddress> _DemographicsAddressesRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctor> _ExternalDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<ReportClass> _ReportClasssRepro;
        private readonly ServicesGenericRepositoryGB<ReportReceived> _ReportReceivedsRepro;
        private readonly ServicesGenericRepositoryGB<PatientRecord> _PatientRecordsRepro;
        private readonly ServicesGenericRepositoryGB<HL7Patient> _HL7PatientsRepro;
        private readonly ServicesGenericRepositoryGB<HL7Report> _HL7ReportsRepro;
        private readonly ServicesGenericRepositoryGB<HL7ReportVersion> _HL7ReportVersionsRepro;
        private readonly ServicesGenericRepositoryGB<HL7Result> _HL7ResultsRepro;
        private readonly ServicesGenericRepositoryGB<VP_CPP_RiskFactor> _VP_CPP_RiskFactorsRepro;
        private readonly ServicesGenericRepositoryGB<VP_CPP_Alert> _VP_CPP_AlertsRepro;
        private readonly ServicesGenericRepositoryGB<VP_CPP_Problem_List> _VP_CPP_Problem_ListsRepro;
        private readonly ServicesGenericRepositoryGB<VP_CPP_FamilyHistory> _VP_CPP_FamilyHistorysRepro;
        private readonly ServicesGenericRepositoryGB<Reason> _ReasonsRepro;
        private readonly ServicesGenericRepositoryGB<DocService> _DocServicesRepro;
        private readonly ServicesGenericRepositoryGB<Requisition> _RequisitionsRepro;
        private readonly ServicesGenericRepositoryGB<RequisitionPatient> _RequisitionPatientsRepro;
        private readonly ServicesGenericRepositoryGB<OfficeUrl> _OfficeUrlsRepro;
        private readonly ServicesGenericRepositoryGB<DoctorComment> _DoctorCommentsRepro;
        private readonly ServicesGenericRepositoryGB<VP_CPP_Immunization> _VP_CPP_ImmunizationsRepro;

        public UnitOfWorkPatientChart()
        {
            _DemographicsRepro = new ServicesGenericRepositoryGB<Demographic>(Demographics);
            _DemographicsFamilyDoctorsRepro = new ServicesGenericRepositoryGB<DemographicsFamilyDoctor>(DemographicsFamilyDoctors);
            _DemographicsMainResponsiblePhysiciansRepro = new ServicesGenericRepositoryGB<DemographicsMainResponsiblePhysician>(DemographicsMainResponsiblePhysicians);
            _DemographicsContactPhoneNumbersRepro = new ServicesGenericRepositoryGB<DemographicsContactPhoneNumber>(DemographicsContactPhoneNumbers);
            _DemographicsEnrollmentsRepro = new ServicesGenericRepositoryGB<DemographicsEnrollment>(DemographicsEnrollments);
            _DemographicsHealthCardsRepro = new ServicesGenericRepositoryGB<DemographicsHealthCard>(HealthCards);
            _DemographicsNextOfKinsRepro = new ServicesGenericRepositoryGB<DemographicsNextOfKin>(DemographicsContacts);
            _DemographicsPhoneNumbersRepro = new ServicesGenericRepositoryGB<DemographicsPhoneNumber>(PhoneNumber);
            _PatientMRNsRepro = new ServicesGenericRepositoryGB<PatientMRN>(PatientMRNs);
            _DemographicsAddressesRepro = new ServicesGenericRepositoryGB<DemographicsAddress>(DemographicsAddress);
            _ExternalDoctorsRepro = new ServicesGenericRepositoryGB<ExternalDoctor>(ExternalDoctors);
            _ReportClasssRepro = new ServicesGenericRepositoryGB<ReportClass>(ReportClasses);
            _ReportReceivedsRepro = new ServicesGenericRepositoryGB<ReportReceived>(ReportsReceived);
            _PatientRecordsRepro = new ServicesGenericRepositoryGB<PatientRecord>(PatientRecords);
            _HL7PatientsRepro = new ServicesGenericRepositoryGB<HL7Patient>(HL7Patients);
            _HL7ReportsRepro = new ServicesGenericRepositoryGB<HL7Report>(HL7Reports);
            _HL7ReportVersionsRepro = new ServicesGenericRepositoryGB<HL7ReportVersion>(HL7ReportVersions);
            _HL7ResultsRepro = new ServicesGenericRepositoryGB<HL7Result>(HL7Results);
            _VP_CPP_RiskFactorsRepro = new ServicesGenericRepositoryGB<VP_CPP_RiskFactor>(VP_CPP_RiskFactor);
            _VP_CPP_AlertsRepro = new ServicesGenericRepositoryGB<VP_CPP_Alert>(VP_CPP_Alert);
            _VP_CPP_Problem_ListsRepro = new ServicesGenericRepositoryGB<VP_CPP_Problem_List>(VP_CPP_Problem_List);
            _VP_CPP_FamilyHistorysRepro = new ServicesGenericRepositoryGB<VP_CPP_FamilyHistory>(VP_CPP_FamilyHistory);
            _ReasonsRepro = new ServicesGenericRepositoryGB<Reason>(Reasons);
            _DocServicesRepro = new ServicesGenericRepositoryGB<DocService>(DocServices);
            _RequisitionsRepro = new ServicesGenericRepositoryGB<Requisition>(Requisition);
            _RequisitionPatientsRepro = new ServicesGenericRepositoryGB<RequisitionPatient>(RequisitionPatient);
            _OfficeUrlsRepro = new ServicesGenericRepositoryGB<OfficeUrl>(OfficeUrls);
            _DoctorCommentsRepro = new ServicesGenericRepositoryGB<DoctorComment>(DoctorComments);
            _VP_CPP_ImmunizationsRepro = new ServicesGenericRepositoryGB<VP_CPP_Immunization>(VP_CPP_Immunization);
        }

        public IGenericRepository<VP_CPP_Immunization> VP_CPP_ImmunizationsRepro
        {
            get
            {
                return _VP_CPP_ImmunizationsRepro;
            }
        }
        public IGenericRepository<DoctorComment> DoctorCommentsRepro
        {
            get
            {
                return _DoctorCommentsRepro;
            }
        }
        public IGenericRepository<OfficeUrl> OfficeUrlsRepro
        {
            get
            {
                return _OfficeUrlsRepro;
            }
        }
        public IGenericRepository<Requisition> RequisitionsRepro
        {
            get
            {
                return _RequisitionsRepro;
            }
        }

        public IGenericRepository<RequisitionPatient> RequisitionPatientsRepro
        {
            get
            {
                return _RequisitionPatientsRepro;
            }
        }

        public IGenericRepository<Reason> ReasonsRepro
        {
            get
            {
                return _ReasonsRepro;
            }
        }

        public IGenericRepository<DocService> DocServicesRepro
        {
            get
            {
                return _DocServicesRepro;
            }
        }

        public IGenericRepository<VP_CPP_FamilyHistory> VP_CPP_FamilyHistorysRepro
        {
            get
            {
                return _VP_CPP_FamilyHistorysRepro;
            }
        }

        public IGenericRepository<VP_CPP_Problem_List> VP_CPP_Problem_ListsRepro
        {
            get
            {
                return _VP_CPP_Problem_ListsRepro;
            }
        }

        public IGenericRepository<VP_CPP_Alert> VP_CPP_AlertsRepro
        {
            get
            {
                return _VP_CPP_AlertsRepro;
            }
        }

        public IGenericRepository<VP_CPP_RiskFactor> VP_CPP_RiskFactorsRepro
        {
            get
            {
                return _VP_CPP_RiskFactorsRepro;
            }
        }

        public IGenericRepository<HL7Result> HL7ResultsRepro
        {
            get
            {
                return _HL7ResultsRepro;
            }
        }

        public IGenericRepository<HL7ReportVersion> HL7ReportVersionsRepro
        {
            get
            {
                return _HL7ReportVersionsRepro;
            }
        }

        public IGenericRepository<HL7Report> HL7ReportsRepro
        {
            get
            {
                return _HL7ReportsRepro;
            }
        }

        public IGenericRepository<HL7Patient> HL7PatientsRepro
        {
            get
            {
                return _HL7PatientsRepro;
            }
        }

        public IGenericRepository<PatientRecord> PatientRecordsRepro
        {
            get
            {
                return _PatientRecordsRepro;
            }
        }

        public IGenericRepository<ReportReceived> ReportReceivedsRepro
        {
            get
            {
                return _ReportReceivedsRepro;
            }
        }

        public IGenericRepository<ReportClass> ReportClasssRepro
        {
            get
            {
                return _ReportClasssRepro;
            }
        }

        public IGenericRepository<ExternalDoctor> ExternalDoctorsRepro
        {
            get
            {
                return _ExternalDoctorsRepro;
            }
        }

        public IGenericRepository<DemographicsAddress> DemographicsAddressesRepro
        {
            get
            {
                return _DemographicsAddressesRepro;
            }
        }

        public IGenericRepository<PatientMRN> PatientMRNsRepro
        {
            get
            {
                return _PatientMRNsRepro;
            }
        }

        public IGenericRepository<Demographic> DemographicsRepro
        {
            get
            {
                return _DemographicsRepro;
            }
        }

        public IGenericRepository<DemographicsFamilyDoctor> DemographicsFamilyDoctorsRepro
        {
            get
            {
                return _DemographicsFamilyDoctorsRepro;
            }
        }

        public IGenericRepository<DemographicsMainResponsiblePhysician> DemographicsMainResponsiblePhysiciansRepro
        {
            get
            {
                return _DemographicsMainResponsiblePhysiciansRepro;
            }
        }

        public IGenericRepository<DemographicsContactPhoneNumber> DemographicsContactPhoneNumbersRepro
        {
            get
            {
                return _DemographicsContactPhoneNumbersRepro;
            }
        }

        public IGenericRepository<DemographicsEnrollment> DemographicsEnrollmentsRepro
        {
            get
            {
                return _DemographicsEnrollmentsRepro;
            }
        }

        public IGenericRepository<DemographicsHealthCard> DemographicsHealthCardsRepro
        {
            get
            {
                return _DemographicsHealthCardsRepro;
            }
        }

        public IGenericRepository<DemographicsNextOfKin> DemographicsNextOfKinsRepro
        {
            get
            {
                return _DemographicsNextOfKinsRepro;
            }
        }

        public IGenericRepository<DemographicsPhoneNumber> DemographicsPhoneNumbersRepro
        {
            get
            {
                return _DemographicsPhoneNumbersRepro;
            }
        }

        public void Commit()
        {
            SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
        }
    }
}