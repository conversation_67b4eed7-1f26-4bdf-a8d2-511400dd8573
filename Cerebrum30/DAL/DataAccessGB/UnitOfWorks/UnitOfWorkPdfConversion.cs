﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum.Data;
using Cerebrum30.Utility;
namespace Cerebrum30.DAL.DataAccessGB
{
    public class UnitOfWorkPdfConversion : CerebrumContext, IUnitOfWorkPdfConversion
    {
        private readonly ServicesGenericRepositoryGB<Demographic> _DemographicsRepro;
        private readonly ServicesGenericRepositoryGB<OfficeOutlook> _OfficeOutlooksRepro;
        private readonly ServicesGenericRepositoryGB<Office> _OfficesRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsHealthCard> _DemographicsHealthCardsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsDefaultReferralDoctor> _DemographicsDefaultReferralDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsFamilyDoctor> _DemographicsFamilyDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctor> _ExternalDoctorsRepro;
        private readonly ServicesGenericRepositoryGB<DemographicsPhoneNumber> _DemographicsPhoneNumberRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctor> _ExternalDoctorRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctorAddress> _ExternalDoctorAddressRepro;
        private readonly ServicesGenericRepositoryGB<ExternalDoctorPhoneNumber> _ExternalDoctorPhoneNumberRepro;
        private readonly ServicesGenericRepositoryGB<Test> _TestRepro;

        public UnitOfWorkPdfConversion()
        {
            _DemographicsRepro = new ServicesGenericRepositoryGB<Demographic>(Demographics);
            _OfficeOutlooksRepro = new ServicesGenericRepositoryGB<OfficeOutlook>(OfficeOutlooks);
            _OfficesRepro = new ServicesGenericRepositoryGB<Office>(Offices);
            _DemographicsHealthCardsRepro = new ServicesGenericRepositoryGB<DemographicsHealthCard>(HealthCards);
            _DemographicsDefaultReferralDoctorsRepro = new ServicesGenericRepositoryGB<DemographicsDefaultReferralDoctor>(DemographicsDefaultReferralDoctors);
            _DemographicsFamilyDoctorsRepro = new ServicesGenericRepositoryGB<DemographicsFamilyDoctor>(DemographicsFamilyDoctors);
            _ExternalDoctorsRepro = new ServicesGenericRepositoryGB<ExternalDoctor>(ExternalDoctors);
            _DemographicsPhoneNumberRepro = new ServicesGenericRepositoryGB<DemographicsPhoneNumber>(PhoneNumber);
            _ExternalDoctorRepro = new ServicesGenericRepositoryGB<ExternalDoctor>(ExternalDoctors);
            _ExternalDoctorAddressRepro = new ServicesGenericRepositoryGB<ExternalDoctorAddress>(ExternalDoctorAddresses);
            _ExternalDoctorPhoneNumberRepro = new ServicesGenericRepositoryGB<ExternalDoctorPhoneNumber>(ExternalDoctorPhoneNumbers);
            _TestRepro = new ServicesGenericRepositoryGB<Test>(Tests);
        }

        public IGenericRepository<Test> TestRepro
        {
            get
            {
                return _TestRepro;
            }
        }
        public IGenericRepository<ExternalDoctorPhoneNumber> ExternalDoctorPhoneNumberRepro
        {
            get
            {
                return _ExternalDoctorPhoneNumberRepro;
            }
        }

        public IGenericRepository<ExternalDoctorAddress> ExternalDoctorAddressRepro
        {
            get
            {
                return _ExternalDoctorAddressRepro;
            }
        }

        public IGenericRepository<ExternalDoctor> ExternalDoctorRepro
        {
            get
            {
                return _ExternalDoctorRepro;
            }
        }

        public IGenericRepository<DemographicsPhoneNumber> DemographicsPhoneNumberRepro
        {
            get
            {
                return _DemographicsPhoneNumberRepro;
            }
        }

        public IGenericRepository<ExternalDoctor> ExternalDoctorsRepro
        {
            get
            {
                return _ExternalDoctorsRepro;
            }
        }

        public IGenericRepository<DemographicsHealthCard> DemographicsHealthCardsRepro
        {
            get
            {
                return _DemographicsHealthCardsRepro;
            }
        }

        public IGenericRepository<DemographicsDefaultReferralDoctor> DemographicsDefaultReferralDoctorsRepro
        {
            get
            {
                return _DemographicsDefaultReferralDoctorsRepro;
            }
        }

        public IGenericRepository<DemographicsFamilyDoctor> DemographicsFamilyDoctorsRepro
        {
            get
            {
                return _DemographicsFamilyDoctorsRepro;
            }
        }

        public IGenericRepository<Demographic> DemographicsRepro
        {
            get
            {
                return _DemographicsRepro;
            }
        }

        public IGenericRepository<OfficeOutlook> OfficeOutlooksRepro
        {
            get
            {
                return _OfficeOutlooksRepro;
            }
        }

        public IGenericRepository<Office> OfficesRepro
        {
            get
            {
                return _OfficesRepro;
            }
        }

        public void Commit()
        {
            SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
        }
    }
}