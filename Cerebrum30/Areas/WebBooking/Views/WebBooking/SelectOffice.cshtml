﻿@model Cerebrum30.Areas.WebBooking.Models.ViewModels.SelectOffice_VM
@{

}

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-timepicker-addon/1.6.3/jquery-ui-sliderAccess.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-timepicker-addon/1.6.3/jquery-ui-timepicker-addon.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-timepicker-addon/1.6.3/jquery-ui-timepicker-addon.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/vue/1.0.21/vue.js"></script>

@{ var dt = DateTime.Now; }

@section scripts
{

    <script>

        $('#div-calender').datetimepicker({
            timeFormat: "hh:mm tt"
@* }); *@

            el: '#tbody-slots',
            data: { List: [] },

                                                <td style="width:20%"> \
                                                    {{item.ID}} \
                                                </td> \
                                                <td style="width:20%"> \
                                                    {{item.DateStr}} \
                                                </td> \
                                                <td style="width:20%"> \
                                                    <button class="btn btn-primary" formaction="WebBooking/WebBooking/SelectSlot?slotID={{item.ID}}&Date=@dt.ToShortDateString()">Book</button> \
                                                </td> \
                                                </tr> \ ',

            methods: {

                LoadScheduleSlots: function () {

                    $.ajax({
                        type: "GET",
                        //url: "Schedule/Appointment/FindAvailableTimeSlot",
                        url: "WebBooking/WebBooking/GetScheduleSlots",
                        //data: { name : '',   dateFrom : '' ,  dateTo : ''  },
                        contentType: "application/json",
                        dataType: "json"

                    }).done(function (data) {

@* console.log(data.data); *@

@* }.bind(this)); *@

        })

        //var LoadScheduleSlots = function () {

        //    $.ajax({
        //        type: "GET",
        //        url: "Schedule/Appointment/FindAvailableTimeSlot",
        //        // data: { name : '',   dateFrom : '' ,  dateTo : ''  },
        //        contentType: "application/json",
        //        dataType: "json",
        //        success: function (data) {

        //            console.log(data);

        //            vm.$data.List = data;

        //        }

        //    });
        //}

        @if (@Model.LoadScheduleSlots)
        {

            @Html.Raw(methodName);
        }

    </script>

<style>
    ol li {
/* list-style: none; */

    .verticalAligned {
/* vertical-align: top; */

    #div-calender-container {
/* margin: 0 auto !important; */
@* text-align: center; *@

    .color-red {
@* color: red; *@

</style>

@using (Html.BeginForm("SelectOffice", "WebBooking", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { model = @Model }))
@using Cerebrum.ViewModels.Patient
{

    @Html.HiddenFor(x => x.PatientID)
    @Html.HiddenFor(x => x.LoadScheduleSlots)

    {
        @Html.HiddenFor(x => @Model.Offices[i].ID)
        @Html.HiddenFor(x => @Model.Offices[i].Name)
    }

    {
        @Html.HiddenFor(x => @Model.Tests[i].ID)
        @Html.HiddenFor(x => @Model.Tests[i].Name)

    }

    <div class="panel panel-info text-center ">
        <div class="panel-heading"><b>Select office and test to request an appointment date</b></div>
        <div class="panel-body ">
            <div class="text-center ">
                <div class="panel panel-default">
                    <div class="panel-heading"><b>Please select Office</b></div>
                    <div class="panel-body">

                        <div class="row">
                            <div>
                                @Html.DropDownListFor(m => m.Office.ID, new SelectList(@Model.Offices, "ID", "Name", 1), "--Select Office--", new { @class = "dropdown" })
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default ">
                    <div class="panel-heading"><b>Please, select requested tests (limit 2 tests per appointment)</b></div>
                    <div class="panel-body">

                        <div class="row">
                            <div class="form-group2">
                                <ol>
                                    @for (int i = 0; i < @Model.Tests.Count; i++)
                                    {
                                        <li class="col-xs-4 text-left">
                                            <span class="">@Html.CheckBoxFor(x => @Model.Tests[i].Selected)&nbsp;&nbsp;@Model.Tests[i].Name</span>
                                        </li>
                                    }
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
@* &nbsp; *@
                </div>

                <div class="row ">
                    <table width="100%">
                        <tr>

                            <td align="center">
                                <label>Selected Slot</label>
                                @Html.TextBoxFor(x => x.DateTimeSlot, new { @id = "div-calender" })
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="row">
@* &nbsp; *@
                </div>
                <div class="row">
@* &nbsp; *@
                </div>
                <div class="row">
                    <div class="col-md-12 text-center color-red ">
                        @foreach (var mssg in @Model.Messages)
                        {
                            <div>@mssg</div>
                        }
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-center">
                        <table class="table table-bordered table-striped table-bordered">
                            <thead>
                                <tr class="">
                                    <td style="width:20%"></td>
                                    <td style="width:20%"><b>Date/Time </b></td>
                                    <td style="width:20%">&nbsp;</td>
                                </tr>
                            </thead>
                            <tbody id="tbody-slots"></tbody>
                        </table>
                    </div>
                </div>

                <div class="row">
@* &nbsp; *@
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-primary">Request an Appointment</button>
                    </div>
                </div>

            </div>

        </div>
    </div>

