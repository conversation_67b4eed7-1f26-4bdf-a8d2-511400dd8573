@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum30.Areas.WebBooking.Models.ViewModels.Patient_Data_VM
@{

}
@* <script src="https://unpkg.com/vue/dist/vue.js"></script> *@ <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/1.0.21/vue.js"></script>

<style> .padded {
/* padding: 10px; */

    .scrollDiv {
/* overflow: auto; */
/* height: 300px; */

    /@* Start by setting display:none to make this hidden.*@
    Then we position it in relation to the viewport window
    with position:fixed. Width, height, top and left speak
    for themselves. Background we set to 80% white with
    our animation centered, and no-repeating @*/*@
    .modalCustom {
@* display: none; *@
@* position: fixed; *@
@* z-index: 1000; *@
@* top: 0; *@
@* left: 0; *@
@* height: 100%; *@
@* width: 100%; *@
@* background: rgba( 255, 255, 255, .8 ) url('http://i.stack.imgur.com/FhHRx.gif') 50% 50% no-repeat; *@

    /*When the body has the loading class, we turn
    the scrollbar off with overflow:hidden*/
    body.loading {
@* overflow: hidden; *@
    }

        /@*Anytime the body has the loading class, our*@
    modal element will be visible*/
        body.loading .modalCustom {
@* display: block; *@
        }
</style>

@section scripts
{

    <script>

        Vue.filter('dateInput', {
            read: function (value) {
                if (value.length > 0)
@* return value.slice(0, 10); *@
            },
            write: function (value, oldValue) {
@* return value; *@

@* }); *@

        $(document).ready(function () {
 
            $(document).on('click', '#btn-Add-Patient', function (e) {

@* e.preventDefault(); *@

                //url = "http://localhost:65062/WebBooking/WebBooking/Index";

                $("#div-modal-content").load(url, function () {

                    $("#modal-container").modal({
                        keyboard: false,
                        backdrop: 'static'
@* }, 'show'); *@
@* }); *@
                 
@* }); *@

            //vue components

                el: '#divData',
                data: { list: [] },

                                        <table class="table table-striped  table-hover"> \
                                            <tr v-for="item in list">\
                                                <td style="width:20%"> \
                                                    {{item.Name}} \
                                                </td> \
                                                <td style="width:20%"> \
                                                    {{item.DOBStr}}  \
                                                </td>  \
                                                <td style="width:20%"> \
                                                    {{item.AppointmentDateStr}} \
                                                </td> \
                                                <td style="width:20%"> \
                                                    {{item.TestName}} \
                                                </td> \
                                                <td><a href="#">Images</a></td> \
                                                <td><a href="#">Report</a></td> \
                                                <td><a href="#">Letter</a></td> \
                                                <td><a href="#">Raw</a></td> \
                                                 <td><a href="/WebBooking/WebBooking/SelectOffice?doctorID=@Model.DoctorID&patientID={{item.PatientID}}"  >Book</a></td> \
                                             </tr> \
                                      </table>     \
                                    <div>  \
                                  </div> \
                                </div>',
                methods: {

                    InitializeData: function () {

                        $.ajax({
                            type: "GET",
                            url: "WebBooking/WebBooking/GetPatientData",
                            contentType: "application/json",
                            dataType: "json",

                        })
                            .done(function (data) {

                            }.bind(this))

                    },

                    GetData: function (e, name, dateFrom, dateTo) {

@* e.preventDefault(); *@

                        if (name.length > 0 || dateFrom.length > 0 || dateTo.length > 0)

                        if (hasSearchParams) {

                            $.ajax({
                                type: "POST",
                                url: "WebBooking/WebBooking/GetPatientData",
                                data: { name: name, dateFrom: dateFrom, dateTo: dateTo },

                            }).done(function (data) {

                            }.bind(this))

                        else {

                            $.ajax({
                                type: "GET",
                                url: "WebBooking/WebBooking/GetPatientData",
                                contentType: "application/json",
                                dataType: "json"

                            })
                                .done(function (data) {

                                }.bind(this))

@* }); *@

                el: '#divSearch',
                data: {
                    vData: vData,
                    name: '',
                    dateFrom: new Date(),
                    dateTo: new Date(),
                }

@* }); *@

@* vData.InitializeData(); *@

            $(document).on({
                ajaxStart: function () { $body.addClass("loading"); },
                ajaxStop: function () { $body.removeClass("loading"); }
@* }); *@

@* $("#txtDateFrom").datepicker(); *@
@* $("#txtDateTo").datepicker(); *@
@* $("#dateOfBirth").datepicker(); *@

            //$.ajax({
            //    type: "GET",
            //    url: "WebBooking/WebBooking/GetPatientData",
            //    // data: { name : '',   dateFrom : '' ,  dateTo : ''  },
            //    contentType: "application/json",
            //    dataType: "json",
            //    success: function (data) {

            //        //loading data via handbars
            //        var source = $("#resultTemplate").html();
            //        var template = Handlebars.compile(source);

            //        var context = { list: data.List };
            //        var html = template(context);

            //$("#divData").html(html);

            //        //loading data via vue.js
            //         //v.$data.list = data.List;

            //        //console.log(data.Result);
            //    }
            //});

            //$("#hlSearch").click(function () {

            //    $.ajax({
            //        type: "POST",
            //        url: "WebBooking/WebBooking/GetPatientData",
            //        data: { name: $("#txtLName").val(), dateFrom: $("#txtDateFrom").val(), dateTo: $("#txtDateTo").val() },
            //        //contentType: "application/json",
            //        //dataType: "json",
            //        success: function (data) {

            //            var source = $("#resultTemplate").html();
            //            var template = Handlebars.compile(source);

            //            var context = { list: data.List };
            //            var html = template(context);

            //$("#divData").html(html);

            //            //console.log(data.Result);
            //        }
            //    });

            //    return false;
            //});

            //  $("#div-Add-Patient").load("WebBooking/WebBooking/_AddPatient");

            //$.ajax({
            //    type: "GET",
            //    url: "WebBooking/WebBooking/_AddPatient",
            //    // data: { name : '',   dateFrom : '' ,  dateTo : ''  },
            //    contentType: "application/json",
            //    //dataType: "json",
            //    success: function (data) {

            //        //loading data via handbars
            //        //var source = $("#resultTemplate").html();
            //        //var template = Handlebars.compile(source);

            //        //var context = { list: data.List };
            //        //var html = template(context);

            //        $("#divData").html(data);

            //        //loading data via vue.js
            //         //v.$data.list = data.List;

            //        console.log(data);
            //    }
            //});

@* }); *@

    </script>

        <div class="scrollDiv">
            <table class="table table-striped  table-hover">
                {{#each list}}
                <tr>
                    <td style="width:20%">
                        {{Name}}
                    </td>
                    <td style="width:20%">
                        {{DOBStr}}
                    </td>
                    <td style="width:20%">
                        {{AppointmentDateStr}}
                    </td>
                    <td style="width:20%">
                        {{TestName}}
                    </td>
                    <td><a href="#">Images</a></td>
                    <td><a href="#">Report</a></td>
                    <td><a href="#">Letter</a></td>
                    <td><a href="#">Raw</a></td>
                    <td><a  href="#" >Book</a></td>
                </tr>
                {{/each}}
            </table>
        </div>
    </script>*
<div class="text-center">

    @Html.HiddenFor(m => m.DoctorID)

        <div class="col-sm-12">
            <a class="btn btn-primary" href='@Url.Action("SelectOffice", new {  doctorID=@Model.DoctorID})'>Book Appointment</a>
        </div>
    </div>
    <div class="row">&nbsp;</div>

    <div>
        <div id="modal-container" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog " role="document" style="width:auto">
                <div class="modal-content">

                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title"></h4>
                        </div>
                    <div class="modal-body ">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <div id="div-modal-content"></div>
                    </div>

                        </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div><!-- /.modal -->
    </div>

    <button id="btn-Add-Patient" class="btn btn-primary" data-url="@Url.Action("_AddPatient", "WebBooking", new { area = "WebBooking" })">Add New Patient</button>

    <div class="row">&nbsp;</div>
@* *@

    <template id="divSearch">
        <div class="panel panel-default">
            <div class="panel-heading">
                <b>Enter Search Criteria</b>
            </div>
            <div class="panel-body">
                <div class="row ">
                    <div class="col-sm-4">
                        <label class="control-label">Name</label>
                        <input v-model="name" type="text" id="txtLName" />
                    </div>
                    <div class="col-sm-4">
                        <label class="control-label">
                            From Date
                        </label>
                        <input v-model="dateFrom | dateInput" type="text" id="txtDateFrom" />
                    </div>
                    <div class="col-sm-4">
                        <label class="control-label">
                            To Date
                        </label>
                        <input v-model="dateTo | dateInput " type="text" id="txtDateTo" />
                    </div>
                </div>
                <div class="row">&nbsp;</div>
                <div class="row">
                    <div class="col-sm-12 text-center">
                        <a href="#" class="btn btn-primary" v-on:click="vData.GetData($event,name ,dateFrom, dateTo)" id="hlSea">Search</a>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <div class="row">&nbsp;</div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <table class="table ">
                <tr>
                    <td style="width:20%">
                        <b>Patient</b>
                    </td>
                    <td style="width:20%">
                        <b>DOB</b>
                    </td>
                    <td style="width:20%">
                        <b>Test Date</b>
                    </td>
                    <td style="width:20%">
                        <b>Test</b>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                 </tr>
            </table>
        </div>
        <div class="panel-body">
            <div id="divData">
            </div>
        </div>
    </div>

    <div class="row">&nbsp;</div>

    <div class="modalCustom"></div>

</div>

