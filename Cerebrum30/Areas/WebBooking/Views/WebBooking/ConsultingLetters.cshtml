﻿
@model Cerebrum30.Areas.WebBooking.Models.ViewModels.Patient_Data_Letter_VM
@using Cerebrum.ViewModels.Patient
@{

}

<style>
    .row div {
/* padding: 10px; */

</style>

<script>

    $(document).ready(function () {

@* $("#txtDateFrom").datepicker(); *@
@* $("#txtDateTo").datepicker(); *@

@* }); *@

</script>

<div class="text-center">

    <div class="row">
        <div class="col-sm-2">
            <a href='@Url.Action("Index", new { doctorID=@Model.DoctorID})'>View Images</a>
        </div>
    </div>

    <div>
        <div class="row">
            <div class="col-sm-3">
                <input type="text" id="txtLName" />
            </div>
            <div class="col-sm-3">
                <input type="text" id="txtDateFrom" />
            </div>
            <div class="col-sm-3">
                <input type="text" id="txtDateTo" />
            </div>
            <div class="col-sm-3">
                <a href="#">Search</a>
            </div>
        </div>
    </div>

    <div class="row"></div>

    <div class="row">
        <div class="col-lg-12">
            <div class="col-sm-3">
                <b>Patient</b>
            </div>

            <div class="col-sm-2">
                <b>DOB</b>
            </div>
            <div class="col-sm-2">
                <b>Test Date</b>
            </div>
            <div class="col-sm-2">
                <b>Type</b>
            </div>
            <div class="col-sm-1">

            </div>
             
        </div>
    </div>

    <div style="overflow:auto; height:800px;">

        @foreach (var item in @Model.Data)
            {

            <div class="row">
                <div class="col-lg-12">
                    <div class="col-sm-3">
                        @item.Name
                    </div>

                    <div class="col-sm-2">
                        @item.DOB
                    </div>
                    <div class="col-sm-2">
                        @item.AppointmentDate
                    </div>
                    <div class="col-sm-2">
                        @item.AppointmentType
                    </div>
                    <div class="col-sm-1">
                        <a href="#">Show Letter</a>
                    </div>
                    
                </div>
            </div>

    </div>

</div>