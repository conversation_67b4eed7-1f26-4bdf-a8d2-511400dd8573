﻿@model   Cerebrum30.Models.DemographicsViewModel
@{

}

<link href="~/Content/forms.css" rel="stylesheet" />
<script src="~/Scripts/jquery-3.1.0.min.js"></script>
<script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>
<script src="~/Scripts/jquery.maskedinput.js"></script>
<link href="~/Content/themes/base/jquery-ui.css" rel="stylesheet" />
<link href="~/Content/themes/base/theme.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>
<script src="~/Scripts/bootstrap.js"></script>

<style>
    .flow_ {
/* margin-left: 5px; */
/* float: left; */
/* position: relative; */
    }

    .width_50 {
/* width: 50px; */

</style>

<script>

    $(function () {
        $('form').submit(function () {
            if ($(this).valid()) {
                $.ajax({
                    url: this.action,
                    type: this.method,
                    data: $(this).serialize(),
                    success: function (result) {

@* $('#div-result').html(result.Result); *@
@* console.log(result.Result); *@
                    }
@* }); *@

@* return false; *@
@* }); *@
@* }); *@

    $("#modal-container").on('hidden.bs.modal', function () {
@* document.location.reload(); *@
    })

</script>

<div id="mainDiv">

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    <div class="form-group">
        <div class="col-md-1">
            <div id="photoId" style="background-color:brown; height:60px; width:60px;">

            </div>
        </div>
        <div class="col-md-3">
            <h2>Demographics</h2>
        </div>
        <div class="col-md-8">
            @Html.LabelFor(m => m.ohipInfo, @Model.ohipInfo)
        </div>
    </div>

    <div id="div-result" class="alert">Result</div>

    <div class="row">&nbsp;</div>

    @using (Html.BeginForm("_AddPatientJSON", "WebBooking", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { demographicsViewModel = Model }))
@using Cerebrum.ViewModels.Patient
    {

        <div class="form-group" style="position:relative;">
            <div class="flow_" style="margin-left:15px; font-weight:700;">
                OHIP #
            </div>
            <div id="ohipDiv" class="flow_ width_50" style="margin-left:102px;">
                @Html.EditorFor(model => model.ohip, new { htmlAttributes = new { @class = "form-control backGreen", maxlength = "10", autocomplete = "off", tabindex = 1 } })
                @Html.ValidationMessageFor(model => model.ohip, "", new { @class = "text-danger" })
            </div>

            <div class="flow_">
                @Html.LabelFor(model => model.version)
            </div>

            <div class="flow_  width_50">
                @Html.EditorFor(model => model.version, new { htmlAttributes = new { @class = "form-control backGreen1", tabindex = 4 } })
            </div>
            <div class="flow_">
                @Html.LabelFor(model => model.skipOHIPCheck)
            </div>
            <div class="flow_">
                @Html.EditorFor(x => x.skipOHIPCheck, new { htmlAttributes = new { tabindex = 5 } })
            </div>
            <div class="flow_ ohipCall" style="margin-left:30px;">
                <input type="submit" value="Call OHIP" class="btn btn-default" style="width:100px; background-color:rgb(200, 206, 210);" />
            </div>
            <div class="flow_" style="font-weight:700;">
                Payment
            </div>
            <div class="flow_" style="width:100px;">
                @Html.CustomEnumDropDownListFor(model => model.payment, htmlAttributes: new { @class = "form-control", tabindex = 29 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.lastName)
            </div>
            <div class="col-md-4 rem_right_padd">
                @Html.EditorFor(model => model.lastName, new { htmlAttributes = new { @class = "form-control backGreen", tabindex = 6 } })
                @Html.ValidationMessageFor(model => model.lastName, "", new { @class = "text-danger" })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.mainDoctor)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-8 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.mainDoctor, new { htmlAttributes = new { @class = "form-control", tabindex = 30 } })
                        @Html.HiddenFor(model => model.mainDoctorHid)
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="mainDocEnrlEdit" class="newBtDiv"><span class="newBut">Edit</span></div>
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="mainDocNew" class="newBtDiv">
                            <span class="newBut removeAncorLine">
                                @Html.ActionLink("New", "Index", "AdminUser/AdminUsers", new { }, new { target = "_blank", id = "mrp_link", @class = @Model.isAdminRole })
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.namePrefix)
            </div>
            <div class="col-md-4">
                @Html.CustomEnumDropDownListFor(model => model.namePrefix, htmlAttributes: new { @class = "form-control salutation", tabindex = 7 })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.famDoctor)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-10 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.famDoctor, new { htmlAttributes = new { @class = "form-control backGreen", tabindex = 31 } })
                        @Html.HiddenFor(model => model.famDoctorHid)
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="famDocNew" class="newBtDiv"><span class="newBut">New</span></div>
                    </div>
                </div>
            </div>
        </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.firstName)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.firstName, new { htmlAttributes = new { @class = "form-control", tabindex = 8 } })
                        </div>
                        <div class="col-md-3 rem_right_padd rem_left_padd">
                            @Html.LabelFor(model => model.middleName)
                        </div>
                        <div class="col-md-4 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.middleName, new { htmlAttributes = new { @class = "form-control", tabindex = 9 } })
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.refDoctor)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-10 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.refDoctor, new { htmlAttributes = new { @class = "form-control", tabindex = 32 } })
                            @Html.HiddenFor(model => model.refDoctorHid)
                        </div>
                        <div class="col-md-2 rem_right_padd rem_left_padd">
                            <div id="refDocNew" class="newBtDiv"><span class="newBut">New</span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.dateOfBirth)
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.EditorFor(model => model.dateOfBirth, new { htmlAttributes = new { @class = "form-control backGreen", tabindex = 10 } })
                    @Html.ValidationMessageFor(model => model.dateOfBirth, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-1 rem_right_padd">
                    @Html.LabelFor(model => model.gender)
                </div>
                <div class="col-md-1 rem_right_padd">
                    @Html.CustomEnumDropDownListFor(model => model.gender, htmlAttributes: new { @class = "form-control", tabindex = 11 })
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.assosDoctor)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-10 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.assosDoctor, new { htmlAttributes = new { @class = "form-control", tabindex = 33 } })
                            @Html.HiddenFor(model => model.assosDoctorHid)
                        </div>
                        <div class="col-md-2 rem_right_padd rem_left_padd">
                            <div id="assosDocNew" class="newBtDiv"><span class="newBut">New</span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.phone)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.phone, new { htmlAttributes = new { @class = "form-control", tabindex = 12 } })
                        </div>
                        <div class="col-md-2 rem_right_padd">
                            @Html.LabelFor(model => model.extentionPhone)
                        </div>
                        <div class="col-md-5 rem_right_padd">
                            @Html.EditorFor(model => model.extentionPhone, new { htmlAttributes = new { @class = "form-control", tabindex = 13 } })
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.issueDate)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-4 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.issueDate, new { htmlAttributes = new { @class = "form-control", tabindex = 34 } })
                        </div>
                        <div class="col-md-4 rem_right_padd">
                            @Html.LabelFor(model => model.validDate)
                        </div>
                        <div class="col-md-4 rem_right_padd">
                            @Html.EditorFor(model => model.validDate, new { htmlAttributes = new { @class = "form-control", tabindex = 35 } })
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.phoneContact)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.phoneContact, new { htmlAttributes = new { @class = "form-control", tabindex = 14 } })
                        </div>
                        <div class="col-md-2 rem_right_padd">
                            @Html.LabelFor(model => model.extentionContact)
                        </div>
                        <div class="col-md-5 rem_right_padd">
                            @Html.EditorFor(model => model.extentionContact, new { htmlAttributes = new { @class = "form-control", tabindex = 15 } })
                            @Html.ValidationMessageFor(model => model.extentionContact, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.expairyDate)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-4 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.expairyDate, new { htmlAttributes = new { @class = "form-control", tabindex = 36 } })
                        </div>
                        <div class="col-md-2 ohipCall">
                        </div>
                        <div class="col-md-6 ohipCall rem_left_padd rem_right_padd">
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.faxPharmacy)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.faxPharmacy, new { htmlAttributes = new { @class = "form-control", tabindex = 16 } })
                        </div>
                        <div class="col-md-2 rem_right_padd">
                            @Html.LabelFor(model => model.cell)
                        </div>
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.cell, new { htmlAttributes = new { @class = "form-control", tabindex = 17 } })
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.ConsentEmail)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-4 rem_right_padd rem_left_padd">
                            @Html.CustomEnumDropDownListFor(model => model.ConsentEmail, htmlAttributes: new { @class = "form-control", tabindex = 37 })
                        </div>
                        <div class="col-md-3 rem_right_padd">
                            @Html.LabelFor(model => model.email)
                        </div>
                        <div class="col-md-5 rem_right_padd">
                            @Html.EditorFor(model => model.email, new { htmlAttributes = new { @class = "form-control", tabindex = 38 } })
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.address)
                </div>
                <div class="col-md-4 rem_right_padd">
                    @Html.EditorFor(model => model.address, new { htmlAttributes = new { @class = "form-control", tabindex = 18 } })
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.kin)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.EditorFor(model => model.kin, new { htmlAttributes = new { @class = "form-control", tabindex = 39 } })
                        </div>
                        <div class="col-md-1 rem_right_padd rem_left_padd">
                            @Html.LabelFor(model => model.kin_phone)
                        </div>
                        <div class="col-md-6 rem_right_padd rem_left_padd">
                            <div class="form-group">
                                <div class="col-md-9 rem_right_padd rem_left_padd">
                                    @Html.EditorFor(model => model.kin_phone, new { htmlAttributes = new { @class = "form-control", tabindex = 40 } })
                                </div>
                                <div class="col-md-3 rem_right_padd rem_left_padd">
                                    <div id="nxtkDid" class="newBtDiv"><span class="newBut">Add</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.city)
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.EditorFor(model => model.city, new { htmlAttributes = new { @class = "form-control", tabindex = 19 } })
                </div>
                <div class="col-md-1 rem_right_padd paddL3">
                    @Html.LabelFor(model => model.province)
                </div>
                <div class="col-md-1 rem_left_padd rem_right_padd">
                    @Html.CustomEnumDropDownListFor(model => model.province, htmlAttributes: new { @class = "form-control", tabindex = 20 })
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.active)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.CustomEnumDropDownListFor(model => model.active, htmlAttributes: new { @class = "form-control", tabindex = 41 })
                        </div>
                        <div class="col-md-2 rem_right_padd rem_left_padd">
                        </div>
                        <div class="col-md-5 rem_right_padd">
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.postalCode)
                </div>
                <div class="col-md-1 rem_right_padd">
                    @Html.EditorFor(model => model.postalCode, new { htmlAttributes = new { @class = "form-control", tabindex = 21 } })
                </div>
                <div class="col-md-1 rem_right_padd">
                    @Html.LabelFor(model => model.country)
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.CustomEnumDropDownListFor(model => model.country, htmlAttributes: new { @class = "form-control", tabindex = 22 })
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.PreferedLanguage)
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div class="form-group">
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.CustomEnumDropDownListFor(model => model.PreferedLanguage, htmlAttributes: new { @class = "form-control", tabindex = 42 })
                        </div>
                        <div class="col-md-5 rem_right_padd rem_left_padd">
                            @Html.LabelFor(model => model.PreferredOfficialLanguageSpecified)
                        </div>
                        <div class="col-md-2 rem_right_padd">
                            @Html.EditorFor(x => x.PreferredOfficialLanguageSpecified, new { htmlAttributes = new { tabindex = 43 } })
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.mrnHospital)
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.DropDownListFor(model => model.SelectedHospitalId, htmlAttributes: new { @class = "form-control", tabindex = 23 })
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.EditorFor(model => model.mrnCode, new { htmlAttributes = new { @class = "form-control", @placeholder = "MRN", tabindex = 24 } })
                </div>
                <div class="col-md-2">
                    @Html.LabelFor(model => model.notes)
                </div>
                <div class="col-md-4 rem_right_padd">
                    @Html.TextAreaFor(x => x.notes, 20, 15, new { @class = "form-control text-box multy-line txtHeight", tabindex = 44 })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.RMB)
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.EditorFor(model => model.RMB, new { htmlAttributes = new { @class = "form-control backGreen", @placeholder = "MRB", tabindex = 25 } })
                </div>
                <div class="col-md-2 rem_right_padd">
                </div>
                <div class="col-md-2">
                    <div id="cohortP_Id" class="newBtDiv" style="width:70px;"><span class="newBut">Add Cohort</span></div>
                </div>
                <div class="col-md-4 rem_right_padd">
                    <div id="ss" class="scroll_div ph_provider_1 checkboxes">
                        @if (@Model.addedCohorts != null && @Model.addedCohorts.Count > 0)
                        {
                            foreach (var item in @Model.addedCohorts)
                            {
                                <label><input checked class='sss_check' type='checkbox' id=@item.value /> <span> @item.text </span></label>
                            }
                        }
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.insurance)
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.CustomEnumDropDownListFor(model => model.insurance, htmlAttributes: new { @class = "form-control", tabindex = 26 })
                </div>
                <div class="col-md-2 rem_right_padd">
                    @Html.DropDownListFor(model => model.SelectedInsurCompanyId, htmlAttributes: new { @class = "form-control", tabindex = 27 })
                </div>
                <div class="col-md-2">
                </div>
                <div class="col-md-4 rem_right_padd">*@<div class="form-group">
                            <div class="col-md-4 rem_right_padd rem_left_padd">
                                @Html.CustomEnumDropDownListFor(model => model.ConsentEmail, htmlAttributes: new { @class = "form-control", tabindex = 20 })
                            </div>
                            <div class="col-md-3 rem_right_padd">
                                @Html.LabelFor(model => model.email)
                            </div>
                            <div class="col-md-5 rem_right_padd">
                                @Html.EditorFor(model => model.email, new { htmlAttributes = new { @class = "form-control", tabindex = 21 } }) </div>* </div>
                </div>
            </div>

            <div class="form-group footer_ margV1">
                <div class="col-md-offset-2 col-md-4">
                    <input type="submit" value="Save" class="btn btn-default" @(@Model.save_disable == true ? "disabled" : "") />
                </div>
                <div class="col-md-offset-2 col-md-4">
                </div>
            </div>

            @Html.HiddenFor(x => x.practiceId)
            @Html.HiddenFor(x => x.Id)
            @Html.HiddenFor(model => model.nxk_contactPurpose)
            @Html.HiddenFor(model => model.nxk_firstName)
            @Html.HiddenFor(model => model.nxk_middleName)
            @Html.HiddenFor(model => model.nxk_lastName)
            @Html.HiddenFor(model => model.nxk_emailAddress)
            @Html.HiddenFor(model => model.nxk_notes)
            @Html.HiddenFor(model => model.nxk_phone)
            @Html.HiddenFor(model => model.ed_enrolled)
            @Html.HiddenFor(model => model.ed_enrollmentStatusSpecified)
            @Html.HiddenFor(model => model.ed_enrollmentDate)
            @Html.HiddenFor(model => model.ed_enrollmentDateSpecified)
            @Html.HiddenFor(model => model.ed_enrollmentTerminationDate)

            @Html.HiddenFor(model => model.ed_enrollmentTerminationDateSpecified)
            @Html.HiddenFor(model => model.ed_terminationReason)
            @Html.HiddenFor(model => model.ed_terminationReasonSpecified)
            @Html.HiddenFor(x => x.ext_addressLine1)
            @Html.HiddenFor(x => x.ext_faxNumber)
            @Html.HiddenFor(x => x.ext_city)
            @Html.HiddenFor(x => x.ext_postalCode)
            @Html.HiddenFor(x => x.ext_province)
            @Html.HiddenFor(x => x.ext_country)

</div>

