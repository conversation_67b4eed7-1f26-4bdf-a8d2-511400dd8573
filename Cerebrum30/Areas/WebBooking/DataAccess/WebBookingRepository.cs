﻿using Cerebrum3.Infrastructure;
using Cerebrum30.Areas.WebBooking.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum.Data;
using Cerebrum30.Utility;
namespace Cerebrum30.Areas.WebBooking.DataAccess
{
    public class WebBookingRepository : GenericRepository<CerebrumContext, VPMeasurement>, IWebBookingRepository
    {


        public List<Patient_Data> GetPatientData(int doctorID)
        {
            List<Patient_Data> vm = new List<Patient_Data>();

            //TODO, we wont see patients that have NOT an appt. So a newly added patient will not show up here

            vm = (from
                  p in context.PatientRecords join d in context.Demographics on p.Id equals d.PatientRecordId
                  join a in context.Appointments on p.Id equals a.PatientRecordId 
                  into third from x in third.DefaultIfEmpty()
                  join at in context.AppointmentTests on x.Id equals at.AppointmentId
                  join ty in context.AppointmentTypes on x.AppointmentTypeId equals ty.Id
                  join t in context.Tests on at.TestId equals t.Id
                  where x.referralDoctorId == doctorID
                  orderby d.lastName, x.appointmentTime
                  select new Patient_Data
                  {
                      AppointmentID = x.Id,
                      Name = d.lastName + " " + d.firstName,
                      AppointmentDate = x.appointmentTime,
                      TestName = t.testFullName,
                      DOB = d.dateOfBirth.Value,
                      DOBStr = d.dateOfBirth.Value.ToString(),
                      AppointmentDateStr = x.appointmentTime.ToString(),
                      TestID = t.Id,
                      AppointmentType = ty.name,
                      PatientID =  p.Id

                  }).ToList();

            

            return vm;
        }
        public List<Patient_Data> GetPatientData(int doctorID, string name,  DateTime? dtFrom, DateTime? dtTo)
        {
            List<Patient_Data> vm = new List<Patient_Data>();

            vm = (from at in context.AppointmentTests
                  join a in context.Appointments on at.AppointmentId equals a.Id
                  join ty in context.AppointmentTypes on a.AppointmentTypeId equals ty.Id
                  join t in context.Tests on at.TestId equals t.Id
                  join p in context.PatientRecords on a.PatientRecordId equals p.Id
                  join d in context.Demographics on p.Id equals d.PatientRecordId
                  where a.referralDoctorId == doctorID  &&
                  (!string.IsNullOrEmpty(name) ?
                  (d.lastName.Contains(name) || d.firstName.Contains(name) ||
                  (d.lastName + " " + d.firstName).Contains(name) ||
                  (d.firstName + " " + d.lastName).Contains(name)
                  ) : 1 == 1) &&
                  ( dtFrom.HasValue && !dtTo.HasValue ? (a.appointmentTime >= dtFrom ) : 1 == 1) &&
                  (!dtFrom.HasValue &&  dtTo.HasValue ? (a.appointmentTime <= dtTo) : 1 == 1) &&
                  ( dtFrom.HasValue && dtTo.HasValue ? (a.appointmentTime >= dtFrom && a.appointmentTime <= dtTo)  : 1==1 ) 
                  orderby d.lastName, a.appointmentTime
                  select new Patient_Data
                  {
                      AppointmentID = a.Id,
                      Name = d.lastName + " " + d.firstName,
                      AppointmentDate = a.appointmentTime,
                      TestName = t.testFullName,
                      DOB = d.dateOfBirth.Value,
                      DOBStr = d.dateOfBirth.Value.ToString(),
                      AppointmentDateStr = a.appointmentTime.ToString(),
                      TestID = t.Id,
                      AppointmentType = ty.name,
                      PatientID = p.Id

                  }).ToList();

            return vm;
        }
        public List<Patient_Data_Letter> GetPatientDataLetters(int doctorID)
        {
            List<Patient_Data_Letter> vm = new List<Patient_Data_Letter>();

            vm = (from a in context.Appointments
                  join p in context.PatientRecords on a.PatientRecordId equals p.Id
                  join t in context.AppointmentTypes on a.AppointmentTypeId equals t.Id
                  join d in context.Demographics on p.Id equals d.PatientRecordId
                  where a.referralDoctorId == doctorID
                  orderby d.lastName, a.appointmentTime
                  select new Patient_Data_Letter
                  {
                      AppointmentID = a.Id,
                      Name = d.lastName + " " + d.firstName,
                      DOB = d.dateOfBirth.Value,
                      AppointmentDate = a.appointmentTime,
                      AppointmentType = t.name

                  }).ToList();

            return vm;
        }
        public void Associate_Doc_User(int userID, int docID)
        {

            var entry = context.WebBookingDoctor.Where(x => x.UserID == userID && x.DoctorID == docID).FirstOrDefault();
            if(entry!= null)
            {
                context.WebBookingDoctor.Remove(entry);
                context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            }

            context.WebBookingDoctor.Add(new WebBookingDoctor()
            {
                DoctorID = docID,
                UserID = userID
            });

            context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
        }
        public int GetDoctorIDByUserID(int userID)
        {
            int retVal = -1;

            var entry = context.WebBookingDoctor.Where(x => x.UserID == userID).FirstOrDefault();

            if (entry != null)
            {
                retVal = entry.DoctorID;
            }

            return retVal;
        }
        public List<SelectOffice_Office_VM> GetOfficesForBooking(int PracticeId)
        {

            List<SelectOffice_Office_VM> retVal = new List<SelectOffice_Office_VM>();

            retVal = context.Offices.Where(o => o.PracticeId == PracticeId).Select(o => new SelectOffice_Office_VM()
                    {
                        ID = o.Id,
                        Name = o.name
                    }).ToList();


            return retVal;
        }
        public List<SelectOffice_Test_VM> GetTestsForBooking(int PracticeId)
        {
            List<SelectOffice_Test_VM> retVal = new List<SelectOffice_Test_VM>();

            retVal = (from t in context.Tests
                      join
                      p in context.PracticeTests on t.Id equals p.TestId
                      where p.PracticeId == PracticeId
                      select new SelectOffice_Test_VM()
                      {

                          ID = t.Id,
                          Name = t.testFullName,
                      }).ToList();

            return retVal;

        }

        public string GetOfficeName(int officeID)
        {
            string retVal = string.Empty;

            var office = context.Offices.Where(o => o.Id == officeID).FirstOrDefault();

            if (office != null)
            {
                retVal = office.name;
            }

            return retVal;
        }
    }
}
