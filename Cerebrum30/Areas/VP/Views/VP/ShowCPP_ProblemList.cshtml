﻿@model Cerebrum.ViewModels.VP.VMCPPProblem

<link href="~/Content/CPP_Styles.css" rel="stylesheet" />
@section scripts {
    <script>

            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
@* $("#div-pat-name").html(data); *@
@* }); *@
@* }; *@

        function clear_form_elements(class_name) {
            jQuery(class_name).find(':input').each(function() {
                switch(this.type) {
                    case 'password':
                    case 'text':
                    case 'textarea':
                    case 'file':
                    case 'select-one':
                    case 'select-multiple':
                    case 'date':
                    case 'number':
                    case 'tel':
                    case 'email':
@* jQuery(this).val(''); *@
@* break; *@
                    case 'checkbox':
                    case 'radio':

@* break; *@

@* }); *@

        $(document).ready(function () {
            $('.hl-ic').popover({
                trigger: 'manual',
                html: true,
                placement: 'auto right'
            }).click(function (e) {
@* $('#ajax-loader').show(); *@
@* e.preventDefault(); *@

                $.ajax({
                    //type: 'POST',
                    url: url,
                    //data: data,
                    success: function (data) {
@* box.attr('data-content', data).popover('show'); *@
                    },
                    error: function (xhr, thrownError) {
@* alert("Error while tryng to call  'VP/VP/IC10'  " + xhr.status + " " + thrownError); *@
                    },
                    complete: function () {
@* $('#ajax-loader').hide(); *@
                    }
@* }); *@
@* }); *@

@* LoadPatientName(); *@

            //$('#btnAdd').click(function () {
            //    $('#divGrd').hide();
            //    $('#divAdd').show();
            //    return false;
            //});

             $(document).on('click', '#hl-search-ic', function (e) {
@* e.preventDefault(); *@

                 $.ajax({
                     type: 'POST',
                     url: url,
                     data: $('#frm-ic-search').serialize(),
                     success: function (data) {
@* $("#div-ic-data").html(data); *@
                     }
@* }); *@
@* }); *@

            $(document).on('click', '.btn-popover-close', function (e) {
@* e.preventDefault(); *@
@* $(".popover-btn").popover('hide'); *@
@* $('[data-original-title]').popover('hide'); *@
@* }); *@

            $(document).on('click','.btn-save-edit-problem', function (e) {
@* e.preventDefault(); *@

                ajaxCall(url, $('#frm-problemlist').serialize(), false, function (data) {

@* $("#span-result-edit").html(''); *@
@* $("#span-result-edit").html(data.Message); *@
                    }
                    else{

@* $("#span-result-edit").html(''); *@
@* $("#span-result-edit").html('Changes Saved'); *@
                        // clear_form_elements('#divEdit');

@* opener.LoadCPP(); *@

@* }); *@
@* }); *@

            $(document).on('click','.btn-save-add', function (e) {
@* e.preventDefault(); *@

                ajaxCall(url, $('#frm-problemlist').serialize(), false, function (data) {

@* $("#span-result-add").html(''); *@
@* $("#span-result-add").html(data.Message); *@
                    }
                    else{

@* $("#span-result-add").html(''); *@
@* $("#span-result-add").html('Changes Saved'); *@
@* clear_form_elements('#divAdd'); *@
@* opener.LoadCPP(); *@

@* }); *@
@* }); *@

            $('#VP_CPP_Problem_List_VM_Add_Units').on('change', function () {
                //console.log($('#FamilyHistory_Add_AgeOnset').val());
                //console.log('---------------------------');
                //console.log($('#FamilyHistory_Add_Unit').val());

                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Add_Years').val() , option: $('#VP_CPP_Problem_List_VM_Add_Units').val()},
                    success: function (data) {

                        {
@* $('#VP_CPP_Problem_List_VM_Add_Life_Stage').val(data.Message); *@
                        }
                    },
                    error: function (xhr, thrownError) {

@* alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError); *@

@* }); *@
@* }); *@

            $('#VP_CPP_Problem_List_VM_Add_Years').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Add_Years').val() , option: $('#VP_CPP_Problem_List_VM_Add_Units').val()},
                    success: function (data) {

                        {

@* $('#VP_CPP_Problem_List_VM_Add_Life_Stage').val(data.Message); *@
                        }
                    },
                    error: function (xhr, thrownError) {
@* alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError); *@

@* }); *@

@* }); *@

            $('#VP_CPP_Problem_List_VM_Edit_Units').on('change', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Edit_Years').val() , option: $('#VP_CPP_Problem_List_VM_Edit_Units').val()},
                    success: function (data) {

                        {
@* $('#VP_CPP_Problem_List_VM_Edit_Life_Stage').val(data.Message); *@
                        }
                    },
                    error: function (xhr, thrownError) {

@* alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError); *@

@* }); *@
@* }); *@

            $('#VP_CPP_Problem_List_VM_Edit_Years').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Edit_Years').val() , option: $('#VP_CPP_Problem_List_VM_Edit_Units').val()},
                    success: function (data) {

                        {

@* $('#VP_CPP_Problem_List_VM_Edit_Life_Stage').val(data.Message); *@
                        }
                    },
                    error: function (xhr, thrownError) {

@* alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError); *@

@* }); *@

@* }); *@

            //console.log(noError);

            {
@* $('#divGrd').hide(); *@
@* $('#divAdd').show(); *@
@* $('#divEdit').hide(); *@
            }

            //console.log(noError);

            {
@* $('#divGrd').hide(); *@
@* $('#divAdd').hide(); *@
@* $('#divEdit').show(); *@
            }

            {
@* $('#divGrd').hide(); *@
@* $('#divAdd').hide(); *@
@* $('#divEdit').show(); *@
            }

@* $("#VP_CPP_Problem_List_VM_Add_DateOfOnset").datepicker(); *@
@* $("#VP_CPP_Problem_List_VM_Add_ResolutionDate").datepicker(); *@
@* $("#VP_CPP_Problem_List_VM_Add_ProcDate").datepicker(); *@

@* $("#VP_CPP_Problem_List_VM_Edit_DateOfOnset").datepicker(); *@
@* $("#VP_CPP_Problem_List_VM_Edit_ResolutionDate").datepicker(); *@
@* $("#VP_CPP_Problem_List_VM_Edit_ProcDate").datepicker(); *@

            $('.hl-delete')
                          .popover({
                              trigger: 'manual',
                              html: true,
                              placement: 'auto right'
                          })

                      .click(function(e){

@* $('#ajax-loader').show(); *@

@* e.preventDefault(); *@

                          ajaxCall(url, data, false, function (data) {
@* box.attr('data-content',data).popover('show'); *@
@* }); *@
@* }); *@

            $(document).on('click', '.btn-popover-close', function (e) {

@* e.preventDefault(); *@
@* $(".popover-btn").popover('hide'); *@
@* $('[data-original-title]').popover('hide'); *@
@* }); *@

@* }); *@

        @Model.ScriptToExecute

    </script>

<div id="div-pat-name"></div>

<div class="row riskFactor">
 *@<div id="div-pat-name"></div>
    @*</div>*@

        <h4 class="label label-primary pull-right" style="font-size:18px;">Problem List/Past Health</h4>@* *@
    </div>
</div>

<div class="text-center">
    @using (Html.BeginForm("ShowCPP_ProblemList", "VP", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-problemlist", model = @Model  }))
@using Cerebrum.ViewModels.Patient
    {
        @Html.HiddenFor(x => x.PatientID)
        @Html.HiddenFor( x => x.IsProblemList)
        <div id="divGrd" style="padding-top:30px;">
            <div class="row">
                <div class="col-sm-12">
                    <button id="btnAdd" class="btn btn-default btn-primary float_l">Add New</button>
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-sm-12">
                    <div class="pre-scrollable">
                        <table class="table table table-striped table-bordered table-condensed  ">
                            <tr>
                                <td class="spanCell"><span class="span"><b>Diagnosis</b></span></td>
                                 
                                <td class="spanCell"><span class="span"><b>Problem Status</b></span></td>
                                 
                                <td class="spanCell"><span class="span"><b>Onset Date</b></span></td>
                                
                                <td class="spanCell"><span class="span"><b>Problem Description</b></span></td>
                                
                                <td class="spanCell"><span class="span"><b>Procedure</b></span></td>
                                 
                                <td class="spanCell"><span class="span"><b>Life Stage</b></span></td>

                                <td class="spanCell"><span class="span"><b>Notes</b></span></td>
                                <td class="spanCell"><span class="span"><b>Added</b></span></td>
                                
                            </tr>
                            @for (int i = 0; i < @Model.VP_CPP_Problem_List_VM.Count; i++)
                            {

                                @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM[i].Id)
                                <tr>
                                    <td>

                                    </td>
                                    <td class="spanCell">
                                        @if (@Model.VP_CPP_Problem_List_VM[i].Problem_Status == 1)
                                        {  <span>Problem List</span> }
                                        else
                                        { <span>Past Health</span>}
                                    </td>
                                    <td>
                                        @(@Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Year > 0 ? @Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Year + " /" : "" )
                                        @(@Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Month > 0 ? @Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Month + " /" : "" )
                                        @(@Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Day > 0 ? @Model.VP_CPP_Problem_List_VM[i].DateOfOnset_Day + " /" : "" )
                                        @ Html.TextBoxFor(model => model.VP_CPP_Problem_List_VM[i].DateOfOnset, new { @class = "txtBox" })
                                    </td>
                                    <td>
                                        @Html.TextAreaFor(model => model.VP_CPP_Problem_List_VM[i].Problem_Description, new { @class = "txtArea" })
                                    </td>
                                    
                                    <td>
                                        @Html.TextAreaFor(model => model.VP_CPP_Problem_List_VM[i].Proc_Interv, new { @class = "txtArea" })
                                    </td>
                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_CPP_Problem_List_VM[i].Life_Stage)
                                        @Model.VP_CPP_Problem_List_VM[i].Years
                                        @Model.VP_CPP_Problem_List_VM[i].Units
                                    </td>
                                    <td class="spanCell">
                                        @Html.TextAreaFor(model => model.VP_CPP_Problem_List_VM[i].Notes, new { @class = "txtArea" })
                                    </td>
                                    <td class="spanCell" >
                                        @Html.DisplayFor(model => model.VP_CPP_Problem_List_VM[i].AddDate)
                                    </td>
                                    <td>
                                        @Html.ActionLink("Edit", "Edit_CPP_Problem", new { EntryID = @Model.VP_CPP_Problem_List_VM[i].Id, patientID = @Model.PatientID,   IsProblemList = @Model.IsProblemList })
                                    </td>
                                    <td>
                                        <a href="#" id="hl-del-@Model.VP_CPP_Problem_List_VM[i].Id" data-name="hl-del-@Model.VP_CPP_Problem_List_VM[i].Id" class="hl-delete" data-patientid="@Model.PatientID" data-cpptype="3" data-rowid="@Model.VP_CPP_Problem_List_VM[i].Id">Delete</a>
                                    </td>
                                </tr>

                        </table>
                    </div>
                </div>
            </div>
        </div>

            <div class="container topPadding" id="divAdd" style='display:none'>
                <div class="row">
                    <div class="col-md-12 text-center">

                        @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Add.PatientRecordId)

                        <div class="rowContainer">

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">*@ Diagnosis <a id="hl-ic"

                                </div>
                                <div class="col-sm-8 text-left">

                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Problem Description :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Add.Problem_Description, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Life Stage :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Add.Years, new { @class = "largetxtBox" })
                                    @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.Units, @Model.Units, "--Select--")
                                    @Html.TextBoxFor(x => x.VP_CPP_Problem_List_VM_Add.Life_Stage)
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Problem Status :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.DropDownListFor(m => m.VP_CPP_Problem_List_VM_Add.Problem_Status, @Model.ActiveList, "--Select--")
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Position in Summary :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Add.Position, new { @class = "largetxtBox" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Procedure /Intervention :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Add.Proc_Interv, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Notes :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Add.Notes, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Date of Onset :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @ Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Add.DateOfOnset, new { @class = "largetxtBox", @readonly = "readonly" })
                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2 ">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.DateOfOnset_Day, @Model.Days, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.DateOfOnset_Month, @Model.Months, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.DateOfOnset_Year, @Model.Years, "--Select--")
                                    </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Resolution Date :
                                </div>
                                <div class="col-sm-8 text-left">

                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.ResolutionDate_Day, @Model.Days, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.ResolutionDate_Month, @Model.Months, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.ResolutionDate_Year, @Model.Years, "--Select--")
                                    </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Procedure Date :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @ Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Add.ProcDate, new { @class = "largetxtBox", @readonly = "readonly" })
                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.ProcDate_Day, @Model.Days, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.ProcDate_Month, @Model.Months, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Add.ProcDate_Year, @Model.Years, "--Select--")
                                    </div>

                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="col-sm-12 ">
                                    <span class="redError" id="span-result-add">
                                        @Html.Raw(@Model.Add_ErrorMessage.Replace("\n", "<br>"))
                                    </span>
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-6 text-right">
                                    <input type="submit" value="Save" class="btn btn-default btn-save-add" />

                                </div>
                                <div class="col-sm-6 text-left">
                                    @Html.ActionLink("Cancel", "Cancel_CPP_ProblemList", "VP", new { area = "VP", patientID = @Model.PatientID, IsProblemList = @Model.IsProblemList }, new { @class = "btn btn-default" })
                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="col-sm-12 ">
                                    @Html.ActionLink("Done", "Cancel_CPP_ProblemList", "VP", new { area = "VP", patientID = @Model.PatientID, IsProblemList = @Model.IsProblemList }, new { @class = "btn btn-primary" })
                                </div>
                            </div>
                        </div>
                        <br />
                    </div>
                </div>
            </div>
            <div class="container topPadding" id="divEdit" style='display:none'>
                <div class="row">
                    <div class="col-md-12 text-center">
                        @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Edit.PatientRecordId)
                        @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Edit.Id)
                        <div class="rowContainer">
                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Diagnosis 
                                    <a id="hl-ic"  

                                </div>
                                <div class="col-sm-8 text-left">

                                </div>
                            </div>
                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Problem Description :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Problem_Description, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Life Stage :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.Years, new { @class = "largetxtBox" })
                                    @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.Units, @Model.Units, "--Select--")
                                    @Html.TextBoxFor(x => x.VP_CPP_Problem_List_VM_Edit.Life_Stage)
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Problem Status :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.DropDownListFor(m => m.VP_CPP_Problem_List_VM_Edit.Problem_Status, @Model.ActiveList, "--Select--")
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Position in Summary :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.Position, new { @class = "largetxtBox" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Procedure /Intervention :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Proc_Interv, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Notes :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Notes, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Date of Onset :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @ Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.DateOfOnset, new { @class = "largetxtBox", @readonly = "readonly" })
                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2 ">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Day, @Model.Days, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Month, @Model.Months, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Year, @Model.Years, "--Select--")
                                    </div>

                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Resolution Date :
                                </div>
                                <div class="col-sm-8 text-left">

                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Day, @Model.Days, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Month, @Model.Months, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Year, @Model.Years, "--Select--")
                                    </div>

                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Procedure Date :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @ Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.ProcDate, new { @class = "largetxtBox", @readonly = "readonly" })
                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Day, @Model.Days, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Month, @Model.Months, "--Select--")
                                    </div>
                                    <div class="col-sm-1 spanCell ">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Year, @Model.Years, "--Select--")
                                    </div>

                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="col-sm-12 ">
                                    <span class="redError" id="span-result-edit">
                                        @Html.Raw(@Model.Add_ErrorMessage.Replace("\n", "<br>"))
                                    </span>
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-6 text-right">
                                    <input type="submit" value="Save" class="btn btn-default btn-save-edit" />

                                </div>
                                <div class="col-sm-6 text-left">
                                    @Html.ActionLink("Cancel", "Cancel_CPP_ProblemList", "VP", new { area = "VP", patientID = @Model.PatientID, IsProblemList = @Model.IsProblemList }, new { @class = "btn btn-default" })
                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="col-sm-12 ">
                                    @Html.ActionLink("Done", "Cancel_CPP_ProblemList", "VP", new { area = "VP", patientID = @Model.PatientID, IsProblemList = @Model.IsProblemList }, new { @class = "btn btn-primary" })
                                </div>
                            </div>

                        </div>

                        <br />
                    </div>
                </div>
            </div>

            <div class="container" id="divAlert">
                <div class="row">
                    <div class="col-sm-12 text-center">
                    </div>
                </div>
            </div>

</div>

