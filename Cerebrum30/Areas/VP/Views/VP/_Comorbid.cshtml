﻿
@model Cerebrum30.Areas.VP.Models.ViewModels.CoMorbid_VM
<style>
    .cpp-list li {
/* margin: 0; */
        //*padding: 1px;*// }
    .cpp-list {*@ list-style: none;
    }
    .txtArea {
/* max-width: 100%; */
/* width: 80%; */
/* -webkit-box-sizing: border-box; */
/* -moz-box-sizing: border-box; */
@* box-sizing: border-box; *@
@* padding: 5px; *@

   .cppBox {
@* /*@height: 100px; font-size: 12px;@*/*@
@* min-height: 100px; *@
@* border: 1px solid #e2e2e2; *@
@* margin-bottom: 3px; *@
@* padding: 3px; *@
@* font-size: 12px; *@

</style>
<div>
@* <div class="clearfix">&nbsp;</div> *@ <ol class="cpp-list __0093453" style="padding:0;margin:0;">
        <li class="col-sm-4">
            <div class="form-inline">
                <div class="form-group">

                        <label class="label label-default ">Problem List</label>
                    </span>
                    <span data-toggle="tooltip" data-placement="right" tooltip="PROBLEM LIST" title="" class="cpp-header" data-original-title="PROBLEM LIST">
                        PROBLEM LIST
                    </span>
                </div>
            </div>
            <div class="form-inline">
                @Html.TextArea("ProblemList", @Model.ProblemList, new { @class = "txtArea cppBox rd", @readonly = "readonly" })
            </div>
        </li>
        <li class="col-sm-4">
            <div class="form-inline">
                <div class="form-group">

                        <label class="label label-default ">Past medical History</label>
                    </span>
                    <span data-toggle="tooltip" data-placement="right" tooltip="PAST MEDICAL HISTORY" title="" class="cpp-header" data-original-title="PAST MEDICAL HISTORY">
                        PAST MEDICAL HISTORY
                    </span>
                </div>
            </div>
            <div class="form-inline">
                @Html.TextArea("PastHelath", @Model.PastHelath, new { @class = "txtArea cppBox rd" })
            </div>
        </li>
        <li class="col-sm-4">
            <div class="form-inline">
                <div class="form-group">

                        <label class="label label-default ">Medications</label>
                        @* *@
                    </span>
                    <span data-toggle="tooltip" data-placement="right" tooltip="MEDICATIONS" title="" class="cpp-header" data-original-title="MEDICATIONS">
                        MEDICATIONS
                    </span>
                </div>
            </div>
            <div class="form-inline">
                @Html.TextArea("Medications", @Model.Medications, new { @class = "txtArea cppBox rd" })
            </div>
        </li> 
    </ol>
    <div class="clearfix">&nbsp;</div>
</div>
<script>
    $('.rd').on('focus', function (e) {
@* e.preventDefault(); *@
@* $(this).css('background', 'transparent'); *@
@* }); *@

</script>