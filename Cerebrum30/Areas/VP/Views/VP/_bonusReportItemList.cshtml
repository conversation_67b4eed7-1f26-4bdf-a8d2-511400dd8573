@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.VP.VMBonusReportItem>

@{ 

    {

    }

<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.PatientRecordId)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.FirstName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LastName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DateOfBirth)
        </th> 
        <th>
            @Html.DisplayNameFor(model => model.HealthCard)
        </th>      
        <th>
            @Html.DisplayNameFor(model => model.GenderDSC)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.EnrollmentDate)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Age)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LastProcedureDate)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.BonusStatus)
        </th>           
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.PatientRecordId)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.FirstName)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.LastName)
        </td>
        <td>
            @if (item.DateOfBirth.HasValue)
            {

                <span>@date</span>
            }
        </td>        
        <td>
            @Html.DisplayFor(modelItem => item.HealthCard)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.GenderDSC)
        </td>
        <td>
            @if (item.EnrollmentDate.HasValue)
            {

                <span>@date</span>
            }
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.Age)
        </td>
        <td>
            @if (item.LastProcedureDate.HasValue)
            {

                <span>@date</span>
            }
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.BonusStatus)
        </td>             
    </tr>

    <tfoot>
        <tr>
            <td colspan="10">
                @if(@Model.Any())
                {
                    <div class="text-right">Bonus Calculation: @bonusCalcPercentage %</div>
                }
            </td>
        </tr>
    </tfoot>
</table>
