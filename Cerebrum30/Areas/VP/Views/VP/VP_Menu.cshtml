﻿@model List<Cerebrum.ViewModels.VP.VMVitalSignHistory>

<style>
    .menu-links {
/* color: gray; */
    }

        .menu-links .table {
/* margin-bottom: 0px; */
/* font-size: 12px; */

            .menu-links .table > thead > tr > th,
            .menu-links .table > tbody > tr > th, .table > tfoot > tr > th,
            .menu-links .table > thead > tr > td, .table > tbody > tr > td,
            .menu-links .table > tfoot > tr > td {
@* border-top: solid 0px black; *@
            }

            .menu-links .table > thead > tr > th {
@* padding: 0px; *@

        .menu-links .dropdown-menu {
@* padding: 0px; *@

</style>
<script>
    //makes header stay at top
    $(document).ready(function () {
        $(".div-scroll-custom").on('scroll', function () {

            //this.querySelector("thead").style.transform = translate;

@* }); *@
@* }); *@
</script>

@if (@Model.Count() > 0)
{
    <div class="menu-links">
        <div class="dropdown  ">
            <div class="container dropdown-menu div-scroll-custom" style="height:200px;overflow:auto;" >
                <table class="table table-condensed ">
                    @{

                    }
                    
                    @foreach (var item in Model)
                    {
                        if (firstRun)
                        {
                            <thead>
                                <tr>
                                    <td>
                                        <table class="table table-condensed  ">
                                            <tr>
                                                <td class="col-md-2"></td>
                                                <td class="col-md-9">
                                                    <table class="table table-condensed  ">
                                                        <tr>
                                                            @foreach (var meas in @item.Measurements)
                                                            {
                                                                <td align="left"  style="width:50px;"><b>@meas.Name</b>                                                                     
                                                                 </td>
                                                            }
                                                    </table>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </thead>

                        <tbody>
                            <tr>
                                <td>
                                    <table class="table  table-condensed  ">
                                        <tr>
                                            <td class="verticalAligned col-md-2">
                                                <label style="min-width:50px;">@item.DateStr </label>
                                                @{ 

                                                    {

                                                    }

                                                <label style="min-width:50px;">@username </label>
                                            </td>
                                            <td class="col-md-9">
                                                <table class="table table-condensed ">
                                                    <tr>
                                                        @foreach (var meas in @item.Measurements)
                                                        {
                                                            <td align="left" style="width:50px;">
                                                                @if (!string.IsNullOrEmpty(meas.Value))
                                                                {
                                                                    @meas.Value

                                                                }
                                                                else
                                                                {

                                                        </td>

                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </tbody>

                </table>
            </div>
            <button class="btn btn-default btn-sm" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <span class="menuText6">History</span>
                <span class="caret"></span>
            </button>
        </div>
    </div>

