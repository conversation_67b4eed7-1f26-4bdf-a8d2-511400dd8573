@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.CDF_VM
@{

    //Layout = "~/Views/Shared/_LayoutFluidPopup.cshtml";

}

@section customcss {
    <!-- TODO: Replace Styles.Render with direct link references for ASP.NET Core -->

@section topscripts {
@

    <script src="//cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="//cdn.datatables.net/1.10.13/css/jquery.dataTables.min.css" />
}

@Html.HiddenFor(x => x.PatientID)

@section patientinfo {*@Html.PartialAsync("_PatientInfoMenu", @Model.PatientID)
    @Html.GetPatientInfo(@Model.PatientID, 0, 0, 0)
}

<script id="tempDetail-template" type="text/x-handlebars-template">
    <div class="text-center pre-scrollable">

        <div class="row ">&nbsp;</div>
        {{#each list}}
        <div id="divEntry_{{Id}}" class="row dataRow" VPTemplateField={{VPTemplateField}}>
            <table class="table table-condensed  table-bordered ">
                <tr>
                    <td style="width:15%">
                        <a href="#" class="hlDelete" id="hlDelete" EntryID="{{Id}}">
                            <i class="glyphicon glyphicon-remove" style="color:red;"></i>
                        </a>
                    </td>
                    <td align="left" style="width:25%">
                        <b>{{TemplateItemName}}</b>
                        {{Units}}
                    </td>
                    <td style="width:10%">
                        <span VPTemplateField={{VPTemplateField}} id="spanNH_{{VPTemplateField}}">{{NH}}</span>
                        @*<input type="text" VPTemplateField={{VPTemplateField}} id="txtNH_{{VPTemplateField}}" class="txtBox " value='{{NH}}' />* </td>
                    <td style="width:10%">
                        <span VPTemplateField={{VPTemplateField}} id="spanNL_{{VPTemplateField}}">{{NL}}</span>
                        @*<input type="text" VPTemplateField={{VPTemplateField}} id="txtNL_{{VPTemplateField}}" class="txtBox " value='{{NL}}' />* </td>
                    <td style="width:10%">
                        <input type="text" VPTemplateField={{VPTemplateField}} id="txtTH_{{VPTemplateField}}" class="txtBox txtTH" value='{{TH}}' />
                    </td>
                    <td style="width:10%">
                        <input type="text" VPTemplateField={{VPTemplateField}} id="txtTL_{{VPTemplateField}}" class="txtBox txtTL" value='{{TL}}' />
                    </td>
                    <td style="width:10%">
                        <input type="text" VPTemplateField={{VPTemplateField}} id="txtFreq_{{VPTemplateField}}" class="txtBox txtFreq" value='{{Frequency}}' />
                    </td>
                    @*<td>*@
                            <input type="text" VPTemplateField={{VPTemplateField}} id="txtVal_{{VPTemplateField}}" class="txtBox txtVal" value='{{Value}}' />
                        </td>
                </tr>
            </table>
        </div>
        {{/each}}
    </div>
</script>

@section scripts
{
    @*<!-- TODO: Replace Scripts.Render with direct script references for ASP.NET Core -->*@
<script src="~/Areas/Measurements/Scripts/SendToContacts.js"></script>
    <script>*@ var LoadPatientTemplateItems = function () {

            //console.log({ templateID: $('#ddTemplate').val(),patientID:@Model.PatientID  });
@* $("#templateName").val($('#SavedTemplateID :selected').text().trim()); *@

            $.ajax({
                type: "GET",
                url: "VP/VP/GetPatientTemplateItemDetails",
                data: { templateID: $('#SavedTemplateID :selected').val(), patientID:@Model.PatientID  },
                contentType: "application/json",
                dataType: "json",
                success: function (data) {

@* $("#divTempDetails").html(html); *@

@* }); *@

@* }; *@
        function LoadTemplatesInDropDown() {

            $.ajax({
                //type: "POST",
                data: { patientId: $('#PatientID').val() },
                url: 'VP/VP/CDF_Templates',
                success: function (data) {
@* $("#div-templates").html(data); *@
                }
            }).done(function () {
                //LoadPatientTemplateItems();
@* }); *@
@* }; *@
        //var table = $('#scrollable-table').DataTable();
        //new $.fn.dataTable.FixedHeader(table);
        $(document).ready(function () {

            $(".btn-popover-container").each(function () {

                $(btn).popover({
                    html: true,
                    title: title,
                    content: content,
                    placement: 'auto top',
                    trigger: 'manual'//,
                    //container: 'body'
                }).on("click", '.popover-btn', function (e) {
@* e.preventDefault(); *@

@* $(btn).popover("show"); *@
                }).on("mouseover", function () {
@* $(".popover").popover("hide"); *@
@* $(btn).popover("show"); *@
@* }); *@

@* }); *@

            $(document).on('click', '.btn-close-status-change', function (e) {

@* e.preventDefault(); *@
@* $(".popover-btn").popover('hide'); *@
@* $('[data-original-title]').popover('hide'); *@
@* }); *@

                ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
@* $("#div-pat-name").html(data); *@
@* }); *@
@* }; *@

            //LoadPatientName();
@* LoadTemplatesInDropDown(); *@
            //LoadPatientTemplateItems();

            //$(document).on('click', '#btn-save-template-data', function (e) {
            $('#btn-save-template-data').click(function (e) {

                //console.log(data);

                $.ajax({
                    type: "POST",
                    url: "VP/VP/SaveTemplateData",
                    data: {
                        json: data,
                        templateName: $("#templateName").val(),
                        PatientID:@Model.PatientID,
                        templateID: $('#SavedTemplateID').val()

                    },
                    success: function (data) {

                        if (data.Errored) {
@* $("#lblSpan").text(data.Message); *@
                        }
                        else {
@* $("#lblSpan").text('Changes Saved to Template'); *@
@* LoadTemplates(); *@

@* }); *@
@* }); *@

            function SaveTemplateDataByPractice() {

                $.ajax({
                    type: "POST",
                    url: "VP/VP/SaveTemplateDataByPractice",
                    data: {
                        json: data,
                        templateName: $("#templateName").val(),
                        PatientID:@Model.PatientID,
                        templateID: $('#SavedTemplateID').val()

                    },
                    success: function (data) {

                        if (data.Errored) {
@* $("#lblSpan").text(data.Message); *@
                        }
                        else {
@* $("#lblSpan").text('Changes Saved to Template for Practice'); *@
@* LoadTemplates(); *@

@* }); *@

            $('#btn-save-template-data-practice').click(function (e) {

@* SaveTemplateDataByPractice(); *@

                //console.log(data);
                //$.ajax({url:"VP/VP/IsTemplateExists",data:{template:$("#templateName").val()},
                //    success: function(rtn){
                //        if(rtn=="1"){

                //        }else
                //        {
                //            $("#lblSpan").text('Template name already exists. Please choose different name');
                //        }
                //    }

                //});

@* }); *@

            $(document).on('click', '.hlDelete', function (e) {
@* e.preventDefault(); *@

@* $('#divEntry_' + entryID).remove(); *@
@* }); *@

            $(document).on('click', '.btn-popover-close', function (e) {
                //e.preventDefault();
                //$(".popover-btn").popover('hide');
                //$('[data-original-title]').popover('hide');
@* }); *@

@* }); *@
    </script>

<style>
    .pre-scrollable {
/* overflow-x: hidden; */

    .table-striped>tbody>tr:nth-child(2n+1)>td,
    .table-striped>tbody>tr:nth-child(2n+1)>th {
/* background-color: #e8f2f8; */

    .btn-popover-container {
@* display: inline-block; *@

    .btn-popover-content {
@* padding-top: 0; *@
@* padding-bottom: 0; *@
@* margin-top: 0; *@
@* margin-bottom: 0; *@
    }

    .btn-popover-container .btn-popover-title,
    .btn-popover-container .btn-popover-content {
@* display: none; *@

    .popover-pointer {
@* cursor: pointer; *@

</style>

<div class="row templateList">&nbsp;</div>
<div style="padding-top:15px;" >
    <div class="row" style="margin: auto">
        <div class="" style="margin: auto">
            <div id="div-templates" align="center" style="align-self:center;margin-top:5px;margin-bottom:5px;"></div>

        </div>
    </div>

    <div class="form-inline" align="center">
        <div class="form-group">

            <div class="btn-popover-container cdf-config-addExist">
                <span class="popover-btn2 popover-pointer cb-text16 text-primary getmeasbtn">
                    <button href="#" class="btn btn-default btn-sm">Add Existing Measurement</button>
                </span>

                <div class="btn-popover-content">
                    <div id="div-measurment-content">
                        @Html.Action("GetMeasurements", "VP", null)
                    </div>

                </div>
            </div>

            <div class="btn-popover-container cdf-config-add">
                <span class="popover-btn2 popover-pointer cb-text16 text-primary">
                    <button href="#" class="btn btn-default btn-sm">Add New Measurement</button>
                </span>

                <div class="btn-popover-content">
                    <div>
                        @Html.Action("AddNewMeasurement", "VP", null)
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="row">&nbsp;</div>

    <div class="row" style="margin-bottom:3px" class="panel-heading">
        New Template Name :
        <input id="templateName" name="templateName" type="text" />
    </div>

    <div>

        <div class="panel-body">
            <div class="row text-center topPadding ">
                <div class="">
                    <table id="" class="table table-condensed table-bordered">
                        <tr>
                            <td style="width:15%">
                                Delete
                            </td>
                            <td align="left" style="width:25%">
                                <b>Name</b>
                            </td>
                            <td style="width:10%">
                                <b>NH</b>
                            </td>
                            <td style="width:10%">
                                <b>NL</b>
                            </td>
                            <td style="width:10%">
                                <b>TH</b>
                            </td>
                            <td style="width:10%">
                                <b>TL</b>
                            </td>
                            <td style="width:10%">
                                <b>Frequency</b>
                            </td>
                        </tr>
                    </table>
                </div>
                <div id="divTempDetails" style="max-height: 200px;  overflow-y: auto;"> </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
*@ &nbsp; </div>
<div class="row text-center" style="color:red">
    <span class="label label-success " id="lblSpan"></span>
</div>
<div class="row">
@* &nbsp; *@
</div>

<div class="row" style="margin-bottom: 27px">
    <button id="btnSaveNyPatient" class="btn btn-default btn-primary btn-sm">Save Patient Data</button>
    @if (cerebrumUser.IsDoctor)
    {
        <button id="btn-save-template-data" class="btn btn-default btn-sm">Save Template Data for all my Patients</button>
    }
    <button id="btn-save-template-data-practice" class="btn btn-default btn-sm">Save Template Data for all Practice
        Patients </button>
    <button class="btn btn-default btn-sm btn-cancel-model ">Close</button>
</div>

<script>

        $('#frm-add-New-meas .required').each(function (i, v) {
@* alert(i + '__' + $(this).attr('id')); *@
@* }); *@
        //return false;
@* }; *@

    $(document).ready(function (e) {

        $('.cdf-config-add').each(function () {

            $(btn).popover({
                html: true,
                title: title,
                content: content,
                placement: 'auto right',
                trigger: 'manual',
                animation: false,
                delay: { show: 0, hide: 0 }
            }).on('mouseenter', function (e) {
                //e.stopImmediatePropagation();                        
                //e.preventDefault();

@* $(this).popover('show'); *@
                $('.btn-popover-close').on('click', function (e) {
@* $(_this).popover('hide'); *@
@* }); *@

                $('.btn-save-meas-new').on('click', function (e) {
                    //if(isValidformAddNewMeas()){
                    //alert( $(this).prop('checked'));    
@* e.preventDefault(); *@
@* savMeasNew(); *@
                    //}
                    //else{
                    //    $('#span-meas-error').html('All fields marked with an asterisk (@*) are required');*@
                    //}
@* }); *@

                $('#IsText').on('click', function (e) {
                    //e.stopPropagation();                          
                    if ($(this).is(':checked')) {
@* $(this).attr('value', 'true'); *@
                    }
                    else {
@* $(this).attr('value', 'false'); *@
                    }
@* }); *@

                $(this).siblings('.popover').on('click', function (e) {
@* e.stopImmediatePropagation(); *@
                    // return false;
@* }); *@
            })
                .on('mouseleave', function (e) {
@* e.stopImmediatePropagation(); *@

                    setTimeout(function () {
                        if (!$('.popover:hover').length) {
@* $(_this).popover('hide'); *@
                        }
@* }, 100); *@
@* }); *@
@* }); *@

        //-------------------------------
        //$('body').on('click', '.cdf-config ', function (e) {
        //   return false;
        //});

        //$('body').on('click', '.cdf-config-add .btn-popover-close', function (e) {
        //    e.stopPropagation();
        //    $('.cdf-config-add .popover').hide();
        //});
        //-------------------------------

        $('.cdf-config-addExist').each(function () {

            $(btn).popover({
                html: true,
                title: title,
                content: content,
                placement: 'auto right',
                trigger: 'manual',
                animation: false,
                delay: { show: 0, hide: 0 }
            }).on('mouseenter', function (e) {

@* $(this).popover('show'); *@

                $('.btn-popover-close').on('click', function (e) {
@* $(_this).popover('hide'); *@
@* }); *@

                $('.btn-sel-meas').on('click', function (e) {
@* selectMeasurement($(this).attr('data-url')); *@
@* }); *@

                $('#IsText').on('click', function (e) {
                    //e.stopPropagation();                          
                    if ($(this).is(':checked')) {
@* $(this).attr('value', 'true'); *@
                    }
                    else {
@* $(this).attr('value', 'false'); *@
                    }
@* }); *@

                $(this).siblings('.popover').on('click', function (e) {
@* e.stopImmediatePropagation(); *@
                    // return false;
@* }); *@
            })
                .on('mouseleave', function (e) {
@* e.stopImmediatePropagation(); *@

                    setTimeout(function () {
                        if (!$('.popover:hover').length) {
@* $(_this).popover('hide'); *@
                        }
@* }, 100); *@
@* }); *@

@* }); *@

@* }); *@

</script>