@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VP_VM

<div class="form-inline menuText menu-links">
    <div class="form-group form-group-sm"> Html.PartialAsync("~/Areas/Measurements/Views/Shared/_PreviousTests.cshtml",*@ *@ new Cerebrum.ViewModels.Measurements.VMPreviousTestItem() { AppointmentID = @Model.AppointmentID, TestID = @Model.TestID })*@ @Html.PartialAsync("~/Areas/Measurements/Views/Shared/_PreviousTests.cshtml",*@ new Cerebrum.ViewModels.Measurements.VMPreviousTestItem() { AppointmentID = @Model.AppointmentID, TestID = @Model.TestID, PatientID = @Model.PatientID })
    </div>
    <div class="form-group form-group-sm">
        <div class="dropdown">
            <button class="btn btn-default btn-sm" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <span class="menuText  ">Settings</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu  menuText">
                <li>
                    <a data-url="VP/VP/EditCPPSetting?docID=@Model.DoctorID" id="btn-edit-cpp" href="#">Edit CPP Settings</a>
                </li>
                <li>
                    <a href="#" data-url='@Url.Action("EditReportPhraseSetting",new {patientID=@Model.PatientID,practiceID=1,UserID=@Model.DoctorID })' id="btn-edi-rp-setting">Edit Root Categories</a>
                </li>
                <li>
                    <a href="#" data-url='@Url.Action("Options",new {docID=@Model.DoctorID,practiceID=1 })' id="btn-edit-options">Edit Options</a>
                </li>
                <li>
                    <a href="#" data-url='@Url.Action("EditMeasurements",new {userID=@Model.UserID,DocID=@Model.DoctorID })' id="btn-edit-meas">Edit Measurements</a>
                </li>

            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm menuText">
        <div class="dropdown">
            <button class="btn btn-default btn-sm" type="button" data-toggle="dropdown">
                <span class="menuText  ">Chronic Disease Flow</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu menuText  ">
                <li>
                    <a class="hl-config"

                </li>
                <li>
                    <a target="_blank" href='@Url.Action("TemplateData",
                                            new
                                            {

                                            })'>Data</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm">
        <div class="dropdown">
            <button class="btn btn-default btn-sm" type="button" data-toggle="dropdown">
                <span class="menuText ">Show</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu  menuText">
                <li>
                    <a id="hlRawDataClassic" target="_blank"

                    </a>
                </li>
                @if (!@Model.IsClassicAppointment)
                {
                    <li>

                        @Html.ActionLink("Preview Letter", "VPReportLetterPreview", "Reports", new { area = "Documents", AppointmentId = @Model.AppointmentID, PatientId = @Model.PatientID, UserId = @Model.UserID, AppointmentTestId = @Model.AppointmentTestID, IsPreview = true }, new { @id = "btn-preview", target = "_blank" })
                        
                    </li>

                <li>
                    @ Html.ActionLink("Letter History", "MultipleLetter_VP_Pdf", "VP", new { area = "VP", appointmentId = @Model.AppointmentID }, new { @id = "btn-preview-multiple", target = "_blank" })
                    @Html.ActionLink("Letter History", "VPReportHistory", "Reports", new { area = "Documents", patientId = @Model.PatientID, appointmentId = @Model.AppointmentID }, new { @id = "btn-preview-multiple", target = "_blank" })
                </li>
                <li>
                    @Html.ActionLink("VP History", "MultipleLetter_VP_PdfByPatient", "VP", new { area = "VP", patientID = @Model.PatientID }, new { target = "_blank" }) </li>*@<li>
                    <a target="_blank" class="" href="#" data-url='@Url.Action("SendReportList", new { appointmentID = @Model.AppointmentID, testID = @Model.TestID, patientID=@Model.PatientID})' id="btn-view-cpp">CPP</a>
                </li>
                <li>
                    <a data-apptest-id="@Model.AppointmentTestID" class="btn-view-send-history" href="#">Send History</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm">
        <div class="dropdown ">
            <button class="btn btn-default btn-sm" type="button" data-toggle="dropdown">
                <span class="menuText ">Goto</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu  menuText">
                <li>
                    <a class="hl-privacy"

                    </a>
                </li>
                <li>
                    <a target="_blank" href="/Medications/@Model.PatientID">Medications</a>
                </li>
                @*<li>*@
                    <a href='@Url.Action("index", "daysheet",
                        new
                        {

                        })'>
                        DaySheet
                    </a>@* *@
                </li>
                <li>
                    <a class="btn-flowsheet"

                </li>
                <li>
                    <a target="_blank" rel="noreferrer" href="https://ccs.ca/frs/">Framingham</a>
                    <a target="_blank" rel="noreferrer" href="https://www.mdcalc.com/cha2ds2-vasc-score-atrial-fibrillation-stroke-risk">CHADS/CHADSVAASC</a>
                    <a target="_blank" rel="noreferrer" href="http://doc2do.com/hcm/webHCM.html">HCM</a>
                    <a target="_blank" rel="noreferrer" href="http://tools.acc.org/DAPTriskapp/?_ga=2.185684524.427119323.**********-762679002.**********#!/content/calculator/">DAPT</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm">
        <button class="btn btn-default btn-sm"  id="btn-externalDocuments-vp">
            <span class="menuText">External Documents</span>
        </button>
    </div>
</div>

<script>
    $(function () {// ready
        $('#btn-externalDocuments-vp').on('click', function (e) { 
@* e.preventDefault(); *@

            //window.location.href = url;
@* window.open(url,'_blank'); *@
@* }); *@
@* }); *@
</script>

