﻿@model List<Cerebrum.ViewModels.VP.VMRecallLog>
@{

}

<style>
    .recallinfo table tr td {
/* text-align: center; */

</style>
<div class="row">&nbsp;</div>
<div class="row">&nbsp;</div>
<div class="pre-scrollable">
    <table class="recallinfo table table-condensed ">
        <thead>
            <tr>
                <th>Date</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td align="center">
                        @item.Date
                    </td>
                    <td align="center">
                        @Html.TextAreaFor(x => item.Notes, new { @class = "form-control" })
                    </td>
                </tr>

        </tbody>
    </table>
</div>
<br />
<div class="row text-center">
    <a href="#" class="btn btn-default btn-sm btn-cancel-model">
        <i style="color:red" class="glyphicon glyphicon-remove"></i>Close
    </a>
</div>
<br />