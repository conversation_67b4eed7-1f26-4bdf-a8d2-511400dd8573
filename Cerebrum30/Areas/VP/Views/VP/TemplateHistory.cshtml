@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMTemplatePatientData
@{

}
<style>
    .list li {
/* list-style: none; */

    .myTable td {
/* text-align: center; */

    .custom-tbl thead td {
/* padding: 2px 1px 2px 1px !important; */
@* min-width: 80px; *@
        /@*webkit-transform: rotate(-90deg) !important;*@
@* -moz-transform: rotate(-90deg) !important; *@
@* -ms-transform: rotate(-90deg) !important; *@
@* -o-transform: rotate(-90deg) !important; *@
    height: 190px;*/

    .myTable td textarea {
@* resize: none !important; *@

    input.chk-col-vis {
@* display: block !important; *@
@* margin: auto !important; *@
@* margin-top: 4px !important; *@
    }

    .outSideTarget {

    .outSideNormal {
    }

    .outSideInterval {
    }

    .tr-item {
@* width: 200px; *@
    }

    .tbl-header-item {
@* width: 200px; *@

    table.dataTable tbody th, table.dataTable tbody td {
@* white-space: nowrap !important; *@

    .dataTables_scrollHeadInner {
@* width: 640px !important; *@

    .tbl-i {
@* font-weight: normal !important; *@
@* color: #444; *@

        .tbl-i tr td:nth-child(1) {
            background-color: #c4c4c4; /*123*/
            /*width: 40px;*/

    #scrollable-table tr td:nth-child(1) {
@* min-width: 35px !important; *@

</style>
<ul id="tbl_switch" style="list-style-type: none;">
    <li><input type="checkbox" id="ctrl_CDF_hideHdrDet1" />  Hide Header Details</li>
    <li><input type="checkbox" id="ctrl_CDF_showSelected1" /> Show Selected Records</li>
</ul>

<div class="">
    <div class="@*row text-center">"

        <span data-toggle="tooltip" data-placement="right" tooltip="HISTORY" title="" class="cpp-header" data-original-title="HISTORY">
            HISTORY
        </span>
    </div>*@ <div class="row">&nbsp;</div>
    <style>
        #tbl_switch {
/* border: unset !important; */
        }

            #tbl_switch tr:nth-child(odd), #tbl_switch tr:nth-child(even) {
/* background: unset !important; */

            #tbl_switch td:first-child {
/* text-align: left; */

    </style>
    <div class="wrapper7">
        <table id="scrollable-table" cellpadding="1" class="table table-bordered table-condensed myTable custom-tbl">

            @for (int z = 0; z < @Model.Logs.Count; z++)
            {

                {
                    <thead>
                        <tr class="tbl-header-item">
                            <td class="verticalAligned _mm 1"></td>
                            @if (@Model.IncludeMedication)
                            {
                                <td class="verticalAligned _mm 2">Medications</td>
                            }
                            <td class="verticalAligned _mm 3">Log Date</td>@* *@

                            @{

                            @foreach (var item in @Model.HeaderItems)
                            {
                                @Html.PartialAsync("CareElementHeaderItem", new Cerebrum.ViewModels.VP.CareElementHeaderItem { Item = item, Count = count });

@* count++; *@
                            }
                        </tr>
                    </thead>

                                <tbody>
                                    <tr class="tr-item">
                                        <td class="verticalAligned 1">
                                            <input type="checkbox" class="chk-select" data-rowid="@Model.Logs[z].Date" />
                                        </td>
                                        @if (@Model.IncludeMedication)
                                        {
                                            <td class="2">
                                                <textarea style="width:200px;height:100px;" class="form-control small" id="<EMAIL>()">
                                                    @foreach (var med in @Model.PatientMedications.Medications.Where(m => m.DateStarted <= @Model.Logs[z].Date).ToList())
                                                    { (med.MedicationName + Environment.NewLine)
                                                        @(med.MedicationName + " " + med.Dose + " " + med.SIG + Environment.NewLine)
                                                    }
                                                </textarea>
                                            </td>

                                        <td class="verticalAligned 3" title="@Model.Logs[z].Date">
                                            @Model.Logs[z].Date
                                        </td>*@<td class="verticalAligned" title="@Model.Logs[z].DateEntered">
                                                @Convert.ToDateTime(@Model.Logs[z].DateEntered).ToShortDateString()
                                            </td>
                                        @* int count2 = 4; *@

                                        @for (int i = 0; i < @Model.Logs[z].Items.Count; i++)
                                        {
                                            @*<td*@

                                            <td

                                                <div>@* *@
                                                    @*[*@ __COL___@count2 - @Model.Logs[z].Items[i].HeaderVPTemplateFieldId ] <br />*@  @*debug - output*@
                                                    @if (@Model.Logs[z].Items[i].IsText)
                                                    {
                                                        <textarea rows="4" class="form-control ">@Model.Logs[z].Items[i].Value</textarea> *@if (!@Model.OutSideInterval && !@Model.OutSideNormal && !@Model.OutSideTarget) {
                                                             <textarea rows="4" class="form-control ">@Model.Logs[z].Items[i].Value</textarea>
                                                            }*
                                                    }

                                                    {
                                                        <span>YES</span>

                                                    else
                                                    {
                                                        if (!string.IsNullOrEmpty(@Model.Logs[z].Items[i].Value))
                                                        {

                                                            <label>@Model.Logs[z].Items[i].Value </label>

                                                            if (@Model.Logs[z].Items[i].OutSideNormal)
                                                            {

                                                                <i tooltip="Out of Normal"

                                                            if (@Model.Logs[z].Items[i].OutSideTarget)
                                                            {
                                                                <i tooltip="Out of Target"

                                                            if (@Model.Logs[z].Items[i].OverDue)
                                                            {
                                                                <i tooltip="Out of Treatment Interval"

"OverDue"
"OverDue"

                                                            @*if (!@Model.OutSideInterval && !@Model.OutSideNormal && !@Model.OutSideTarget)*@
                                                                {
                                                                     <label>@Model.Logs[z].Items[i].Value </label>

                                                                    if (@Model.Logs[z].Items[i].OutSideNormal)
                                                                    {

                                                                     <i data-toggle="tooltip"

                                                                    }

                                                                    if (@Model.Logs[z].Items[i].OutSideTarget)
                                                                    {
                                                                     <i data-toggle="tooltip"

                                                                    }

                                                                    if (@Model.Logs[z].Items[i].OverDue)
                                                                    {
                                                                     <i data-toggle="tooltip"

                                                                    }

                                                                else
                                                                {

                                                                    if (@Model.OutSideNormal && @Model.Logs[z].Items[i].OutSideNormal)
                                                                    {

                                                                    }
                                                                    else
                                                                    {

                                                                    if (@Model.OutSideTarget && @Model.Logs[z].Items[i].OutSideTarget)
                                                                    {

                                                                    else
                                                                    {

                                                                    if (@Model.OutSideInterval && @Model.Logs[z].Items[i].OverDue)
                                                                    {

                                                                    else
                                                                    {

                                                                    if (!string.IsNullOrEmpty(valueToShow))
                                                                    {
                                                                     <label>@valueToShow </label>

                                                                        if (@Model.Logs[z].Items[i].OutSideNormal)
                                                                        {

                                                                         <i data-toggle="tooltip"

                                                                        }

                                                                        if (@Model.Logs[z].Items[i].OutSideTarget)
                                                                        {
                                                                         <i data-toggle="tooltip"

                                                                        }

                                                                        if (@Model.Logs[z].Items[i].OverDue)
                                                                        {
                                                                         <i data-toggle="tooltip"

                                                                        }

@* *@

                                                        else
                                                        {
                                                            <label></label>

                                                </div>
                                            </td>
@* count2++; *@

                                    </tr>
                                </tbody>

        </table>
    </div>
</div>
<script>
    $("#txtDateFrom").datepicker({
        changeMonth: true,
        changeYear: true
@* }); *@

    $("#txtDateTo").datepicker({
        changeMonth: true,
        changeYear: true
@* }); *@

</script>
