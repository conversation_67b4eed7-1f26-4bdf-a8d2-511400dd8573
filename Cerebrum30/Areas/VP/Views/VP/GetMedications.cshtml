@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.VP.VMVPMedication
@functions{
    public string showMedicationNameWithStrength(string name, string strength)
    {
        if (!string.IsNullOrEmpty(strength) && !name.ToUpper().Contains(strength.ToUpper()))
        {
// return name + " " + strength; 
        }
@* return name; *@

} if (@Model.Medications.Count > 0)*
*@{@* @Html.HiddenFor(x => x.PatientID) @*<div >*@
<!-- style="margin-top:0px;margin-bottom:0px;" class="div-med"--->
@*<div class="row">* <div class="col-xl-2 col-lg-2 col-md-2 __2ndCol div-medications-allergies">
    <div class="panel panel-info div-medications">
        <div class="panel-heading">
            <div class="pull-left">
                <label class="panel-title">
                    <a target="_blank" class="custom-label" href="/Medications/@Model.PatientID">

                    </a>
                </label>
            </div>
            <div class="clearfix"></div>
        </div>
        <div class="panel-body">
            <div class="">
                <div class="form-inline">
                    <div class="form-group">
                        @if (@Model.Allergies.Count > 0)
                        {
                            <ol>
                                @foreach (var item in @Model.Allergies)
                                {
                                    <li class="">
                                        <span class="small"> @item.MedicationName, <span class="tealcolor">@item.Severity</span></span>
                                    </li>
                                }
                            </ol>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-xl-10 col-lg-10 col-md-10    __3rdCol  div-medications-medications">
    <div class="panel panel-info div-medications">
        <div class="panel-heading">
            <div class="pull-left">
                <label class="panel-title">
                    <a target="_blank" class="custom-label" href="/Medications/@Model.PatientID">

                    </a>
                </label>
            </div>
            <div class="clearfix"></div>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                    <div class="form-inline">
                        <div class="">
                            <label class="label label-primary customLbl">Current</label>
                            @if (@Model.Prior.Count > 0)
                            {
                                <ol>
                                    @foreach (var item in @Model.Prior.OrderBy(o=>o.DateStarted).ToList())
                                    {
                                        <li>
                                            <span class="small">   @showMedicationNameWithStrength(item.MedicationName, item.Strength) <span class="tealcolor">@item.Dose </span> <span class="coralcolor"> @item.SIG</span></span>
                                        </li>
                                    }
                                </ol>
                                @*<div class="clearfix">&nbsp;</div>* }
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                    <div class="form-inline">
                        <div class="">
                            <label class="label label-primary customLbl">Added</label>
                            @if (@Model.Added.Count > 0)
                            {
                                <ol>
                                    @foreach (var item in @Model.Added.OrderBy(o => o.DateStarted).ToList())
                                    {
                                        <li>
                                            <span class="small">   @showMedicationNameWithStrength(item.MedicationName, item.Strength) <span class="tealcolor">@item.Dose </span> <span class="coralcolor"> @item.SIG</span></span>
                                        </li>
</ol>
                                @*<div class="clearfix">&nbsp;</div>* }
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                    <div class="form-inline">
                        <div class="">
                            <label class="label label-primary customLbl">Discontinued</label>
                            @if (@Model.Discontinued.Count > 0)
                            {
                                <ol>
                                    @foreach (var item in @Model.Discontinued.OrderBy(o => o.DateStarted).ToList())
                                    {
                                        <li>
                                            <span class="small">   @showMedicationNameWithStrength(item.MedicationName, item.Strength) <span class="tealcolor">@item.Dose </span> <span class="coralcolor"> @item.SIG</span></span>
                                        </li>
</ol>
                                @*<div class="clearfix">&nbsp;</div>* }
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                    <div class="form-inline">
                        <div class="">
                            <label class="label  label-primary customLbl">Dose Changed</label>
                            @if (@Model.DoseChanged.Count > 0)
                            {
                                <ol>
                                    @foreach (var item in @Model.DoseChanged.OrderBy(o => o.DateStarted).ToList())
                                    {
                                        <li>
                                            <span class="small">   @showMedicationNameWithStrength(item.MedicationName, item.Strength) <span class="tealcolor">@item.Dose </span> <span class="coralcolor"> @item.SIG</span></span>
                                        </li>
</ol>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@* </div> <!-- /row --> *@
</div>
