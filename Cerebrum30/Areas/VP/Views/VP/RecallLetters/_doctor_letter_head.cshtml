﻿@model Cerebrum.ViewModels.Medications.VMDoctor
<tr>
    <td>
        <div style="text-align:center;margin-bottom:15px;">
            @{

                {

                }

            @if (!String.IsNullOrWhiteSpace(officeImg))
            {
                <div><img style="width:200px;height:75px;" src="@officeImg" /></div>

                @if (!String.IsNullOrWhiteSpace(@Model.FullName))
                { <div>@Model.FullName</div> } if (!String.IsNullOrWhiteSpace(@Model.CPSO))*@
                    { <div>CPSO: @Model.CPSO</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.OfficeName))
                { <div>@Model.OfficeName</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.Address))
                { <div>@Model.Address</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.Phone))
                { <div>Phone: @Model.Phone</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.Fax))
                { <div>Fax: @Model.Fax</div> }
            </div>

        </td>
    </tr>