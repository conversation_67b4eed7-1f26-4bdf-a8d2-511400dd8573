@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMPatientRecallLetter

<table class="tbl-recall-letter-print">
    <thead>
        <tr>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @{Html.RenderPartial("~/Areas/VP/Views/VP/RecallLetters/_doctor_letter_head.cshtml", @Model.Doctor); }
        <tr>
            <td>
                <div>@Model.CurrentDate.ToShortDateString()</div>
                <div>First Reminder; You are due for @Model.TestName</div>   
                @{if (!String.IsNullOrWhiteSpace(@Model.DateLastServiced))
                    { <div>You had this procedure done last on @Model.DateLastServiced.</div> }}
                <div>@Model.Patient.FirstName @Model.Patient.LastName</div>
                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Address))
                { <div>@Model.Patient.Address</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.Patient.City))

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.PostalCode))

                <div style="clear:both"></div>

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Province))

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Country))

                <div style="clear:both"></div> if (!String.IsNullOrWhiteSpace(@Model.Patient.Phone))
                { <div>Phone: @Model.Patient.Phone</div> }
                <div class="letter-salutation">Dear @Model.Patient.Salutation @Model.Patient.LastName</div>

                <div class="letter-body">
                    <p>
                        I am writing to ask you to get screened for cervical cancer. This year, cervical cancer will be found in about 1,500 women in Canada and at least one woman will die every day from this disease. The good news is that you can take steps to protect yourself from cervical cancer by having regular Pap tests.
                    </p>
                    <p>
                        The Pap test is a screening test that looks for early warning signs of cervical cancer. As long as your test results are normal, you should have this screening test every three years. We will contact you about your Pap test result about a month after your test and another letter when it is time for your next cervical screening test
                    </p>
                    <p>
                        Our records indicate that you have not been screened in the last three years.  This is our first invitation to you.  If our records are incorrect or if you have a question; please don’t hesitate to contact us at the telephone number above.
                    </p>

                    <p>
                        Call us to book your Pap test appointment.
                    </p>

                    <p>
                        Having a Pap test is an important part of staying healthy. Cervical cancer can most often be prevented with regular screening and by having proper follow-up, if necessary.
                    </p>
                </div>
            </td>
        </tr>
    </tbody>
    <tfoot>
        <tr>
            <td>
                <div>Regards;</div>
                @* var signatureImg = ""; *@

                    {

                    }

                @if (!String.IsNullOrWhiteSpace(signatureImg))
                {
                    <div><img style="width:200px;height:75px;" src="@signatureImg" /></div>

                <div>@Model.Doctor.FullName</div>
                
            </td>            
        </tr>
    </tfoot>
</table>