@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMPatientRecallLetter

<table class="tbl-recall-letter-print">
    <thead>
        <tr>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @{Html.RenderPartial("~/Areas/VP/Views/VP/RecallLetters/_doctor_letter_head.cshtml", @Model.Doctor); }
        <tr>
            <td>
                <div>@Model.CurrentDate.ToShortDateString()</div>
                <div>Second Reminder; You are due for @Model.TestName</div>   
                @{if (!String.IsNullOrWhiteSpace(@Model.DateLastServiced))
                    { <div>You had this procedure done last on @Model.DateLastServiced.</div> }}
                <div>@Model.Patient.FirstName @Model.Patient.LastName</div>
                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Address))
                { <div>@Model.Patient.Address</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.Patient.City))

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.PostalCode))

                <div style="clear:both"></div>

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Province))

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Country))

                <div style="clear:both"></div> if (!String.IsNullOrWhiteSpace(@Model.Patient.Phone))
                { <div>Phone: @Model.Patient.Phone</div> }
                <div class="letter-salutation">Dear @Model.Patient.Salutation @Model.Patient.LastName</div>

                <div class="letter-body">
                    <p>
                        We recently wrote to you to remind you that your annual flu vaccination is now due.  This is our second attempt to reach you. 
                    </p>
                    <p>
                        Flu vaccination provides the best protection against an unpredictable virus which infects many people and can cause serious illness and death each year.  
                    </p>
                    <p>
                        Please phone us to book an appointment for your flu vaccination or to let us know if you received the vaccine from another healthcare provider so we can update your records.
                    </p>

                    <p>
                        he vaccination is free and recommended yearly for those most at risk of flu; including everyone aged 65 and over.
                    </p>

                    <p>
                        If there is someone you rely on to care for you, please ask them to contact their own physician as they are eligible for a free flu vaccination.
                    </p>
                    <p>
                        We look forward to seeing you soon.
                    </p>
                </div>
            </td>
        </tr>
    </tbody>
    <tfoot>
        <tr>
            <td>
                <div>Regards;</div>
                @* var signatureImg = ""; *@

                    {

                    }

                @if (!String.IsNullOrWhiteSpace(signatureImg))
                {
                    <div><img style="width:200px;height:75px;" src="@signatureImg" /></div>

                <div>@Model.Doctor.FullName</div>
                
            </td>            
        </tr>
    </tfoot>
</table>