@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMPatientRecallLetter

<table class="tbl-recall-letter-print">
    <thead>
        <tr>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @{Html.RenderPartial("~/Areas/VP/Views/VP/RecallLetters/_doctor_letter_head.cshtml", @Model.Doctor); }
        <tr>           
            <td>
                <div>@Model.CurrentDate.ToShortDateString()</div>
                <div>First Reminder; You are due for @Model.TestName.</div>             
                @{if (!String.IsNullOrWhiteSpace(@Model.DateLastServiced))
                    { <div>You had this procedure done last on @Model.DateLastServiced.</div> }}
                <div>Guardian of @Model.Patient.FirstName @Model.Patient.LastName</div>
                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Address))
                { <div>@Model.Patient.Address</div> }
                @if (!String.IsNullOrWhiteSpace(@Model.Patient.City))

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.PostalCode))

                <div style="clear:both"></div>

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Province))

                @if (!String.IsNullOrWhiteSpace(@Model.Patient.Country))

                <div style="clear:both"></div> if (!String.IsNullOrWhiteSpace(@Model.Patient.Phone))
                { <div>Phone: @Model.Patient.Phone</div> }
                <div class="letter-salutation">Dear Sir or Madam</div>

                <div class="letter-body">
                    <p>
                        This is our first attempt to reach you regarding the immunization schedule for @Model.Patient.FirstName
                    </p>
                    <p>
                        Childhood immunizations provide the best protection against many diseases which can cause serious illness and even death.  Immunizations can even stop or slow the spread of communicable diseases for not just your child but for their friends and family as well.
                    </p>
                    <p>
                        The province of Ontario pays for a standard set of immunizations for your child.  These immunizations are due when your child is 2 months, 4 months, 6 months, 12 months, 15 months, and 18 months old and are normally given during standard wellness appointments.
                    </p>

                    <p>
                        Please phone us to book an appointment for your child to be brought up to date on their immunizations or to let us know if you received them from another healthcare provider so we can update your child’s records.
                    </p>

                    <p>
                        If you have questions regarding the immunizations or have a religious/ethical objection, please call us to discuss.
                    </p>
                    <p>
                        We look forward to seeing you soon.
                    </p>
                </div>
            </td>
        </tr>
    </tbody>
    <tfoot>
        <tr>
            <td>
                <div>Regards;</div>
                @* var signatureImg = ""; *@

                    {

                    }

                @if (!String.IsNullOrWhiteSpace(signatureImg))
                {
                    <div><img style="width:200px;height:75px;" src="@signatureImg" /></div>

                <div>@Model.Doctor.FullName</div>
                
            </td>            
        </tr>
    </tfoot>
</table>