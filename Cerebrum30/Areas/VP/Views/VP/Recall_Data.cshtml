@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.VP.VMRecall
<script>
    $(document).ready(function () {

            "fixedHeader": true,
@* }); *@

        $("table").on("change", ":checkbox", function () {
@* $(this).parents("tr:first").css('background-color', 'lightgrey'); *@
@* }); *@
@* }); *@
</script>
<div class="row small ">
    <table id="table-recall" class="table table-condensed table-striped">
        <thead>
            <tr>
                <th><b>Select</b></th>
                <th><b>Patient Name</b></th>
                @*<th><b>Doctor</b></th>*@
                <th><b>Phone</b></th>
                <th><b>Contacted By Phone</b></th>
                <th><b>Date Contacted By Phone</b></th>
                <th><b>Letter1 Sent Status</b></th>@*>* @*<th><b>Name</b></th>*@
                <th><b>Procedure Type</b></th>
                <th><b>Status</b></th>
                <th><b>Last Contact Date</b></th>
                <th><b>Last Activity Date</b></th>
                <th><b>Last Service Date</b></th>
                <th><b>Notes</b></th>
                <th><b>Info</b></th>
                <th><b></b></th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < @Model.Patients.Count; i++)
            {
                @Html.PartialAsync("Recall_Row", @Model.Patients[i])
            }
        </tbody>
    </table>
</div>