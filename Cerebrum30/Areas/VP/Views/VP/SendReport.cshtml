﻿@model Cerebrum.ViewModels.VP.VP_VM
@{

}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>SendReport</title>
    <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css" rel="stylesheet">
    <script src="//code.jquery.com/jquery-1.12.0.min.js"></script>
    <!-- markItUp! RTE files -->
    <link rel="stylesheet" type="text/css" href="~/Scripts/markitup/skins/markitup/style.css">
    <link rel="stylesheet" type="text/css" href="~/Scripts/markitup/sets/default/style.css">
    <script src="~/Scripts/markitup/jquery.markitup.js"></script>
    <script type="text/javascript" src="~/Scripts/markitup/sets/default/set.js"></script>
    <!-- markItUp! RTE files -->
    <style>
        .divPreview {
/* border: solid 1px grey; */
/* width: 600px; */
/* margin-left: 15px; */
/* padding: 5px; */
        }
    </style>
</head>
<body>

    @{
        @Model.DataStr = @Model.ReportPhraseString + " " + @Model.VitalSignMeasurementString + " " + @Model.LabResultMeasurementString;

        @Model.DataStr = @Model.DataStr.Replace("\\n", "\r\n");
    }

    <script>

        $(document).ready(function () {

@* $("#DataStr").markItUp(mySettings); *@

            $(".prevLink").click(function () {

@* $("#output_div").html($("#DataStr").val().replace(/\n/g, '<br />')); *@
@* return false; *@
@* }); *@

@* }); *@

    </script>

    <br />

    <div class="row">
        <div class="col-md-4">
            <a class="prevLink" href="#">Preview</a>
        </div>
    </div>

    <br />

    @using (Html.BeginForm("SendReport", "VP", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { model = @Model }))
    {

        @Html.HiddenFor(x => x.ReportPhraseString)
        @Html.HiddenFor(x => x.VitalSignMeasurementString)
        @Html.HiddenFor(x => x.LabResultMeasurementString)

        @Html.HiddenFor(x => x.AppointmentID)
        @Html.HiddenFor(x => x.PatientID)

        <div class="text-center">
            <div class="row">
                <div class="col-md-6">

                    @Html.TextAreaFor(model => model.DataStr, new { cols = "80", rows = "20" })
                    <div class="divPreview text-left" id="output_div"></div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <input type="submit" value="Save" id="btnSend" />
                </div>
            </div>
        </div>

    <br />

    @using (Html.BeginForm("AmmendReport", "VP", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { model = @Model }))
@using Cerebrum.ViewModels.Patient
    {
        @Html.HiddenFor(x => x.ReportPhraseString)
        @Html.HiddenFor(x => x.VitalSignMeasurementString)
        @Html.HiddenFor(x => x.LabResultMeasurementString)

        @Html.HiddenFor(x => x.AppointmentID)
        @Html.HiddenFor(x => x.PatientID)

        <div class="text-center">
            <div class="row">
                <div class="col-md-4">
                    <input type="submit" value="Amend" id="btnAMend" />
                </div>
            </div>
        </div>

    <br />

    <div class="text-center">
        <div class="row">
            <div class="col-md-4">
                <span style="color:red">@Model.Message</span>
            </div>
        </div>
    </div>

</body>
</html>
