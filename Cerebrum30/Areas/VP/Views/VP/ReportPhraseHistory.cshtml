﻿@model IEnumerable<Cerebrum30.Areas.Measurements.Models.DataObjects.ReportPhraseHistoryData>
@{

}
<style>
    .smallFont {
/* font-size: 10px; */

    .top-legend {
/* display: block; */
/* padding-left: 0; */
/* margin-bottom: 0; */
/* list-style: none; */
/* padding-top: 10px; */
@* font-size: 10px; *@

        .top-legend li {
@* float: left; *@

        .top-legend .legend-label {
@* font-weight: bold; *@
@* padding-top: 3px; *@

        .top-legend .li-color-desc {
@* padding: 3px; *@
@* border-right: 1px solid #999999; *@
@* border-top: 1px solid #999999; *@
@* border-bottom: 1px solid #999999; *@
        }

        .top-legend li:nth-child(2) {
@* -webkit-border-top-left-radius: 3px; *@
@* -webkit-border-bottom-left-radius: 3px; *@
@* -moz-border-radius-topleft: 3px; *@
@* -moz-border-radius-bottomleft: 3px; *@
@* border-top-left-radius: 3px; *@
@* border-bottom-left-radius: 3px; *@
@* border-left: 1px solid #999999; *@

        .top-legend li:last-child {
@* -webkit-border-top-right-radius: 3px; *@
@* -webkit-border-bottom-right-radius: 3px; *@
@* -moz-border-radius-topright: 3px; *@
@* -moz-border-radius-bottomright: 3px; *@
@* border-top-right-radius: 3px; *@
@* border-bottom-right-radius: 3px; *@
@* border-left: none; *@

    .legend-name {
@* padding-right: 3px; *@

    .legend-color-container {
@* width: 15px; *@
@* height: 15px; *@
@* border-radius: 2px; *@
@* margin-top: 1px; *@
@* margin-right: 3px; *@

</style>

<div class="row">&nbsp;</div>
<div class="row">&nbsp;</div>

<div class="form-group text-center">
    <label class="label label-primary " style="font-size:14px;">@ViewBag.ReportPhraseName</label>
</div>

<div class="form-inline">
    <div class="form-group ">
        <ul class="top-legend">
            <li>
                <div class="pull-left legend-label">
                    <span class=""> &nbsp;</span>
                </div>
            </li>
            <li class="li-color-desc text-center">
                <div class="pull-left legend-name">Changed</div>
                <div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:green;">&nbsp;&nbsp;</div>
            </li>
            <li class="li-color-desc text-center">
                <div class="pull-left legend-name">Deleted</div>
                <div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:red;">&nbsp;&nbsp;</div>
            </li>
            <li class="li-color-desc text-center">
                <div class="pull-left legend-name">Added</div>
                <div class="pull-left legend-color-container" style="border:solid 1px gray;background-color:blue;">&nbsp;&nbsp;</div>
            </li>

        </ul>
    </div>

</div>

<div class="row text-center">

    <div class="row">&nbsp;</div>
    <div class="pre-scrollable">
        <table class="table table-condensed">
            <tr>
                <td style="width:100px;"><b>Name</b></td>
                <td style="width:150px;"><b>Log Date</b></td>
                <td style="width:400px;"><b>Value</b></td>
                <td><b>Difference</b></td>
            </tr>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.Name</td>
                    <td>@item.LogDate</td>
                    <td>
                        <textarea class="form-control">@item.Value</textarea>
                    </td>
                    <td>
                        <span class="spanCell">@Html.Raw(@item.Difference)</span>
                    </td>
                </tr>

        </table>
    </div>
    <div class="row">&nbsp;</div>
    <table class="table table-condensed">
        <tr>
            <td colspan="2"><b>Accumulative Value</b></td>
            <td colspan="2">
                <textarea>@ViewBag.AccumulativeValue</textarea>
            </td>
        </tr>
    </table>
    <div class="row">&nbsp;</div>
    <div class="row">&nbsp;</div>
    <a href="#" class="btn btn-default btn-sm btn-cancel-model">
        <i style="color:red" class="glyphicon glyphicon-remove"></i>&nbsp;Close
    </a>
    <div class="row">&nbsp;</div>
    <div class="row">&nbsp;</div>
</div>

