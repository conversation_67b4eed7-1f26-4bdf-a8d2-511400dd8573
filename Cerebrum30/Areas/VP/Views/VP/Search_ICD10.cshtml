﻿

@model Cerebrum.ViewModels.VP.VMICD10
<style>
    .popover {
        /*  max-width: 100%;  Max Width of the popover (depending on the container!)  */
    }

    .__8763453 {
        width: 539px !important; /*  temporary fix  */

</style>
<script type="text/javascript">
    $(document).ready(function () {
        //$('.select').click(function () {
        //    $("#VP_CPP_Problem_List_VM_Add_Diagnosis", parent.document).val('d');
        //    return false;
        //});
@* }); *@

@* $("#@Model.ControlIDToUpdate", parent.document).val(code); *@
@* return false; *@

    function searchICD10Results(element) {
        //e.preventDefault();
        //$("#div-ic-data").html('<img src="../../Content/fancybox_loading.gif" />');

        $.ajax({
            type: 'POST',
            url: url,
            data: $('#frm-ic-search').serialize(),
            success: function (data) {
@* $("#div-ic-data").html(data); *@
            }
@* }); *@

@* return false; *@
@* }; *@

    @*$(document).on('click', '#hl-fh-sel', function (e) {*@
@* e.preventDefault(); *@

@* $("#@Model.ControlIDToUpdateCode").val(code); *@

@* $("#@Model.ControlIDToUpdateName").val(name); *@

    }); </script>
<div class="___89354345">
    <!--  temporary fix -->

    @using (Html.BeginForm("Search_ICD10", "VP", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-ic-search", model = @Model  }))
    {
        @Html.HiddenFor(x => x.ID)
        @Html.HiddenFor(x => x.Name)
        @Html.HiddenFor(x => x.Code)
        @Html.HiddenFor(x => x.ControlIDToUpdateCode)
        @Html.HiddenFor(x => x.ControlIDToUpdateName)

        <div class="row">
            <div class="col-sm-2">
                ICD10 Search
            </div>
            <div class="col-sm-3">
                @Html.TextBoxFor(x => x.SearchString, new { @class = "txtBox min-width-100" })
            </div>
            <div class="col-sm-7">
                <a data-url="VP/VP/ICD10Results"

            </div>
        </div>
        <div>
            <div class="row">&nbsp;</div>
            <div id="div-ic-data" style="height: 300px;">&nbsp;</div>
        </div>

</div>