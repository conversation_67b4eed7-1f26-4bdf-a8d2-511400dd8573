@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMRecallLettersPrintView
<style>
    .tbl-recall-letter-print{
/* margin-bottom:15px; */
/* width: 100%; */
/* background-color:#ffffff; */
    }

    .tbl-recall-letter-print tr, .tbl-recall-letter-print tr td{        
/* background-color:#ffffff; */

    #patients-recall-letters{
@* max-height:500px; *@
@* width: 100%; *@
@* overflow:auto; *@
    }
</style>
@Html.ModalHeader("Recall Letter(s) Total: "+@Model.PatientsRecallLetters.Count() + " for "+@Model.ProcedureType)
<div class="modal-body">    

    <div id="patients-recall-letters">
        <style media="print">
            .tbl-recall-letter-print {                
@* page-break-after:always; *@
            }            
        </style>

        @foreach (var letter in @Model.PatientsRecallLetters)
        {
@* Html.RenderPartial("~/Areas/VP/Views/VP/RecallLetters/"+@Model.RecallViewName+".cshtml", letter); *@
        }
    </div>
</div>
<div class="modal-footer">
    <button id="btn-print-recall-letters" type="button" class="btn btn-default btn-sm btn-spacing">Print</button>
    <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
</div>

