﻿@model  Cerebrum30.Areas.VP.Models.ViewModels.VP_ReportPhrase_Val
@{

}
@section scripts {
    <script>
        $(document).ready(function () {
            @Model.ScriptToExecute
@* }); *@
    </script>

@using (Html.BeginForm("ReportPhraseCustomize", "VP", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-vp-rp-customize", model = @Model  }))
{
    <div class="text-center row form-inline">

        @Html.HiddenFor(x => x.ReportPhraseID)
        @Html.HiddenFor(x => x.UserID)
        @Html.HiddenFor(x => x.OriginalText)

        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>

        <div class="row">
            <div class="col-md-12">
                <div class="col-md-6"><label>Original Text</label></div>
                <div class="col-md-6 text-left">
                    @Html.DisplayTextFor(x => x.OriginalText)

                </div>
            </div>
        </div>

        <div class="row">&nbsp;</div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="col-md-6"><label>Original Value</label></div>
                <div class="col-md-6 text-left">
                    @Html.DisplayTextFor(x => x.Value)
                </div>
            </div>
        </div>

        <div class="row">&nbsp;</div>

        <div class="row">
            <div class="col-md-12">
                <div class="col-md-6"><label>Custom Text</label></div>
                <div class="col-md-6 text-left">
                    @Html.TextAreaFor(x => x.Text, new { @cols = "100", @rows = "5" })
                </div>
            </div>
        </div>

        <div class="row">&nbsp;</div>

        <div class="row">
            <div class="col-md-12 text-center">
                <span style="color:red">
                    @ViewBag.Message
                </span>
                <span id="span-reportphrasecustomize-mssg" style="color:red">

                </span>
            </div>
        </div>

        <div class="row">&nbsp;</div>

        <div class="row">
            <a href="#" data-url='@Url.Action("ReportPhraseCustomize")' class="btn btn-default" id="btn-Save-vp-phrases">Save</a>
            <a href="#" class="btn btn-primary btn-sm btn-cancel-model">Cancel</a>
        </div>
    </div>

    <br />
    <br />

