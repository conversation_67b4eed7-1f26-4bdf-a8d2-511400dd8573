@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.VP.VMImmunizationPatientRecord
<tr id="<EMAIL>">
    <td>
        <input data-dtserviced="@Model.DateServiced"
 *@ data-dtlastserviced="@Model.LastServiceDatePartial"

    </td>
    <td>
        @Model.RecallID
        @Model.PatientName
    </td>
    @*<td>@Model.DoctorName</td>*@
    <td>@Model.PhoneNumber</td>
    <td>
        <span id="<EMAIL>">
           @(@Model.ContactedByPhone.HasValue ? @Model.ContactedByPhone.Value ? "Yes" : "No" : "No")
        </span>
    </td>
    <td>
        <span id="<EMAIL>">            
            @(@Model.ContactDate.HasValue ? @Model.ContactDate.Value.ToString() : string.Empty )
        </span>
    </td>
    <td><b>@Model.Letter1Sent - @Model.Letter1SentDate</b></td>
    <td><b>@Model.Letter2Sent - @Model.Letter2SentDate</b></td>@* *@
    @*<td>@Model.Name</td>*@
    <td>@Model.VP_CPP_ImmunizationType</td>
    <td>@Model.VP_CPP_ImmunizationStatus</td>
    <td>
        <span id="<EMAIL>">
            @(@Model.DateServiced.HasValue ? @Model.DateServiced.Value.ToString() : string.Empty )
        </span>
    </td>
    <td>
        <span id="<EMAIL>">
            @(@Model.DateCreated.HasValue ? @Model.DateCreated.Value.ToString() : string.Empty )
        </span>
    </td>
    <td>
        <span id="<EMAIL>">
            @Model.LastServiceDatePartial (@Model.LastServiceDate.HasValue ? @Model.LastServiceDate.Value.ToString() : string.Empty )
        </span>
    </td>
    <td>
        <textarea class="form-control input-xs small" id="<EMAIL>">@Model.Notes</textarea>
    </td>
    <td>
        <a 

            <i style="color:red" class="glyphicon glyphicon-info-sign"></i>
        </a>
    </td>
    <td>
        <a data-toggle='tooltip' data-placement='bottom' title='View immunizations'

            <i style="color:grey" class="glyphicon glyphicon-option-vertical"></i>
        </a>
    </td>
    <td>
        <a href="#" data-id="@Model.Id" id="btn-update-note" class="btn btn-default btn-xs">
            <i style="color:grey" class="glyphicon glyphicon-edit"></i>
            Update Note
        </a>
    </td>
</tr>
