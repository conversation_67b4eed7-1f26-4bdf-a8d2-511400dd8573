@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMTemplatePatientDataItem
@{

    <div>

        @if (!string.IsNullOrWhiteSpace(@Model.Value))
        {

            {

                if (vals.Contains(@Model.Value))
                {

@* bool.TryParse(@Model.Value, out v); *@
                    <label>@Html.Raw(v?"YES":"NO")</label>
                }
                else
                {
                    <label>@Html.Raw(@Model.Value)</label>

            else if (@Model.IsText)
            {
                <textarea rows="4" class="form-control ">@Model.Value</textarea>

            else
            {

                <label>@Model.Value </label>

                if (@Model.OutSideNormal)
                {

                    <i tooltip="Out of Normal"

                if (@Model.OutSideTarget)
                {
                    <i tooltip="Out of Target"

                if (@Model.OverDue)
                {
                    <i tooltip="Out of Treatment Interval"

"OverDue"@*"OverDue" *@

                }

    </div>

