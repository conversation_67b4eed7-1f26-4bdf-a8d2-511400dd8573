@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VP_VM
<style>
    /@*.cpp-list li{*@

/* margin:0; */
/* padding:1px; */
    }*/
    /*.skipLetter .active {
/* background-color: #428bca; */
/* border-color: #357ebd; */
/* color: #ffffff; */
    }@*/*@
</style>

<div class="modal fade in col-sm-12" id="modal-template-id" z-index="0">
    <div class="modal-dialog col-sm-12" id="modal-template-content-id" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
        <div class="modal-content col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
            <div class="modal-header no-borders col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                    <h4 class="modal-title" id="modal-template-title-id" style="padding-left: 16px;"></h4>
                </div>
                <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times; &nbsp;&nbsp;&nbsp;</span>
                    </button>
                </div>
            </div>
            <div class="modal-body col-sm-12" id="modal-template-body-id" style="overflow-y: auto;"></div>
        </div>
    </div>
</div>

<div class="modal fade in col-sm-12" id="modal-template-id2" z-index="0">
    <div class="modal-dialog col-sm-12" id="modal-template-content-id2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
        <div class="modal-content col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
            <div class="modal-header no-borders col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                    <h4 class="modal-title" id="modal-template-title-id2" style="padding-left: 16px;"></h4>
                </div>
                <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times; &nbsp;&nbsp;&nbsp;</span>
                    </button>
                </div>
            </div>
            <div class="modal-body col-sm-12" id="modal-template-body-id2" style="overflow-y: auto;"></div>
        </div>
    </div>
</div>

@if (@Model.CPP_Categories.Count > 0)
{

    <div class="panel panel-info">
*>*@@
    <div class="panel-heading">CPP</div>
    <div class="row panel-body">

        <ol class="cpp-list  _56777">
            @for (int i = 0; i < @Model.CPP_Categories.Count; i++)
                {

                    switch (@Model.ReportPhrases.Count)
                    {

                        case 1:

@* break; *@

                        case 2:

@* break; *@

                        case 3:

@* break; *@

                        case 4:

@* break; *@

                        default:
@* break; *@

                <li class="col-sm-@size">
                    <div class="form-inline">
                        <div class="form-group">
                            <span data-toggle="tooltip"

                                @Model.CPP_Categories[i].Text
                            </span>

                                </span>
                            <a href="javascript:void(0)" data-toggle="tooltip" data-original-title="Edit" categoryid="@Model.CPP_Categories[i].Id"

                                <i style="margin-top: -7px;" class="vert-center glyphicon glyphicon-pencil ___ c-pointer"></i>
                            </a>

                            <div style="display: inline" data-toggle="tooltip"

                                <i style="margin-top: -7px;" data-selected=@(@Model.CPP_Categories[i].Skipped ? "1" : "0")

                                               @(@Model.CPP_Categories[i].Skipped ? "glyphicon-remove color-red" : "glyphicon-ok color-green")
                                               btn-apptest-status
                                               c-pointer"></i>
                            </div>

                        </div>

                                <div data-toggle="tooltip"

                                    <i data-selected=@(@Model.CPP_Categories[i].Skipped ? "1" : "0")

                                               @(@Model.CPP_Categories[i].Skipped ? "glyphicon-remove color-red" : "glyphicon-ok color-green")
                                               btn-apptest-status
                                               c-pointer"></i>
                                </div>@* *@
                            </div>
                    </div>
                    <div class="row marginBottom">
                        <div class="col-sm-12"> if (@Model.CPP_Categories[i].Id == 5) //immunization
                                {
                                    <div class='cpp-immun-div @(@Model.CPP_Categories[i].IsHighlighted ? "bk-color-aquamarine" : "")' onclick='javascript:OpenWindow("@Model.CPP_Categories[i].Id" ,"@Model.PatientID")'>
                                        <span class="txtArea cppBox " style="height:100px;overflow:auto">
                                            @Html.Raw(@Model.CPP_Categories[i].Value)
                                        </span>
                                    </div>

                                else
                                {
                            @* var className = " cppBox " + (@Model.CPP_Categories[i].IsHighlighted ? "bk-color-aquamarine" : ""); }
                            <div categoryid="@Model.CPP_Categories[i].Id"

                                @Html.Raw(@Model.CPP_Categories[i].Value.Replace("\r\n", "<br />"))
                            </div>
*@Html.TextAreaFor(x => x.CPP_Categories[i].Value, new
                                {
                                    @class = "txtArea cppBox " + (@Model.CPP_Categories[i].IsHighlighted ? "bk-color-aquamarine" : ""),*  categoryid = @Model.CPP_Categories[i].Id, *  patientid = @Model.PatientID,
                                    @rows = 9,
                                    @cols = 50 ,

                                })@* *@
                            @*}* </div>
                    </div>
                </li>

        </ol>
        @* <div class="clearfix">&nbsp;</div> *@
        </div>

            NOTE: Double click any field to Add/Edit
        </div>

</div>

    <script>

        $(document).ready(function () {

                //console.log('adjust cppBox class height if  <=4');
                $('.cppBox').css('min-height','150px')

            $(".cppBoxEdit").on("click", function () {

@* OpenWindow(catid, patientID); *@
@* }); *@

            $(".cppBox, .cppBoxEdit").on("dblclick", function () {

                //console.log(patientID, catid);
@* OpenWindow(catid, patientID); *@
@* }); *@

            $(document).on('click', '.btn-cancel-model', function (e) {
@* e.preventDefault(); *@
@* $('#vp-modal-container').modal('hide'); *@
@* }); *@

            //add following code to prevent multiple modals overlay issue
            $(document).on('show.bs.modal', '.modal', function (event) {*@ var zIndex = 1040 + (10 *@ $('.modal:visible').length); *@ $(this).css('z-index', zIndex);
@* setTimeout(function () { $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack'); }, 0); *@
@* }); *@
@* }); *@

        function ICD10Clicked (element) {

            //$("#" + modalTemplateContentId).width(width);
            //$("#" + modalTemplateBodyId).height(height);
@* $("#" + modalTemplateTitleId2).text("ICD10"); *@

@* $('#ajax-loader').show(); *@
            $("#" + modalTemplateBodyId2).load(url, function (response, status, xhr) {
@* $('#ajax-loader').hide(); *@

@* checkAjaxError(xhr, modalTemplateId2); *@
                } else {
@* $("#" + modalTemplateId2).on("shown.bs.modal", function () { }); *@
@* $("#" + modalTemplateId2).on("hidden.bs.modal", function () { }); *@
@* $("#" + modalTemplateId2).modal({ keyboard: false, backdrop: "static" }, "show"); *@

@* }); *@

@* return false; *@

        function cppButtonDeleteClicked (element) {

@* $("#" + modalTemplateTitleId).text("Delete"); *@
            ajaxCall(url, data, false, function (data) {
@* $("#" + modalTemplateBodyId).html(data); *@
@* $("#" + modalTemplateId).on("shown.bs.modal", function () { }); *@
@* $("#" + modalTemplateId).on("hidden.bs.modal", function () { }); *@
@* $("#" + modalTemplateId).modal({ keyboard: false, backdrop: "static" }, "show"); *@
@* }); *@

@* return false; *@
@* }; *@

        function initPopover() {
            $(".btn-popover-container").each(function () {

                $(btn).popover({
                    html: true,
                    title: title,
                    content: content,
                    placement: 'auto left',
                    trigger: 'manual'//,
                    //container:'body'
                }).on("mouseenter", function () {

@* $(this).popover("show"); *@
                    $(this).siblings(".popover").on("mouseleave", function () {
@* $(_this).popover('hide'); *@
@* }); *@
                }).on("mouseleave", function () {

                    setTimeout(function () {
                        if (!$(".popover:hover").length) {
@* $(_this).popover("hide"); *@
                        }
@* }, 100); *@
@* }); *@
@* }); *@

    </script>

