﻿@model IEnumerable<Cerebrum.ViewModels.VP.VMTemplateDetail>

<table cellpadding="0" class="table custom-table table-condensed __77654 spacer-btm-7">
    <tr>
        <td>
            @*<p>*@
                    <span class="label label-primary label-app-type">
                        CDF
                    </span>
                </p>
            <div>
                <ol>
                    @* var ModelList = @Model.ToList(); *@

                    @for (int j = 0; j < @Model.Count(); j++)
                    {
                        <li class="col-sm-2">
                            <div>
                                <div style="">

                                    <span class="hideOverflowMeas small "

                                        @ModelList[j].TemplateItemName
                                    </span>

                                    @*TextBoxFor*@
                                    @* bool isUnit = false;//mutli-line *@
                                        if (!string.IsNullOrEmpty(ModelList[j].Units))
                                        { *@ isUnit = true; }

                                    @if (ModelList[j].ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo)
                                    {

                                        if (bool.TryParse(ModelList[j].Value, out chcked))
                                        {
                                            @Html.CheckBox(nm, chcked)
                                        }

                                        {
                                            @Html.CheckBox(nm, false)

                                        else
                                        {
                                            @Html.CheckBox(nm, false)

                                        @Html.Hidden(nmvt, ModelList[j].ValueType)

                                    else
                                    {

                                        @Html.TextAreaFor(x => @ModelList[j].Value, new)
                                                   {
                                                       @class = " txtBox vp-meas-box meas-cdf-item",
                                                       @data = @ModelList[j].TemplateItemName,
                                                       @readonly = "readonly",
                                                       @data_toggle = "tooltip",
                                                       @data_placement = "right",
                                                       @title = ModelList[j].Value,
                                                       @style = "white-space:pre; resize: none; overflow:hidden;padding:2px",
                                                       @isunit = @isUnit,
                                                       @rows = 1
                                                   })
                                    } if (!string.IsNullOrEmpty(@Model.vm_cdf.TemplateDetails[j].Units))
                                    <div style="display: inline-block; position: absolute; margin-left: 3px;">
                                        @if (isUnit)
                                        {
                                            <a style="margin-top: -2px"*@ data-toggle="tooltip" *  data-placement="right"

                                                (@ModelList[j].Units)
                                            </a>

                                        @if (ModelList[j].OverDue)
                                        {

                                    </div> if (!string.IsNullOrEmpty(@Model[j].ErrorMessage))
                                            {
                                                <br />
                                                <span class="label label-danger">@Model[j].ErrorMessage </span>

                                </div>
                            </div>
                        </li>

                </ol>
            </div>
    </tr>
</table>

