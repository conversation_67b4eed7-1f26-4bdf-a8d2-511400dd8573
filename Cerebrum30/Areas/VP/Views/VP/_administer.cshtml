﻿@model Cerebrum.ViewModels.VP.VMVPAdminister

@{

}
@using (Html.BeginForm("AdministerImmunization", "vp", new { area = "vp" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-administer-procedure" }))
@using Cerebrum.ViewModels.Patient
{

    @Html.ModalHeader("Administer " + @Model.ProcedureType)
    <div class="modal-body">
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PatientId)
    @Html.HiddenFor(model => model.PatientName)
    @Html.HiddenFor(model => model.ProcedureTypeId)
    @Html.HiddenFor(model => model.ProcedureType)
    @Html.HiddenFor(model => model.Frequency)
    @Html.HiddenFor(model => model.AgeFrom)
    @Html.HiddenFor(model => model.AgeTo)
    @Html.HiddenFor(model => model.Gender)
    @Html.HiddenFor(model => model.DoctorId)

    <div class="form-horizontal">
        
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group form-group-sm">           
                @Html.LabelFor(model => model.PatientName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <p class="form-control-static">@Html.DisplayFor(model => model.PatientName)</p>
                </div>            
        </div>

        <div class="form-group form-group-sm">
            <label class="control-label col-md-2">Start Date</label>
            <div class="col-md-4">
                @Html.EditorFor(model => model.DateServicedDay, new { htmlAttributes = new { @class = "form-control pull-left xs-txt-box" } }) <div class="pull-left" style="padding-top:4px; padding-left:2px;">-</div>
                @Html.ValidationMessageFor(model => model.DateServicedDay, "", new { @class = "text-danger" })

                @Html.EditorFor(model => model.DateServicedMonth, new { htmlAttributes = new { @class = "form-control pull-left xs-txt-box" } }) <div class="pull-left" style="padding-top:4px; padding-left:2px;">-</div>
                @Html.ValidationMessageFor(model => model.DateServicedMonth, "", new { @class = "text-danger" })

                @Html.EditorFor(model => model.DateServicedYear, new { htmlAttributes = new { @class = "form-control pull-left sm-txt-box" } }) <div class="pull-left" style="padding-top:4px; padding-left:2px;">DD-MM-YYYY</div>
                @Html.ValidationMessageFor(model => model.DateServicedYear, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.DateServiced, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.EditorFor(model => model.DateServiced, new { htmlAttributes = new { @class = "form-control date-picker" } })
                @Html.ValidationMessageFor(model => model.DateServiced, "", new { @class = "text-danger" })
            </div>
        </div>             

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.statusId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.statusId, new SelectList(ViewBag.Statuses, "Id", "Status", @Model.statusId), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.statusId, "", new { @class = "text-danger" })
            </div>
        </div>               
        
        <div class="form-group">            
            @Html.LabelFor(model => model.AdministerNotes, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.TextAreaFor(model => model.AdministerNotes, new { @class = "form-control", @rows = 3 })
                @Html.ValidationMessageFor(model => model.AdministerNotes, "", new { @class = "text-danger" })
            </div>

            @Html.LabelFor(model => model.AdministerReason, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.TextAreaFor(model => model.AdministerReason, new { @class = "form-control", @rows = 3 })
                @Html.ValidationMessageFor(model => model.AdministerReason, "", new { @class = "text-danger" })
            </div>
        </div>   
        
        <hr />
        <div class="form-group form-group-sm">
            <div class="form-group form-group-sm">
                <label class="col-md-2 control-label">Procedure Details</label>
                <div class="col-md-10"></div>
            </div>
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.Gender, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-2">
                <p class="form-control-static">@Html.DisplayFor(model => model.Gender)</p>
            </div>

            <label class="col-md-2 control-label">Age</label>
            <div class="col-md-2">
                <p class="form-control-static">@Html.DisplayFor(model => model.AgeFrom) - @Html.DisplayFor(model => model.AgeTo) <span class="age-dsc"> @ageDsc</span></p>
            </div>

            @Html.LabelFor(model => model.Frequency, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-2">
                <p class="form-control-static">@Html.DisplayFor(model => model.Frequency)</p>
            </div>
        </div>   
        
    </div>
    </div>
    @Html.ModalFooter("Administer", "blue")

