@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMCPPMain
<style>
    
</style>

@if (@Model.CPP_Categories.Count > 0)
{
    <div class="panel panel-info">
       
    <div class="panel-heading">CPP <span class="btn-refresh-meds glyphicon glyphicon-refresh c-pointer" data-toggle="tooltip" data-placement="bottom" title="Refresh CPP">&nbsp;</span></div>
    <div class="row panel-body">

        <ol class="cpp-list  _56777">
            @for (int i = 0; i < @Model.CPP_Categories.Count; i++)
            {

                <li class="cpp-col-size">
                    <div class="form-inline">
                        <div class="form-group">
                            <span data-toggle="tooltip"

                                @Model.CPP_Categories[i].Text
                            </span>                            

                                <i style="margin-top: -7px;" class="vert-center glyphicon glyphicon-pencil ___ c-pointer"></i>@* *@
                            </a>
                            <div class="form-group form-group-sm">     

                                <div style="margin-top: -7px;margin-left:5px;" 

                            </div>
                        </div>
                        
                    </div>
                    <div class="row marginBottom">
                        <div class="col-sm-12">                            

                            @{

                            }
                            <div categoryid="@Model.CPP_Categories[i].Id"

                                @Html.Raw(@Model.CPP_Categories[i].Value.Replace("\r\n", "<br />"))
                            </div>                           
                        </div>
                    </div>
                </li>

        </ol>
        
    </div>
    
</div>

    <script>

        $(document).ready(function () {

@* $('.cppBox').css('min-height','150px'); *@
            }           
@* }); *@
       
    </script>

