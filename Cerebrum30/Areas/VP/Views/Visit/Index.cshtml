@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMVPMain
@{

@section customcss {
    <!-- TODO: Replace Styles.Render with direct link references for ASP.NET Core -->

@section topscripts {

@section scripts
{
    @if (isValidVP)
    {
        <!-- TODO: Replace Scripts.Render with direct script references for ASP.NET Core -->
    }
    <script src="~/Scripts/autosize.min.js"></script>

@section patientinfo {
    @if (isValidVP)
    {
        @Html.GetPatientInfo(@Model.PatientId, @Model.AppointmentId, @Model.AppointmentTestId, @Model.TestId)
    }
}

@section topLinks {
    @if (isValidVP)
    {
        <div class="container-fluid">
            <div id="toplink-container">
                <div id="toplink-content">
                    <div id="div-links" class="vp-links spacer-top-10">
                        @* Html.RenderPartial("_vplinks", @Model.LinksMain); *@
                    </div>
                </div>
            </div>
        </div>

<div id="div-vp-main-messages" class="spacer-top-10">
    @if (!isValidVP)
    {
        <script type="text/javascript">
            $(function () {

@* divMessages.html(messageHTML); *@
@* }); *@
        </script>

</div>

@if (isValidVP)
{

    {
        @autocomplete = "off",
        @id =
    "frm-vp"
    }))
    {
        <div id="test-base-wrapper">
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.AppointmentTestLogId)
            @Html.HiddenFor(m => m.PrevAppointmentTestLogId)
            @Html.HiddenFor(m => m.PrevAppTestLogIdDoctor)
            @Html.HiddenFor(m => m.AppointmentTestLogDate)
            @Html.HiddenFor(m => m.AppointmentTestId)
            @Html.HiddenFor(m => m.AppointmentId)
            @Html.HiddenFor(m => m.AppointmentTime)
            @Html.HiddenFor(m => m.AppointmentStatus)
            @Html.HiddenFor(m => m.PatientId)
            @Html.HiddenFor(m => m.PracticeId)
            @Html.HiddenFor(m => m.OfficeId)
            @Html.HiddenFor(m => m.PracticeTemplateId)
            @Html.HiddenFor(m => m.PracticeDoctor)
            @Html.HiddenFor(m => m.PracticeDoctorId)
            @Html.HiddenFor(m => m.PracticeDoctorUserId)
            @Html.HiddenFor(m => m.PracticeDoctorSpecialtyId)
            @Html.HiddenFor(m => m.ExternalDoctorId)
            @Html.HiddenFor(m => m.PatientSalutation)
            @Html.HiddenFor(m => m.PatientFirstName)
            @Html.HiddenFor(m => m.PatientLastName)
            @Html.HiddenFor(m => m.PatientAge)
            @Html.HiddenFor(m => m.PatientAgeStr)
            @Html.HiddenFor(m => m.PatientGender)
            @Html.HiddenFor(m => m.PatientDOB)
            @Html.HiddenFor(m => m.TestId)
            @Html.HiddenFor(m => m.TestGroupId)
            @Html.HiddenFor(m => m.TestStatusId)
            @Html.HiddenFor(m => m.TestStatusIdSave)
            @Html.HiddenFor(m => m.TestTime)
            @Html.HiddenFor(m => m.TestStatus)
            @Html.HiddenFor(m => m.TestStatusCSS)
            @Html.HiddenFor(m => m.IsAmended)
            @Html.Hidden("IsTrainee", cerebrumUser.IsTrainee)
            @Html.Hidden("SaveType", 1)@* default is regular save*@
            @Html.HiddenFor(m => m.RootPhraseFormat)
            @Html.HiddenFor(m => m.ReportPhraseFormatTable, new { @id = "RootPhraseFormatTable" })
        </div>
        <div id="vp-main-wrapper">

            <div id="div-billing" class="spacer-top-10">
                @* Html.RenderPartial("_billingCodes", @Model.BillingMain); *@

            </div>

            <div id="div-cpp-meds">
                @* Html.RenderPartial("_cppMedsMain", @Model.CPPMedsMain); *@
            </div>

            <div id="div-phrases" class="spacer-top-10">
            </div>

                    @{Html.RenderPartial("_measurements", @Model.MeasurementsMain);}
                </div>
            <div id="div-meas">
                @* Html.RenderPartial("_measurements_", @Model.MeasurementsMain); *@
            </div>

            <div class="row">&nbsp;</div>

            <div id="vp-cc-doctor-holder"></div>

            <div class="row">&nbsp;</div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="text-left nopadding form-group form-inline form-group-sm">
                        <span><b>Log</b></span>
                        <select style="min-width:100px" class="dropdown form-control" name="drp-vp-app-logs"

                            @foreach (var dbLog in @Model.AppointmentTestLogsLKP)
                            {

                                <option data-practice-template-id="@dbLog.PracticeTemplateId"

                            }

                        </select>
                    </div>
                </div>

                <div class="col-sm-6" id="save-buttons">
                    <div class="form-inline pull-right">
                        <div class="form-group form-group-sm _toCloneIntoVP2">
                            <div class="save-btn-wrapper">

                                @if (cerebrumUser.HasPermission("Save VP"))
                                {

                                    <div style="margin-right:15px;padding:5px;"

                                        <span class="status-desc-holder">Status: @Model.TestStatus</span>
                                        @if (@Model.TestStatusId !=
                                                                    (int)AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.ReportCompleted

                                                                    (int)AwareMD.Cerebrum.Shared.Enums.AppointmentTestStatuses.BeingSent)
                                        {
                                            <div class="btn-popover-container">
                                                <button type="button" class="btn btn-default btn-xs popover-btn">
                                                    <span class="glyphicon glyphicon-pencil text-primary"></span>
                                                </button>
                                                <div class="btn-popover-title">Test Status</div>
                                                <div class="btn-popover-content">
                                                    <ul class="ul-appstatus"></ul>
                                                </div>
                                            </div>

                                    </div>

                                    <button type="button"

                                        <i class="glyphicon glyphicon-floppy-save"></i>
                                        Save
                                    </button>

                                @if (cerebrumUser.HasPermission("SendLetter"))
                                {
                                    <a data-cb-tp="tooltip"

                                        Save Chart Note
                                    </a>
                                    if (!isBeingSent)
                                    {
                                        <a data-ds-url="@Url.Action("index", "daysheet", new { Area = "Schedule", OfficeId = @Model.OfficeId, Date = appDateStr })"

                                            <i class="glyphicon glyphicon-share-alt"></i>
                                            Send Letter
                                        </a>

                            </div>
                        </div>
                    </div>

                </div>

            </div>

        </div>
    }// end using

