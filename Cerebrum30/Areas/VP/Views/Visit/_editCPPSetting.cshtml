﻿@model  Cerebrum.ViewModels.VP.VMCPPSettingMain

@using (Html.BeginForm("EditCPPSetting", "Visit", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-cpp-setting" }))
{
    @Html.PartialAsync("_validationSummary")
    <div>       
        @Html.HiddenFor(model => model.ExternalDoctorId)
        <div class="row ">
            <div class="col-md-12 ">
                <table class="table  tbl-editCPP tbl-sortable-tbody">
                    <thead>
                        <tr class="tbl-header-item">
                            <th align="center">
                                <div class="text-center">Text</div>
                            </th>                           
                            <th align="center">
                                <div class="text-center">Visible</div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < @Model.CategorySettings.Count; i++)
                        {
                            <tr>
                                <td>
                                    <input type="hidden" name="CategorySettings.Index" value="@i" />
                                    @Html.HiddenFor(model => model.CategorySettings[i].Id)
                                    @Html.HiddenFor(model => model.CategorySettings[i].Group)
                                    @Html.HiddenFor(model => model.CategorySettings[i].CustomText)
                                    @Html.HiddenFor(model => model.CategorySettings[i].Text)
                                    @Html.HiddenFor(model => model.CategorySettings[i].Skipped)
                                    @Html.HiddenFor(model => model.CategorySettings[i].Order)
                                    <span class="glyphicon glyphicon-resize-vertical"> </span>
                                    @Html.DisplayTextFor(model => model.CategorySettings[i].Text)                                   
                                  
                                </td>                               
                                <td>
                                    @Html.CheckBoxFor(model => @Model.CategorySettings[i].Visible)
                                </td>
                            </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="row " style="margin-top: 15px">
            <div class="col-md-12 text-right">
                <button type="submit" href="#" class="btn btn-default btn-sm btn-primary modal-submit-btn" id="btn-save-cpp-setting">Save</button>                
            </div>
        </div>
    </div>

