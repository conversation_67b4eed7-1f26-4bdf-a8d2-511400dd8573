﻿@model Cerebrum.ViewModels.TestBase.VMRootCategoryCustom

@{

<ul class="nav drp-container">
    <li class="dropdown">       
    <a class="dropdown-toggle rep-phrase-header-holder" href="#" data-toggle="dropdown">
        <span class="rep-phrase-header drp-header">@categoryName</span>
    </a>           

        @if (rootCategory.PhrasesLoaded && !String.IsNullOrWhiteSpace(rootCategory.PhrasesHTML))
        {
            @Html.Raw(rootCategory.PhrasesHTML)
        }
        else if (rootCategory.PhrasesLoaded)
        {
            @Html.PartialAsync("_rootCategoryItemList", rootCategory)

    </li>
</ul>

