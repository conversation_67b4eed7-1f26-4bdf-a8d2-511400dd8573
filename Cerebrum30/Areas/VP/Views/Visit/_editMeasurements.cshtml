﻿@model Cerebrum.ViewModels.VP.VMMeasurementCustom

@using (Html.BeginForm("SaveMeasurements", "visit", new { area = "VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new
{
    @id =
"frm-save-custom-measurements"
}))
{
    @Html.HiddenFor(x => x.ExternalDoctorId)
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default content-height200">
                <div class="panel-heading">Vitals (@Model.VitalItems.Count())</div>
                <table class="table table-condensed table-bordered tbl-sortable-tbody">
                    <thead>
                        <tr>
                            <th></th>
                            <th style="width:10%"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < @Model.VitalItems.Count; i++)
                        {
                            <tr>
                                <td>
                                    <input type="hidden" name="VitalItems.Index" value="@i" />
                                    @Html.HiddenFor(x => x.VitalItems[i].Value)
                                    @Html.HiddenFor(x => x.VitalItems[i].Text)
                                    @Html.HiddenFor(x => x.VitalItems[i].Code)
                                    @Html.HiddenFor(x => x.VitalItems[i].Group)
                                    <span class="glyphicon glyphicon-resize-vertical"> </span>
                                    @Html.DisplayTextFor(model => model.VitalItems[i].Text)
                                </td>
                                <td>
                                    @Html.CheckBoxFor(model => model.VitalItems[i].Selected)
                                </td>
                            </tr>

                    </tbody>
                </table>
            </div>
            <div class="panel panel-default content-height200">
                <div class="panel-heading">Labs (@Model.LabItems.Count())</div>
                <table class="table table-condensed table-bordered tbl-sortable-tbody">
                    <thead>
                        <tr>
                            <th></th>
                            <th style="width:10%"></th>
                        </tr>
                    </thead>

                    <tbody>
                        @for (int i = 0; i < @Model.LabItems.Count; i++)
                        {
                            <tr>
                                <td>
                                    <input type="hidden" name="LabItems.Index" value="@i" />
                                    @Html.HiddenFor(x => x.LabItems[i].Value)
                                    @Html.HiddenFor(x => x.LabItems[i].Text)
                                    @Html.HiddenFor(x => x.LabItems[i].Code)
                                    @Html.HiddenFor(x => x.LabItems[i].Group)
                                    <span class="glyphicon glyphicon-resize-vertical"> </span>
                                    @Html.DisplayTextFor(model => model.LabItems[i].Text)
                                </td>
                                <td>
                                    @Html.CheckBoxFor(model => model.LabItems[i].Selected)
                                </td>
                            </tr>

                    </tbody>

                </table>
            </div>
        </div>
    </div>

    <div style="margin-top:15px;" class="text-right">
        <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
    </div>

