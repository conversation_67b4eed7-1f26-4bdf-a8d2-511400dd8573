﻿@model Cerebrum.ViewModels.TestBase.VMRootCategoryPhraseMain

@using (Html.BeginForm("EditPhraseSubCategories", "Visit", new { area = "VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-EditPhraseSubCategories" }))
@using Cerebrum.ViewModels.Patient
{

    <div class="small">
        @Html.PartialAsync("_validationSummary")
        @Html.HiddenFor(x => x.ExternalDoctorId)       
        @Html.HiddenFor(x => x.CategoryName)
        @Html.HiddenFor(x => x.AppointmentId)
        @Html.HiddenFor(x => x.RootCategoryId)
        @Html.HiddenFor(x => x.PhraseId)
        @Html.HiddenFor(x => x.ParentId)
        @Html.HiddenFor(x => x.PatientId) 
        @Html.HiddenFor(x => x.PracticePhraseId) 
        @Html.HiddenFor(x => x.PracticeTemplateId) 
        @Html.HiddenFor(x => x.GroupId)       
        
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default content-height500">
                    <div id="dr-root-phrases-total" data-total="@totalSubCategories" data-row-count="@totalSubCategories" data-update-text="Total Sub Categories" class="panel-heading row-count-holder">Total Sub Categories (@totalSubCategories)</div>
                    <table id="tbl-phrase-sub-categories" class="table table-condensed table-bordered tbl-sortable-tbody">
                        <thead>
                            <tr>
                                <th>
                                    <label>Text</label>
                                </th>     
                                <th>
                                    <label>Value</label>
                                </th> 
                                <th>
                                    Category
                                </th>                              
                                <th style="width:10%;">

                                </th>   
                                <th></th>                            
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < @Model.SubCategories.Count; i++)
                            {

                                <tr id="<EMAIL>[i].PhraseId">
                                    <td>
                                        <input type="hidden" name="SubCategories.Index" value="@i" />
                                        @Html.HiddenFor(x => x.SubCategories[i].DoctorRootCategoryPhraseId)
                                        @Html.HiddenFor(x => x.SubCategories[i].PhraseId)
                                        @Html.HiddenFor(x => x.SubCategories[i].RootCategoryId)
                                        @Html.HiddenFor(x => x.SubCategories[i].PracRootCatPhraseId)                                        
                                        @Html.HiddenFor(x => x.SubCategories[i].Text)
                                        @Html.HiddenFor(x => x.SubCategories[i].Rank)
                                        @Html.HiddenFor(x => x.SubCategories[i].IsCategory)
                                        @Html.HiddenFor(x => x.SubCategories[i].IsDeleted, new { @class="is-deleted" })
                                        <span class="glyphicon glyphicon-resize-vertical"> </span>
                                        @Html.DisplayTextFor(model => model.SubCategories[i].Text)
                                    </td>
                                    <td>@Html.DisplayTextFor(model => model.SubCategories[i].Value)</td>
                                    <td>@Html.DisplayFor(model => model.SubCategories[i].IsCategory)</td>
                                    <td>
                                        @Html.CheckBoxFor(model => model.SubCategories[i].Visible, new { @class = "chk-visible" })
                                    </td>
                                    <td>
                                        <button data-item-name="@name" type="button" class="btn btn-danger btn-xs btn-delete-dr-phrase">Delete</button>
                                    </td>
                                </tr>

                        </tbody>

                    </table>
                </div>
            </div>

        </div>

        @if (totalSubCategories > 0)
        {
            <div style="margin-top:15px;" class="text-right">
                <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
            </div>
        }
    </div>

