﻿@model Cerebrum.ViewModels.TestBase.VMPreviousTestsMain

<script type="text/javascript">
    $(function () {

@* setTestImpressions(); *@
@* }); *@
</script>

<div id="divPreTest-holder" class="form-inline">
    <div class="form-group"><span class="btn-testhistory-main glyphicon glyphicon-refresh c-pointer" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Refresh Test History"></span></div>
    <div id="divPrevTests" class="form-group drp-container">
        <div class="dropdown">
            <button class="btn btn-default btn-sm custom-menu-link" type="button" aria-haspopup="true" aria-expanded="true">
                <span class="menuText">Test History</span>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu menuText">
                @foreach (var item in @Model.PreviousTests)
                {

                    <li class="dropdown-submenu">
                        <a class="hl-test-link-open"

                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Status: @testStatus" class="appointment-status-box c-pointer @testStatusCSS">&nbsp;&nbsp;</span>
                            <span class="@linkColor">@testDateStr:@testName</span>
                        </a>
                        @if (impressions.Any())
                        {
                            <ul class="dropdown-menu menuText pre-scrollable" style="width:600px !important">
                                <li class="" style="width:100% !important">
                                    @foreach (var imp in impressions)
                                    {

                                        <div data-imp-box="@Newtonsoft.Json.JsonConvert.SerializeObject(impOject, Newtonsoft.Json.Formatting.Indented)" class="smallLabelBlack paddedLeft has-test-impressions">@rootCategory</div>
                                        <div class="form-inline paddedLeft" style="width:100% !important">
                                            <div class="form-group" style="width:100% !important">
                                                <textarea class="txtArea impresion-box" style="width:100% !important">@impValue</textarea>
                                            </div>
                                        </div>

                                </li>
                            </ul>

                    </li>

            </ul>
        </div>
    </div>
    <div id="divPrev-VP-Tests" class="form-group drp-container">
        <div class="dropdown">
            <button class="btn btn-default btn-sm custom-menu-link" type="button" aria-haspopup="true" aria-expanded="true">
                <span class="menuText">VP History</span>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu menuText">
                @foreach (var item in @Model.PreviousVPTests)
                {

                    <li class="dropdown-submenu">
                        <a class="hl-test-link-open"

                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Status: @testStatus" class="appointment-status-box c-pointer @testStatusCSS">&nbsp;&nbsp;</span>
                            <span class="@linkColor">@testDateStr @practiceDoctor</span>
                            <span class="small text-danger">
                                @consultCode
                            </span>
                            <span class="small text-info">
                                @diagnisticCode
                            </span>
                            <span class="small text-info">
                                @immunizationCode
                            </span>
                        </a>
                        @if(impressions.Any())
                        { 
                            <ul class="dropdown-menu menuText pre-scrollable" style="width:600px !important">
                                <li class="" style="width:100% !important">
                                    @foreach (var imp in impressions)
                                    {

                                        <span class="smallLabelBlack text-center paddedLeft">@rootCategory</span>
                                        <div class="form-inline paddedLeft" style="width:100% !important">
                                            <div class="form-group" style="width:100% !important">
                                                <textarea class="txtArea impresion-box">@impValue</textarea>
                                            </div>
                                        </div>

                                </li>

                            </ul>

                    </li>

            </ul>
        </div>
        
    </div>
</div>
