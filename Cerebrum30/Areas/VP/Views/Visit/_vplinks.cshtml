@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.VP.VMLinksMain

<div class="form-inline menuText menu-links">
    <div class="form-group form-group-sm">
        <div id="div-prevtest-main">
            <button class="btn-testhistory-main btn btn-default btn-sm" type="button">
                View Test History
            </button>
        </div>
    </div>
    <div class="form-group form-group-sm drp-container">
        <div class="dropdown">
            <button class="btn btn-default btn-sm" type="button" aria-haspopup="true" aria-expanded="true">
                <span class="menuText  ">Settings</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu  menuText">
                <li>
                    <a data-url="@Url.Action("EditCPPSetting","Visit",new { area = "vp", externalDoctorId = @Model.ExternalDoctorId })" id="btn-edit-cpp-setting" href="#">Edit CPP Settings</a>
                </li>
                @if (cerebrumUser.HasPermission("EditRootCategories"))
                {
                    <li>
                        <a href="#" class="btn-edit-rootcategory-custom-setting c-pointer">Edit Root Categories</a>
                    </li>
                }
                <li>
                    <a href="#" data-url="@Url.Action("EditMeasurements","Visit",new { area = "vp", externalDoctorId = @Model.ExternalDoctorId})" id="btn-edit-measurements">Edit Measurements</a>
                </li>

            </ul>
        </div>
    </div>

            <div class="dropdown">
                <button class="btn btn-default btn-sm" type="button">
                    <span class="menuText  ">Chronic Disease Flow</span> <span class="caret"></span>
                </button>
                <ul class="dropdown-menu menuText">
                    <li>
                        <a class="hl-config"

                    </li>
                    <li>
                        <a target="_blank" href='@Url.Action("TemplateData","vp",
                                                new
                                                {

                                                })'>Data</a>
                    </li>
                </ul>
            </div>
        </div>
    <div class="form-group form-group-sm drp-container">
        <div class="dropdown">
            <button class="btn btn-default btn-sm" type="button">
                <span class="menuText ">Show</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu  menuText">
                <li>
                    <a id="hlRawDataClassic" target="_blank"

                    </a>
                </li>
                <li>
                    <a data-save-type="@AwareMD.Cerebrum.Shared.Enums.VPWSSaveType.Preview" class="btn-vp-save-type" href="@Url.Action("GetReportPreview", "Reports", new { area = "documents", appointmentTestId = @Model.AppointmentTestId })" id="btn-prev-report">Preview Letter</a>
                </li>

                <li>
                    @Html.ActionLink("Letter History", "VPReportHistory", "Reports", new { area = "Documents", patientId = @Model.PatientId, appointmentId = @Model.AppointmentId }, new { @id = "btn-preview-multiple", target = "_blank" })
                </li>
                <li>
                    @Html.ActionLink("VP History", "VPHistory", "reports", new { area = "documents", patientID = @Model.PatientId }, new { target = "_blank" })
                </li>
                <li>
                    <a data-apptest-id="@Model.AppointmentTestId" class="btn-view-send-history" href="#">Send History</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm drp-container">
        <div class="dropdown ">
            <button class="btn btn-default btn-sm" type="button">
                <span class="menuText ">Goto</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu  menuText">
                @*<li>*@
                        <a class="hl-privacy"

                        </a>
                    </li>
                <li>
                    <a class="btn-link-to-patient-meds" target="_blank" href="@Url.Action("index", "patientmedications", new { area = "medications", patientId = @Model.PatientId})">Medications</a>
                </li>
                <li>
                    <a class="btn-flowsheet" target="_blank"

                </li>
                <li>
                    <a target="_blank" rel="noreferrer" href="https://ccs.ca/frs/">Framingham</a>
                    <a target="_blank" rel="noreferrer" href="https://www.mdcalc.com/cha2ds2-vasc-score-atrial-fibrillation-stroke-risk">CHADS/CHADSVAASC</a>
                    <a target="_blank" rel="noreferrer" href="https://doc2do.com/hcm/offline/webHCM.html">HCM</a>
                    <a target="_blank" rel="noreferrer" href="http://tools.acc.org/DAPTriskapp/#!/content/calculator/">DAPT</a>
                    <a href="/StaticDocs/Lipid_Algorithm_2021_CCS_Guidelines.pdf" id="btn-static-link-aorta" title="2021 CCS Guidelines - Lipid Algorithm"

                </li>
            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm">
        <button type="button" class="btn btn-default btn-sm" id="btn-externalDocuments-vp">
            <span class="menuText">External Documents</span>
        </button>
    </div>
    <div class="form-group form-group-sm">
        @if (cerebrumUser.HasPermission("Connecting Ontario"))
        {
            <button class="btn btn-default btn-sm btn-connecting-ontario" data-patient-record-id="@Model.PatientId" href="#" data-url="@Url.Action("Form", "ConnectingOntario", new { area = "ExternalComm" })">
                <span class="menuText">Connecting Ontario</span>
            </button>
        }
    </div>
    @{ Html.RenderPartial("~/Areas/VirtualVisit/Views/VideoConference/_joinVirtualVisitRoom.cshtml", Model); }
    <div class="save-btns-clone" style="float:right;"></div>
</div>

<script>
    $(function () {
        $('#btn-externalDocuments-vp').on('click', function (e) {
@* e.preventDefault(); *@

@* window.open(url,'_blank'); *@
@* }); *@
@* }); *@
</script>

