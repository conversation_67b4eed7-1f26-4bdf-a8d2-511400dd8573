﻿@model Cerebrum.ViewModels.TestBase.VMSendHistoryMain

@using (Html.BeginForm("", "visit", new { area = "VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-save-custom-measurements" }))
@using Cerebrum.ViewModels.Patient
{
    @Html.HiddenFor(x => x.AppointmentId)
    @Html.HiddenFor(x => x.TestId)
    @Html.HiddenFor(x => x.AppointmentTestId)
    @Html.HiddenFor(x => x.PatientId)

    <div class="form-horizontal">        
        @{ Html.RenderPartial("_sendHistoryList", @Model.SendHistoryItems);}       
    </div>
        
    <div style="margin-top:15px;" class="text-right">
        <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
    </div>             

