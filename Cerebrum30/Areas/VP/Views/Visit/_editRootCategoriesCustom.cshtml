﻿@model Cerebrum.ViewModels.TestBase.VMRootCategoryCustomMain

<div class="row" style="margin-bottom: 15px;">    
    <div class="col-md-4">
        @using (Html.BeginForm("AddRootCategory", "visit", new { area="VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-new-root-category" }))
        {

            @Html.HiddenFor(x => x.ExternalDoctorId)
            @Html.HiddenFor(x => x.PracticeId)
            @Html.Hidden("TestGroupId",@Model.GroupId)
            @Html.Hidden("PracticeRootCategoryTemplateId", @Model.PracticeRootCatTemplateId)

            <div class="">
                <div class="form-inline">
                    <div class="form-group form-group-sm">
                        <input class="form-control" type="text" id="Name" name="Name" />
                        <button type="submit" id="btn-vp-newroot"

                    </div>                    
                </div>
            </div>

    </div>
    <div class="col-lg-offset-4 col-md-4">
       
    </div>              
</div>

@using (Html.BeginForm("EditRootCategoriesCustom", "Visit", new { area = "VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-EditRootCategoriesCustom" }))
@using Cerebrum.ViewModels.Patient
{    
    <div>
        @Html.PartialAsync("_validationSummary")
        @Html.HiddenFor(x => x.ExternalDoctorId)       
        @Html.HiddenFor(x => x.PatientId)
        @Html.HiddenFor(x => x.PracticeId)
        @Html.HiddenFor(x => x.GroupId)               
       
        <div class="form-inline">
            <div class="form-group form-group-sm">
                <label class="control-label" for="PracticeRootCatTemplateId">Templates</label>
                @Html.DropDownList("PracticeRootCatTemplateId", new SelectList(@Model.DoctorTemplates, "PracticeTemplateId", "DisplayLookupName", @Model.PracticeRootCatTemplateId), new { @class = "form-control" })
            </div> 
            <div class="form-group form-group-sm" style="margin-bottom: 15px;">
                    
                    <div style="padding-top:5px;" class="checkbox">
                        <label>
                            @Html.CheckBoxFor(model => model.IsDefaultTemplate, new { @class = "cb-is-default-template" }) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsDefaultTemplate)</span>
                        </label>
                    </div>

            </div>
        </div>

        <div id="root-categories-custom-list">
            @{ Html.RenderPartial("_rootCategoriesCustomList", @Model.RootCategoryCustoms); }
        </div>      

        <div style="margin-top:15px;" class="text-right">
            <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
        </div>
    </div>

