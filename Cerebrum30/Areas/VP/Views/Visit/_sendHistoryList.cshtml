@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.TestBase.VMSendHistoryItem>

<div class="content-height300">
    <table class="table  table-condensed table-bordered">
        <tr>
            <td>
                <b>Doctor</b>
            </td>
            <td>
                <b>Email</b>
            </td>
            <td>
                <b>Fax</b>
            </td>
            <td>
                <b>Date</b>
            </td>
            <td>
                <b>Sent</b>
            </td>
            <td>
                <b>Sent Method</b>
            </td>
            <td>
                <b>URL</b>
            </td>
            <td><b>Error</b></td>
        </tr>
        @foreach (var item in Model)
        {

            <tr class="@trClass">
                <td>
                    @Html.DisplayFor(modelItem => item.DocName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Email)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Fax)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.DateEntered)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Sent)
                </td>

                <td>
                    @Html.DisplayFor(modelItem => item.SendType)
                </td>
                <td>                    
                    <a target="_blank" href="@reportUrl">@linkText</a>                   
                </td>
                <td>
                    @Html.TextAreaFor(modelItem => item.ErrorMessage, new { @class = "form-control" })
                </td>
            </tr>

    </table>
</div>

