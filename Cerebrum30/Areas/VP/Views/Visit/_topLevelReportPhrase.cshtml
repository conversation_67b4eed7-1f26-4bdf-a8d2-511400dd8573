﻿@model Cerebrum.ViewModels.VP.VMTopLevelRptPhrase

@{

  <li>
      <div class="rpt-phrases-config">
          <div class="pull-left">
              <a data-phrase-id="@phraseId"

          </div>
          <div class="pull-right">
              <a data-phrase-id="@phraseId"

          </div>
          <div class="clearfix"></div>
      </div>
    </li>
    <li class="divider"></li>

    @foreach (var item in topPhrase.Phrases)
    {        
@* Html.RenderPartial("_reportPhrase", item); *@
    }

