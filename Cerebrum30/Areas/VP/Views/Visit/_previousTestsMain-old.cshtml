﻿@model Cerebrum.ViewModels.TestBase.VMPreviousTestsMain

<style>
    smallLabelBlack {
/* color: black; */
/* font-size: 9px; */
    }

    li.sub-menu {
/* position: relative; */

    li.sub-menu span.glyphicon {
@* font-size: 0.7em; *@

    li.sub-menu:hover .dropdown-menu {
@* display: block; *@
@* top: 0; *@
@* left: 100%; *@
    }

    .nonBulletLst {
@* color: black; *@
@* font-size: 9px; *@

    ._mm {
@* cursor: pointer; *@

    /* Appointment status hack, will need t clean thisup */
    .appointment-status-box{
@* width: 5px; *@
@* height: 5px; *@
    }

@* /*@== status for appointment test end*@/ .prev-test-link-color
{*@ color:blue !important;

</style>
<script>
    $(document).ready(function () {

        //$("#divPrevTests").html('<img src="../../Content/fancybox_loading.gif" />');

@* setPreviousTestsData(); *@

        $(document).on('click','.hl-test-link-open', function(e){
@* e.preventDefault(); *@

            //console.log(url);
@* window.open(url, '_blank'); *@
@* }); *@

        $(document).on('click','._mm', function(e){

@* showNotificationMessage('success', '\'MOST RECENT INVESTIGATIONS\' has been updated. Please click \'Save\' to save your changes.'); *@
@* $('.mostRecent').prepend(htmlStr); *@
@* }); *@
@* }); *@
    function setPreviousTestsData()
    {
        Handlebars.registerHelper("isImpression", function(arg,TestDateStr, TestName, Value, DoctorName, AppointmentStatusStr, TestStatusCSS) {

                if( $('.mostRecent').length>0){ // mostRecent exist

                else{//need to show

@* return link; *@
@* }); *@

        Handlebars.registerHelper("linkColor", function(totalImpressions) {

            if(totalImpressions > 0){

            }
@* return cssClass; *@
@* }); *@

@* $("#divPrevTests").html(html); *@

@* $("#divPrev-VP-Tests").html(htmlVP); *@

</script>
<div class="form-inline">
    <div id="divPrevTests" class="form-group"></div>
    <div id="divPrev-VP-Tests" class="form-group"></div>
</div>
<script id="prevTest-template" type="text/x-handlebars-template">
    <div class="dropdown">
        <button class="btn btn-default btn-sm dropdown-toggle custom-menu-link" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            <span class="menuText">Test History</span>
            <span class="caret"></span>            
        </button>
        <ul class="dropdown-menu menuText">
            {{#each list}}
            <li class="dropdown-submenu">
                <a class="hl-test-link-open" 

                    <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Status: {{TestStatus}}" class="appointment-status-box c-pointer {{TestStatusCSS}}">&nbsp;&nbsp;</span>
                    <span class="{{linkColor Impressions.length}}">{{TestDateStr}}:{{TestName}}</span></a>
                {{#if Impressions.length}}
                <ul class="dropdown-menu menuText pre-scrollable" clas="nonBulletLst 999" style="width:600px !important">
                    <li class="dropdown-submenu" style="width:100% !important">
                        {{#each Impressions}}
                        <div class="smallLabelBlack paddedLeft"> {{TopLevelReportPhraseText}} {{{isImpression @@index ../TestDateStr ../TestName Value}}}</div>
                        <div class="form-inline paddedLeft" style="width:100% !important">
                            <div class="form-group" style="width:100% !important">
                                <textarea class="txtArea impresion-box" style="width:100% !important">{{Value}}</textarea>
                            </div>
                        </div>
                        {{/each}}
                    </li>
                </ul>
                {{/if}}
            </li>
            {{/each}}
        </ul>
    </div>
</script>
<script id="prevTest-vp-template" type="text/x-handlebars-template">
    <div class="dropdown">
        <button class="btn btn-default btn-sm dropdown-toggle custom-menu-link" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
            <span class="menuText">VP History</span>
            <span class="caret"></span>
        </button>
        <ul class="dropdown-menu menuText">
            {{#each list}}
            <li class="dropdown-submenu">
                <a class="hl-test-link-open"

                    <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Status: {{TestStatus}}" class="appointment-status-box c-pointer {{TestStatusCSS}}">&nbsp;&nbsp;</span>
                    <span class="{{linkColor Impressions.length}}">{{TestDateStr}} {{PracticeDoctor}}</span>
                    <span class="small text-danger">                        
                        {{ConsultCode}}                        
                    </span>
                    <span class="small text-info">
                        {{DiagnosticCode}}                        
                    </span>
                    <span class="small text-info">
                        {{ImmunizationCode}}                        
                    </span>
                </a>
                {{#if Impressions.length}}
                <ul class="dropdown-menu menuText pre-scrollable" clas="nonBulletLst 67891" style="width:600px !important">
                    <li class="dropdown-submenu" style="width:100% !important">
                        {{#each Impressions}}
                        <span class="smallLabelBlack text-center paddedLeft">{{TopLevelReportPhraseText}}</span>
                        <div class="form-inline paddedLeft" style="width:100% !important">
                            <div class="form-group" style="width:100% !important">
                                <textarea class="txtArea impresion-box">{{Value}}</textarea>
                            </div>
                        </div>
                        {{/each}}
                    </li>

                </ul>
                {{/if}}
            </li>
            {{/each}}
        </ul>
    </div>
</script>
