﻿@model Cerebrum.ViewModels.VP.VMMeasurementCustom {*@ var maxColumns = 4;

   @* *@
} using (Html.BeginForm("SaveMeasurements", "visit", new { area = "VP" },*@ Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-save-custom-measurements" }))
{

<div class="form-horizontal">
    @Html.HiddenFor(x => x.ExternalDoctorId)

    @foreach (var grp in groups)
    {

        <p>
            <span class="label label-primary label-app-type">
                @grp.Key
            </span>

        </p>

        <div class="form-group form-group-sm">
            @for (int i = 0; i < totalItems; i++)
            {

                {
                if (i > 0)
                {
                    @Html.Raw(divClose)
                }

            if (printDivOpen)
            {

                @Html.Raw(divOpen)
            }
            <div>
                <input type="hidden" name="MeasurementItems.Index" value="@index" />
                <input type="hidden" name="@valueName" id="@valueId" value="@valueValue" />
                <input type="hidden" name="@textName" id="@textId" value="@textValue" />
                <input type="hidden" name="@codeName" id="@codeId" value="@codeValue" />
                <input type="hidden" name="@groupName" id="@groupId" value="@groupValue" />

                @Html.CheckBox(selectedName, selectedValue, new { @id = selectedId }) <span>@textValue</span> <span

            </div>

            {

            }

            if (printDivClose)
            {

                @Html.Raw(divClose)
            }

@* index++; *@

    </div>

</div>

<div style="margin-top:15px;" class="text-right">
    <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
</div>
@* TODO: Review this section *@
