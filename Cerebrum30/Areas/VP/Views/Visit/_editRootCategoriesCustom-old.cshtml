﻿@model Cerebrum.ViewModels.TestBase.VMRootCategoryCustomMain

<div class="row" style="margin-bottom: 15px;">
    <div class="col-md-4">
        <div class="">
            <div id="div-search-root-phrases" class="form-inline">
                <div class="form-group form-group-sm">
                    <label>Search Root Categories</label>
                    <input type="text" id="txt-search-root-phrases" name="txt-search-root-phrases" />                    
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        @using (Html.BeginForm("AddRootCategory", "visit", new { area="VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-new-root-category" }))
        {

            @Html.HiddenFor(x => x.ExternalDoctorId)
            @Html.HiddenFor(x => x.PracticeId)
            @Html.Hidden("TestGroupId",@Model.GroupId)
            @Html.Hidden("PracticeRootCategoryTemplateId", @Model.PracticeRootCatTemplateId)

            <div class="">
                <div class="form-inline">
                    <div class="form-group form-group-sm">
                        <input type="text" id="Name" name="Name" />
                        <button type="submit" id="btn-vp-newroot"

                    </div>                    
                </div>
            </div>

    </div>
    <div class="col-md-4 text-right">
       
    </div>              
</div>

@using (Html.BeginForm("EditRootCategoriesCustom", "Visit", new { area = "VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-EditRootCategoriesCustom"}))
@using Cerebrum.ViewModels.Patient
{

    <div>
        @Html.PartialAsync("_validationSummary")
        @Html.HiddenFor(x => x.ExternalDoctorId)       
        @Html.HiddenFor(x => x.PatientId)
        @Html.HiddenFor(x => x.PracticeId)
        @Html.HiddenFor(x => x.GroupId)               
       
        <div class="form-inline">
            <div class="form-group form-group-sm">
                <label class="control-label" for="PracticeRootCatTemplateId">Doctor(s)</label>
                @Html.DropDownList("PracticeRootCatTemplateId", new SelectList(@Model.DoctorTemplates, "PracticeRootCatTemplateId", "DisplayLookupName", @Model.PracticeRootCatTemplateId), new { @class = "form-control" })
            </div> 
            <div class="form-group form-group-sm" style="margin-bottom: 15px;">
                    
                    <div class="checkbox">
                        <label>
                            @Html.CheckBoxFor(model => model.IsDefaultTemplate, new { @class = "cb-is-default-template" }) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsDefaultTemplate)</span>
                        </label>
                    </div>

            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default content-height500">
                    <div id="dr-root-categories-total" data-root-categories-total ="@totalRootCategories" data-root-categories-row-count ="@totalRootCategories" class="panel-heading">Total (@totalRootCategories)</div>
                    <table id="tbl-dr-root-categories" class="table table-condensed table-bordered tbl-sortable-tbody">
                        <thead>
                            <tr>
                                <th >
                                    <label>Original Text</label>
                                </th>
                                <th >
                                    <label>Custom Text</label>
                                </th>
                                <th >
                                    Visible
                                </th>
                                <th>
                                    Show in Letter
                                </th>                                
                                <th>
                                    Cumulative
                                </th>
                                <th>
                                    Show Toolbar
                                </th>                               
                                <th style="width:10%">

                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < @Model.RootCategoryCustoms.Count; i++)
                            {

                                <tr id="<EMAIL>[i].RootCategoryId">
                                    <td>
                                        <input type="hidden" name="RootCategoryCustoms.Index" value="@i" />
                                        @Html.HiddenFor(x => x.RootCategoryCustoms[i].RootCategoryId)
                                        @Html.HiddenFor(x => x.RootCategoryCustoms[i].PracRootCategoryTempId)
                                        @Html.HiddenFor(x => x.RootCategoryCustoms[i].OriginalName)
                                        @Html.HiddenFor(x => x.RootCategoryCustoms[i].Rank)
                                        <span class="glyphicon glyphicon-resize-vertical"> </span>
                                        @Html.DisplayTextFor(model => model.RootCategoryCustoms[i].OriginalName)
                                    </td>
                                    <td>
                                        @Html.TextAreaFor(model => model.RootCategoryCustoms[i].Name)
                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(model => model.RootCategoryCustoms[i].IsVisible, new { @class = "chk-visible" })
                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(model => model.RootCategoryCustoms[i].ShowInLetter, new { @class = "chk-visible" })
                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(model => model.RootCategoryCustoms[i].Accumulative)
                                    </td>
                                    <td>
                                        @Html.CheckBoxFor(model => model.RootCategoryCustoms[i].ShowToolbar)
                                    </td>                                   
                                    <td>
                                        <button type="button" class="btn btn-danger btn-xs btn-delete-dr-root-cat">Delete</button>
                                    </td>
                                </tr>

                        </tbody>

                    </table>
                </div>
            </div>

        </div>

        <div style="margin-top:15px;" class="text-right">
            <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
        </div>
    </div>

