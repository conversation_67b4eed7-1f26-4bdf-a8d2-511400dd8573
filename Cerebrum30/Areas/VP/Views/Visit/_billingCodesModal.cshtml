﻿@model Cerebrum.ViewModels.VP.VMVPCodesMain

<div>    
    <div class="row">
        <div class="col-md-6">
            <div class="form-inline">
                <div class="form-group form-group-sm">
                    <label class="control-label" for="txtFilterBillingCodes">Filter Table Codes</label>
                    <input type="text" id="txtFilterBillingCodes", name="txtFilterBillingCodes" class = "form-control"/>
                </div>   
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-inline">
                <div class="form-group form-group-sm">
                    <label class="control-label" for="txtSearchBillingCodes">Search Database Codes</label>
                    <input data-input-id="@Model.InputId" 

                </div>
            </div>
        </div>
    </div>
<div class="row">
    <div class="col-md-12">
        <div class="panel panel-default content-height500">
            <div id="div-codes-total" data-total="@Model.TotalCodes" class="panel-heading">Total Codes (@Model.TotalCodes)</div>
            <table id="tbl-billing-code-list" class="table table-condensed table-bordered table-hover">
                <thead>
                    <tr>
                        <th>
                            <label>Code</label>
                        </th>
                        <th>
                            <label>Description</label>
                        </th>                        
                        <th style="width:10%;">                            
                        </th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < @Model.Codes.Count; i++)
                    {                       
                        <tr>
                            <td>                                
                                @Html.DisplayTextFor(model => model.Codes[i].Code)
                            </td>
                            <td>@Html.DisplayTextFor(model => model.Codes[i].Description)</td>                            
                            <td>
                                <button 

                            </td>
                        </tr>

                </tbody>

            </table>
        </div>
    </div>

</div>

</div>