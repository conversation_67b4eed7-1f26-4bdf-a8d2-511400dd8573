﻿@model Cerebrum.ViewModels.TestBase.VMRootCategoriesInputMain

@{

}

<script>
    $(document).ready(function () {
@* buildAllPhrasesForRootCategories(); *@

@* }); *@
</script>

<div id="div-vp-rp" data-can-edit-settings="@canEditSettings" data-can-edit-practice-settings="@canEditPracticeSettings" class="__00985 AA panel panel-info">
    <div class="panel-heading">
        <div class="form-inline div-padding-5">
            <div class="form-group form-group-sm">
                <label class="control-label" for="PhrasesPracticeRootCatTemplateId">VP Phrases - </label>
            </div>
            <div class="form-group form-group-sm">
                <label class="control-label" for="PhrasesPracticeRootCatTemplateId">Templates</label>
                @Html.DropDownList("PhrasesPracticeRootCatTemplateId", new SelectList(@Model.DoctorTemplates, "PracticeTemplateId", "DisplayLookupName", @Model.PracticeTemplateId), new { @class = "form-control" })
            </div>
            <div class="form-group form-group-sm pull-right div-bordered-gray">
                <select id="ddlAddToEmptyorAll" class="form-control">
                    <option value="0">Add to Empty Categories</option>
                    <option value="1">Add to All Categories</option>
                </select>
               <input type="text" id="search-root-subcategory" placeholder="search subcategory" class="form-control ui-autocomplete-input"> <input type="button" id="btn-add-category-phrase-default" value="Add" data-subcategory-text="" class="btn btn-primary btn-sm" />
            </div>
        </div>
    </div>
    <div class="panel-body">        
        <div style="margin-top:5px;" class="row">
            <ol id="vp-rep-phrases-list" data-total-phrases="@rootCategoriesCount" data-practice-template-id="@Model.PracticeTemplateId" class="vp-list">
                @for (int i = 0; i < rootCategoriesCount; i++)
                {                    

                    <span id="<EMAIL>">
                        @Html.HiddenFor(x => @Model.RootCategories[i].RootCategoryId)
                        @Html.HiddenFor(x => @Model.RootCategories[i].PracRootCategoryTempId)
                        @Html.HiddenFor(x => @Model.RootCategories[i].OriginalName)
                        @Html.HiddenFor(x => @Model.RootCategories[i].Name)
                        @Html.HiddenFor(x => @Model.RootCategories[i].ControlName)
                        @Html.HiddenFor(x => @Model.RootCategories[i].ShowInLetter, new { @class = "hidden-input-showletter" })
                    </span>

                    <li class="root-cat-col-size">
                        <div class="form-inline  ">
                            <div id="<EMAIL>" data-category-phrases="@Newtonsoft.Json.JsonConvert.SerializeObject(rootCategory, Newtonsoft.Json.Formatting.Indented)" class="form-group form-group-sm ul-phrases-holder">                                
                                @* phrases are now loaded with javascript from the data-category-phrases attribute @Html.PartialAsync("_rootCategoryItem",rootCategory)*@
                            </div>
                            <div class="form-group form-group-sm ">
                                <div class="form-inline">
                                                                       
                                    <div class="form-group form-group-sm ">
                                        <div data-selected=@(@Model.RootCategories[i].ShowInLetter ? "1" : "0")
                                            *@ data-toggle="tooltip"

                                    </div>

                                        <div data-toggle="tooltip"

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                @* var mostRecent = ""; *@
                                    if (isMostRecent)
                                    {

                                    }

                                @Html.TextAreaFor(x => x.RootCategories[i].Value)

                                        @class = "txtArea txtReportPhrases form-control " + mostRecent,
                                        @cols = 50,

                                    })
                            </div>
                        </div>
                    </li>@* *@
              } @*end for loop*@
            </ol>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
@* autosize(document.querySelectorAll('textarea')); *@
@* }, false); *@

    $(document).ready(function(){

        $('.txtReportPhrases').each(function (i, e) {

@* txtBox.css('height', '100px'); *@
            }
@* setSelectedPhrases(rootCategoryId); *@
@* countRootCategoryText(txtBox); *@
@* }); *@

        $(selectorsToExtend).on('keyup paste', function () {
@* autosize(this); *@
@* countRootCategoryText($(this)); *@
@* }).keyup(); *@

@* }); *@
</script>   