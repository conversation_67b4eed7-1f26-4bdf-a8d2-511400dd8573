﻿@model Cerebrum.ViewModels.TestBase.VMRootCategoryCustom

@{

<ul id="ul-top-level-wrapper-@topLevelId" data-top-rp-phrase-id="@topLevelId" data-top-rp-phrase-ctrl="@topLeveltxtBox"

    @if (canEditPracticeSettings || canEditSettings)
    {
        <li>
            <div class="rpt-phrases-config">
                <div class="pull-left">
                    <a data-phrase-id="@phraseId" data-phrase-name="@phraseName" data-rootcategory-id="@topLevelId"

                </div>
                <div class="pull-right">
                    <a data-phrase-id="@phraseId" data-phrase-name="@phraseName" data-rootcategory-id="@topLevelId"

                        Subcategories</a>
                </div>
                <div class="clearfix"></div>
            </div>
        </li>
        <li class="divider"></li>

    @for (int i = 0; i < @Model.Phrases.Count(); i++)
    {
        @Html.DisplayFor(mo => @Model.Phrases[i], "RootCategoryCustomPhrase")

</ul>
