﻿@model Cerebrum.ViewModels.VP.VMLabsMain
<script>
    $(document).ready(function () {

@* }); *@
</script>
       
<span id="hl7-lab-label" data-collection-date="@Model.HL7_CollectionDate" class="custom-label">Labs</span> <span class="btn-refresh-labs glyphicon glyphicon-refresh c-pointer" data-toggle="tooltip" data-placement="bottom" title="Refresh Labs">&nbsp;</span>
<table cellpadding="0" class="table custom-table table-condensed table-bordered __0098432 spacer-btm-7">
    @for (int i = 0; i < @Model.LabResultCategories.Count; i++)
    {
        <tr>
            <td>      
                @Html.HiddenFor(x => x.LabResultCategories[i].id)                         
                <div class="form-inline">
                    <ol>
                        @for (int j = 0; j < @Model.LabResultCategories[i].Measurements.Count; j++)
                        {
                            <li class="col-sm-2">
                                <div class="">
                                    @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Id)
                                    @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Name)                                    

                                    <span class="hideOverflowMeas small "

                                        @Model.LabResultCategories[i].Measurements[j].Name
                                    </span>

                                    @Html.TextBoxFor(x => x.LabResultCategories[i].Measurements[j].Value, new { @class = " txtBox vp-meas-box form-control" })

                                    <a data-toggle="tooltip"

                                        (@Model.LabResultCategories[i].Measurements[j].Units)
                                    </a>

                                    @if (!string.IsNullOrEmpty(@Model.LabResultCategories[i].Measurements[j].ErrorMessage))
                                    {
                                        <br />
                                        <span class="label label-danger">@Model.LabResultCategories[i].Measurements[j].ErrorMessage</span>
                                    }
                                    @if (@Model.LabResultCategories[i].Measurements[j].OverTarget)
                                    {

                                        <i data-toggle="tooltip"

                                </div>
                            </li>

                    </ol>
                </div>
            </td>
        </tr>

</table>

    