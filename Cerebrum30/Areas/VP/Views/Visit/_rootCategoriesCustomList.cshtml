﻿@model IEnumerable<Cerebrum.ViewModels.TestBase.VMRootCategoryCustom>
 @{ 

 }

<div class="row">    
    <div class="col-md-12">
        <hr />
        <div class="form-inline">
            <div class="form-group form-group-sm">
                <label class="control-label" for="PracticeCategoriesLK">Practice Root Categories</label>
                <div id="div-practice-rootcategories-lk">
                    @Html.DropDownList("PracticeCategoriesLK", new SelectList(practiceCategoriesLK, "Value", "Text"), new { @class = "form-control" })
                </div>
                <button id="btn-add-new-root-cat-tolist" class="btn btn-primary btn-xs">Add to list</button>
            </div>            
        </div>
        </div>
        <div class="panel panel-default">
            <div id="dr-root-categories-total" data-root-categories-total="@totalRootCategories" data-root-categories-row-count="@totalRootCategories" class="panel-heading">Total (@totalRootCategories)</div>
            <table id="tbl-dr-root-categories" class="table table-condensed table-bordered tbl-sortable-tbody">
                <thead>
                    <tr>
                        <th>
                            <label>Original Text</label>
                        </th>
                        <th>
                            <label>Custom Text</label>
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Shows the category on the page">Visible</span>
                            <input type="checkbox" data-toggle-class="chk-visible" class="cb-toggleICON"/>
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Shows the category name in the letter">Show Label</span>
                            <input type="checkbox" data-toggle-class="chk-visible-label" class="cb-toggleICON" />
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Shows the category in the letter">Show in Letter</span>
                            <input type="checkbox" data-toggle-class="chk-visible-letter" class="cb-toggleICON" />
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Keeps the data from the previous visits">Cumulative</span>
                            <input type="checkbox" data-toggle-class="chk-visible-accumulative" class="cb-toggleICON" />
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Shows the patient's tool bar when adding phrases">Toolbar</span>
                            <input type="checkbox" data-toggle-class="chk-visible-toolbar" class="cb-toggleICON" />
                        </th>
                        <th style="width:10%">

                        </th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < RootCategoryCustoms.Count; i++)
                    {

                        @Html.EditorFor(model => RootCategoryCustoms[i], "RootCategoriesCustomItem", String.Format("{0}[{1}]", "RootCategoryCustoms", i))
                        
                    }
                </tbody>

            </table>
        </div>
</div>

