﻿@model Cerebrum.ViewModels.TestBase.VMAllDoctorPracticeSettingsMain

@using (Html.BeginForm("EditAllDoctorPracticeSetting", "Visit", new { area = "VP" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-EditAllDoctorPracticeSetting"}))
{

    <div>
        @Html.PartialAsync("_validationSummary")
        @Html.HiddenFor(x => x.SetForAllTests)              
        @Html.HiddenFor(x => x.GroupId)         

        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default content-height500">
                    <div class="panel-heading">Total (@totalDoctors)</div>
                    <table id="tbl-dr-root-categories" class="table table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th >
                                    <label>Doctor</label>
                                </th>                                                          
                                <th style="width:20%">
                                    <input type="checkbox" name="cb-all-practice-settings-selected" id="cb-all-practice-settings-selected" />
                                    Use Practice Setting
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < @Model.Doctors.Count; i++)
                            {

                                <tr>
                                    <td>
                                        <input type="hidden" name="Doctors.Index" value="@i" />
                                        @Html.HiddenFor(x => x.Doctors[i].ExternalDoctorId)
                                        @Html.HiddenFor(x => x.Doctors[i].PracticeDoctorId)
                                        @Html.HiddenFor(x => x.Doctors[i].DoctorName)
                                        @Html.HiddenFor(x => x.Doctors[i].RootCategorySettingsId)                                        
                                        @Html.DisplayTextFor(model => model.Doctors[i].DoctorName)
                                    </td>                                   
                                    <td>
                                        @Html.CheckBoxFor(model => model.Doctors[i].Selected, htmlAttributes: new { @class = "cb-settings-selected", data_external_doctor_id= externalDoctorId })
                                    </td>
                                </tr>

                        </tbody>

                    </table>
                </div>
            </div>

        </div>

        <div style="margin-top:15px;" class="text-right">
            <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
        </div>
    </div>

