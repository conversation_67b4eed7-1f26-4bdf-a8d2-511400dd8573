﻿using Cerebrum.Data;
using Cerebrum3.Infrastructure;
using Cerebrum30.Areas.PdfConversions.Models.ViewModels;
using Cerebrum30.Areas.VP.Models.ViewModels;
using LocalVP = Cerebrum30.Areas.VP.Models.ViewModels;
using Cerebrum30.DAL.DataAccess.Repositories;
using Cerebrum30.Utility;
using Cerebrum30.Utility.HL7;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.Data.Entities.VisitPage;
using Cerebrum30.Areas.Medications.DataAccess;
using Cerebrum.ViewModels.Medications;
using Cerebrum.ViewModels.Patient;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Mvc.Rendering;
using Cerebrum30.Areas.PdfConversions;
using Microsoft.EntityFrameworkCore;
using Cerebrum.ViewModels.VP;
using Cerebrum.ViewModels.Doctor;
using Cerebrum.VisitPage;
using Cerebrum.BLL.Medications;
using Cerebrum30.Areas.AdminUser.DataAccess;

namespace Cerebrum30.Areas.VP.DataAccess
{
    public class VPRepository : GenericRepository<CerebrumContext, VPMeasurement>, IVPRepository
    {
        private SendReportBLL _sendReportBll;
        private MedicationBLL _medicationBLL;
        private VisitPageBLL _vpBll;
        private readonly string VP_UPLOADS = @"Areas\VP\uploads\";
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string basePath;

        public int OfficeId { get; private set; }

        public VPRepository()
        {
            _sendReportBll = new SendReportBLL(context);
            _medicationBLL = new MedicationBLL(context);
            _vpBll = new VisitPageBLL(context);
        }

        public VPRepository(string _basePath, int officeId)
        {
            this.basePath = _basePath;
            this.OfficeId = officeId;
            _sendReportBll = new SendReportBLL(context);
            _medicationBLL = new MedicationBLL(context);
            _vpBll = new VisitPageBLL(context);
        }

        #region Options 
        // TODO: Moved to VPBLL. Delete!
        public List<VMOption> LoadPatientOptions(int doctorID, List<VMOption> options)
        {
            //var lst = context.VPOptionByPatient.Where(p => p.PatientRecordId == doctorID).ToList();

            List<SqlParameter> parms = new List<SqlParameter>
            {
              new SqlParameter("doctorID",doctorID)
            };

            var lst = context.GetData<VMOption>("[dbo].[Get_VP_DoctorOptions]", parms).ToList();
            options.ForEach(o =>
            {
                var option = lst.FirstOrDefault(x => x.Id == o.Id);
                if (option != null)
                {
                    o.Selected = true;
                }
            });

            return options;
        }
        
        // TODO: Moved to VPBLL. Delete!
        public List<Cerebrum.ViewModels.VP.VMOption> GetOptions()
        {
            return context.GetData<Cerebrum.ViewModels.VP.VMOption>("[dbo].[Get_VP_Options]").ToList();
        }
      
        #endregion 

        #region CPP Settings
        // TODO: Moved to VPBLL. Delete!
        public List<VMCPPCategory> GetCPPCats(int? practiceDoctorId = null)
        {
            List<VMCPPCategory> lst = new List<VMCPPCategory>();
            if (practiceDoctorId == null)
            {
                lst = (from a in context.VP_CPP_Category
                       where a.Group == 0
                       select new VMCPPCategory { Id = a.Id, Text = a.Text.Trim().Replace("\r\n", ""), Visible = true }).ToList();
            }
            else if (practiceDoctorId != null && practiceDoctorId > 0)
            {
                lst = (from a in context.VP_CPP_Category
                       join s in context.VP_CPP_Setting on a.Id equals s.VP_CPP_Category_Id
                       where s.DoctorID == practiceDoctorId
                       select new VMCPPCategory { Id = a.Id, Text = a.Text.Trim().Replace("\r\n", ""), Visible = s.Visible }).ToList();
            }
            
            return lst;

        }
        // TODO: Moved to VPBLL. Delete!
        public List<VMCPPCategory> GetCustomCPP(int DoctorID, List<VMCPPCategory> lst)
        {
            List<VMCPPCategory> retLst = new List<VMCPPCategory>();

            //var lstCPP = context.VP_CPP_Setting.Where(s => s.DoctorID == DoctorID).ToList();

            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("DoctorID",DoctorID)
            };
            var lstCPP = context.GetData<VMCPPCategory>("[dbo].[Get_VP_CPP_Setting]", parms).ToList();
            foreach (var item in lst)
            {
                var entry = lstCPP.FirstOrDefault(s => s.VP_CPP_Category_Id == item.Id && s.DoctorID == DoctorID);
                if (entry != null)
                {
                    item.DoctorID = entry.DoctorID;
                    item.Text = entry.Text.Trim().Replace("\r\n", "");
                    item.Order = entry.Order;
                    item.Visible = entry.Visible;
                }
            }


            foreach (var item in lst)
            {
                if (item.Visible)
                    retLst.Add(item);
            }

            retLst = retLst.OrderBy(s => s.Order).ToList();

            return retLst;

        }
        public List<VMCPPCategory> GetCPPCatValues(List<VMCPPCategory> lst,int patientID, int practiceDoctorId, DateTime? visitStartDt, DateTime? visitEndDt)

        {
            visitEndDt = visitEndDt.Value.AbsoluteEnd();
            var _vp = new Cerebrum.BLL.VP.VPBLL(context);

            foreach (var cpp in lst)
            {
                StringBuilder sb = new StringBuilder();
                StringBuilder sb2 = new StringBuilder();
                bool hasOverDue = false;
                VMCPPVisibleField visibleField = new VMCPPVisibleField();

                if (cpp.Id == (int)CPP_Categories.IMMUNIZATIONANDPREVENTIVECARE)
                {
                    var lstImmunes = GetImmunizationRecords(patientID, visitStartDt, visitEndDt);
                    hasOverDue = lstImmunes.Any(x => x.VP_CPP_ImmunizationStatusId == (int)ImmunizationStatus.OVERDUE);
                    if (lstImmunes.Count > 0)
                    {
                        foreach (var x in lstImmunes)
                        {
                            if (x.VP_CPP_ImmunizationStatusId == (int)ImmunizationStatus.OVERDUE)
                            {
                                sb2 = new StringBuilder();
                                sb2.Append(GetCPPString(true, x.VP_CPP_ImmunizationType));
                                sb2.Append(GetCPPString(true, x.Name, ", "));
                                sb2.Append(GetCPPString(true, x.VP_CPP_ImmunizationStatus, ", "));
                                if (x.DateServiced.HasValue)
                                    sb2.Append(GetCPPString(true, x.DateServiced.Value.Day, x.DateServiced.Value.Month, x.DateServiced.Value.Year, " "));

                                sb.Append(sb2.ToString().Trim(',') + Environment.NewLine);
                            }
                        }

                    }
                    cpp.IsHighlighted = hasOverDue;
                    cpp.Value = sb.ToString();

                }
                else if (cpp.Id == (int)CPP_Categories.ALERTSANDSPECIALNEEDS)
                {
                    visibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Categories.ALERTSANDSPECIALNEEDS);
                    var lsAlerts = GetCPPAlerts(patientID, visitStartDt, visitEndDt);
                    if (lsAlerts.Count > 0)
                    {
                        foreach (var x in lsAlerts)
                        {
                            if (!x.Visible)
                                continue;

                            sb2 = new StringBuilder();
                            sb2.Append(GetCPPString(visibleField.Col1Visible, x.Description));
                            sb2.Append(GetCPPString(visibleField.Col2Visible, x.Notes, ","));
                            sb2.Append(GetCPPString(visibleField.Col3Visible, x.DateActive_Day, x.DateActive_Month, x.DateActive_Year, ","));
                            sb2.Append(GetCPPString(visibleField.Col4Visible, x.EndDate_Day, x.EndDate_Month, x.EndDate_Year, ","));
                            sb2.Append(GetCPPString(visibleField.Col5Visible, x.eSubmitDate.Day, x.eSubmitDate.Month, x.eSubmitDate.Year, ","));

                            sb.Append(sb2.ToString().Trim(',') + Environment.NewLine);
                        }
                    }

                    cpp.Value = sb.ToString();
                }
                else if (cpp.Id == (int)CPP_Categories.RISKFACTORS)
                {
                    visibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Categories.RISKFACTORS);
                    var lstRisks = this.GetRiskFactors(patientID, visitStartDt, visitEndDt);
                    if (lstRisks.Count > 0)
                    {
                        foreach (var x in lstRisks)
                        {
                            if (!x.Visible)
                                continue;

                            string status = x.Status == 0 ? "InActive" : "Active";
                            sb2 = new StringBuilder();
                            sb2.Append(GetCPPString(visibleField.Col2Visible, x.RiskFactor));
                            sb2.Append(GetCPPString(visibleField.Col1Visible, x.ExposureDetails, ","));
                            sb2.Append(GetCPPString(visibleField.Col4Visible, x.OnsetAge ?? 0, x.Unit, x.LifeStage, ","));
                            sb2.Append(GetCPPString(visibleField.Col5Visible, x.StartDateDay, x.StartDateMonth, x.StartDateYear, ","));
                            sb2.Append(GetCPPString(visibleField.Col6Visible, x.EndDateDay, x.EndDateMonth, x.EndDateYear, ","));
                            sb2.Append(GetCPPString(visibleField.Col9Visible, status, ","));
                            sb2.Append(GetCPPString(visibleField.Col3Visible, x.Notes, ","));

                            sb.Append(sb2.ToString().Trim(',') + Environment.NewLine);
                        }
                    }

                    cpp.Value = sb.ToString();
                }
                else if (cpp.Id == (int)CPP_Categories.FAMILYHISTORY)
                {
                    visibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Categories.FAMILYHISTORY);
                    var lstFamily = this.GetFamilyHistory(patientID, visitStartDt, visitEndDt);
                    if (lstFamily.Count > 0)
                    {
                        foreach (var x in lstFamily)
                        {
                            if (!x.Visible)
                                continue;

                            sb2 = new StringBuilder();
                            sb2.Append(GetCPPString(visibleField.Col10Visible, x.DiagnosticCode));
                            sb2.Append(GetCPPString(visibleField.Col11Visible, string.IsNullOrEmpty(sb2.ToString().Trim()) ? x.DiagnosticDescription : (string.IsNullOrEmpty(x.DiagnosticDescription) ? string.Empty : "(" + x.DiagnosticDescription + ")")));
                            sb2.Append(GetCPPString(visibleField.Col1Visible, x.ProblemDescription, ","));
                            sb2.Append(GetCPPString(visibleField.Col3Visible, x.RelationShip, ","));
                            sb2.Append(GetCPPString(visibleField.Col4Visible, x.AgeOnset, x.Unit, x.LifeStage, ","));
                            sb2.Append(GetCPPString(visibleField.Col2Visible, x.Treatment, ","));
                            sb2.Append(GetCPPString(visibleField.Col6Visible, x.StartDateDay, x.StartDateMonth, x.StartDateYear, ","));
                            sb2.Append(GetCPPString(visibleField.Col9Visible, x.Notes, ","));

                            sb.Append(sb2.ToString().Trim(',') + Environment.NewLine);
                        }
                    }

                    cpp.Value = sb.ToString();
                }
                else if (cpp.Id == (int)CPP_Categories.PROBLEMLIST || cpp.Id == (int)CPP_Categories.PASTHEALTH)
                {
                    bool isProblemList = true;
                    CPP_Categories CPP_Category = CPP_Categories.PROBLEMLIST;
                    if (cpp.Id == (int)CPP_Categories.PASTHEALTH)
                    {
                        isProblemList = false;
                        CPP_Category = CPP_Categories.PASTHEALTH;
                    }

                    visibleField = _vp.GetCPPVisibleField(practiceDoctorId, CPP_Category);
                    var lstProblem = this.GetCPP_ProblemList(patientID, isProblemList, visitStartDt, visitEndDt);
                    if (lstProblem.Count > 0)
                    {
                        var cppProblemStatus = context.VP_CPP_Problem_Status.ToList();
                        foreach (var x in lstProblem)
                        {
                            if (!x.visible)
                                continue;

                            sb2 = new StringBuilder();
                            //string description = GetCPPString(visibleField.Col3Visible, x.Problem_Description);
                            //string procedure = GetCPPString(visibleField.Col2Visible, x.Proc_Interv);
                            //string diagnosticDescription = GetCPPString(visibleField.Col1Visible, x.DiagnosticDescription);
                            //string problemStatus = x.Problem_Status == 0 ? "Resolved" : "Ongoing";
                            string problemStatus = string.Empty;
                            if (x.Problem_Status != null)
                                problemStatus = cppProblemStatus.Where(a => a.Id == x.Problem_Status.Value).Select(b => b.Text).FirstOrDefault();

                            sb2.Append(GetCPPString(visibleField.Col10Visible, x.DiagnosticCode));
                            sb2.Append(GetCPPString(visibleField.Col1Visible, string.IsNullOrEmpty(sb2.ToString().Trim()) ? x.DiagnosticDescription : (string.IsNullOrEmpty(x.DiagnosticDescription) ? string.Empty : "(" + x.DiagnosticDescription + ")")));

                            //if (string.IsNullOrEmpty(description))
                            //    sb.Append(procedure + (string.IsNullOrEmpty(diagnosticDescription) ? string.Empty : "(" + diagnosticDescription + ")"));
                            //else
                            //    sb.Append(description + (string.IsNullOrEmpty(diagnosticDescription) ? string.Empty : "(" + diagnosticDescription + ")") + (string.IsNullOrEmpty(procedure) ? string.Empty : ", " + procedure));

                            sb2.Append(GetCPPString(visibleField.Col3Visible, x.Problem_Description, ","));
                            sb2.Append(GetCPPString(visibleField.Col2Visible, x.Proc_Interv, ","));
                            sb2.Append(GetCPPString(visibleField.Col4Visible, problemStatus, ","));
                            sb2.Append(GetCPPString(visibleField.Col5Visible, x.DateOfOnset_Day, x.DateOfOnset_Month, x.DateOfOnset_Year, ","));
                            sb2.Append(GetCPPString(visibleField.Col6Visible, x.ResolutionDate_Day, x.ResolutionDate_Month, x.ResolutionDate_Year, ","));
                            sb2.Append(GetCPPString(visibleField.Col7Visible, x.ProcDate_Day, x.ProcDate_Month, x.ProcDate_Year, ","));
                            sb2.Append(GetCPPString(visibleField.Col8Visible, x.Years, x.Units, x.Life_Stage, ","));
                            sb2.Append(GetCPPString(visibleField.Col9Visible, x.Notes, ","));

                            sb.Append(sb2.ToString().Trim(',') + Environment.NewLine);
                        }
                    }

                    cpp.Value = sb.ToString();
                }

            }

            return lst;
        }
        // TODO: Moved to VPBLL. Delete!
        public List<VMCPPAlert> GetCPPAlerts(int patientID, DateTime? frm = null, DateTime? to = null)
        {
            List<VMCPPAlert> lst = new List<VMCPPAlert>();

            lst = (from x in (from a in context.VP_CPP_Alert
                              where a.PatientRecordId == patientID &&
                              ((frm.HasValue && to.HasValue) ? (a.AddDate >= frm && a.AddDate <= to) : 1 == 1)
                              group a by new { a.ParentId } into g
                              select g.OrderByDescending(b => b.AddDate).FirstOrDefault())
                   where x.Deleted == false
                   //orderby x.Position
                   select new VMCPPAlert()
                   {
                       Id = x.Id,
                       PatientRecordId = x.PatientRecordId,
                       UserId = x.UserId,
                       Description = x.Description,
                       Notes = x.Notes,
                       EndDate = x.EndDate,
                       Status = x.Status,
                       eSubmitDate = x.eSubmitDate,
                       DateActive_Day = x.DateActive_Day,
                       DateActive_Month = x.DateActive_Month,
                       DateActive_Year = x.DateActive_Year,
                       EndDate_Day = x.EndDate_Day,
                       EndDate_Month = x.EndDate_Month,
                       EndDate_Year = x.EndDate_Year,
                       DateActive = x.DateActive,
                       Visible = x.Visible,
                       ResidualInformation = x.ResidualInformation ?? string.Empty
                   }).ToList();

            return lst;
        }
        public int Add_CPP_Alert(VMCPPAlertList model,int userId,string ipAddress)
        {
            var alert = new VP_CPP_Alert()
            {
                PatientRecordId = model.PatientID,
                UserId = model.UserID,
                Description = model.Description,
                Notes = model.Note,
                eSubmitDate = DateTime.Now,
                DateActive = DateTime.Now,
                DateActive_Day = model.DateActiveDay,
                DateActive_Month = model.DateActiveMonth,
                DateActive_Year = model.DateActiveYear,
                EndDate_Day = model.EndDateDay,
                EndDate_Month = model.EndDateMonth,
                EndDate_Year = model.EndDateYear,
                Status = true,
                Visible = model.Visible,
                AddedBy = model.AddedBy,
                AddDate = DateTime.Now.AddYears(-5)
            };

            alert.PropSetValueForce("ResidualInformation", model.ResidualInformation);

            List<string> resList = new List<string>();
            int descrLen = CdsDBHelper.PropGetMaxLength<VP_CPP_Alert>("Description");
            if (descrLen >= model.Description.Length)
            {
                alert.Description = model.Description;
            }
            else
            {
                alert.Normalize("Description",
                                  model.Description,
                                  ref resList,
                                  alert.Notes.Length);
                if (resList.Count > 0)
                {
                    if (!alert.PropSetValue("Notes", alert.Notes + "\n" + model.Description))
                    {
                        alert.PropSetValueForce("Notes", alert.Notes + "\n" + model.Description);
                        _log.Info($"Add_CPP_Alert: Description length {model.Description.Length} exceeds [Alert].[Description] field size {descrLen} and will be truncated:");
                        _log.Info(model.Description);
                    }
                }
            }

            context.VP_CPP_Alert.Add(alert);
            context.SaveChanges(userId,ipAddress);

            alert.ParentId = alert.Id;
            context.SaveChanges(userId, ipAddress);

            return alert.Id;
        }
        public void Edit_CPP_Alert(VMCPPAlert model,int userId,string ipAddress)
        {

            var entry = context.VP_CPP_Alert.Find(model.Id);
            if (entry != null)
            {
                entry.UpdateDate = DateTime.Now;
                entry.UpdatedBy = userId;

                var newEntry = new VP_CPP_Alert();

                newEntry.ParentId = entry.ParentId;
                newEntry.PatientRecordId = entry.PatientRecordId;
                newEntry.Notes = model.Notes;
                newEntry.Status = entry.Status;

                if (model.EndDate.HasValue)
                    newEntry.EndDate = model.EndDate.Value;

                //newEntry.Status = model.Status;
                newEntry.Description = model.Description;
                newEntry.Notes = model.Notes;
                newEntry.DateActive_Day = model.DateActive_Day;
                newEntry.DateActive_Month = model.DateActive_Month;
                newEntry.DateActive_Year = model.DateActive_Year;
                newEntry.EndDate_Day = model.EndDate_Day;
                newEntry.EndDate_Month = model.EndDate_Month;
                newEntry.EndDate_Year = model.EndDate_Year;
                newEntry.eSubmitDate = DateTime.Now;
                newEntry.DateActive = DateTime.Now;
                newEntry.Visible = model.Visible;
                newEntry.ResidualInformation = entry.ResidualInformation;
                newEntry.AddedBy = userId;
                newEntry.AddDate = DateTime.Now;
                newEntry.UpdatedBy = 0;
                newEntry.UpdateDate = null;
                newEntry.UserId = newEntry.AddedBy;
                context.VP_CPP_Alert.Add(newEntry);

                context.SaveChanges(userId, ipAddress);
            }
        }

        public List<VMRiskFactor> GetRiskFactors(int patientID, DateTime? frm = null, DateTime? to = null)
        {
            var factors = (from x in (from a in context.VP_CPP_RiskFactor
                                      where a.PatientRecordId == patientID &&
                              ((frm.HasValue && to.HasValue) ? (a.AddDate >= frm && a.AddDate <= to) : 1 == 1)
                                      group a by new { a.ParentId } into g
                                      select g.OrderByDescending(b => b.AddDate).FirstOrDefault())
                           where x.Deleted == false
                           orderby x.Position
                           select new VMRiskFactor()
                           {
                               Id = x.id,
                               PatientId = x.PatientRecordId,
                               RiskFactor = x.RiskFactor,
                               ExposureDetails = x.ExposureDetails,
                               OnsetAge = x.OnsetAge,
                               EndDate = x.EndDate,
                               LifeStage = x.LifeStage,
                               Notes = x.Notes,
                               Position = x.Position,
                               Status = x.Status ? 1 : 0,
                               ReasonForDelete = x.ReasonForDel,
                               SubmitDate = x.SubmitDate,
                               StartDate = x.StartDate,
                               StartDateDay = x.StartDateDay,
                               StartDateMonth = x.StartDateMonth,
                               StartDateYear = x.StartDateYear,
                               EndDateDay = x.EndDateDay,
                               EndDateMonth = x.EndDateMonth,
                               EndDateYear = x.EndDateYear,
                               Unit = x.Unit,
                               Visible = x.Visible,
                               ResidualInformation = x.ResidualInformation ?? string.Empty
                           }).ToList();

            return factors;
        }

        public VMRiskFactor GetRiskFactor(int entryID)
        {
            return context
                .VP_CPP_RiskFactor
                .Where(s => s.id == entryID)
                .OrderBy(x => x.Position)
                .Select(x => new VMRiskFactor()
                {
                    Id = x.id,
                    PatientId = x.PatientRecordId,
                    ReasonForDelete = x.ReasonForDel,
                    Notes = x.Notes,
                    EndDate = x.EndDate,
                    Status = x.Status ? 1 : 0,
                    Unit = x.Unit,
                    RiskFactor = x.RiskFactor,
                    ExposureDetails = x.ExposureDetails,
                    OnsetAge = x.OnsetAge,
                    LifeStage = x.LifeStage,
                    Position = x.Position,
                    SubmitDate = x.SubmitDate,
                    StartDateDay = x.StartDateDay,
                    StartDateMonth = x.StartDateMonth,
                    StartDateYear = x.StartDateYear,
                    EndDateDay = x.EndDateDay,
                    EndDateMonth = x.EndDateMonth,
                    EndDateYear = x.EndDateYear,
                    Visible = x.Visible,
                    ResidualInformation = x.ResidualInformation

                }).FirstOrDefault();
        }
        public int AddRiskFactor(VMRiskFactor vm,int userId,string ipAddress)
        {

            //RemoveRiskFactors(vm.PatientId);

            DateTime dtSubmit = DateTime.Now;

            var riskFactor = new VP_CPP_RiskFactor()
            {
                PatientRecordId = vm.PatientId,
                OnsetAge = vm.OnsetAge ?? 0,
                //EndDate = vm.EndDate,
                //Notes = vm.Notes,
                Position = vm.Position,
                Status = true,
                SubmitDate = dtSubmit,
                StartDateDay = vm.StartDateDay,
                StartDateMonth = vm.StartDateMonth,
                StartDateYear = vm.StartDateYear,
                EndDateDay = vm.EndDateDay,
                EndDateMonth = vm.EndDateMonth,
                EndDateYear = vm.EndDateYear,
                Unit = vm.Unit,
                Visible = vm.Visible,
                ResidualInformation = vm.ResidualInformation ?? string.Empty,
                AddDate = DateTime.Now.AddYears(-5),
                AddedBy = vm.AddedBy

            };
            // set according DB field len
            riskFactor.PropSetValueForce("Notes", vm.Notes);
            riskFactor.PropSetValueForce("RiskFactor", vm.RiskFactor);
            riskFactor.PropSetValueForce("ExposureDetails", vm.ExposureDetails);
            riskFactor.PropSetValueForce("LifeStage", vm.LifeStage);
            riskFactor.PropSetValueForce("ReasonForDel", vm.ReasonForDelete);

            context.VP_CPP_RiskFactor.Add(riskFactor);
            context.SaveChanges(userId, ipAddress);
            riskFactor.ParentId = riskFactor.id;
            context.SaveChanges(userId, ipAddress);

            return riskFactor.id;
        }
        public void EditRiskFactor(VMRiskFactor vm, int userId, string ipaddress)
        {

            var entry = context.VP_CPP_RiskFactor.FirstOrDefault(s => s.id == vm.VP_RiskFactor_Edit.Id);
            if (entry != null)
            {
                //entry.PatientRecordId = vm.VP_RiskFactor_Edit.PatientId;
                //entry.RiskFactor = vm.VP_RiskFactor_Edit.RiskFactor;
                //entry.ExposureDetails = vm.VP_RiskFactor_Edit.ExposureDetails;
                //entry.OnsetAge = vm.VP_RiskFactor_Edit.OnsetAge ?? 0;
                //entry.EndDate = vm.VP_RiskFactor_Edit.EndDate;
                //entry.LifeStage = vm.VP_RiskFactor_Edit.LifeStage;
                //entry.Notes = vm.VP_RiskFactor_Edit.Notes;
                //entry.Position = vm.VP_RiskFactor_Edit.Position;
                //entry.ReasonForDel = vm.VP_RiskFactor_Edit.ReasonForDelete;
                //if (vm.VP_RiskFactor_Edit.SubmitDate.HasValue)
                //    entry.SubmitDate = vm.VP_RiskFactor_Edit.SubmitDate.Value;

                //entry.StartDateDay = vm.VP_RiskFactor_Edit.StartDateDay;
                //entry.StartDateMonth = vm.VP_RiskFactor_Edit.StartDateMonth;
                //entry.StartDateYear = vm.VP_RiskFactor_Edit.StartDateYear;
                //entry.Unit = vm.VP_RiskFactor_Edit.Unit;

                //entry.Notes = vm.VP_RiskFactor_Edit.Notes;
                //entry.Position = vm.VP_RiskFactor_Edit.Position;
                //entry.Status = vm.VP_RiskFactor_Edit.Status == 1;

                //entry.UpdatedBy = userId;// vm.UpdatedBy;
                //entry.UpdateDate = DateTime.Now;

                ////context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                //context.SaveChanges(userId, ipaddress);

                entry.UpdateDate = DateTime.Now;
                entry.UpdatedBy = vm.UpdatedBy;

                var newRiskFactor = new VP_CPP_RiskFactor();
                newRiskFactor.ParentId = entry.ParentId;
                newRiskFactor.PatientRecordId = vm.VP_RiskFactor_Edit.PatientId;
                newRiskFactor.RiskFactor = vm.VP_RiskFactor_Edit.RiskFactor;
                newRiskFactor.ExposureDetails = vm.VP_RiskFactor_Edit.ExposureDetails;
                newRiskFactor.OnsetAge = vm.VP_RiskFactor_Edit.OnsetAge ?? 0;
                newRiskFactor.EndDate = vm.VP_RiskFactor_Edit.EndDate;
                newRiskFactor.LifeStage = vm.VP_RiskFactor_Edit.LifeStage;
                newRiskFactor.Notes = vm.VP_RiskFactor_Edit.Notes;
                newRiskFactor.Position = vm.VP_RiskFactor_Edit.Position;
                newRiskFactor.ReasonForDel = vm.VP_RiskFactor_Edit.ReasonForDelete;
                if (vm.VP_RiskFactor_Edit.SubmitDate.HasValue)
                    newRiskFactor.SubmitDate = vm.VP_RiskFactor_Edit.SubmitDate.Value;

                newRiskFactor.StartDateDay = vm.VP_RiskFactor_Edit.StartDateDay;
                newRiskFactor.StartDateMonth = vm.VP_RiskFactor_Edit.StartDateMonth;
                newRiskFactor.StartDateYear = vm.VP_RiskFactor_Edit.StartDateYear;
                newRiskFactor.EndDateDay = vm.VP_RiskFactor_Edit.EndDateDay;
                newRiskFactor.EndDateMonth = vm.VP_RiskFactor_Edit.EndDateMonth;
                newRiskFactor.EndDateYear = vm.VP_RiskFactor_Edit.EndDateYear;
                newRiskFactor.Unit = vm.VP_RiskFactor_Edit.Unit;

                newRiskFactor.SubmitDate = DateTime.Now;
                newRiskFactor.Notes = vm.VP_RiskFactor_Edit.Notes;
                newRiskFactor.Position = vm.VP_RiskFactor_Edit.Position;
                newRiskFactor.Status = vm.VP_RiskFactor_Edit.Status == 1;
                newRiskFactor.Visible = vm.VP_RiskFactor_Edit.Visible;
                newRiskFactor.ResidualInformation = entry.ResidualInformation;

                newRiskFactor.AddedBy = userId;// vm.UpdatedBy;
                newRiskFactor.AddDate = DateTime.Now;
                newRiskFactor.UpdatedBy = 0;
                newRiskFactor.UpdateDate = null;
                context.VP_CPP_RiskFactor.Add(newRiskFactor);

                //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                context.SaveChanges(userId, ipaddress);
            }
        }
        public List<VMCPPProblemList> GetCPP_AllProblemList(int practiceId, int patientID, bool isProblemList)
        {
            List<VMCPPProblemList> lst = new List<VMCPPProblemList>();

            //lst = (from x in context.VP_CPP_Problem_List
            //       join p in context.PatientRecords on x.PatientRecordId equals p.Id
            //       let appointment = context.Appointments.Where(a => a.Id == x.AppointmentID).FirstOrDefault()
            //       where p.PracticeId == practiceId && (x.PatientRecordId == patientID &&
            //               x.Deleted == false &&
            //               x.IsProblemList == isProblemList)
            //       orderby x.Position, x.Id descending
            //       select new VP_CPP_Problem_List_VM
            //       {
            //           Id = x.Id,
            //           AddDate = x.AddDate,
            //           UpdateDate = x.UpdateDate,
            //           PatientRecordId = x.PatientRecordId,
            //           Life_Stage = x.Life_Stage,
            //           Diagnosis = x.Diagnosis,
            //           Problem_Description = x.Problem_Description,
            //           Problem_Status = x.Problem_Status ? 1 : 0,
            //           Notes = x.Notes,
            //           Position = x.Position,
            //           Proc_Interv = x.Proc_Interv,
            //           ProcDate_Day = x.ProcDate_Day,
            //           ProcDate_Month = x.ProcDate_Month,
            //           ProcDate_Year = x.ProcDate_Year,
            //           ProcDate = x.ProcDate,
            //           Diagnosis_id = x.Diagnosis_id,
            //           Record_Status = x.Record_Status,
            //           Reason_for_Del = x.Reason_for_Del,
            //           Submit_Date = x.Submit_Date,

            //           DateOfOnset_Day = x.DateOfOnset_Day,
            //           DateOfOnset_Month = x.DateOfOnset_Month,
            //           DateOfOnset_Year = x.DateOfOnset_Year,
            //           DateOfOnset = x.DateOfOnset,
            //           ResolutionDate_Day = x.ResolutionDate_Day,
            //           ResolutionDate_Month = x.ResolutionDate_Month,
            //           ResolutionDate_Year = x.ResolutionDate_Year,
            //           ResolutionDate = x.ResolutionDate,
            //           Years = x.Years,
            //           Units = x.Units,
            //           visible = x.Visible,
            //           AppointmentDate = (appointment == null ? null : (DateTime?)appointment.appointmentTime)
            //       }).ToList();

            lst = (from x in (from a in context.VP_CPP_Problem_List
                              where a.PatientRecordId == patientID && a.IsProblemList == isProblemList
                              group a by new { a.ParentId } into g
                              select g.OrderByDescending(b => b.AddDate).FirstOrDefault())
                   let appointment = context.Appointments.Where(a => a.Id == x.AppointmentID).FirstOrDefault()
                   where x.Deleted == false
                   orderby x.Position
                   select new VMCPPProblemList()
                   {
                       Id = x.Id,
                       PatientRecordId = x.PatientRecordId,
                       Life_Stage = x.Life_Stage,
                       DiagnosticDescription = x.DiagnosticDescription,
                       Problem_Description = x.Problem_Description,
                       Problem_Status = x.Problem_Status,
                       Notes = x.Notes,
                       Position = x.Position,
                       Proc_Interv = x.Proc_Interv,
                       ProcDate_Day = x.ProcDate_Day,
                       ProcDate_Month = x.ProcDate_Month,
                       ProcDate_Year = x.ProcDate_Year,
                       ProcDate = x.ProcDate,
                       Diagnosis_id = x.Diagnosis_id,
                       Record_Status = x.Record_Status,
                       Reason_for_Del = x.Reason_for_Del,
                       Submit_Date = x.Submit_Date,
                       DateOfOnset_Day = x.DateOfOnset_Day,
                       DateOfOnset_Month = x.DateOfOnset_Month,
                       DateOfOnset_Year = x.DateOfOnset_Year,
                       DateOfOnset = x.DateOfOnset,
                       ResolutionDate_Day = x.ResolutionDate_Day,
                       ResolutionDate_Month = x.ResolutionDate_Month,
                       ResolutionDate_Year = x.ResolutionDate_Year,
                       ResolutionDate = x.ResolutionDate,
                       Years = x.Years,
                       Units = x.Units,
                       visible = x.Visible,
                       AppointmentDate = (appointment == null ? null : (DateTime?)appointment.appointmentTime)
                   }).ToList();



            return lst;
        }
        public List<VMCPPProblemList> GetCPP_ProblemList(int patientID, bool isProblemList, DateTime? frm = null, DateTime? to = null)
        {
            List<VMCPPProblemList> lst = new List<VMCPPProblemList>();

            lst = (from x in (from a in context.VP_CPP_Problem_List
                              where a.PatientRecordId == patientID && a.IsProblemList == isProblemList &&
                              ((frm.HasValue && to.HasValue) ? (a.AddDate >= frm && a.AddDate <= to) : 1 == 1)
                              group a by new { a.ParentId } into g
                              select g.OrderByDescending(b => b.AddDate).FirstOrDefault())
                   let appointment = context.Appointments.Where(a => a.Id == x.AppointmentID).FirstOrDefault()
                   let u = context.Users.Where(t => t.UserID == x.AddedBy).FirstOrDefault()
                   where x.Deleted == false
                   orderby x.Position
                   select new VMCPPProblemList()
                   {
                       Id = x.Id,
                       PatientRecordId = x.PatientRecordId,
                       Life_Stage = x.Life_Stage,
                       DiagnosticDescription = x.DiagnosticDescription,
                       Problem_Description = x.Problem_Description,
                       Problem_Status = x.Problem_Status,
                       Notes = x.Notes,
                       Position = x.Position,
                       Proc_Interv = x.Proc_Interv,
                       ProcDate_Day = x.ProcDate_Day,
                       ProcDate_Month = x.ProcDate_Month,
                       ProcDate_Year = x.ProcDate_Year,
                       ProcDate = x.ProcDate,
                       Diagnosis_id = x.Diagnosis_id,
                       Record_Status = x.Record_Status,
                       Reason_for_Del = x.Reason_for_Del,
                       Submit_Date = x.Submit_Date,
                       DateOfOnset_Day = x.DateOfOnset_Day,
                       DateOfOnset_Month = x.DateOfOnset_Month,
                       DateOfOnset_Year = x.DateOfOnset_Year,
                       DateOfOnset = x.DateOfOnset,
                       ResolutionDate_Day = x.ResolutionDate_Day,
                       ResolutionDate_Month = x.ResolutionDate_Month,
                       ResolutionDate_Year = x.ResolutionDate_Year,
                       ResolutionDate = x.ResolutionDate,
                       Units = x.Units,
                       Years = x.Years,
                       visible = x.Visible,
                       ResidualInformation = x.ResidualInformation ?? string.Empty,
                       CodingSystem = x.CodingSystem,
                       DiagnosticCode = x.DiagnosticCode,
                       AddDate = x.AddDate,
                       AddedByName = (u == null ? string.Empty : ((u.LastName ?? string.Empty) + ", " + (u.FirstName ?? string.Empty))),
                       AppointmentDate = (appointment == null ? null : (DateTime?)appointment.appointmentTime)
                   }).ToList();

            //lst = context.VP_CPP_Problem_List
            //    .Where(p => p.PatientRecordId == patientID &&
            //            p.IsProblemList == isProblemList && p.Visible &&
            //            p.Deleted == false &&
            //            ((frm.HasValue && to.HasValue) ? (p.AddDate >= frm && p.AddDate <= to) : 1 == 1))
            //    .OrderBy(b => b.Position).ThenByDescending(t => t.Id)
            //    .Select(x => new VP_CPP_Problem_List_VM()
            //    {
            //        Id = x.Id,
            //        PatientRecordId = x.PatientRecordId,
            //        Life_Stage = x.Life_Stage,
            //        Diagnosis = x.Diagnosis,
            //        Problem_Description = x.Problem_Description,
            //        Problem_Status = x.Problem_Status ? 1 : 0,
            //        Notes = x.Notes,
            //        Position = x.Position,
            //        Proc_Interv = x.Proc_Interv,
            //        ProcDate_Day = x.ProcDate_Day,
            //        ProcDate_Month = x.ProcDate_Month,
            //        ProcDate_Year = x.ProcDate_Year,
            //        ProcDate = x.ProcDate,
            //        Diagnosis_id = x.Diagnosis_id,
            //        Record_Status = x.Record_Status,
            //        Reason_for_Del = x.Reason_for_Del,
            //        Submit_Date = x.Submit_Date,
            //        DateOfOnset_Day = x.DateOfOnset_Day,
            //        DateOfOnset_Month = x.DateOfOnset_Month,
            //        DateOfOnset_Year = x.DateOfOnset_Year,
            //        DateOfOnset = x.DateOfOnset,
            //        ResolutionDate_Day = x.ResolutionDate_Day,
            //        ResolutionDate_Month = x.ResolutionDate_Month,
            //        ResolutionDate_Year = x.ResolutionDate_Year,
            //        ResolutionDate = x.ResolutionDate,
            //        Units = x.Units,
            //        Years = x.Years

            //    }).ToList();

            return lst;
        }
        public List<VMFamilyHistory> GetFamilyHistory(int patientID, DateTime? frm = null, DateTime? to = null)
        {
            List<VMFamilyHistory> lst = new List<VMFamilyHistory>();
            lst = (from x in (from a in context.VP_CPP_FamilyHistory
                              where a.PatientRecordId == patientID &&
                              ((frm.HasValue && to.HasValue) ? (a.AddDate >= frm && a.AddDate <= to) : 1 == 1)
                              group a by new { a.ParentId } into g
                              select g.OrderByDescending(b => b.AddDate).FirstOrDefault())
                   where x.Deleted == false
                   orderby x.Position
                   select new VMFamilyHistory()
                   {
                       Id = x.Id,
                       AddDate = x.AddDate,
                       ProblemDescription = x.ProblemDescription,
                       Treatment = x.Treatment,
                       Notes = x.Notes,
                       StartDateDay = x.StartDateDay,
                       StartDateMonth = x.StartDateMonth,
                       StartDateYear = x.StartDateYear,
                       AgeOnset = x.AgeOnset,
                       YearsDays = x.YearsDays,
                       RelationShip = x.RelationShip,
                       Status = x.Status,
                       Position = x.Position,
                       Unit = x.Units,
                       LifeStage = x.LifeStage,
                       Visible = x.Visible,
                       ResidualInformation = x.ResidualInformation ?? string.Empty,
                       CodingSystem = x.CodingSystem,
                       DiagnosticCode = x.DiagnosticCode,
                       DiagnosticDescription = x.DiagnosticDescription
                   }).ToList();

            return lst;
        }
        public VMFamilyHistory GetSpecificFamilyHistory(int entryID)
        {
            VMFamilyHistory vm = context
                                    .VP_CPP_FamilyHistory
                                    .Where(s => s.Id == entryID)
                                    .Select(x => new VMFamilyHistory()
                                    {
                                        Id = x.Id,
                                        ProblemDescription = x.ProblemDescription,
                                        Treatment = x.Treatment,
                                        Notes = x.Notes,
                                        StartDateDay = x.StartDateDay,
                                        StartDateMonth = x.StartDateMonth,
                                        StartDateYear = x.StartDateYear,
                                        AgeOnset = x.AgeOnset,
                                        YearsDays = x.YearsDays,
                                        Unit = x.Units,
                                        RelationShip = x.RelationShip,
                                        Status = x.Status,
                                        LifeStage = x.LifeStage,
                                        Position = x.Position,
                                        ReasonForDelete = x.ReasonForDelete,
                                        Visible = x.Visible,
                                        CodingSystem = x.CodingSystem,
                                        DiagnosticCode = x.DiagnosticCode,
                                        DiagnosticDescription = x.DiagnosticDescription

                                    }).FirstOrDefault();

            return vm;

        }
        public int AddFamilyHistory(VMFamilyHistory vm,int userId,string ipAddress, out string resData)
        {
            var familyHistory = new VP_CPP_FamilyHistory()
            {
                PatientRecordId = vm.PatientID,
                Notes = vm.Notes,
                StartDateDay = vm.StartDateDay,
                StartDateMonth = vm.StartDateMonth,
                StartDateYear = vm.StartDateYear,
                AgeOnset = vm.AgeOnset,
                YearsDays = vm.YearsDays,
                Status = 1,
                Units = vm.Unit,
                Position = vm.Position,
                Visible = vm.Visible,
                CodingSystem = vm.CodingSystem,
                DiagnosticCode = vm.DiagnosticCode,
                ResidualInformation = vm.ResidualInformation,
                AddDate = DateTime.Now.AddYears(-5),
                AddedBy = vm.AddedBy
            };

            familyHistory.PropSetValueForce("Treatment", vm.Treatment);
            familyHistory.PropSetValueForce("RelationShip", vm.RelationShip);
            familyHistory.PropSetValueForce("LifeStage", vm.LifeStage);
            familyHistory.PropSetValueForce("DiagnosticDescription", vm.DiagnosticDescription);

            resData = string.Empty;
            if (!string.IsNullOrWhiteSpace(vm.DiagnosticDescription))
            {
                familyHistory.PropSetValueForce("DiagnosticDescription", vm.DiagnosticDescription);
                if (vm.DiagnosticDescription.Length > familyHistory.DiagnosticDescription.Length)
                {
                    resData = vm.DiagnosticDescription;
                }
            }

            List<string> resList = new List<string>();
            if (CdsDBHelper.PropGetMaxLength<VP_CPP_FamilyHistory>("ProblemDescription") >= vm.ProblemDescription.Length)
            {
                familyHistory.ProblemDescription = vm.ProblemDescription;
            }
            else
            {
                familyHistory.Normalize("ProblemDescription",
                                  vm.ProblemDescription,
                                  ref resList,
                                  CdsDBHelper.PropGetMaxLength<FamilyHistoryResidualInfo>("content"));
            }

            int idx = 0;
            foreach (var v in resList)
            {
                FamilyHistoryResidualInfo info = new Cerebrum.Data.FamilyHistoryResidualInfo();
                info.name = "";
                if (!info.PropSetValue("content", v))
                {
                    info.PropSetValueForce("content", v);
                }
                info.name = "content" + idx.ToString("000");
                info.dataType = "description";
                if (familyHistory.FamilyHistoryResidualInfos == null)
                {
                    familyHistory.FamilyHistoryResidualInfos = new List<FamilyHistoryResidualInfo>();
                }
                familyHistory.FamilyHistoryResidualInfos.Add(info);
                idx++;
            }

            if (!string.IsNullOrWhiteSpace(vm.ProblemDescription)) //(resList.Count > 0)
            {
                resData = vm.ProblemDescription + "\n" + resData;
            }

            context.VP_CPP_FamilyHistory.Add(familyHistory);
            context.SaveChanges(userId, ipAddress);

            familyHistory.ParentId = familyHistory.Id;
            context.SaveChanges(userId, ipAddress);
            //if (!string.IsNullOrWhiteSpace(resData))
            //{
                //AddPatientNotes(vm.PatientID, resData, "Family History",OfficeId);
            //}

            return familyHistory.Id;
        }
        public void EditFamilyHistory(VMFamilyHistory vm,int userId,string ipAddress)
        {
            var entry = context.VP_CPP_FamilyHistory.FirstOrDefault(s => s.Id == vm.Id);
            if (entry != null)
            {
                entry.UpdateDate = DateTime.Now;
                entry.UpdatedBy = userId;

                var newFamilyHistory = new VP_CPP_FamilyHistory();
                newFamilyHistory.ParentId = entry.ParentId;
                newFamilyHistory.PatientRecordId = entry.PatientRecordId;
                newFamilyHistory.ProblemDescription = vm.ProblemDescription;

                newFamilyHistory.Notes = vm.Notes;
                newFamilyHistory.StartDateDay = vm.StartDateDay;
                newFamilyHistory.StartDateMonth = vm.StartDateMonth;
                newFamilyHistory.StartDateYear = vm.StartDateYear;
                newFamilyHistory.AgeOnset = vm.AgeOnset;
                newFamilyHistory.YearsDays = vm.YearsDays;
                newFamilyHistory.RelationShip = vm.RelationShip;
                newFamilyHistory.Status = vm.Status;
                newFamilyHistory.Units = vm.Unit;
                newFamilyHistory.LifeStage = vm.LifeStage;
                newFamilyHistory.Position = vm.Position;
                newFamilyHistory.Notes = vm.Notes;
                newFamilyHistory.Status = vm.Status;
                newFamilyHistory.Position = vm.Position;
                newFamilyHistory.Visible = vm.Visible;

                newFamilyHistory.ResidualInformation = entry.ResidualInformation;
                newFamilyHistory.CodingSystem = vm.CodingSystem;
                newFamilyHistory.Treatment = vm.Treatment;
                newFamilyHistory.DiagnosticCode = vm.DiagnosticCode;
                newFamilyHistory.PropSetValueForce("DiagnosticDescription", vm.DiagnosticDescription );

                //newFamilyHistory.ReasonForDelete = vm.ReasonForDelete;
                newFamilyHistory.AddDate = DateTime.Now;
                newFamilyHistory.AddedBy = userId;

                context.VP_CPP_FamilyHistory.Add(newFamilyHistory);

                context.SaveChanges(userId, ipAddress);
            }
        }
        public void AddCPPProblem(VMCPPProblemList vm,int userId,string ipAddress)
        {
            context.VP_CPP_Problem_List.Add(new VP_CPP_Problem_List()
            {
                Id = vm.Id,
                PatientRecordId = vm.PatientRecordId,
                Life_Stage = vm.Life_Stage,
                DiagnosticDescription = vm.DiagnosticDescription,
                Problem_Description = vm.Problem_Description,
                Problem_Status = vm.Problem_Status,
                Notes = vm.Notes,
                Position = vm.Position,
                Proc_Interv = vm.Proc_Interv,
                ProcDate_Day = vm.ProcDate_Day,
                ProcDate_Month = vm.ProcDate_Month,
                ProcDate_Year = vm.ProcDate_Year,
                ProcDate = vm.ProcDate,
                Diagnosis_id = vm.Diagnosis_id,
                Record_Status = vm.Record_Status,
                Reason_for_Del = vm.Reason_for_Del,
                Submit_Date = DateTime.Now,
                DateOfOnset_Day = vm.DateOfOnset_Day,
                DateOfOnset_Month = vm.DateOfOnset_Month,
                DateOfOnset_Year = vm.DateOfOnset_Year,
                DateOfOnset = vm.DateOfOnset,
                ResolutionDate_Day = vm.ResolutionDate_Day,
                ResolutionDate_Month = vm.ResolutionDate_Month,
                ResolutionDate_Year = vm.ResolutionDate_Year,
                ResolutionDate = vm.ResolutionDate,
                Units = vm.Units,
                Years = vm.Years,

                AddedBy = vm.AddedBy,
                AddDate = DateTime.Now

            });

            context.SaveChanges(userId, ipAddress);
        }
        public int EditCPPProblem(VMCPPProblemList vm,int userId,string ipAddress, out string resDataOut)
        {
            resDataOut = string.Empty;
            int parentId = 0;
            string residualInformation = vm.ResidualInformation;
            if (vm.Id > 0)
            {
                var oldProblemList = context.VP_CPP_Problem_List.FirstOrDefault(p => p.Id == vm.Id);
                if (oldProblemList != null)
                {
                    oldProblemList.UpdateDate = DateTime.Now;
                    oldProblemList.UpdatedBy = vm.UpdatedBy;
                    parentId = oldProblemList.ParentId;
                    residualInformation = oldProblemList.ResidualInformation;
                }
            }

            var problemList = new VP_CPP_Problem_List();
            problemList.ParentId = parentId;
            problemList.Visible = vm.visible;
            problemList.IsProblemList = vm.isProblemList;
            problemList.PatientRecordId = vm.PatientRecordId;
            problemList.AppointmentID = vm.AppointmentID;

            problemList.ProcDate_Day = vm.ProcDate_Day;
            problemList.ProcDate_Month = vm.ProcDate_Month;
            problemList.ProcDate_Year = vm.ProcDate_Year;
            problemList.Diagnosis_id = vm.Diagnosis_id;
            problemList.Record_Status = vm.Record_Status;
            problemList.Submit_Date = vm.Submit_Date;
            problemList.DateOfOnset_Day = vm.DateOfOnset_Day;
            problemList.DateOfOnset_Month = vm.DateOfOnset_Month;
            problemList.DateOfOnset_Year = vm.DateOfOnset_Year;
            problemList.ResolutionDate_Day = vm.ResolutionDate_Day;
            problemList.Problem_Status = vm.Problem_Status;
            problemList.Position = vm.Position;
            problemList.ResolutionDate_Month = vm.ResolutionDate_Month;
            problemList.ResolutionDate_Year = vm.ResolutionDate_Year;
            problemList.Submit_Date = DateTime.Now;
            problemList.Years = vm.Years;

            problemList.PropSetValueForce("Notes", vm.Notes);
            problemList.PropSetValueForce("CodingSystem", vm.CodingSystem);
            problemList.PropSetValueForce("DiagnosticCode", vm.DiagnosticCode);
            problemList.PropSetValueForce("Units", vm.Units);
            problemList.PropSetValueForce("Reason_for_Del", vm.Reason_for_Del);
            problemList.PropSetValueForce("ResidualInformation", residualInformation);
            problemList.PropSetValueForce("Proc_Interv", vm.Proc_Interv);
            problemList.PropSetValueForce("Life_Stage", vm.Life_Stage);

            if (!string.IsNullOrWhiteSpace(vm.DiagnosticDescription))
            {
                problemList.PropSetValueForce("DiagnosticDescription", vm.DiagnosticDescription);
                if (vm.DiagnosticDescription.Length > problemList.DiagnosticDescription.Length)
                {
                    resDataOut = vm.DiagnosticDescription;
                }
            }
            problemList.AddDate = DateTime.Now.AddYears(-5);
            problemList.AddedBy = vm.AddedBy;

            List<string> resList = new List<string>();
            if (CdsDBHelper.PropGetMaxLength<VP_CPP_Problem_List>("Problem_Description") >= vm.Problem_Description.Length)
            {
                problemList.Problem_Description = vm.Problem_Description;
            }
            else
            {
                problemList.Normalize("Problem_Description",
                                  vm.Problem_Description,
                                  ref resList,
                                  CdsDBHelper.PropGetMaxLength<ProblemListResidualInfo>("content"));
            }

            // save chunked ProblemDescription to residual
            int idx =0;
            foreach (var v in resList)
            {
                ProblemListResidualInfo info = new Cerebrum.Data.ProblemListResidualInfo();
                info.name = "";
                if (!info.PropSetValue("content", v))
                {
                    info.PropSetValueForce("content", v);
                }
                info.name = "content" + idx.ToString("000");
                info.dataType = "description";
                if (problemList.ProblemListResidualInfos == null)
                {
                    problemList.ProblemListResidualInfos = new List<ProblemListResidualInfo>();
                }
                problemList.ProblemListResidualInfos.Add(info);
            }

            if (!string.IsNullOrWhiteSpace(vm.Problem_Description)) //(resList.Count>0)
            {
                resDataOut = vm.Problem_Description + "\n" + resDataOut;
            }
            if (string.IsNullOrWhiteSpace(resDataOut))
            {
                resDataOut = vm.Notes;
            }

            context.VP_CPP_Problem_List.Add(problemList);
            context.SaveChanges(userId, ipAddress);

            if (parentId == 0)
            {
                problemList.ParentId = problemList.Id;
                context.SaveChanges(userId, ipAddress);
            }

            //if (!string.IsNullOrWhiteSpace(resDataOut))
            //{
            //    AddPatientNotes(vm.PatientRecordId, resDataOut, "Problem List", OfficeId);
            //}

            return problemList.Id;
        }
        public VMCPPProblemList GetCPPProblem(int entryID)
        {
            VMCPPProblemList vm = new VMCPPProblemList();

            var entry = context.VP_CPP_Problem_List.FirstOrDefault(x => x.Id == entryID);
            if (entry != null)
            {
                vm.Id = entry.Id;
                vm.PatientRecordId = entry.PatientRecordId;
                vm.Life_Stage = entry.Life_Stage;
                vm.DiagnosticDescription = entry.DiagnosticDescription;
                vm.Problem_Description = entry.Problem_Description;
                vm.Problem_Status = entry.Problem_Status;
                vm.Notes = entry.Notes;
                vm.Position = entry.Position;
                vm.Proc_Interv = entry.Proc_Interv;
                vm.Years = entry.Years;
                vm.ProcDate_Day = entry.ProcDate_Day;
                vm.ProcDate_Month = entry.ProcDate_Month;
                vm.ProcDate_Year = entry.ProcDate_Year;
                vm.ProcDate = entry.ProcDate;
                vm.Units = entry.Units;
                vm.Diagnosis_id = entry.Diagnosis_id;
                vm.Record_Status = entry.Record_Status;
                vm.Reason_for_Del = entry.Reason_for_Del;
                vm.Submit_Date = entry.Submit_Date;

                vm.DateOfOnset_Day = entry.DateOfOnset_Day;
                vm.DateOfOnset_Month = entry.DateOfOnset_Month;
                vm.DateOfOnset_Year = entry.DateOfOnset_Year;
                vm.DateOfOnset = entry.DateOfOnset;

                vm.ResolutionDate_Day = entry.ResolutionDate_Day;
                vm.ResolutionDate_Month = entry.ResolutionDate_Month;
                vm.ResolutionDate_Year = entry.ResolutionDate_Year;
                vm.ResolutionDate = entry.ResolutionDate;
            }

            return vm;

        }
        #endregion

        #region Logs
        public List<VMAppointmentTestLog> GetLogs(int appointmentID, int patientID)
        {
            //return
            //        (from x in context.VP_AppointmentTestLog
            //         join u in context.Users on x.UserId equals u.UserID
            //         into ao
            //         from p in ao.DefaultIfEmpty()
            //         where
            //         x.AppointmentId == appointmentID &&
            //         x.PatientId == patientID
            //         select new VP_AppointmentTestLog_VM()
            //         {
            //             Id = x.Id,
            //             Date = x.Date,
            //             Status = x.Status,
            //             AppointmentId = x.AppointmentId,
            //             PatientRecordId = x.PatientId,
            //             IP = x.IP,
            //             UserId = x.UserId,
            //             UserName = p.UserName
            //         }).ToList();


            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("appointmentID",appointmentID),
                new SqlParameter("patientID",patientID )
            };
            return context.GetData<VMAppointmentTestLog>("[dbo].[Get_VP_Logs]", parms).ToList();


        }

        public int GetLastAppointmentID(int patientID)
        {

            int retVal = 0;

            var appt = context.VP_AppointmentTestLog
                        .Where(l => l.PatientRecordId == patientID).OrderByDescending(x => x.Date)
                        .FirstOrDefault();

            if (appt != null)
            {
                retVal = appt.AppointmentId;

            }
            return retVal;
        }

        #endregion

        #region Root Categories
        public string GetReportPhraseByID(int rpid)
        {
            string retStr = string.Empty;
            var rp = context.VPReportPhrase.FirstOrDefault(r => r.Id == rpid);
            if (rp != null)
            {
                retStr = rp.Name;
            }
            return retStr;
        }

        public void AddRootPhrase(VMReportPhrase vm,int userId,string ipAddress)
        {
            var phrase = context.VPReportPhrase.Add(new VPReportPhrase()
            {
                DrID = vm.DoctorID,
                Name = vm.Name,
                Value = vm.Name,
                Parent = -1,
                Root = -1
            });

            context.SaveChanges(userId, ipAddress);
         
        }

        public void AddPhraseSubItem(AddPhraseSubItem_VM vm,int userId, string ipAddress)
        {

            var newItem = new VPReportPhrase()
            {
                //Id = maxID,
                Name = vm.Name,
                Root = vm.RootPhraseID,
                Parent = vm.ParentID,
                Value = vm.Value,
                DrID = vm.DocID
            };

            context.VPReportPhrase.Add(newItem);
            context.SaveChanges();

            if (vm.ItemType == ItemType.Category)
            {
                //adding child element if type is "category" and not "item"
                context.VPReportPhrase.Add(new VPReportPhrase()
                {

                    Name = string.Empty,
                    Root = vm.RootPhraseID,
                    Parent = newItem.Id,
                    Value = string.Empty,
                    DrID = vm.DocID
                });
            }

            context.SaveChanges(userId, ipAddress);

        }

        public void AddOpeningStatementSubItem(AddPhraseSubItem_VM vm,int userId,string ipAddress)
        {

            var newItem = new VPReportPhrase()
            {
                //Id = maxID,
                Name = vm.Name,
                Root = vm.RootPhraseID,
                Parent = vm.ParentID,
                Value = vm.Value,
                Status = 2,
                DrID = vm.DocID

            };

            context.VPReportPhrase.Add(newItem);
            context.SaveChanges(userId, ipAddress);
            if (vm.ItemType == ItemType.Category)
            {
                //adding child element if type is "category" and not "item"
                context.VPReportPhrase.Add(new VPReportPhrase()
                {

                    Name = string.Empty,
                    Root = vm.RootPhraseID,
                    Parent = newItem.Id,
                    Value = string.Empty,
                    Status = 2,
                    DrID = vm.DocID
                });
            }

            context.SaveChanges(userId, ipAddress);

        }

        public List<VMTopLevelReportPhrase> GetReportPhrasesSkipped(List<VMTopLevelReportPhrase> lst, int doctorID, int PatientID)
        {

            //var dbList = (from r in context.VP_ReportPhrases_Skipped
            //              where r.UserID == doctorID
            //              select new VP_TopLevelReportPhrase()
            //              {
            //                  Id = r.VP_ReportPhraseID,
            //                  Skipped = r.Skip

            //              }).ToList();


            List<SqlParameter> parms = new List<SqlParameter>
            {
              new SqlParameter("doctorID",doctorID)
            };
            var dbList = context.GetData<VP_ReportPhrases_Skipped>("[dbo].[Get_VP_ReportPhrases_Skipped]", parms).ToList();
            lst.ForEach(r =>
            {
                var entry = dbList.FirstOrDefault(x => x.VP_ReportPhraseID == r.Id);
                if (entry != null)
                {
                    r.Skipped = entry.Skip;
                }

            });

            return lst;
        }

        public List<VMCPPCategory> GetCPPSkipped(List<VMCPPCategory> lst, int doctorID, int PatientID)
        {

            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("DoctorID",doctorID),
            };
            var dbList = context.GetData<VMCPPCategory>("[dbo].[Get_VP_CPP_Skipped]", parms).ToList();
            //var dbList = (from r in context.VP_CPP_Skipped
            //              where r.UserID == doctorID
            //              select new VP_CPP_Category_VM()
            //              {

            //                  Id = r.CPP_Category_ID,
            //                  Skipped = r.Skip

            //              }).ToList();

            lst.ForEach(r =>
            {
                var entry = dbList.FirstOrDefault(x => x.Id == r.Id);
                if (entry != null)
                {
                    r.Skipped = entry.Skipped;
                }

            });

            return lst;
        }

        public void SaveReportPhrasesSkipped(List<VMTopLevelReportPhrase> list, int userID, int PatientID,int modifyingUserId,string ipAddress)
        {
            var existingEntries = context.VP_ReportPhrases_Skipped.Where(r => r.UserID == userID).ToList();

            if (existingEntries != null)
            {
                context.VP_ReportPhrases_Skipped.RemoveRange(existingEntries);
                context.SaveChanges(modifyingUserId, ipAddress);
            }


            list.ForEach(r =>
            {
                context.VP_ReportPhrases_Skipped.Add(new VP_ReportPhrases_Skipped()
                {

                    VP_ReportPhraseID = r.Id,
                    UserID = userID,
                    //PatientRecordId = PatientID,
                    Skip = r.Skipped
                });
            });
            context.SaveChanges(modifyingUserId, ipAddress);
        }

        public void SaveCPPSkipped(List<VMCPPCategory> list, int userID, int PatientID, int modifyingUserId, string ipAddress)
        {
            var existingEntries = context.VP_CPP_Skipped.Where(r => r.UserID == userID).ToList();

            if (existingEntries != null)
            {
                context.VP_CPP_Skipped.RemoveRange(existingEntries);
                context.SaveChanges(modifyingUserId, ipAddress);
            }


            list.ForEach(r =>
            {
                context.VP_CPP_Skipped.Add(new VP_CPP_Skipped()
                {
                    CPP_Category_ID = r.Id,
                    UserID = userID,
                    //PatientRecordId = PatientID,
                    Skip = r.Skipped
                });
            });

            context.SaveChanges(modifyingUserId, ipAddress);
        }

        public List<VMTopLevelReportPhrase> GetPhraseSubItems(int docID, int parentID)
        {
            List<VMTopLevelReportPhrase> lst = new List<VMTopLevelReportPhrase>();

            var phrases = context.VPReportPhrase.Where(x => x.Status == 0).ToList();
            phrases = phrases.Where(p => p.Parent == parentID && p.Parent != -1).ToList(); //by parent report phrase
            phrases = phrases.Where(x => !x.DrID.HasValue || (x.DrID.HasValue && x.DrID == docID)).ToList(); //by DocID
            foreach (var item in phrases)
            {
                VMTopLevelReportPhrase newRP = new VMTopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.Name,

                };

                lst.Add(newRP);
            }

            return lst;
        }

        public List<VMTopLevelReportPhrase> GetReportPhrases(List<Cerebrum.ViewModels.VP.VMOption> options, int userID)
        {
            //select id, name  from vpop where status=0 and spec=0 and parent=-1  and (opt is null ) and (dr is null or dr=7) and (grp is null or grp like '%,0,%')  order by ordernumber
            List<VMTopLevelReportPhrase> lst = new List<VMTopLevelReportPhrase>();

            var allPhrases = context.VPReportPhrase.ToList();
            var phraseOptions = context.VPReportPhraseOption.ToList();
            var phrasesByPractice = context.VPReportPhraseByDoctor.ToList();

            #region  filtering by options 
            List<int> lstOptionIDs = new List<int>();

            options.Where(o => o.Selected == true).ToList()
                .ForEach(o => lstOptionIDs.Add(o.Id));

            var phrasesWithOptions =
                                    (from a in allPhrases
                                     join o in phraseOptions
                                     on a.Id equals o.VPVPReportPhraseId
                                     where lstOptionIDs.Contains(o.VPOptionId)
                                     select a).Distinct().ToList();

            var phrasesWithoutOptions =

                                    (from a in allPhrases
                                     join o in phraseOptions
                                      on a.Id equals o.VPVPReportPhraseId
                                      into ao
                                     from p in ao.DefaultIfEmpty()
                                     where p == null
                                     select a).Distinct().ToList();

            ////topLevelPhrases = topLevelPhrases.Where(p => p.Status == 0 && p.Spec == 0 && p.Parent == -1).OrderBy(m => m.Order).ToList();

            #endregion

            //var phrases = allPhrases;

            var phrases = phrasesWithOptions.Concat(phrasesWithoutOptions);

            phrases = phrases.Where(x => !x.DrID.HasValue || (x.DrID.HasValue && x.DrID == userID));

            var topLevelPhrases = phrases.Where(p => p.Status == 0 && p.Spec == 0 && p.Parent == -1).OrderBy(m => m.Order).ToList();

            foreach (var item in topLevelPhrases)
            {

                VMTopLevelReportPhrase newRP = new VMTopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.Name,
                    Order =  item.Order
                };

                var secLst = phrases.Where(x =>
                                            x.Parent == item.Id &&
                                            x.Root == item.Id &&
                                            x.Status == 0)
                                            .OrderBy(x => x.Name).ToList();

                if (secLst.Count > 0)
                {
                    foreach (var subItem in secLst)
                    {

                        var secLevelRP = new VMReportPhrase()
                        {
                            Id = subItem.Id,
                            Name = subItem.Name,
                            Value = string.IsNullOrEmpty(subItem.Value) ? string.Empty : subItem.Value,
                            CustomText = string.Empty

                        };

                        var thirdLst = phrases.Where(x =>
                                                        x.Parent == subItem.Id &&
                                                        x.Status == 0)
                                                        .OrderBy(x => x.Name).ToList();


                        foreach (var thirdLevelItem in thirdLst)
                        {
                            var thirdLevelRP = new VMReportPhrase()
                            {
                                Id = thirdLevelItem.Id,
                                Name = thirdLevelItem.Name,
                                Value = string.IsNullOrEmpty(thirdLevelItem.Value) ? string.Empty : thirdLevelItem.Value,
                                CustomText = string.Empty

                            };

                            var fourthLst = phrases.Where(x =>
                                                             x.Parent == thirdLevelItem.Id &&
                                                             x.Status == 0)
                                                             .OrderBy(x => x.Name).ToList();



                            foreach (var fourthLevelItem in fourthLst)
                            {
                                var fourthLevelRP = new VMReportPhrase()
                                {
                                    Id = fourthLevelItem.Id,
                                    Name = fourthLevelItem.Name,
                                    Value = string.IsNullOrEmpty(fourthLevelItem.Value) ? string.Empty : fourthLevelItem.Value,
                                    CustomText = string.Empty

                                };
                                thirdLevelRP.Phrases.Add(fourthLevelRP);
                            }

                            secLevelRP.Phrases.Add(thirdLevelRP);
                        }

                        newRP.Phrases.Add(secLevelRP);
                    }

                    lst.Add(newRP);
                }
                else
                {
                    //default phrase so that add option show up
                    newRP.Phrases.Add(new VMReportPhrase()
                    {
                        Name = string.Empty
                    });
                    lst.Add(newRP);
                }
            }

            var phrasesWithCustomText = phrasesByPractice.Where(x => x.DrID == userID).ToList();

            foreach (var item in lst)
            {
                foreach (var level1 in item.Phrases)
                {
                    level1.CustomText = GetCustomPhrase(level1.Id, phrasesWithCustomText);
                    foreach (var level2 in level1.Phrases)
                    {
                        level2.CustomText = GetCustomPhrase(level2.Id, phrasesWithCustomText);
                        foreach (var level3 in level2.Phrases)
                        {
                            level3.CustomText = GetCustomPhrase(level3.Id, phrasesWithCustomText);
                        }
                    }
                }
            }

            return lst;
        }

        private string GetCustomPhrase(int reportPhraseID, List<VPReportPhraseByDoctor> lst)
        {
            string retVal = string.Empty;
            var entry = lst.Where(x => x.VPReportPhraseID == reportPhraseID).FirstOrDefault();
            if (entry != null)
            {
                retVal = entry.Text;
            }
            return retVal;
        }

        public string GetReportPhraseValue(int rpid, int appointmentID, int patientID, int VP_AppointmentTestLogId)
        {
            string retVal = string.Empty;

            var savedValues = context.VP_ReportPhrasesSavedText.Where(s =>

                                             s.AppointmentId == appointmentID &&
                                             s.PatientRecordId == patientID &&
                                             s.TopLevelVPReportPhraseID == rpid &&
                                             s.VP_AppointmentTestLogId == VP_AppointmentTestLogId).FirstOrDefault();


            if (savedValues != null)
            {
                retVal = savedValues.Value;
            }

            return retVal;
        }

        public string GetAccumulativeValue(int lastApptID, int patientID, int rpid)
        {
            string result = string.Empty;
            List<SqlParameter> parms = new List<SqlParameter>
                        {
                            new SqlParameter("appointmentID",lastApptID),
                            new SqlParameter("patientID",patientID),
                            new SqlParameter("VP_AppointmentTestLogId",0)
                        };
            var entry = context.GetData<VP_ReportPhrasesSavedText>("[dbo].[Get_VP_ReportPhrasesSavedText]", parms).ToList()
                        .Where(v => v.TopLevelVPReportPhraseID == rpid)
                                    .OrderByDescending(a => a.VP_AppointmentTestLogId)
                                    .FirstOrDefault();


            if (entry != null)
            {
                result = entry.Value;
            }

            return result;
        }
        public List<VMTopLevelReportPhrase> LoadReportPhraseSavedValues(List<VMTopLevelReportPhrase> lst,int appointmentID,int patientID,int VP_AppointmentTestLogId)
        {



            //var savedValues = context.VP_ReportPhrasesSavedText.Where(s =>

            //                                 s.AppointmentId == appointmentID &&
            //                                 s.PatientRecordId == patientID &&
            //                                 s.VP_AppointmentTestLogId == VP_AppointmentTestLogId
            //         ).ToList();


            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("appointmentID",appointmentID),
                new SqlParameter("patientID",patientID),
                new SqlParameter("VP_AppointmentTestLogId",VP_AppointmentTestLogId)
            };
            var savedValues = context.GetData<VP_ReportPhrasesSavedText>("[dbo].[Get_VP_ReportPhrasesSavedText]", parms).ToList();


            int? lastApptID = -1;
            if (lst.Any(x => x.Accumulative))
            {
                lastApptID = GetPreviousVPAppointment(appointmentID, patientID);
            }

            lst.ForEach(s =>
            {
                if (savedValues != null && savedValues.Count > 0)
                {

                    //var entry = savedValues.Where(v => v.TopLevelVPReportPhraseID == s.Id).ToList().FirstOrDefault();
                    var entry = savedValues.Where(v => v.TopLevelVPReportPhraseID == s.Id).FirstOrDefault();
                    s.Value += entry != null ? entry.Value : string.Empty;
                }
                else

                if (s.Accumulative)
                {
                    StringBuilder sbAccum = new StringBuilder();
                    //int? lastApptID = GetPreviousVPAppointment(appointmentID, patientID);
                    if (lastApptID.HasValue)
                    {
                        //var entry = context
                        //                        .VP_ReportPhrasesSavedText
                        //                        .Where(p =>

                        //                                  p.AppointmentId == lastApptID &&
                        //                                  p.PatientRecordId == patientID
                        //                                  ).ToList()
                        //                         .Where(v => v.TopLevelVPReportPhraseID == s.Id)
                        //                        .OrderByDescending(a => a.VP_AppointmentTestLogId)
                        //                        .FirstOrDefault();


                        List<SqlParameter> parms2 = new List<SqlParameter>
                        {
                            new SqlParameter("appointmentID",lastApptID),
                            new SqlParameter("patientID",patientID),
                            new SqlParameter("VP_AppointmentTestLogId",0)
                        };
                        var entry = context.GetData<VP_ReportPhrasesSavedText>("[dbo].[Get_VP_ReportPhrasesSavedText]", parms2).ToList()
                                    .Where(v => v.TopLevelVPReportPhraseID == s.Id)
                                                .OrderByDescending(a => a.VP_AppointmentTestLogId)
                                                .FirstOrDefault();


                        if (entry != null)
                            sbAccum.Append(entry.Value).Append(Environment.NewLine);

                        s.Value += sbAccum.ToString();
                    }
                }


            });


            return lst;

        }
        public int? GetPreviousVPAppointment(int appointmentID, int patientID)
        {

            int? apptID = null;

            var lst = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetPreviousVPAppointment(appointmentID, patientID);
            if (lst != null && lst.Count > 0)
            {
                apptID = lst.FirstOrDefault().AppointmentID;
            }

            //var apptDt =  (new UserRepository()).GetAppointmentDate(appointmentID) ;
            //var appt = context
            //    .Appointments
            //    .Where(
            //            a =>
            //            a.PatientRecordId == patientID &&
            //            a.appointmentTime < apptDt)
            //            .OrderByDescending(y => y.appointmentTime)
            //            .Select(a => a).ToList().FirstOrDefault();

            //if (appt != null)
            //{
            //    apptID = appt.Id;
            //}

            return apptID;

        }

        public List<VMTopLevelReportPhrase> GetCustomReportPhrases(List<VMTopLevelReportPhrase> lst, int doctorID)
        {
            List<VMTopLevelReportPhrase> resLst = new List<VMTopLevelReportPhrase>();

            List<SqlParameter> parms = new List<SqlParameter>
            {
              new SqlParameter("doctorID",doctorID)
            };
            var lstCustom = context.GetData<VP_ReportPhrase_Custom>("[dbo].[Get_VP_ReportPhrases_Custom]", parms).ToList();
            if (lstCustom != null && lstCustom.Count > 0)
            {
                List<VMTopLevelReportPhrase> newLst = new List<VMTopLevelReportPhrase>();
                //going through main level
                //var mainLst = lstCustom.Where(x => x.Rank != -1).ToList();
                var mainLst = lstCustom.ToList();
                if (mainLst != null)
                {
                    foreach (var item in mainLst)
                    {
                        if (item.Visible)
                        {
                            var entry = lst.Where(x => x.Id == item.VP_ReportPhraseId).FirstOrDefault();
                            if (entry != null)
                            {
                                VMTopLevelReportPhrase newEntry = new VMTopLevelReportPhrase()
                                {
                                    Id = item.VP_ReportPhraseId,
                                    Name = item.Text,
                                    Order = item.Rank                // read order(rank) from Custom phrases
                                };
                                newEntry.Accumulative = item.Accumulative;
                                newEntry.Phrases = entry.Phrases;
                                //newEntry.Value = entry.Value;
                                newEntry.Value = entry.Value;
                                newLst.Add(newEntry);
                            }
                        }
                    }
                }

                lst.ForEach(l =>
                {
                    var entry = lstCustom.Where(x => x.VP_ReportPhraseId == l.Id).FirstOrDefault();
                    if (entry == null)
                    {
                        VMTopLevelReportPhrase newEntry = new VMTopLevelReportPhrase()
                        {
                            Id = l.Id,
                            Name = l.Name
                        };
                        newEntry.Accumulative = l.Accumulative;
                        newEntry.Phrases = l.Phrases;
                        newEntry.Value = l.Value;
                        newEntry.Order = l.Order;                    // read order(rank) from original phrases
                        newLst.Add(newEntry);
                    }
                });

                resLst = newLst.OrderBy(x => x.Order).ToList();
            }
            else
            {
                resLst = lst;
            }

            return resLst;
        }

        public List<VP_ReportPhraseSetting> LoadReportPhraseSettings(List<VMTopLevelReportPhrase> lstTopLevel)
        {
            List<VP_ReportPhraseSetting> lst = new List<VP_ReportPhraseSetting>();

            int counter = 1;

            foreach (var item in lstTopLevel)
            {
                lst.Add(new VP_ReportPhraseSetting()
                {
                    Id = item.Id,
                    VPReportPhraseID = item.Id,
                    Text = item.Name,
                    OriginalText = item.Name,
                    Visible = true,
                    Rank = counter++
                });
            }

            return lst;
        }

        public List<VP_CPP_Setting_VM> GetCPPSettings()
        {
            int order = 1;

            List<VP_CPP_Setting_VM> lst = new List<VP_CPP_Setting_VM>();

            var lstCPP = context.VP_CPP_Category.ToList();

            foreach (var item in lstCPP)
            {
                lst.Add(new VP_CPP_Setting_VM()
                {
                    VP_CPP_Item_ID = item.Id,
                    Text = item.Text,
                    Order = order++,
                    Visible = true
                });
            }

            return lst;
        }

        public void SaveCPPSettings(int doctorID, List<VP_CPP_Setting_VM> lst,int modifyingUserId,string ipAddress)
        {

            var lstCPP = context.VP_CPP_Setting.Where(s => s.DoctorID == doctorID).ToList();

            context.VP_CPP_Setting.RemoveRange(lstCPP);

            context.SaveChanges(modifyingUserId, ipAddress);
            foreach (var item in lst)
            {
                context.VP_CPP_Setting.Add(new VP_CPP_Setting()
                {
                    //PatientID = patientID,
                    DoctorID = doctorID,
                    Text = item.Text,
                    Order = item.Order,
                    Visible = item.Visible,
                    VP_CPP_Category_Id = item.VP_CPP_Item_ID,
                });
            }

            context.SaveChanges(modifyingUserId, ipAddress);

        }

        public List<VP_CPP_Setting_VM> GetCPPSettingsCustom(int doctorID, List<VP_CPP_Setting_VM> lst)
        {

            var lstCPP = context.VP_CPP_Setting.Where(s => s.DoctorID == doctorID).ToList();

            foreach (var item in lst)
            {
                var entry = lstCPP.Where(s => s.VP_CPP_Category_Id == item.VP_CPP_Item_ID && s.DoctorID == doctorID).FirstOrDefault();
                if (entry != null)
                {
                    item.DoctorID = entry.DoctorID;
                    item.Text = entry.Text;
                    item.Order = entry.Order;
                    item.Visible = entry.Visible;
                }
            }

            lst = lst.OrderBy(s => s.Order).ToList();

            return lst;
        }

        public List<VP_ReportPhraseSetting> GetReportPhraseSettingsCustom(List<VP_ReportPhraseSetting> lst, int doctorID)
        {
            List<VP_ReportPhraseSetting> lstNew = new List<VP_ReportPhraseSetting>();

            //var lstCustom = context.VP_ReportPhrase_Custom.Where(p => p.DoctorID == doctorID).OrderBy(m => m.Rank).ToList();
            List<SqlParameter> parms = new List<SqlParameter>
            {
              new SqlParameter("doctorID",doctorID)
            };
            var lstCustom = context.GetData<VP_ReportPhrase_Custom>("[dbo].[Get_VP_ReportPhrases_Custom]", parms).ToList();

            //lstCustom = MainLevel ? lstCustom.Where(x => x.Rank != -1).ToList() : lstCustom.Where(x => x.Rank == -1).ToList();

            if (lstCustom != null && lstCustom.Count > 0)
            {
                foreach (var item in lstCustom)
                {

                    string orgText = string.Empty;
                    var entry = lst.Where(x => x.VPReportPhraseID == item.VP_ReportPhraseId).FirstOrDefault();
                    if (entry != null)
                    {
                        orgText = entry.OriginalText;
                    }


                    lstNew.Add(new VP_ReportPhraseSetting()
                    {
                        Id = item.Id,
                        Text = item.Text,
                        OriginalText = string.IsNullOrEmpty(orgText) ? item.Text : orgText,
                        Visible = item.Visible,
                        Rank = item.Rank,
                        VPReportPhraseID = item.VP_ReportPhraseId,
                        Accumulative = item.Accumulative
                    });
                }

                lst.ForEach(l =>
                {
                    var entry = lstCustom.Where(x => x.VP_ReportPhraseId == l.Id).FirstOrDefault();
                    if (entry == null)
                    {
                        lstNew.Add(new VP_ReportPhraseSetting()
                        {
                            Id = l.Id,
                            Text = l.Text,
                            OriginalText = l.Text,
                            Visible = l.Visible,
                            Rank = l.Rank,
                            VPReportPhraseID = l.VPReportPhraseID,
                            Accumulative = l.Accumulative
                        });
                    }
                });
            }
            else
            {
                lstNew = lst;
            }
            
            return lstNew;
        }


        public void SaveReportPhraseSetting(VP_ReportPhraseSetting_VM model,int modifyingUserId,string ipAddress)
        {

            model.PatientID = model.PatientID;
            model.PracticeID = 0;
            // model.UserID = model.UserID;
            //deleting already existing entries
            var lstToDel = context.VP_ReportPhrase_Custom.Where(p => p.DoctorID == model.DoctorID).OrderBy(m => m.Rank).ToList();
            context.VP_ReportPhrase_Custom.RemoveRange(lstToDel);
            context.SaveChanges(modifyingUserId, ipAddress);

            List<VP_ReportPhrase_Custom> lst = new List<VP_ReportPhrase_Custom>();
            model.VP_ReportPhraseSettings = model.VP_ReportPhraseSettings.OrderBy(m => m.Rank).ToList();
            foreach (var setting in model.VP_ReportPhraseSettings)
            {
                lst.Add(new VP_ReportPhrase_Custom()
                {
                    VP_ReportPhraseId = setting.VPReportPhraseID,
                    Text = setting.Text,
                    Visible = setting.Visible,
                    Rank = setting.Rank,
                    Accumulative = setting.Accumulative,
                    PatientRecordId = model.PatientID,
                    PracticeID = model.PracticeID,
                    DoctorID = model.DoctorID,

                });
            }
            context.VP_ReportPhrase_Custom.AddRange(lst);
            context.SaveChanges(modifyingUserId, ipAddress);

        }

        public VP_ReportPhrase_Val GetReportPhraseCustomValue(int reportPhraseID, int userID)
        {
            VP_ReportPhrase_Val vm = new VP_ReportPhrase_Val();

            vm.ReportPhraseID = reportPhraseID;
            vm.UserID = userID;
            vm.Text = string.Empty;
            vm.OriginalText = string.Empty;

            var entryOrg = context.VPReportPhrase.Where(x => x.Id == reportPhraseID).FirstOrDefault();
            if (entryOrg != null)
            {
                vm.OriginalText = entryOrg.Name;
                vm.Value = entryOrg.Value;
            }

            var entry = context.VPReportPhraseByDoctor
                .Where(x => x.VPReportPhraseID == reportPhraseID && x.DrID == userID).FirstOrDefault();

            if (entry != null)
            {
                vm.Text = entry.Text;
            }


            return vm;
        }

        public void SaveReportPhraseCustomValue(VP_ReportPhrase_Val vm,int modifyingUserId,string ipAddress)
        {
            var entry = context.VPReportPhraseByDoctor
                .Where(x => x.VPReportPhraseID == vm.ReportPhraseID && x.DrID == vm.UserID).FirstOrDefault();

            if (entry != null)
            {
                context.VPReportPhraseByDoctor.Remove(entry);
                context.SaveChanges(modifyingUserId, ipAddress);
            }

            context.VPReportPhraseByDoctor.Add(new VPReportPhraseByDoctor()
            {
                DrID = vm.UserID,
                Text = vm.Text,
                VPReportPhraseID = vm.ReportPhraseID
            });

            context.SaveChanges(modifyingUserId, ipAddress);
        }


        public void AddReportPhraseSavedValue(int appointmentID, int patientID, int reportPhraseID, int topLevelReportPhraseID, int userId,string ipAddress)
        {
            context.VP_ReportPhrasesSavedValue.Add(
                                                   new VP_ReportPhrasesSavedValue()
                                                   {
                                                       AppointmentId = appointmentID,
                                                       PatientRecordId = patientID,
                                                       VPReportPhraseId = reportPhraseID,
                                                       TopLevelReportPhraseId = topLevelReportPhraseID,
                                                       UserID = userId,
                                                       DateEntered = DateTime.Now
                                                   });


            context.SaveChanges(userId, ipAddress);
        }
        public void RemoveReportPhraseSavedValue(int appointmentID, int patientID, int reportPhraseID, int topLevelReportPhraseID,int userId,string ipAddress)
        {
            var entry =

                context.VP_ReportPhrasesSavedValue.Where(r =>
                                                            r.AppointmentId == appointmentID &&
                                                            r.PatientRecordId == patientID &&
                                                            r.VPReportPhraseId == reportPhraseID &&
                                                            r.TopLevelReportPhraseId == topLevelReportPhraseID).FirstOrDefault();


            if (entry != null)
            {
                context.VP_ReportPhrasesSavedValue.Remove(entry);

                context.SaveChanges(userId, ipAddress);
            }
        }

        #endregion

        #region Measuremets
        public Tuple<List<VMCategory>, List<VMCategory>> GetUniqueCategories()
        {

            List<VMCategory> catsVS = new List<VMCategory>();
            List<VMCategory> catsLabs = new List<VMCategory>();

            var catsdDB = context.VPCategory.ToList();
            var measDB = context.VPUniqueMeasurement.OrderBy(x => x.Order).ToList();

            #region Vital Sings

            var meas = measDB.Where(m =>
                                            m.Status == 0 &&
                                            m.Spec == 0
                                            && m.Type == 0
                                    ).ToList();

            if (meas.Count > 0)
            {

                VMCategory newCat = new VMCategory()
                {
                    Name = "Vitals"
                };

                foreach (var mea in meas)
                {
                    newCat.Measurements.Add(new Cerebrum.ViewModels.VP.VMVisitMeasurement()
                    {

                        Id = mea.Id,
                        Name = mea.Name,
                        Order = mea.Order,
                        Units = mea.Units,
                        Normal = mea.Normal,
                        Range1 = mea.Range1,
                        Range2 = mea.Range2,
                        Spec = mea.Spec,
                        Type = mea.Type,
                        DrID = mea.DrID,
                        Status = mea.Status,
                        Options = mea.Options,
                        Testcode = mea.Testcode,
                        Cdid = mea.Cdid,
                        ValueType = mea.ValueType
                    });
                }

                catsVS.Add(newCat);
            }

            #endregion

            #region Labs
            meas = measDB.Where(m =>
                                          m.Status == 0 &&
                                          m.Spec == 0 &&
                                          m.Type == 1
                                  ).ToList();



            if (meas.Count > 0)
            {

                VMCategory newCat = new VMCategory()
                {
                    Name = "Labs"
                };

                foreach (var mea in meas)
                {
                    newCat.Measurements.Add(new Cerebrum.ViewModels.VP.VMVisitMeasurement()
                    {

                        Id = mea.Id,
                        Name = mea.Name,
                        Order = mea.Order,
                        Units = mea.Units,
                        Normal = mea.Normal,
                        Range1 = mea.Range1,
                        Range2 = mea.Range2,
                        Spec = mea.Spec,
                        Type = mea.Type,
                        DrID = mea.DrID,
                        Status = mea.Status,
                        Options = mea.Options,
                        Testcode = mea.Testcode,
                        Cdid = mea.Cdid,
                        ValueType = mea.ValueType
                    });
                }

                catsLabs.Add(newCat);
            }

            #endregion

            return new Tuple<List<VMCategory>, List<VMCategory>>(catsVS, catsLabs);
        }
        public void LoadSavedValues(List<VMCategory> lstVS,List<VMCategory> lstLab,List<VMTemplateDetail> lstCDF,int appointmentID,int patientID,int VP_AppointmentTestLogId,DateTime? logDate = null,DateTime? appointmentDate = null)
        {

            List<SqlParameter> parms = new List<SqlParameter>
            {
              new SqlParameter("appointmentId",appointmentID),
              new SqlParameter("PatientRecordId",patientID),
              new SqlParameter("VP_AppointmentTestLogId",VP_AppointmentTestLogId),
            };
            var savedValues = context.GetData<VMVisitMeasurementSavedValue>("[dbo].[SP_Get_VP_MeasurementSavedValue]", parms);

            //var labMeas = lstLab.SelectMany(c => c.Measurements).Distinct();
            //var vitalsMeas = lstVS.SelectMany(c => c.Measurements).Distinct();

            //var savedValues =
            //context.VP_MeasurementSavedValue
            //.Where(s => s.AppointmentId == appointmentID &&
            //            s.PatientRecordId == patientID &&
            //            s.VP_AppointmentTestLogId == VP_AppointmentTestLogId
            //       ).ToList();


            foreach (var c in lstLab)
            {
                foreach (var m in c.Measurements)
                {
                    var entry = (savedValues.Where(s => s.VPMeasurementId == m.Id && s.VP_AppointmentTestLogId == VP_AppointmentTestLogId).FirstOrDefault());

                    if (entry != null && (!string.IsNullOrWhiteSpace(entry.Value)))
                    {
                        m.Value = entry != null ? entry.Value : string.Empty;
                        m.ValueType = entry.ValueType;
                        m.OverTarget = (entry != null && (!string.IsNullOrWhiteSpace(entry.Value))) ? GetFlag(entry.Value, m.Range1, m.Range2) : false;
                        // m.OverDue = (entry != null && (!string.IsNullOrWhiteSpace(entry.Value))) ? GetOverDue(appointmentDate, logDate, m.Frequency) : false;
                    }
                    else
                    {
                        //m.OverDue = GetOverDue(appointmentDate, DateTime.Now, m.Frequency);
                    }
                }
            };

            foreach (var m in lstVS.SelectMany(s => s.Measurements))
            //lstVS.ForEach(c => c.Measurements
            //.ForEach(m =>
            {
                var entry = (savedValues.Where(s => s.VPMeasurementId == m.Id && s.VP_AppointmentTestLogId == VP_AppointmentTestLogId)).FirstOrDefault();
                if (entry != null)
                {

                    m.Value = entry.Value;
                    m.ValueType = entry.ValueType;
                    m.User = new VMAddedByUser { UserId = entry.UserId, UserName = entry.UserName };


                }
                try
                {
                    var whoentered = (savedValues.Where(s => s.VPMeasurementId == m.Id)).OrderBy(o => o.VP_AppointmentTestLogId).FirstOrDefault();
                    if (whoentered != null)
                        m.HistoricalValues.Add(new VMMeasurementHistory { Id = m.Id, LogDate = whoentered.Date, Value = whoentered.Value, UserName = whoentered.UserName });
                }
                catch
                {

                }
            }
            //));

            if (lstCDF != null && lstCDF.Count() > 0)
            {
                foreach (var c in lstCDF)
                {
                    var entry = (savedValues.Where(s => s.VPMeasurementId == c.VPTemplateField && s.VP_AppointmentTestLogId == VP_AppointmentTestLogId)
                                     .FirstOrDefault());
                    if (entry != null && (!string.IsNullOrWhiteSpace(entry.Value)))
                    {
                        c.Value = entry != null ? entry.Value : string.Empty;
                        //c.ValueType = entry.ValueType;
                        c.OverDue = (entry != null && (!string.IsNullOrWhiteSpace(entry.Value))) ? GetOverDue(appointmentDate, logDate, c.Frequency) : false;
                        c.OverTarget = (entry != null && (!string.IsNullOrWhiteSpace(entry.Value))) ? GetFlag(entry.Value, c.TL, c.TH) : false;
                    }
                    else
                    {
                        c.OverDue = GetOverDue(appointmentDate, DateTime.Now, c.Frequency);
                    }
                };
            }

        }
        public int GetMaxLogID(List<SelectListItem> lst)
        {

            int retVal = -1;

            lst = (from c in lst
                   where c.Value != "-1"
                   select c).ToList();

            if (lst != null && lst.Count > 0)
            {
                var maxID = lst.OrderByDescending(l => Int32.Parse(l.Value)).ToList()[0];

                if (maxID != null)

                    retVal = Int32.Parse(maxID.Value);
            }

            return retVal;
        }
        public string LoadOpeningStatement(int appointmentID)
        {
            string retVal = string.Empty;

            
            List<SqlParameter> parms = new List<SqlParameter>
            {
              new SqlParameter("appointmentID",appointmentID)
            };
            var entry = context.GetData<string>("[dbo].[Get_VP_OpeningStatement]", parms).FirstOrDefault();
            if (entry != null)
                retVal = entry;

            return retVal;
        }
        
        #endregion

        #region CDF
        public bool IsTemplateExists(string templateName, int practiceId)
        {
            templateName = templateName.ToLower().Trim();
            return context.VP_Template.Any(a => a.Name.Trim().ToLower().Equals(templateName) && a.PracticeID == practiceId);
        }
        public VMTemplateDetail GetTemplateItem(int mid)
        {
            VMTemplateDetail retObj = new VMTemplateDetail();

            retObj =

                 (from m in context.VPUniqueMeasurement
                  where m.Id == mid
                  select new VMTemplateDetail()
                  {
                      Id = m.Id,
                      TemplateItemName = m.Name,
                      VPTemplateField = m.Id,
                      NH = m.NH ?? 0,
                      NL = m.NL ?? 0,
                      TH = m.TH ?? 0,
                      TL = m.TL ?? 0,
                      Frequency = m.Frequency

                  }).FirstOrDefault();

            return retObj;
        }
        public void AddNewTemplate(string name,int modifyingUserId,string ipAddress)
        {
            context.VP_Template.Add(new VP_Template()
            {
                Name = name
            });
            context.SaveChanges(modifyingUserId, ipAddress);
        }
        public List<VMTemplateDetail> GetTemplateDetails(List<VMTemplateDetail> lst,int templateID,int patientID)
        {
            var pat = GetPatientDemographic(patientID);
            var gen = pat.gender.ToString();
            var lstPatient = (from d in context.VP_Template_Detail
                              join m in context.VPUniqueMeasurement on d.VPTemplateField equals m.Id
                              //join t in context.VP_Template on d.VP_TemplateId equals t.Id
                              // join c in context.VPCategory on f.VPCategoryID equals c.id
                              where
                               //d.PatientID == patientID &&
                               d.VP_TemplateId == templateID && (m.Gender == "" || m.Gender == null ? true : m.Gender.Equals(gen))
                              select new VMTemplateDetail()
                              {
                                  Id = d.Id,
                                  Value = d.Value,
                                  VPTemplateField = m.Id,
                                  VP_TemplateId = d.VP_TemplateId,
                                  TemplateItemName = m.Name,
                                  //TemplateName = t.Name,
                                  NH = m.NH ?? 0,
                                  NL = m.NL ?? 0,
                                  TH = d.TH ?? 0,
                                  TL = d.TL ?? 0,
                                  Frequency = d.Frequency


                              }).ToList();
            if (lstPatient == null || lstPatient.Count == 0)
            {
                lstPatient = GetTemplateDetailsByPatient(lstPatient, templateID, patientID);
            }
            if (lstPatient != null && lstPatient.Count > 0)
            {
                lst.Clear();
                lst.AddRange(lstPatient);

            }

            return lstPatient;
        }
        public VMDemographic GetPatientDemographic(int patientRecordId)
        {
            var demo = new VMDemographic();

            demo = (from d in context.Demographics
                    where d.PatientRecordId == patientRecordId && d.active == Active.Active
                    orderby d.Id descending
                    select new VMDemographic
                    {
                        firstName = d.firstName,
                        lastName = d.lastName,
                        gender = d.gender,
                        dateOfBirth = d.dateOfBirth
                    }).FirstOrDefault();


            return demo;
        }

        public List<VMTemplateDetail> GetTemplateDetailsByPatient(
           List<VMTemplateDetail> lst,
           int templateID,
           int patientID)
        {
            var lstPatient = (from d in context.VP_Template_Detail
                              join m in context.VPUniqueMeasurement on d.VPTemplateField equals m.Id
                              //join t in context.VP_Template on d.VP_TemplateId equals t.Id
                              // join c in context.VPCategory on f.VPCategoryID equals c.id
                              where
                               //d.PatientID == patientID &&
                               d.VP_TemplateId == templateID
                              select new VMTemplateDetail()
                              {
                                  Id = d.Id,
                                  Value = d.Value,
                                  VPTemplateField = m.Id,
                                  VP_TemplateId = d.VP_TemplateId,
                                  TemplateItemName = m.Name,
                                  //TemplateName = t.Name,
                                  NH = m.NH ?? 0,
                                  NL = m.NL ?? 0,
                                  TH = d.TH ?? 0,
                                  TL = d.TL ?? 0,
                                  Frequency = d.Frequency


                              }).ToList();
            if (lstPatient == null || lstPatient.Count == 0)
            {
                lstPatient = (from d in context.VP_Template_Patient_Detail
                              join f in context.VPUniqueMeasurement on d.VPTemplateField equals f.Id
                              //join t in context.VP_Template on d.VP_TemplateId equals t.Id
                              // join c in context.VPCategory on f.VPCategoryID equals c.id
                              where
                              //d.PatientRecordId == patientID
                              //&& 
                              d.VP_TemplateId == templateID
                              select new VMTemplateDetail()
                              {
                                  Id = d.Id,
                                  Value = d.Value,
                                  VPTemplateField = f.Id,
                                  VP_TemplateId = d.VP_TemplateId,
                                  TemplateItemName = f.Name,
                                  // TemplateName = t.Name,
                                  NH = d.NH ?? 0,
                                  NL = d.NL ?? 0,
                                  TH = d.TH ?? 0,
                                  TL = d.TL ?? 0,
                                  Frequency = d.Frequency,
                                  Units = f.Units

                              }).ToList();
            }


            return lstPatient;
        }
        public List<VMTemplateDetail> GetTemplateDetailsByPatient(int patientID)
        {
            List<VMTemplateDetail> lstPatient = new List<VMTemplateDetail>();
            try
            {
                List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("patientRecordId",patientID )
            };
                lstPatient = context.GetData<VMTemplateDetail>("[dbo].[VP_TemplateDetailsByPatient]", parms).ToList();
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
            }
            // var vals= GetTemplateDetailsByPatient_OLD(patientID);
            return lstPatient;
        }

        public List<VMTemplatePatientDataItemHeader> GetTemplateHeadersByPatient(int patientID)
        {

            var lst =

                   (from d in context.VP_Template_Patient_Detail
                    join f in context.VPUniqueMeasurement on d.VPTemplateField equals f.Id
                    orderby f.Id
                    where d.PatientRecordId == patientID

                    select new VMTemplatePatientDataItemHeader()
                    {
                        ID = d.Id,
                        VPTemplateFieldId = f.Id,
                        VPTemplateFieldName = f.Name,
                        Value = d.Value,
                        TestCode = f.Testcode,
                        NH = f.NH,
                        NL = f.NL,
                        TH = d.TH,
                        TL = d.TL,
                        Frequency = d.Frequency,
                        IsText = f.IsText,
                        Units = f.Units,
                        ShortName = f.ShortName,
                        HeaderGroupId = f.careElementGroup != null ? (f.careElementGroup.IsDataMigrationGroupChild ? 0 : f.careElementGroup.Id) : 0,
                        HeaderGroup = f.careElementGroup != null ? (f.careElementGroup.IsDataMigrationGroupChild ? null : f.careElementGroup.GroupName) : null,
                        ValueType = f.ValueType

                    }).OrderBy(o => o.IsText).ToList();

            return lst;
        }
        public List<VMTherapeuticClassCDF> CDFTemplateTherapeuticClasses()
        {
            return _medicationBLL.TherapeuticClassesForCDFTemplate();
        }
        public List<VMTemplateLog> GetTemplatePatientData(
                       List<VMTemplatePatientDataItemHeader> lst,
                       int patientID,
                       string dateFrom,
                       string dateTo)
        {

            DateTime? dtFrom = null;
            DateTime? dtTo = null;
            DateTime dummyDt;
            if (DateTime.TryParse(dateFrom, out dummyDt))
            {
                dtFrom = dummyDt;
            }
            if (DateTime.TryParse(dateTo, out dummyDt))
            {
                dtTo = dummyDt;
            }
            List<int> lstField = lst.Select(x => x.VPTemplateFieldId).Distinct().ToList();

            List<VMTemplateLog> retVal = new List<VMTemplateLog>();

            // int templateID = this.GetSavedTemplateByPatient(patientID);

            List<VMTemplatePatientDataItem> lstAll =

              (from pd in context.VP_Template_Patient_Data
               join m in context.VPUniqueMeasurement on pd.VPTemplateFieldId equals m.Id
               join d in context.VP_Template_Patient_Detail on pd.VPTemplateFieldId equals d.VPTemplateField
               //orderby pd.LogId
               where
                 pd.PatientRecordId == patientID
                 && pd.PatientRecordId == d.PatientRecordId
                 && (dtFrom.HasValue && !dtTo.HasValue ? (pd.LogDate >= dtFrom) : 1 == 1)
                 && (!dtFrom.HasValue && dtTo.HasValue ? (pd.LogDate <= dtTo) : 1 == 1)
                 && (dtFrom.HasValue && dtTo.HasValue ? (pd.LogDate >= dtFrom && pd.LogDate <= dtTo) : 1 == 1)
               //  d.TemplateId == templateID &&
               // lstField.Contains(pd.VPTemplateFieldId)
               orderby pd.LogDate descending
               select new VMTemplatePatientDataItem()
               {

                   Id = pd.Id,
                   LogID = pd.LogId,
                   LogDate = pd.LogDate,
                   Value = pd.Value,
                   DateEntered = pd.DateEntered,
                   ParentId = m.VPUniqueMeasurementId,

                   HeaderVPTemplateFieldId = d.VPTemplateField,

                   VPTemplateFieldId = pd.VPTemplateFieldId,
                   VPTemplateFieldName = m.Name,
                   NH = m.NH,
                   NL = m.NL,
                   TH = d.TH,
                   TL = d.TL,
                   Frequency = d.Frequency,
                   IsText = m.IsText,
                   ShortName = m.ShortName,
                   ValueType = m.ValueType
               }).ToList();


            var lstLogs =
                 lstAll.GroupBy(s => s.LogID)
                .Select(x => x.FirstOrDefault())
                .ToList();

            int lstLogsCount = lstLogs.Count();
            foreach (var x in lstLogs)
            {

                VMTemplateLog newLog = new VMTemplateLog { ID = x.Id, LogID = x.LogID, Date = x.LogDate, DateEntered = x.DateEntered };

                var lstTemp = lstAll
                            .Where(z => z.LogID == x.LogID)
                            .OrderBy(e => e.VPTemplateFieldId)
                            .Select(a => new VMTemplatePatientDataItem()
                            {
                                Id = a.Id,
                                HeaderVPTemplateFieldId = a.HeaderVPTemplateFieldId,
                                VPTemplateFieldId = a.VPTemplateFieldId,
                                Value = a.Value,
                                DateEntered = a.DateEntered,
                                LogID = a.LogID,
                                LogDate = a.LogDate,
                                VPTemplateFieldName = a.VPTemplateFieldName,
                                Category = a.Category,
                                NH = a.NH,
                                NL = a.NL,
                                TH = a.TH,
                                TL = a.TL,
                                Frequency = a.Frequency,
                                IsText = a.IsText,
                                ValueType = a.ValueType
                            }).ToList();

                var lstallgrp = (from c in lstAll
                                 group c by c.HeaderVPTemplateFieldId into ng
                                 select new { ng.Key, Other = ng }).ToList();
                //matching headers and values saved 
                foreach (var h in lst)
                {
                    var entryFound = lstTemp.Where(t => t.VPTemplateFieldId == h.VPTemplateFieldId)
                                    .ToList()
                                    .FirstOrDefault();
                    //if value frm db matches value in header list , add it to list
                    if (entryFound != null)
                    {
                        entryFound.ShortName = h.ShortName;
                        entryFound.NH = h.NH;
                        entryFound.NL = h.NL;
                        entryFound.TH = h.TH;
                        entryFound.TL = h.TL;
                        entryFound.HeaderVPTemplateFieldId = h.VPTemplateFieldId;
                        entryFound.HeaderGroupId = h.HeaderGroupId;
                        entryFound.HeaderGroup = h.HeaderGroup;
                        entryFound.ValueType = h.ValueType;
                        if ((string.IsNullOrWhiteSpace(entryFound.Value)) && h.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo)
                        {
                            entryFound.Value = "YES";
                        }
                        if (!entryFound.IsText)
                        {
                            entryFound.OutSideNormal = GetFlag(entryFound.Value, entryFound.NL, entryFound.NH);
                            entryFound.OutSideTarget = GetFlag(entryFound.Value, entryFound.TL, entryFound.TH);
                        }

                        newLog.Items.Add(entryFound);
                    }
                    else
                    {
                        var emptyElem = new VMTemplatePatientDataItem();
                        emptyElem.HeaderGroupId = h.HeaderGroupId;
                        emptyElem.HeaderGroup = h.HeaderGroup;
                        emptyElem.ShortName = h.ShortName;
                        emptyElem.HeaderVPTemplateFieldId = h.VPTemplateFieldId;
                        emptyElem.VPTemplateFieldName = h.VPTemplateFieldName;
                        emptyElem.VPTemplateFieldId = h.VPTemplateFieldId;
                        emptyElem.IsText = h.IsText;
                        emptyElem.Frequency = h.Frequency;
                        emptyElem.ValueType = h.ValueType;
                        newLog.Items.Add(emptyElem);
                    }

                }
                //var count = newLog.Items.Count();
                //var itemvalcount = newLog.Items.Count(w => string.IsNullOrWhiteSpace(w.Value));
                //if (count != itemvalcount)
                retVal.Add(newLog);
            }
            if (retVal != null && retVal.Count() > 0)
            {
                int count = 0;
                //var latestoutofFlag = retVal.FirstOrDefault();
                foreach (var l in retVal)
                {
                    foreach (var i in l.Items)
                    {

                        if (!i.IsText)
                        {
                            if (count == 0)
                            {
                                //i.OutSideNormal = GetFlag(i.Value, i.NL, i.NH);
                                //i.OutSideTarget = GetFlag(i.Value, i.TL, i.TH);
                                i.OverDue = GetOverDue(i.LogDate, DateTime.Now, i.Frequency);
                            }
                            else
                            {
                                //i.OutSideNormal = GetFlag(i.Value, i.NL, i.NH);
                                //i.OutSideTarget = GetFlag(i.Value, i.TL, i.TH);

                                var hdr1 = retVal.Count(h => h.Items.Any(a => a.VPTemplateFieldId == i.HeaderVPTemplateFieldId));
                                var hdr2 = retVal.Count(h => h.Items.Any(a => a.VPTemplateFieldId == i.HeaderVPTemplateFieldId && string.IsNullOrWhiteSpace(a.Value)));
                                //var hdr3 = retVal.Where(h => h.Items.Any(a => a.VPTemplateFieldId == i.HeaderVPTemplateFieldId));
                                if (hdr1 == hdr2)
                                {
                                    var hdr = lst.FirstOrDefault(h => h.VPTemplateFieldId == i.HeaderVPTemplateFieldId);
                                    hdr.OverDue = true;
                                }
                            }
                        }
                        else
                        {
                            if (string.IsNullOrWhiteSpace(i.Value))
                            {
                                //i.OverDue = GetOverDue(i.LogDate, DateTime.Now, i.Frequency);
                                var hdr1 = retVal.Count(h => h.Items.Any(a => a.VPTemplateFieldId == i.HeaderVPTemplateFieldId));
                                var hdr2 = retVal.Count(h => h.Items.Any(a => a.VPTemplateFieldId == i.HeaderVPTemplateFieldId && string.IsNullOrWhiteSpace(a.Value)));
                                //var hdr3 = retVal.Where(h => h.Items.Any(a => a.VPTemplateFieldId == i.HeaderVPTemplateFieldId));
                                if (hdr1 == hdr2)
                                {
                                    var hdr = lst.FirstOrDefault(h => h.VPTemplateFieldId == i.HeaderVPTemplateFieldId);
                                    hdr.OverDue = true;
                                }
                                else
                                {

                                }
                            }
                        }
                        count++;
                    }
                }
            }
            //calculating BMI dynamically
            //retVal.ForEach(l =>
            foreach (var l in retVal)
            {
                decimal weight = 0;
                decimal height = 0;
                decimal bmi = 0;
                decimal dummy = 0;

                var weightMeas = l.Items.Where(s => s.VPTemplateFieldName == "Weight").FirstOrDefault();
                var heightMeas = l.Items.Where(s => s.VPTemplateFieldName == "Height").FirstOrDefault();
                var bmiMeas = l.Items.Where(s => s.VPTemplateFieldName == "BMI").FirstOrDefault();

                if (weightMeas != null)
                {
                    if (!string.IsNullOrEmpty(weightMeas.Value) && decimal.TryParse(weightMeas.Value, out dummy))
                        weight = decimal.Parse(weightMeas.Value);
                }
                if (heightMeas != null)
                {
                    if (!string.IsNullOrEmpty(heightMeas.Value) && decimal.TryParse(heightMeas.Value, out dummy))
                        height = decimal.Parse(heightMeas.Value);
                }
                if (bmiMeas != null)
                {
                    if (!string.IsNullOrEmpty(bmiMeas.Value) && decimal.TryParse(bmiMeas.Value, out dummy))
                        bmi = decimal.Parse(bmiMeas.Value);
                }

                if (bmi <= 0 && (weight > 0 && height > 0))
                {
                    bmiMeas.Value = "(" + (weight / (height * height)).ToString() + ")";
                }
            }

            return retVal;
        }
        public bool GetOverDue(DateTime? appointmentDate, DateTime? logDate, int? frequency)
        {
            bool overdue = false;
            if (appointmentDate != null && logDate != null && frequency != null)
            {
                TimeSpan tsp = ((DateTime)logDate - (DateTime)appointmentDate);

                if (tsp.TotalDays > frequency)
                {
                    overdue = true;
                }
                else
                {
                    overdue = false;
                }
            }
            return overdue;
        }
        public bool GetFlag(string Value, decimal? NL, decimal? NH)
        {
            decimal dummy;
            bool OutSideNormal = false;
            if (!string.IsNullOrEmpty(Value) && decimal.TryParse(Value, out dummy))
            {
                if (NL.HasValue && NL.Value > 0 && decimal.Parse(Value) < NL.Value)
                {
                    OutSideNormal = true;
                }
                if (NH.HasValue && NH.Value > 0 && decimal.Parse(Value) > NH.Value)
                {
                    OutSideNormal = true;
                }
            }
            return OutSideNormal;
        }
        public List<VMTemplateLog> GetTemplateInfluenzaVaccineData(int practiceId,List<VMTemplatePatientDataItemHeader> lst,int patientID,string dateFrom,string dateTo)
        {

            DateTime? dtFrom = null;
            DateTime? dtTo = null;
            DateTime dummyDt;
            if (DateTime.TryParse(dateFrom, out dummyDt))
            {
                dtFrom = dummyDt;
            }
            if (DateTime.TryParse(dateTo, out dummyDt))
            {
                dtTo = dummyDt;
            }
            List<int> lstField = lst.Select(x => x.VPTemplateFieldId).Distinct().ToList();
            List<VMTemplateLog> retVal = new List<VMTemplateLog>();
            var immunInfluenza = context.VPUniqueMeasurement.Where(i => i.Name == "Influenza Vaccine").FirstOrDefault();
            List<VMImmuniationData> ImmunData = new List<VMImmuniationData>();
            //var lstPatientImmuns2 = GetImmunizationGroups(practiceId, patientID);
            var lstPatientImmuns = GetImmunizationGroups(practiceId, patientID, "Infl");
            //lstPatientImmuns = lstPatientImmuns.Where(i => i.ImmunizationName == "Influenza").ToList();//influenza
            if (lstPatientImmuns != null && lstPatientImmuns.Count > 0)
            {
                ImmunData = lstPatientImmuns[0].Data.ToList();
            }

            List<VMTemplatePatientDataItem> lstAll =
                          (from i in ImmunData
                           where (dtFrom.HasValue && !dtTo.HasValue ? (i.NextDate >= dtFrom) : 1 == 1)
                && (!dtFrom.HasValue && dtTo.HasValue ? (i.NextDate <= dtTo) : 1 == 1)
                && (dtFrom.HasValue && dtTo.HasValue ? (i.NextDate >= dtFrom && i.NextDate <= dtTo) : 1 == 1)
                           select new VMTemplatePatientDataItem()
                           {
                               //Id = i.Id,
                               LogID = i.ID,
                               LogDate = i.NextDate,
                               //Value = pd.Value,
                               ShortName = i.Name,
                               DateEntered = i.NextDate,
                               HeaderVPTemplateFieldId = immunInfluenza.Id,
                               VPTemplateFieldId = immunInfluenza.Id,
                               VPTemplateFieldName = immunInfluenza.Name,
                               Category = i.Status,
                               IsText = true,
                               Frequency = immunInfluenza.Frequency
                           }).ToList();


            var lstLogs =

                 lstAll.GroupBy(s => s.LogID)
                .Select(x => x.FirstOrDefault())
                .ToList();
            foreach (var x in lstLogs)
            {
                VMTemplateLog newLog = new VMTemplateLog { ID = x.Id, LogID = x.LogID, Date = x.LogDate, DateEntered = x.DateEntered };
                var lstTemp = lstAll
                            .Where(z => z.LogID == x.LogID)
                            .OrderBy(e => e.VPTemplateFieldId)
                            .Select(a => new VMTemplatePatientDataItem()
                            {
                                Id = a.Id,
                                HeaderVPTemplateFieldId = a.HeaderVPTemplateFieldId,
                                VPTemplateFieldId = a.VPTemplateFieldId,
                                Value = a.DateEntered.ToString() + Environment.NewLine +
                                         a.Category + Environment.NewLine + a.ShortName,
                                DateEntered = a.DateEntered,
                                LogID = a.LogID,
                                LogDate = a.LogDate,
                                VPTemplateFieldName = a.VPTemplateFieldName,
                                Category = a.Category,
                                NH = a.NH,
                                NL = a.NL,
                                TH = a.TH,
                                TL = a.TL,
                                Frequency = a.Frequency,
                                IsText = a.IsText
                            }).ToList();

                //matching headers and values saved 
                foreach (var h in lst)
                {
                    var entryFound = lstTemp.Where(t => t.VPTemplateFieldId == h.VPTemplateFieldId)
                                .ToList()
                                .FirstOrDefault();
                    //if value frm db matches value in header list , add it to list
                    if (entryFound != null)
                    {
                        entryFound.NH = h.NH;
                        entryFound.NL = h.NL;
                        entryFound.TH = h.TH;
                        entryFound.TL = h.TL;
                        entryFound.Frequency = h.Frequency;
                        entryFound.HeaderVPTemplateFieldId = h.VPTemplateFieldId;

                        newLog.Items.Add(entryFound);
                    }
                    else
                    {
                        var emptyElem = new VMTemplatePatientDataItem();
                        emptyElem.HeaderVPTemplateFieldId = h.VPTemplateFieldId;
                        newLog.Items.Add(emptyElem);
                    }

                }

                retVal.Add(newLog);
            };

            foreach (var l in retVal)
            {
                foreach (var i in l.Items)
                //l.Items.ForEach(i =>
                {
                    if (!i.IsText)
                    {
                        int dummy;

                        if (!string.IsNullOrEmpty(i.Value) && Int32.TryParse(i.Value, out dummy))
                        {
                            if (i.NL.HasValue && i.NL.Value > 0 && Int32.Parse(i.Value) < i.NL.Value)
                            {
                                i.OutSideNormal = true;
                            }

                            if (i.NH.HasValue && i.NH.Value > 0 && Int32.Parse(i.Value) > i.NH.Value)
                            {
                                i.OutSideNormal = true;
                            }


                            if (i.TL.HasValue && i.TL.Value > 0 && Int32.Parse(i.Value) < i.TL.Value)
                            {
                                i.OutSideTarget = true;
                            }

                            if (i.TH.HasValue && i.TH.Value > 0 && Int32.Parse(i.Value) > i.TH.Value)
                            {
                                i.OutSideTarget = true;
                            }

                            if ((DateTime.Now - i.LogDate).Value.TotalDays > i.Frequency)
                            {
                                i.OverDue = true;
                            }
                            else
                            {
                                i.OverDue = false;
                            }
                        }
                    }
                    else
                    {
                        i.OverDue = GetOverDue(i.LogDate, DateTime.Now, i.Frequency);
                    }
                }
            }
            //calculating BMI dynamically
            foreach (var l in retVal)
            //retVal.ForEach(l =>
            {
                decimal weight = 0;
                decimal height = 0;
                decimal bmi = 0;
                decimal dummy = 0;

                var weightMeas = l.Items.Where(s => s.VPTemplateFieldId == 34).FirstOrDefault();
                var heightMeas = l.Items.Where(s => s.VPTemplateFieldId == 33).FirstOrDefault();
                var bmiMeas = l.Items.Where(s => s.VPTemplateFieldId == 37).FirstOrDefault();

                if (weightMeas != null)
                {
                    if (!string.IsNullOrEmpty(weightMeas.Value) && decimal.TryParse(weightMeas.Value, out dummy))
                        weight = decimal.Parse(weightMeas.Value);
                }
                if (heightMeas != null)
                {
                    if (!string.IsNullOrEmpty(heightMeas.Value) && decimal.TryParse(heightMeas.Value, out dummy))
                        height = decimal.Parse(heightMeas.Value);
                }
                if (bmiMeas != null)
                {
                    if (!string.IsNullOrEmpty(bmiMeas.Value) && decimal.TryParse(bmiMeas.Value, out dummy))
                        bmi = decimal.Parse(bmiMeas.Value);
                }

                if (bmi <= 0 && (weight > 0 && height > 0))
                {
                    bmiMeas.Value = "(" + (weight / (height * height)).ToString() + ")";
                }
            }

            return retVal;
        }
        public List<VMTemplateLog> GetTemplatePneumoVaccineData(int practiceId,List<VMTemplatePatientDataItemHeader> lst,int patientID,string dateFrom,string dateTo)
        {

            DateTime? dtFrom = null;
            DateTime? dtTo = null;
            DateTime dummyDt;
            List<VMImmuniationData> ImmunData = new List<VMImmuniationData>();
            List<VMTemplateLog> retVal = new List<VMTemplateLog>();

            if (DateTime.TryParse(dateFrom, out dummyDt))
            {
                dtFrom = dummyDt;
            }
            if (DateTime.TryParse(dateTo, out dummyDt))
            {
                dtTo = dummyDt;
            }
            List<int> lstField = lst.Select(x => x.VPTemplateFieldId).Distinct().ToList();
            var immunInfluenza = context.VPUniqueMeasurement.Where(i => i.Name == "Pneumococcal Vaccine").FirstOrDefault();
            var lstPatientImmuns = GetImmunizationGroups(practiceId, patientID, "Pneu C-7");
            //lstPatientImmuns = lstPatientImmuns.Where(i => i.ImmunizationName == "Pneumo").ToList();//influenza
            if (lstPatientImmuns != null && lstPatientImmuns.Count > 0)
            {
                ImmunData = lstPatientImmuns[0].Data.ToList();
            }

            List<VMTemplatePatientDataItem> lstAll =
                          (from i in ImmunData
                           select new VMTemplatePatientDataItem()
                           {
                               //Id = i.Id,
                               LogID = i.ID,
                               LogDate = i.NextDate,
                               //Value = pd.Value,
                               ShortName = i.Name,
                               DateEntered = i.NextDate,
                               HeaderVPTemplateFieldId = immunInfluenza.Id,
                               VPTemplateFieldId = immunInfluenza.Id,
                               VPTemplateFieldName = immunInfluenza.Name,
                               Category = i.Status,
                               IsText = true
                           }).ToList();


            var lstLogs =

                 lstAll.GroupBy(s => s.LogID)
                .Select(x => x.FirstOrDefault())
                .ToList();

            lstLogs.ForEach(x =>
            {
                VMTemplateLog newLog = new VMTemplateLog { ID = x.Id, LogID = x.LogID, Date = x.LogDate, DateEntered = x.DateEntered };

                var lstTemp = lstAll
                            .Where(z => z.LogID == x.LogID)
                            .OrderBy(e => e.VPTemplateFieldId)
                            .Select(a => new VMTemplatePatientDataItem()
                            {
                                Id = a.Id,
                                HeaderVPTemplateFieldId = a.HeaderVPTemplateFieldId,
                                VPTemplateFieldId = a.VPTemplateFieldId,
                                Value = a.DateEntered.ToString() + Environment.NewLine +
                                         a.Category + Environment.NewLine + a.ShortName,
                                DateEntered = a.DateEntered,
                                LogID = a.LogID,
                                LogDate = a.LogDate,
                                VPTemplateFieldName = a.VPTemplateFieldName,
                                Category = a.Category,
                                NH = a.NH,
                                NL = a.NL,
                                TH = a.TH,
                                TL = a.TL,
                                Frequency = a.Frequency,
                                IsText = a.IsText

                            }).ToList();

                //matching headers and values saved 
                lst.ForEach(h =>
            {
                var entryFound = lstTemp.Where(t => t.VPTemplateFieldId == h.VPTemplateFieldId)
                            .ToList()
                            .FirstOrDefault();
                //if value frm db matches value in header list , add it to list
                if (entryFound != null)
                {
                    entryFound.NH = h.NH;
                    entryFound.NL = h.NL;
                    entryFound.TH = h.TH;
                    entryFound.TL = h.TL;
                    entryFound.Frequency = h.Frequency;
                    entryFound.HeaderVPTemplateFieldId = h.VPTemplateFieldId;

                    newLog.Items.Add(entryFound);
                }
                else
                {
                    var emptyElem = new VMTemplatePatientDataItem();
                    emptyElem.HeaderVPTemplateFieldId = h.VPTemplateFieldId;
                    newLog.Items.Add(emptyElem);
                }

            });

                retVal.Add(newLog);
            });

            retVal.ForEach(l =>
            {
                l.Items.ForEach(i =>
                {

                    if (!i.IsText)
                    {
                        int dummy;

                        //if (i.VPTemplateFieldName == "2 hr PC BG" && !string.IsNullOrEmpty(i.Value))
                        //{
                        //    string s = "";
                        //}

                        if (!string.IsNullOrEmpty(i.Value) && Int32.TryParse(i.Value, out dummy))
                        {
                            if (i.NL.HasValue && i.NL.Value > 0 && Int32.Parse(i.Value) < i.NL.Value)
                            {
                                i.OutSideNormal = true;
                            }

                            if (i.NH.HasValue && i.NH.Value > 0 && Int32.Parse(i.Value) > i.NH.Value)
                            {
                                i.OutSideNormal = true;
                            }


                            if (i.TL.HasValue && i.TL.Value > 0 && Int32.Parse(i.Value) < i.TL.Value)
                            {
                                i.OutSideTarget = true;
                            }

                            if (i.TH.HasValue && i.TH.Value > 0 && Int32.Parse(i.Value) > i.TH.Value)
                            {
                                i.OutSideTarget = true;
                            }

                            if ((DateTime.Now - i.LogDate).Value.TotalDays > i.Frequency)
                            {
                                i.OverDue = true;
                            }
                            else
                            {
                                i.OverDue = false;
                            }
                        }
                    }
                    else
                    {
                        i.OverDue = GetOverDue(i.LogDate, DateTime.Now, i.Frequency);
                    }
                });
            });

            return retVal;
        }
        public DateTime? GetLatestCDFDate(int patientID, int appointmentId)
        {
            //DateTime dtStart = DateTime.Now.AbsoluteStart();
            //DateTime dtEnd = DateTime.Now.AbsoluteEnd();
            // DateTime dt = DateTime.Now;
            return (from d in context.VP_Template_Patient_Data
                    join f in context.VPUniqueMeasurement on d.VPTemplateFieldId equals f.Id
                    join a in context.VP_AppointmentTestLog on d.LogId equals a.Id
                    where
                          d.PatientRecordId == patientID
                          && a.AppointmentId == appointmentId
                    //&& DbFunctions.TruncateTime(d.LogDate) == DbFunctions.TruncateTime(apptDate)
                    //d.LogDate <= dt
                    orderby d.LogId descending
                    select d.LogDate
                    ).ToList().FirstOrDefault();

        }        
        public VP_Template AddNewTemplate(VP_Template t,int modifyingUserId,string ipAddress)
        {
            context.VP_Template.Add(t);

            context.SaveChanges(modifyingUserId, ipAddress);

            return t;
        }
        public void SaveTemplateDataByPractice(IList<VMTemplateDetail> vm, string templateName, int templateID, int practiceID, int modifyingUserId, string ipAddress)
        {
            var templateid = vm.FirstOrDefault().VP_TemplateId;
            //var newtemplateId = new_patient_template(templateName, templateid, practiceID, null, null);
            List<VP_Template_Detail> lstToSave = new List<VP_Template_Detail>();
            VP_Template newTemplate = null;
            List<VP_Template> existingPracticeEntry = null;
            if (!string.IsNullOrWhiteSpace(templateName))
            {
                existingPracticeEntry = context.VP_Template.Where(t => t.Name.ToLower() == templateName.ToLower() && t.PracticeID == practiceID && !t.PatientRecordId.HasValue && !t.DoctorID.HasValue).ToList();
            }
            else
            {
                newTemplate = context.VP_Template.FirstOrDefault(t => t.Id == templateID);
                existingPracticeEntry = context.VP_Template.Where(t => t.Name.ToLower() == newTemplate.Name.ToLower() && t.PracticeID == practiceID && !t.PatientRecordId.HasValue && !t.DoctorID.HasValue).ToList();

                templateName = newTemplate.Name;


            }
            if (existingPracticeEntry == null || existingPracticeEntry.Count() == 0)
            {
                newTemplate = new VP_Template()
                {
                    Name = templateName,
                    PracticeID = practiceID,
                    CreatedDateTime = DateTime.Now,
                    UpdatedDateTime = DateTime.Now
                };
                newTemplate = this.AddNewTemplate(newTemplate,modifyingUserId,ipAddress);
            }
            else
            {
                newTemplate = existingPracticeEntry.OrderByDescending(o => o.Id).FirstOrDefault();
                var lstToRemove = context.VP_Template_Detail.Where(c => c.VP_TemplateId == newTemplate.Id).ToList();
                context.VP_Template_Detail.RemoveRange(lstToRemove);
                context.SaveChanges(modifyingUserId,ipAddress);
            }

            foreach (var x in vm.ToList())
            //vm.ToList().ForEach(x =>
            {
                lstToSave.Add(new VP_Template_Detail()
                {
                    VP_TemplateId = newTemplate.Id,
                    MeasurementID = x.VPTemplateField,
                    NH = x.NH,
                    NL = x.NL,
                    TH = x.TH,
                    TL = x.TL,
                    Frequency = x.Frequency ?? 0,
                    VPTemplateField = x.VPTemplateField
                });
            }
            context.VP_Template_Detail.AddRange(lstToSave);
            context.SaveChanges(modifyingUserId,ipAddress);
        }
        public void SaveTemplateDataByDoctor(IList<VMTemplateDetail> vm, string templateName, int templateID, int doctorID, int practiceId, int modifyingUserId, string ipAddress)
        {
            var existingName = string.Empty;
            List<VP_Template_Detail> lstToSave = new List<VP_Template_Detail>();
            VP_Template newTemplate = new VP_Template();
            List<VP_Template> existingPracticeEntry = null;
            if (!string.IsNullOrWhiteSpace(templateName))
            {
                existingPracticeEntry = context.VP_Template.Where(t => t.Name.ToLower() == templateName.ToLower() && t.DoctorID == doctorID && t.PracticeID == practiceId).ToList();
            }
            else
            {
                newTemplate = context.VP_Template.FirstOrDefault(t => t.Id == templateID);
                existingPracticeEntry = context.VP_Template.Where(t => t.Name.ToLower() == newTemplate.Name.ToLower() && t.DoctorID == doctorID && t.PracticeID == practiceId).ToList();

                templateName = newTemplate.Name;
            }

            if (existingPracticeEntry == null || existingPracticeEntry.Count() == 0)
            {
                newTemplate = context.VP_Template.Add(new VP_Template()
                {
                    Name = templateName,
                    DoctorID = doctorID,
                    PracticeID = practiceId,
                    CreatedDateTime = DateTime.Now,
                    UpdatedDateTime = DateTime.Now
                }).Entity;
                context.SaveChanges(modifyingUserId,ipAddress);
            }
            else
            {
                newTemplate = existingPracticeEntry.OrderByDescending(o => o.Id).FirstOrDefault();
                var lstToRemove = context.VP_Template_Detail.Where(c => c.VP_TemplateId == newTemplate.Id).ToList();
                context.VP_Template_Detail.RemoveRange(lstToRemove);
                context.SaveChanges(modifyingUserId,ipAddress);
            }


            vm.ToList().ForEach(x =>
            {
                lstToSave.Add(new VP_Template_Detail()
                {
                    VP_TemplateId = newTemplate.Id,
                    MeasurementID = x.VPTemplateField,
                    NH = x.NH,
                    NL = x.NL,
                    TH = x.TH,
                    TL = x.TL,
                    Frequency = x.Frequency ?? 0,
                    VPTemplateField = x.VPTemplateField
                });
            });
            context.VP_Template_Detail.AddRange(lstToSave);
            context.SaveChanges(modifyingUserId,ipAddress);
        }
        public void SaveTemplateDetailsByPatientData(IList<VMTemplatePatientDataItem> vm,int TemplateID,int PatientID,string DateStr, int modifyingUserId, string ipAddress)
        {

            int maxLogID = 1;

            var maxLogIdDB =

                 context
                     .VP_Template_Patient_Data
                    .OrderByDescending(x => x.LogId).FirstOrDefault();

            if (maxLogIdDB != null)
            {
                maxLogID = maxLogIdDB.LogId + 1;
            }

            //  DateTime? dtnow = DateTime.Now;

            DateTime dtUser = DateTime.Now;
            if (!string.IsNullOrEmpty(DateStr))
            {
                DateTime.TryParse(DateStr, out dtUser);
            }
            var vpuniqids = vm.Select(s => s.VPTemplateFieldId).ToArray();
            var meas = context.VPUniqueMeasurement.Where(w => vpuniqids.Contains(w.Id)).ToList();

            List<VP_Template_Patient_Data> lstToSave = new List<VP_Template_Patient_Data>();

            //clearing previous entries 
            var prevEntries = context.VP_Template_Patient_Data
                            .Where(x => x.PatientRecordId == PatientID && x.LogDate.HasValue && x.LogDate.Value.Date == dtUser.Date &&
                            x.From_VP_Page).ToList();

            //context.VP_Template_Patient_Data.RemoveRange(prevEntries);
            //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");



            if (prevEntries != null && prevEntries.Count() > 0)
            {

                foreach (var nv in vm)
                {
                    var dbtype = meas.FirstOrDefault(fd => fd.Id == nv.VPTemplateFieldId);
                    if (dbtype != null)
                    {
                        int outval = -1;
                        if (dbtype.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo && int.TryParse(nv.Value, out outval))
                        {
                            nv.Value = ((YesNo)outval).ToString();
                        }
                    }
                    var pv = prevEntries.FirstOrDefault(f => f.VPTemplateFieldId == nv.VPTemplateFieldId);
                    if (pv != null)
                    {
                        pv.LogDate = dtUser;
                        pv.LogId = maxLogID;
                        pv.DateEntered = DateTime.Now;
                        pv.PatientRecordId = nv.PatientID;
                        if (!string.IsNullOrWhiteSpace(nv.Value))
                        {
                            // TODO: Fix Value property assignment - 'Value' is a method group, not a property
                            // Need to investigate VP_Template_Patient_Data entity structure
                            // pv.Value = nv.Value;
                        }
                        pv.VPTemplateFieldId = nv.VPTemplateFieldId;
                        pv.From_VP_Page = true;
                    }
                    else if (!string.IsNullOrWhiteSpace(nv.Value))
                    {
                        lstToSave.Add(new VP_Template_Patient_Data()
                        {
                            TemplateId = TemplateID,
                            LogDate = dtUser,
                            LogId = maxLogID,
                            DateEntered = DateTime.Now,
                            PatientRecordId = nv.PatientID,
                            Value = nv.Value,
                            VPTemplateFieldId = nv.VPTemplateFieldId,
                            From_VP_Page = true
                        });
                    }
                }
            }
            else
            {
                foreach (var x in vm)
                {
                    var dbtype = meas.FirstOrDefault(fd => fd.Id == x.VPTemplateFieldId);
                    if (dbtype != null)
                    {
                        int outval = -1;
                        if (dbtype.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo && int.TryParse(x.Value, out outval))
                        {
                            x.Value = ((YesNo)outval).ToString();
                        }
                    }
                    // save data only
                    if (!string.IsNullOrWhiteSpace(x.Value))
                    {
                        lstToSave.Add(new VP_Template_Patient_Data()
                        {
                            TemplateId = TemplateID,
                            LogDate = dtUser,
                            LogId = maxLogID,
                            DateEntered = DateTime.Now,
                            PatientRecordId = x.PatientID,
                            Value = x.Value,
                            VPTemplateFieldId = x.VPTemplateFieldId,
                            From_VP_Page = true
                        });
                    }
                }
            }
            if (lstToSave.Count() > 0)
            {
                context.VP_Template_Patient_Data.AddRange(lstToSave);
            }
            context.SaveChanges(modifyingUserId, ipAddress);

        }
        #endregion

        #region Recall 

        public void SendRecallLetter1(int patientID, string patientName, string email, string immunName, DateTime? dtServiced)
        {
            email = "<EMAIL>";
            //email = "<EMAIL>";

            string body = string.Format(@"Dear Mr {0}.
                        You are due to have a repeat “{1}” by “{2}”.
                        Please contact our office to have this scheduled for you.

                            Sincerely,
                            Receptionist for Dr Khan.
                            ", patientName, immunName, dtServiced);

            Helper.SendMail("<EMAIL>", email, body, "Recall Due - First reminder");
        }

        public void SendRecallLetter2(int patientID, string patientName, string email, string immunName, DateTime? dtServiced)
        {
            email = "<EMAIL>";
            //email = "<EMAIL>";

            string body = string.Format(@"Dear Mr {0}.
                        You are due to have a repeat “{1}” by “{2}”.
                        Please contact our office to have this scheduled for you.

                            Sincerely,
                            Receptionist for Dr Khan.
                            ", patientName, immunName, dtServiced);

            Helper.SendMail("<EMAIL>", email, body, "Recall Due - Second reminder");
        }


        public void ContactPhone(int ID, DateTime date, int modifyingUserId, string ipAddress)
        {
            var entry = context.VP_CPP_Immunization.Where(x => x.Id == ID).FirstOrDefault();
            if (entry != null)
            {
                entry.ContactedByPhone = true;
                entry.ContactedByPhoneDate = date;
                context.SaveChanges(modifyingUserId,ipAddress);
            }
        }

        public void AddRecallLog(int rowid, int patientID, string notes, int modifyingUserId, string ipAddress)
        {
            context.RecallLog.Add(new Cerebrum.Data.Entities.VisitPage.RecallLog()
            {
                Date = DateTime.Now,
                ImmunizationRecallID = rowid,
                PatientRecordId = patientID,
                Notes = notes
            });
            context.SaveChanges(modifyingUserId, ipAddress);
        }

        public void AddRecallLog(int rowid, int patientID, string notes, DateTime date, int modifyingUserId, string ipAddress)
        {
            context.RecallLog.Add(new Cerebrum.Data.Entities.VisitPage.RecallLog()
            {
                Date = date,
                ImmunizationRecallID = rowid,
                PatientRecordId = patientID,
                Notes = notes
            });
            context.SaveChanges(modifyingUserId, ipAddress);
        }

        #endregion
        public List<VMTopLevelReportPhrase> LoadPhrasesData(int AppointmentID, int PatientID, int DoctorID)
        {
            var vm = new LocalVP.VP_VM();

            vm.AppointmentID = AppointmentID;
            vm.DoctorID = DoctorID;
            vm.PatientID = PatientID;
            vm.AppointmentTestLogID = 1;

            var log = GetLogs(vm.AppointmentID, vm.PatientID).OrderByDescending(L => L.Id).ToList().FirstOrDefault();
            if (log != null)
            {
                vm.AppointmentTestLogID = log.Id;
            }

            vm.Options = GetOptions();
            vm.Options = LoadPatientOptions(vm.PatientID, vm.Options);

//            vm.ReportPhrases = new List<VMTopLevelReportPhrase>();
            vm.ReportPhrases = GetReportPhrases(vm.Options, vm.DoctorID);

            vm.ReportPhrases = GetCustomReportPhrases(vm.ReportPhrases, vm.DoctorID);

            vm.ReportPhrases = GetReportPhrasesSkipped(vm.ReportPhrases, vm.DoctorID, vm.PatientID);

            vm.ReportPhrases = LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.PatientID, vm.AppointmentTestLogID);

            return vm.ReportPhrases;

        }

        public LocalVP.VP_VM LoadData(int patientID, int appointmentID = 0, int userID = 0)
        {

            LocalVP.VP_VM vm = new LocalVP.VP_VM();

            vm.PatientID = patientID;
            //vm.UserID = userID;
            vm.UserID = GetMainDoctor(appointmentID);

            if (appointmentID != 0)
            {
                vm.AppointmentID = appointmentID;
            }
            else
            {
                vm.AppointmentID = GetLastAppointmentID(vm.PatientID);
            }


            if (vm.AppointmentID > 0)
            {
                var apptDate = (new UserRepository()).GetAppointmentDate(vm.AppointmentID);
                vm.Logs = GetLogs(vm.AppointmentID, vm.PatientID).OrderByDescending(L => L.Id).ToList();

                var entry = vm.Logs.FirstOrDefault();

                if (entry != null)
                {
                    vm.AppointmentTestLogID = entry.Id;
                }

                var result = GetUniqueCategories();
                vm.VitalSignCategories = result.Item1;
                vm.LabResultCategories = result.Item2;
                vm.vm_cdf.TemplateDetails = GetTemplateDetailsByPatient(vm.PatientID);
                vm.CDF_CollectionDate = GetLatestCDFDate(vm.PatientID, vm.AppointmentID);
                //var result = GetCategories(vm.Options);
                //vm.VitalSignCategories = result.Item1;
                //vm.LabResultCategories = result.Item2;

                LoadSavedValues(
                                    vm.VitalSignCategories,
                                    vm.LabResultCategories,
                                    vm.vm_cdf.TemplateDetails,
                                    vm.AppointmentID,
                                    vm.PatientID,
                                    vm.AppointmentTestLogID, entry != null ? entry.Date : null, apptDate);


                vm.Options = GetOptions();

                vm.Options = LoadPatientOptions(vm.UserID, vm.Options);
                vm.ReportPhrases = GetReportPhrases(vm.Options, vm.UserID);
                vm.ReportPhrases = GetCustomReportPhrases(vm.ReportPhrases, vm.UserID);
                vm.ReportPhrases = GetReportPhrasesSkipped(vm.ReportPhrases, vm.UserID, vm.PatientID);

                vm.ReportPhrases = LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.PatientID, vm.AppointmentTestLogID);

                //make report phrases bolded
                var boldedPhrases = vm.ReportPhrases.Where(p => p.Name == "Impression" || p.Name == "Reason for Test").ToList();
                if (boldedPhrases != null)
                {
                    boldedPhrases.ForEach(p => p.Bolded = true);
                }
                //load CPP
                var appointment = (from p in context.PracticeDoctors
                                   join a in context.Appointments on p.Id equals a.PracticeDoctorId
                                   where a.Id == vm.AppointmentID
                                   select p).FirstOrDefault();
                int? externalDoctorId = appointment != null ? (int?)appointment.ExternalDoctorId : null;

                vm.CPP_Categories = GetCPPCats(externalDoctorId);

                vm.CPP_Categories = GetCustomCPP(vm.UserID, vm.CPP_Categories);

                var visitStartDt = DateTime.MinValue;

                var visitDt = (new UserRepository()).GetAppointmentDate(vm.AppointmentID);

                var visitEndDtMed = visitDt.Value.AbsoluteStart();
                var visitEndDtCPP = visitDt.Value.AbsoluteEnd();

                vm.CPP_Categories = GetCPPCatValues(vm.CPP_Categories, vm.PatientID, vm.DoctorID, visitStartDt, visitEndDtCPP);

                vm.CPP_Categories = this.GetCPPSkipped(vm.CPP_Categories, vm.UserID, vm.PatientID);

                vm.CPP_RiskFactors = GetRiskFactors(vm.PatientID, visitStartDt, visitEndDtCPP);

                vm.VP_CPP_Alerts = GetCPPAlerts(vm.PatientID, visitStartDt, visitEndDtCPP);

                if (vm.CPP_Categories.Any(a => a.Text.ToLower().Equals("medications") && a.Visible == true))
                {
                    //loading medications 
                    var lstMedication = GetMedications(vm.PatientID, visitStartDt, visitEndDtMed);

                    //vm.med_vm.Prior = lstMedication.Where(x => (x.IsDiscontinued == false || x.DateDiscontinued >= visitEndDtMed) && x.DateStarted.Date < visitEndDtMed).ToList();
                    //vm.med_vm.Added = lstMedication.Where(x => x.IsDiscontinued == false && x.DateStarted.Date == visitEndDtMed).ToList();
                    //vm.med_vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date >= visitEndDtMed).ToList();
                    ////vm.med_vm.DoseChanged = lstMedication.Where(p => p.DiscontinuedMedications.Any() == true && p.IsDiscontinued == false && p.DateStarted >= visitStartDt && p.DateStarted <= visitEndDtMed).ToList();
                    //var doseChanged = lstMedication.Where(p => p.DiscontinuedMedications.Any() == true && p.IsDiscontinued == false && p.DateStarted >= visitStartDt && p.DateStarted <= visitEndDtMed).ToList();
                    //doseChanged.ForEach(d =>
                    //{
                    //    vm.med_vm.DoseChanged.Add(d.DiscontinuedMedications.FirstOrDefault());
                    //});


                    var prior = lstMedication.ToList();
                    prior.ForEach(p =>
                    {
                        if (p.DiscontinuedMedications.Count > 0)
                        {
                            if (p.DateStarted < visitEndDtMed)
                            {
                                if (!p.DateDiscontinued.HasValue)
                                    vm.med_vm.Prior.Add(p);
                                else
                                if (p.DateDiscontinued.Value >= visitEndDtMed)
                                {
                                    vm.med_vm.Prior.Add(p);
                                }
                            }
                            else
                            {
                                //var med = p.DiscontinuedMedications.First();
                                foreach (var med in p.DiscontinuedMedications)
                                {
                                    if (med.DateDiscontinued.HasValue && med.DateDiscontinued.Value >= visitEndDtMed)
                                    {
                                        vm.med_vm.Prior.Add(med);
                                        break;
                                    }
                                }
                            }
                        }
                        else
                        {
                            if (p.DateDiscontinued.HasValue)
                            {
                                if (p.DateDiscontinued.Value >= visitEndDtMed)
                                    vm.med_vm.Prior.Add(p);
                            }
                            else
                            if (p.DateStarted < visitEndDtMed)
                            {
                                vm.med_vm.Prior.Add(p);
                            }
                        }
                    });

                    vm.med_vm.Added = lstMedication.Where(x =>
                                                        x.DateStarted.Date.AbsoluteStart() == visitEndDtMed &&
                                                        (!x.DateDiscontinued.HasValue || x.DateDiscontinued >= visitEndDtMed)
                                                        && x.DiscontinuedMedications.Count == 0)

                                                        .ToList();

                    vm.med_vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date.AbsoluteStart() == visitEndDtMed).ToList();

                    lstMedication.ForEach(m =>
                    {
                        if (m.DiscontinuedMedications.Count > 0 && m.DateStarted.AbsoluteStart() == visitEndDtMed)
                        {
                            //m.DiscontinuedMedications.Where(x => x.DateStarted.AbsoluteStart() == visitEndDt).ToList().FirstOrDefault();
                            vm.med_vm.DoseChanged.Add(m);
                        }
                    });

                    vm.med_vm.Allergies = (new UnitOfWorkPatientAllergy()).allergyRep.GetPatientAllergies(patientID, visitStartDt, visitEndDtMed);
                }
            }

            return vm;

        }

        public VP_Report_VM LoadReportData(int appointmentID, int userID)
        {
            VP_Report_VM vmReturn = new VP_Report_VM();

            int vpid = GetVPTestID();
            var entry = context.AppointmentTests.Where(x => x.AppointmentId == appointmentID && x.TestId == vpid).FirstOrDefault();
            if (entry != null)
            {
                vmReturn.ReportData.Date = entry.startTime;

                int? patientId = (new UserRepository()).GetPatientByAppointment(appointmentID);

                if (patientId.HasValue)
                {
                    LocalVP.VP_VM vm = LoadData(patientId.Value, appointmentID, userID);

                    // Send unique user from log
                    var loggrp = from l in vm.Logs
                                 group l by l.UserId into lg
                                 select new { lg.Key, Other = lg };
                    foreach (var g in loggrp)
                    {
                        vmReturn.ReportData.Logs.Add(g.Other.FirstOrDefault());
                    }

                    foreach (var r in vm.ReportPhrases)
                    //vm.ReportPhrases.ForEach(r =>
                    {

                        if (!r.Skipped)
                        {
                            vmReturn.ReportData.Phrases.Add(

                                                       new ValueLabel()
                                                       {
                                                           label = r.Name,
                                                           value = r.Value,
                                                           Bolded = r.Bolded
                                                       });
                        }
                    }
                    #region CDF Before Lab
                    List<ValueLabel> mesurements1 = new List<ValueLabel>();
                    vm.vm_cdf.TemplateDetails.ForEach(c =>
                    {

                        if (!string.IsNullOrWhiteSpace(c.Value))
                        {
                            var meas = new ValueLabel()
                            {
                                label = c.TemplateItemName,
                                value = string.IsNullOrWhiteSpace(c.Value) ? c.Value : $"{c.Value} {c.Units}",
                                IsText = c.IsText,
                                OverDue = c.OverDue,
                                OverTarget = c.OverTarget
                            };
                            mesurements1.Add(meas);
                            vmReturn.ReportData.CDF.Add(meas);
                        }

                    });
                    List<ValueLabel> newmeaslst = new List<ValueLabel>();
                    List<Cerebrum.ViewModels.VP.VMVisitMeasurement> finallist = new List<Cerebrum.ViewModels.VP.VMVisitMeasurement>();
                    try
                    {
                        var catmeas = vm.LabResultCategories.SelectMany(sm => sm.Measurements);
                        var vitMeasu = vm.VitalSignCategories.SelectMany(nm => nm.Measurements);
                        finallist = catmeas.Concat(vitMeasu).ToList();
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex.ToString());
                    }

                    foreach (var em in mesurements1)
                    {
                        if (finallist.Count() > 0)
                        {
                            if (!finallist.Any(it => it.Name.Equals(em.label)))
                            {
                                newmeaslst.Add(em);
                            }
                        }
                        else
                        {
                            newmeaslst.Add(em);
                        }
                    }
                    vmReturn.ReportData.categories.Add(
                        new MesurementByCategory()
                        {
                            mesurements = newmeaslst,
                            name = "CDF"
                        });
                    #endregion
                    var labResultCategories = vm.LabResultCategories;
                    foreach (var c in labResultCategories)
                    {

                        List<ValueLabel> mesurements = new List<ValueLabel>();
                        foreach (var measure in c.Measurements)
                        {
                            mesurements.Add(new ValueLabel()
                            {
                                label = measure.Name,
                                value = measure.Value, //string.IsNullOrWhiteSpace(measure.Value) ? measure.Value : $"{measure.Value} {measure.Units}",
                                Units = measure.Units,
                                IsVitalSigns = false,
                                Bolded = measure.OverTarget
                            });
                        }


                        vmReturn.ReportData.categories.Add(
                         new MesurementByCategory()
                         {
                             mesurements = mesurements,
                             name = c.Name
                         });

                    }


                    vm.VitalSignCategories.ForEach(c =>
                    {
                        List<ValueLabel> mesurements = new List<ValueLabel>();
                        foreach (var measure in c.Measurements)
                        {
                            mesurements.Add(new ValueLabel()
                            {
                                label = measure.Name,
                                value = string.IsNullOrWhiteSpace(measure.Value) ? measure.Value : $"{measure.Value} {measure.Units}",
                                Units = measure.Units,
                                IsVitalSigns = true
                            });
                        }


                        vmReturn.ReportData.categories.Add(
                         new MesurementByCategory()
                         {
                             mesurements = mesurements,
                             name = c.Name
                         });

                    });


                    vm.CPP_Categories.ForEach(c =>
                    {

                        if (!c.Skipped)
                        {

                            vmReturn.ReportData.CPP.Add(new ValueLabel()
                            {
                                label = c.Text,
                                value = c.Value

                            });
                        }
                    });
                    //List<ValueLabel> mesurements1 = new List<ValueLabel>();
                    //vm.vm_cdf.TemplateDetails.ForEach(c =>
                    //{

                    //    if (!string.IsNullOrWhiteSpace(c.Value))
                    //    {
                    //        var meas = new ValueLabel()
                    //        {
                    //            label = c.TemplateItemName,
                    //            value = c.Value

                    //        };
                    //        mesurements1.Add(meas);
                    //        vmReturn.ReportData.CDF.Add(meas);
                    //    }

                    //});

                    //List<ValueLabel> newmeaslst = new List<ValueLabel>();
                    //var catmeas = vmReturn.ReportData.categories.SelectMany(sm => sm.mesurements);
                    //foreach (var em in mesurements1)
                    //{
                    //    if(!catmeas.Any(it=>it.label.Equals(em.label)))
                    //    {
                    //            newmeaslst.Add(em);
                    //    }
                    //}
                    //vmReturn.ReportData.categories.Add(
                    //    new MesurementByCategory()
                    //    {
                    //        mesurements = newmeaslst,
                    //        name = "CDF"
                    //    });

                    vmReturn.ReportData.OpeningStatement = LoadOpeningStatement(vm.AppointmentID);

                    vm = LoadBillingCodes(vm);
                    vmReturn.ReportData.ConsultCode = vm.ConsultCode;
                    vmReturn.ReportData.DiagnoseCode = vm.DiagnoseCode;
                    vmReturn.ReportData.DiagnoseCode2 = vm.DiagnoseCode2;
                    vmReturn.ReportData.DiagnoseCode3 = vm.DiagnoseCode3;

                    //loading medications 
                    UnitOfWorkPatientAllergy unitRep = new UnitOfWorkPatientAllergy();
                    var visitStartDt = DateTime.MinValue;
                    var visitEndDt = (new UserRepository()).GetAppointmentDate(appointmentID);
                    visitEndDt = visitEndDt.Value.AbsoluteStart();
                    if (vm.CPP_Categories.Any(a => a.Text.ToLower().Equals("medications") && a.Visible == true))
                    {
                        var lstMedication = GetMedications(vm.PatientID, visitStartDt, visitEndDt.Value);

                        var prior = lstMedication.ToList();

                        prior.ForEach(p =>
                        {
                            if (p.DiscontinuedMedications.Count > 0)
                            {
                                if (p.DateStarted < visitEndDt.Value)
                                {
                                    if (!p.DateDiscontinued.HasValue)
                                        vmReturn.ReportData.med_vm.Prior.Add(p);
                                    else
                                    if (p.DateDiscontinued.Value >= visitEndDt.Value)
                                    {
                                        vmReturn.ReportData.med_vm.Prior.Add(p);
                                    }
                                }
                                else
                                {
                                    //var med = p.DiscontinuedMedications.First();
                                    foreach (var med in p.DiscontinuedMedications)
                                    {
                                        if (med.DateDiscontinued.HasValue && med.DateDiscontinued.Value >= visitEndDt.Value)
                                        {
                                            vmReturn.ReportData.med_vm.Prior.Add(med);
                                            break;
                                        }
                                    }
                                }
                            }
                            else
                            {
                                if (p.DateDiscontinued.HasValue)
                                {
                                    if (p.DateDiscontinued.Value >= visitEndDt.Value)
                                        vmReturn.ReportData.med_vm.Prior.Add(p);
                                }
                                else
                                if (p.DateStarted < visitEndDt.Value)
                                {
                                    vmReturn.ReportData.med_vm.Prior.Add(p);
                                }
                            }
                        });

                        vmReturn.ReportData.med_vm.Added = lstMedication.Where(x =>
                                                            x.DateStarted.Date.AbsoluteStart() == visitEndDt.Value &&
                                                            (!x.DateDiscontinued.HasValue || x.DateDiscontinued >= visitEndDt.Value)
                                                            && x.DiscontinuedMedications.Count == 0)

                                                            .ToList();

                        vmReturn.ReportData.med_vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date.AbsoluteStart() == visitEndDt).ToList();

                        lstMedication.ForEach(m =>
                        {
                            if (m.DiscontinuedMedications.Count > 0 && m.DateStarted.AbsoluteStart() == visitEndDt)
                            {
                                //m.DiscontinuedMedications.Where(x => x.DateStarted.AbsoluteStart() == visitEndDt).ToList().FirstOrDefault();
                                vmReturn.ReportData.med_vm.DoseChanged.Add(m);
                            }
                        });


                    }

                    if (vm.CPP_Categories.Any(a => a.Text.ToLower().Equals("allergies") && a.Visible == true))
                        vmReturn.ReportData.med_vm.Allergies = unitRep.allergyRep.GetPatientAllergies(vm.PatientID, visitStartDt, visitEndDt.Value);
                }
            }
            else return null;

            return vmReturn;

        }
        
        public List<ReportHistory> GetReportHistory(int appointmentID, int patientID)
        {
            List<ReportHistory> lst = new List<ReportHistory>();

            lst = (from s in context.VP_SendReport
                   join t in context.SendType on
                     s.SendTypeId equals t.Id
                   where s.AppointmentId == appointmentID && s.PatientRecordId == patientID
                   orderby s.Id descending
                   select new ReportHistory
                   {
                       Location = s.Location,
                       DateEntered = s.DateEntered,
                       Sent = s.Sent,
                       Amended = s.Amended,
                       AppointmentId = s.AppointmentId,
                       PatientId = s.PatientRecordId,
                       SendType = t.Name,
                       URL = s.URL,
                       Email = s.Email,
                       Fax = s.Fax,
                       DocName = s.DocName,
                       ErrorMessage = s.ErrorMessage

                   }).ToList();

            return lst;
        }

        public List<VMPrivacyNoteItem> GetAllPrivacyNotes(int patientID)
        {

            return (from n in context.VP_Privacy_Notes
                    join u in context.Users on n.UserId equals u.UserID
                    where n.PatientRecordId == patientID
                    orderby n.Id descending
                    select new VMPrivacyNoteItem()
                    {
                        Id = n.Id,
                        Note = n.Notes,
                        PatientId = n.PatientRecordId,
                        UserId = n.UserId,
                        UserName = u.UserName,
                        DateEntered = n.DateEntered

                    }).ToList();
        }

        public List<VMPrivacyNoteItem> GetPrivacyNotes(int userID, int patientID)
        {

            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("userID",userID),
                new SqlParameter("patientID",patientID ),
            };
            return context.GetData<VMPrivacyNoteItem>("[dbo].[Get_VP_PrivacyNotes]", parms).ToList();


            //return (from n in context.VP_Privacy_Notes
            //            //       join 
            //            //u in context.Users on n.UserId equals u.UserID
            //        where n.UserId == userID && n.PatientRecordId == patientID
            //        orderby n.Id descending
            //        select new VP_Privacy_Note_Item()
            //        {
            //            Id = n.Id,
            //            Note = n.Notes,
            //            PatientId = n.PatientRecordId,
            //            UserId = n.UserId,
            //            //UserName = u.UserName,
            //            DateEntered = n.DateEntered

            //        }).ToList();
        }

        public void AddPrivacyNote(VMPrivacyNoteItem vm, int modifyingUserId, string ipAddress)
        {
            context.VP_Privacy_Notes.Add(new VP_Privacy_Notes()
            {
                Notes = vm.Note,
                PatientRecordId = vm.PatientId,
                UserId = vm.UserId,
                DateEntered = DateTime.Now
            });
            
            context.SaveChanges(modifyingUserId,ipAddress);

        }

        public string GetXMLFileName(int appointmentID, int patientID, int doctorID)
        {
            //office_test_report~appt_time~Bill_Num~OHIP~fax
            string fileName = "Newmarket555_Echocardiogram_Report~1_12_2016~169193~**********~**********.pdf";

            var ohipBillingNumber = "000000";
            //var doctorID = GetMainDoctor(appointmentID);
            var doctor = context.ExternalDoctors.Where(e => e.Id == doctorID).FirstOrDefault();

            var dem = context.Demographics.Where(d => d.PatientRecordId == patientID).FirstOrDefault();

            var appointment = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();

            var office = context.Offices.Where(o => o.Id == appointment.OfficeId).FirstOrDefault();

            if (doctor != null)
            {
                ohipBillingNumber = doctor.OHIPPhysicianId;
            }

            var fax = (from e in context.ExternalDoctorPhoneNumbers
                       where
                            e.ExternalDoctorId == doctorID &&
                            !string.IsNullOrEmpty(e.faxNumber)
                       orderby e.Id descending
                       select
                       e.faxNumber
                       ).ToList().FirstOrDefault();


            if (fax != null)
            {
                fax = fax.Replace("-", string.Empty)
                         .Replace("(", string.Empty)
                         .Replace(")", string.Empty)
                         .Replace(" ", string.Empty);
            }

            var healthCards = context.HealthCards.Where(h => h.DemographicId == dem.Id)
                              .OrderByDescending(x => x.Id).FirstOrDefault();

            var test = context.Tests.Where(t => t.testShortName == "VP").FirstOrDefault();

            var testName = test.testFullName;

            var appTime = appointment.appointmentTime;

            var officeName = office.name;
            var officeFax = office.fax;

            var ohipNumber = "0000";
            if (healthCards != null)
                ohipNumber = healthCards.number;

            fileName = string.Format("{0}_{1}_Report~{2}~{3}~{4}~{5}.pdf",

                                        officeName,
                                        testName,
                                        appTime.ToShortDateString().Replace("/", "_"),
                                        ohipBillingNumber,
                                        ohipNumber,
                                        fax
                                    //officeFax.Replace("-", string.Empty)
                                    //         .Replace("(", string.Empty)
                                    //         .Replace(")", string.Empty)
                                    //         .Replace(" ", string.Empty)
                                    );

            return fileName;

        }
        public List<Cerebrum.ViewModels.VP.SendReport_VM> NotSentHRM()
        {
            return _sendReportBll.NotSentHRM();
        }
        public HL7_Data_VM LoadHL7_VM(int appointmentID, int testID, int physicianID, int patientID)
        {
            HL7_Data_VM vm = new HL7_Data_VM();

            var p = (from d in context.Demographics
                     where d.PatientRecordId == patientID
                     select new
                     {
                         d.Id,
                         d.PatientRecordId,
                         d.lastName,
                         d.firstName,
                         d.dateOfBirth,
                         gender = d.gender.ToString()
                     }).FirstOrDefault();



            vm.HL7_Data_Patient.DemographicID = p.Id;
            vm.HL7_Data_Patient.PatientID = p.PatientRecordId;
            vm.HL7_Data_Patient.lastName = p.lastName;
            vm.HL7_Data_Patient.firstName = p.firstName;
            vm.HL7_Data_Patient.DOB = p.dateOfBirth.Value;
            vm.HL7_Data_Patient.Sex = p.gender;

            var a = context.DemographicsAddress.Where(c => c.DemographicId == vm.HL7_Data_Patient.DemographicID)
                          .OrderByDescending(x => x.Id).FirstOrDefault();
            if (a != null)
            {
                vm.HL7_Data_Patient.Address = a.addressLine1;
                vm.HL7_Data_Patient.City = a.city;
                vm.HL7_Data_Patient.PostalCode = a.postalCode;
            }


            var phone = context.DemographicsContactPhoneNumbers.Where(ph => ph.DemographicsContactId == vm.HL7_Data_Patient.DemographicID)
                .OrderByDescending(x => x.Id).FirstOrDefault();

            if (phone != null)
            {
                vm.HL7_Data_Patient.Phone = phone.contactPhoneNumber;
            }

            var test = context.Tests.FirstOrDefault(t => t.Id == testID);

            if (test != null)
            {
                vm.HL7_Data_Test.examTypeShort = test.testShortName;
                vm.HL7_Data_Test.examTypeLong = test.testFullName;  //"Peripheral arterial doppler/lower extremities";
                                                                    //vm.HL7_Data_Test.modality = test.modality.ToString(); //"VASCULAR STUDIES";
                var modalities = test.Modalities.Any() ? string.Join(",", test.Modalities.Select(s => s.Modality.modalityName).ToArray()) : "";
                vm.HL7_Data_Test.modality = modalities;
                //vm.HL7_Data_Test.addReport = 1 ;
                // vm.HL7_Data_Test.addReportSufix = "";
                //hl7.DocType = Exams[id].DocType;        //"MR";
                //vm.HL7_Data_Test.EXAM_TYPE = test.;   //"DI";
                //vm.HL7_Data_Test.sendingFacility = "";  //"DI";
            }


            var healthCard = context.HealthCards.Where(h => h.DemographicId == vm.HL7_Data_Patient.DemographicID)
                .OrderByDescending(d => d.Id).FirstOrDefault();

            if (healthCard != null)
            {
                if (!string.IsNullOrWhiteSpace(healthCard.number))
                    vm.HL7_Data_Patient.HealthCardNumber = healthCard.number;
                if (!string.IsNullOrWhiteSpace(healthCard.version))
                    vm.HL7_Data_Patient.HealthCardCode = healthCard.version.Trim();
            }

            var appt = context.Appointments.Where(at => at.Id == appointmentID).FirstOrDefault();
            var referralDoctorId = appt.referralDoctorId;

            //appt.referralDoctorId

            var refDoc = (from d in context.DemographicsDefaultReferralDoctors
                          join e in context.ExternalDoctors
                          on d.ExternalDoctorId equals e.Id
                          where d.DemographicId == vm.HL7_Data_Patient.DemographicID
                          orderby d.Id descending
                          select e).FirstOrDefault();

            var extDoc = (from d in context.DemographicsMainResponsiblePhysicians
                          join e in context.ExternalDoctors
                          on d.ExternalDoctorId equals e.Id
                          where d.DemographicId == vm.HL7_Data_Patient.DemographicID
                          orderby d.Id descending
                          select e).FirstOrDefault();

            if (refDoc != null)
            {
                vm.HL7_Data_Physician.OrderingDoctorID = refDoc.Id.ToString();
                vm.HL7_Data_Physician.OrderingDoctorLastName = refDoc.lastName;
                vm.HL7_Data_Physician.OrderingDoctorFirstName = refDoc.firstName;
            }

            if (extDoc != null)
            {
                vm.HL7_Data_Physician.DoctorID = extDoc.Id.ToString();
                vm.HL7_Data_Physician.DoctorLastName = extDoc.lastName;
                vm.HL7_Data_Physician.DoctorFirstName = extDoc.firstName;
            }

            return vm;
        }

        public Tuple<string, string, string> GetDocInfo(int docID)
        {

            Tuple<string, string, string> retVal = null;

            var externalDoc = (from e in context.ExternalDoctors
                               where e.Id == docID
                               select e).FirstOrDefault();

            if (externalDoc != null)
            {
                retVal = Tuple.Create<string, string, string>(externalDoc.CPSO, "MD", docID.ToString());
            }
            return retVal;
        }
        public List<VMDoctorReport> GetContactList(int appointmentID, DoctorNames docNames = null, int testID = 0)
        {
            List<VMDoctorReport> docList = new List<VMDoctorReport>();
            var appointment = context.Appointments.Find(appointmentID);
            var hasDocNames = docNames != null ? true : false;
            if (appointment != null)
            {
                var patientID = appointment.PatientRecordId;
                var appDate = appointment.appointmentTime;
                var reportBLL = new Cerebrum.BLL.Documents.ReportBLL(context);
                var reportDoctors = reportBLL.GetReportDoctorsByAppointment(appointment);
                var referralDoc = reportDoctors.Where(x => x.DocType == DocType.Referral).FirstOrDefault();
                var familyDoc = reportDoctors.Where(x => x.DocType == DocType.Family).FirstOrDefault();
                var mainDoc = reportDoctors.Where(x => x.DocType == DocType.Reporting && x.IsMainResponsibleDoctor == false).FirstOrDefault();
                var ccDoctors = reportDoctors.Where(x => x.DocType == DocType.CC).ToList();
                var newReportDocs = new List<Cerebrum.ViewModels.Documents.VMReportDoctor>();

                if (mainDoc != null)
                {
                    newReportDocs.Add(mainDoc);
                    if (hasDocNames)
                    {
                        docNames.reportingDocId = mainDoc.ExternalDoctorId;
                        docNames.mainDoc = "Dr. " + mainDoc.FirstName + " " + mainDoc.LastName;
                        //docNames.reportingDoc = "Dr. " + mainDoc.FirstName + " " + mainDoc.LastName;
                        docNames.reportingDoc = mainDoc.FirstName + " " + mainDoc.LastName;
                    }
                }

                if (referralDoc != null)
                {
                    newReportDocs.Add(referralDoc);
                    if (hasDocNames)
                    {
                        docNames.refferingDocId = referralDoc.ExternalDoctorId;
                        docNames.refferingDoc = "Dr. " + referralDoc.FirstName + " " + referralDoc.LastName;
                    }
                }

                if (familyDoc != null)
                {
                    newReportDocs.Add(familyDoc);
                    if (hasDocNames)
                    {
                        docNames.familyDocId = familyDoc.ExternalDoctorId;
                        docNames.familyDoc = "Dr. " + familyDoc.FirstName + " " + familyDoc.LastName;
                    }
                }

                if (hasDocNames)
                {
                    foreach (var doc in ccDoctors)
                    {
                        docNames.AssocciatedDoctors.Add("Dr. " + doc.FirstName + " " + doc.LastName);
                    }

                    docNames.VisitDate = appDate.ToString("MMMM dd, yyyy");
                }

                newReportDocs.AddRange(ccDoctors);
                foreach (var item in newReportDocs)
                {
                    docList.Add(new VMDoctorReport()
                    {
                        ID = item.ExternalDoctorId,
                        Email = item.Email,
                        Fax = item.Fax,
                        HRM = item.HRM,
                        Mail = item.Mail,
                        Name = item.LastName + " " + item.FirstName,
                        LastName = item.LastName,
                        FirstName = item.FirstName,
                        EmailAddress = item.EmailAddress,
                        FaxNumber = item.FaxNumber,
                        CPSO = item.CPSO,
                        OHIPId = item.OHIPId,
                        DocType = item.DocType,
                        PhysicianType = item.PhysicianType
                    });                    
                }

                #region Technician 
                if (testID > 0)
                {
                    var appointmentest = context.AppointmentTests.Where(a => a.AppointmentId == appointmentID && a.TestId == testID).FirstOrDefault();
                    if (appointmentest != null)
                    {
                        var apptTest = context.AppointmentTestResources.Where(a =>
                                                        a.AppointmentTestId == appointmentest.Id &&
                                                        a.isActive == true &&
                                                        a.isDoctorRequiredInOffice == false)
                                                        .ToList().FirstOrDefault();
                        if (apptTest != null)
                        {
                            if (apptTest.performedByUserId.HasValue)
                            {
                                var user = context.Users.Where(s => s.UserID == apptTest.performedByUserId.Value).FirstOrDefault();
                                if (user != null)
                                {
                                    if (hasDocNames)
                                    {
                                        docNames.technician = user.LastName + " " + user.FirstName;
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                #region Additional test Doc e.g nuclear
                var appTest = context.AppointmentTests
                    .Include("Appointment")
                    .Include("AppointmentTestResources")
                    .Where(a => a.AppointmentId == appointmentID && a.TestId == testID && a.IsActive)
                    .FirstOrDefault();
                if (appTest != null)
                {
                    var appPracticeDoctorId = appTest.Appointment.PracticeDoctorId;                    
                    var userIds = appTest.AppointmentTestResources
                        .Where(x => x.assignedToUserId != null && x.isActive)
                        .Select(x => x.assignedToUserId)
                        .Distinct().ToList();


                    var testDocs = (from e in context.ExternalDoctors
                                    join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                                    join u in context.Users on p.ApplicationUserId equals u.Id
                                    where userIds.Contains(u.UserID)
                                    && p.ApplicationUserId != null
                                    && u.CerebrumUserType == UserTypeEnum.Doctor
                                    && p.Id != appPracticeDoctorId && appPracticeDoctorId > 0
                                    select e).ToList();

                    var testDoc = testDocs.FirstOrDefault();

                    if (testDoc != null)
                    {
                        docList.Add(new VMDoctorReport()
                        {
                            ID = testDoc.Id,
                            Email = testDoc.email,
                            Fax = testDoc.fax,
                            HRM = testDoc.HRM,
                            Mail = testDoc.mail,
                            Name = "Dr. " + testDoc.firstName + " " + testDoc.lastName,
                            LastName = testDoc.lastName,
                            FirstName = testDoc.firstName,
                            EmailAddress = testDoc.emailAddress,
                            DocType = DocType.Test,
                            PhysicianType = testDoc.PhysicianType
                        });

                        if (hasDocNames)
                        {
                            docNames.reportingDocId2 = testDoc.Id;
                            docNames.reportingDoc2 = "Dr. " + testDoc.firstName + " " + testDoc.lastName;
                            docNames.reportingDoc2Degrees = testDoc.Degrees;
                        }
                    }

                }
                #endregion
            }

            return docList.OrderBy(o => o.DocType).ToList();
        }
        public List<VMDoctorReport> GetContactListBooking(int appointmentID, DoctorNames docNames = null, int testID = 0)
        {

            List<VMDoctorReport> docList = new List<VMDoctorReport>();
            
            #region  geting referral doctor
            var appointment = context.Appointments.Find(appointmentID);
            if(appointment != null)
            {
                var patientId = appointment.PatientRecordId;
                var referralDocId = appointment.referralDoctorId;
                if (referralDocId > 0)
                {
                    var dbRefDoc = context.ExternalDoctors.Find(referralDocId);
                    if (dbRefDoc != null)
                    {
                        var reportBLL = new Cerebrum.BLL.Documents.ReportBLL(context);
                        var referralDoctor = reportBLL.LoadReportDoctor(appointment, dbRefDoc, DocType.Referral);

                        if(referralDoctor != null)
                        {
                            docList.Add(new VMDoctorReport()
                            {
                                ID = referralDoctor.ExternalDoctorId,
                                Email = referralDoctor.Email,
                                Fax = referralDoctor.Fax,
                                HRM = referralDoctor.HRM,
                                Mail = referralDoctor.Mail,
                                Name = referralDoctor.LastName + " " + referralDoctor.FirstName,
                                LastName = referralDoctor.LastName,
                                FirstName = referralDoctor.FirstName,
                                EmailAddress = referralDoctor.EmailAddress,
                                FaxNumber = referralDoctor.FaxNumber,
                                CPSO = referralDoctor.CPSO,
                                OHIPId = referralDoctor.OHIPId,
                                Address = referralDoctor.Address,
                                City = referralDoctor.City,
                                PostalCode = referralDoctor.PostalCode,
                                Province = referralDoctor.Province,
                                DocType = DocType.Referral
                            });

                            if (docNames != null)
                            {
                                docNames.refferingDocId = referralDoctor.ExternalDoctorId;
                                docNames.refferingDoc = referralDoctor.LastName + " " + referralDoctor.FirstName;
                            }
                        }
                    }
                }                
                
            }
          
            #endregion

            return docList;
        }
        public LocalVP.VP_VM LoadBillingCodes(LocalVP.VP_VM vm)
        {

            var entry = context.AppointmentBill.Where(b => b.AppointmentID == vm.AppointmentID).FirstOrDefault();

            if (entry != null)
            {
                if (entry.ConsultCode > 0)
                {
                    var consultCode = context.ConsultCode.Where(c => c.Id == entry.ConsultCode).FirstOrDefault();

                    vm.ConsultCode.Id = entry.ConsultCode;
                    vm.ConsultCode.Code = consultCode.Code;
                }

                if (entry.ConsultCode2 > 0)
                {
                    var consultCode = context.ConsultCode.Where(c => c.Id == entry.ConsultCode2).FirstOrDefault();

                    vm.ConsultCode2.Id = entry.ConsultCode2;
                    vm.ConsultCode2.Code = consultCode.Code;
                }

                if (entry.ConsultCode3 > 0)
                {
                    var consultCode = context.ConsultCode.Where(c => c.Id == entry.ConsultCode3).FirstOrDefault();

                    vm.ConsultCode3.Id = entry.ConsultCode3;
                    vm.ConsultCode3.Code = consultCode.Code;
                }

                if (entry.DiagnosticCode > 0)
                {
                    var DiagnosticCode = context.DiagnoseCode.Where(c => c.Id == entry.DiagnosticCode).FirstOrDefault();

                    vm.DiagnoseCode.Id = entry.DiagnosticCode;
                    vm.DiagnoseCode.Diagnosis = DiagnosticCode.Diagnosis;
                    vm.DiagnoseCode.Code = DiagnosticCode.Code;
                }
                if (entry.DiagnosticCode2 > 0)
                {
                    var DiagnosticCode2 = context.DiagnoseCode.Where(c => c.Id == entry.DiagnosticCode2).FirstOrDefault();

                    vm.DiagnoseCode2.Id = entry.DiagnosticCode2;
                    vm.DiagnoseCode2.Diagnosis = DiagnosticCode2.Diagnosis;
                    vm.DiagnoseCode2.Code = DiagnosticCode2.Code;
                }
                if (entry.DiagnosticCode3 > 0)
                {
                    var DiagnosticCode3 = context.DiagnoseCode.Where(c => c.Id == entry.DiagnosticCode3).FirstOrDefault();

                    vm.DiagnoseCode3.Id = entry.DiagnosticCode3;
                    vm.DiagnoseCode3.Diagnosis = DiagnosticCode3.Diagnosis;
                    vm.DiagnoseCode3.Code = DiagnosticCode3.Code;
                }

                if (entry.billStatusId.HasValue)
                {
                    vm.AppointmentBillStatus = entry.billStatusId;
                }

                if (entry.ImmunizationCode > 0)
                {
                    var immunCode = context.PreventiveCareBonusCode.Where(c => c.Id == entry.ImmunizationCode).FirstOrDefault();
                    if (immunCode != null)
                    {
                        vm.ImmunizationCode.Id = immunCode.Id;
                        vm.ImmunizationCode.Diagnosis = immunCode.Code;
                        vm.ImmunizationCode.Code = immunCode.Code;
                    }
                }
            }

            return vm;
        }
        public List<VMImmunizationPatientRecord> GetImmunizationRecords(int patientID, DateTime? frm = null, DateTime? to = null)
        {

            //GetLastContactDate(recallID, x.PatientId)

            var lst =

                (from x in context.VP_CPP_Immunization
                 join t in context.VP_CPP_ImmunizationType on x.VP_CPP_ImmunizationTypeId equals t.Id
                 let Recall = (from r in context.ImmunizationRecall
                               join s in context.VP_CPP_ImmunizationStatus on r.VP_CPP_ImmunizationStatusId equals s.Id
                               where r.VP_CPP_Immunization_ID == x.Id && r.Active
                               select new { r.DateCreated, s.Status, s.Id }
                              ).ToList().FirstOrDefault()
                 where
                    x.PatientRecordId == patientID &&
                    ((frm.HasValue && to.HasValue) ? (x.SubmitDate >= frm && x.SubmitDate <= to) : 1 == 1)
                 // && t.IsImmunization      
                 //((frm.HasValue && to.HasValue) ? (  x.SubmitDate >= to) : 1 == 1)
                 select new VMImmunizationPatientRecord()
                 {

                     DateServiced = Recall == null ? DateTime.MinValue : Recall.DateCreated,
                     VP_CPP_ImmunizationStatus = Recall == null ? string.Empty : Recall.Status,
                     VP_CPP_ImmunizationStatusId = Recall == null ? 0 : Recall.Id,

                     Id = x.Id,
                     PatientId = x.PatientRecordId,
                     PhysicianId = x.PhysicianId,
                     AdministeredBy = x.AdministeredBy,
                     Table_Of_Doctors_Names = x.Table_Of_Doctors_Names,
                     ImmunizationDate = x.ImmunizationDate,
                     ImmunizationRefusedDate = x.ImmunizationRefusedDate,
                     Name = x.Name,
                     VP_CPP_ImmunizationType = t.Name,
                     Notes = x.Notes,
                     Manufacturer = x.Manufacturer,
                     LotNumber = x.LotNumber,
                     Route = x.Route,
                     Site = x.Site,
                     Dose = x.Dose,
                     Instructions = x.Instructions,
                     NextDate = x.NextDate,
                     NotRemind = x.NotRemind,
                     ReasonNextDate = x.ReasonNextDate,
                     CodingVocabulary = x.CodingVocabulary,
                     ImmunizationCode = x.ImmunizationCode,
                     ReasonForDel = x.ReasonForDel,
                     SubmitDate = x.SubmitDate,
                     isactive = x.isactive,
                     Setid = x.Setid,
                     Colonoscopy = x.Colonoscopy,
                     VP_CPP_ImmunizationTypeId = x.VP_CPP_ImmunizationTypeId,
                     VP_CPP_ImmunizationTypeCode = t.Code,
                     IsImmunization = t.IsImmunization

                     //VP_CPP_ImmunizationType = x.VP_CPP_ImmunizationTypeId

                 }).OrderByDescending(x => x.SubmitDate).ToList();




            return lst;
        }
        public List<VMImmunizationGroup> GetImmunizationGroups(int practiceId, int patientID, string ImmunizationName)
        {
            List<VMImmunizationGroup> lst = new List<VMImmunizationGroup>();
            List<SqlParameter> parms = new List<SqlParameter>
                {
                    new SqlParameter("PracticeId",practiceId),
                    new SqlParameter("PatientRecordId",patientID),
                    new SqlParameter("ImmunizationName",ImmunizationName)
                };
            var idLst = context.GetData<VMCppImmunizationType>("[dbo].[SP_Get_Patient_VP_CPP_Immunization_Types]", parms);

            foreach (var item in idLst)
            {
                VMImmunizationGroup vm = new VMImmunizationGroup();
                vm.ImmunizationTypeID = item.TypeId;
                vm.ImmunizationName = item.Name;

                List<SqlParameter> parms2 = new List<SqlParameter>
                {
                    new SqlParameter("PatientRecordId",patientID),
                    new SqlParameter("VP_CPP_ImmunizationTypeId",item.TypeId)
                };
                var patientRecords = context.GetData<VMCppPatientImmunization>("[dbo].[SP_Get_Patient_ImmunizationType]", parms2).ToList();

                foreach (var patientRecord in patientRecords.OrderByDescending(x => x.DateCreated))
                {
                    vm.Data.Add(new VMImmuniationData()
                    {
                        ID = patientRecord.Id,
                        ImmunizationRecallID = patientRecord.RecallID,
                        ImmunizationStatusID = patientRecord.VP_CPP_ImmunizationStatusId,
                        Name = patientRecord.Name,
                        PartialDate = GetBuildDateFromPartial(patientRecord.ImmunizationDay, patientRecord.ImmunizationMonth, patientRecord.ImmunizationYear),
                        Date = patientRecord.ImmunizationDate,
                        NextDate = patientRecord.DateCreated,
                        PartialNextDate = GetBuildDateFromPartial(patientRecord.DateServicedDay, patientRecord.DateServicedMonth, patientRecord.DateServicedYear),
                        ImmunType = patientRecord.ProcedureType,
                        ImmunTypeID = patientRecord.ProcedureTypeID,
                        Status = patientRecord.Status
                        //Age = (new UserRepository()).GetAge(patientRecord.PatientId)
                    });
                }

                lst.Add(vm);
            }
            return lst;
        }
        public bool AdministerImmunization(VMImmunizationPatientRecord vm, int modifyingUserId, string ipAddress)
        {
            bool succeeded = true;
            try
            {
                var newImmunization = context.VP_CPP_Immunization.Add(new VP_CPP_Immunization()
                {
                    PatientRecordId = vm.PatientId,
                    PhysicianId = vm.PhysicianId,
                    AdministeredBy = vm.AdministeredBy,
                    Table_Of_Doctors_Names = vm.Table_Of_Doctors_Names,
                    ImmunizationDate = vm.ImmunizationDate,
                    ImmunizationRefusedDate = vm.ImmunizationRefusedDate,
                    Name = vm.Name,
                    Notes = vm.Notes,
                    Manufacturer = vm.Manufacturer,
                    LotNumber = vm.LotNumber,
                    Route = vm.Route,
                    Site = vm.Site,
                    Dose = vm.Dose,
                    DoseUnit = vm.DoseUnit,
                    Instructions = vm.Instructions,
                    NextDate = vm.NextDate,
                    NotRemind = vm.NotRemind,
                    ReasonNextDate = vm.ReasonNextDate,
                    CodingVocabulary = vm.CodingVocabulary,
                    ImmunizationCode = vm.ImmunizationCode,
                    ReasonForDel = vm.ReasonForDel,
                    SubmitDate = DateTime.Now,
                    isactive = vm.isactive,
                    Setid = vm.Setid,
                    Colonoscopy = vm.Colonoscopy,
                    VP_CPP_ImmunizationTypeId = vm.VP_CPP_ImmunizationTypeId,
                    VP_CPP_ImmunizationStatusId = vm.VP_CPP_ImmunizationStatusId
                });
                
                context.SaveChanges(modifyingUserId,ipAddress);

                //add new status
                context.ImmunizationRecall.Add(new ImmunizationRecall()
                {
                    VP_CPP_Immunization_ID = newImmunization.Entity.Id,
                    VP_CPP_ImmunizationStatusId = (int)ImmunizationStatus.COMPLETED,
                    DateCreated = DateTime.Now,
                    Active = true
                });
                
                context.SaveChanges(modifyingUserId,ipAddress);
            }
            catch (Exception)
            {
                succeeded = false;
            }

            return succeeded;

        }
        public bool EditImmunization(VMImmunizationPatientRecord vm, int modifyingUserId, string ipAddress)
        {
            bool succeeded = true;
            try
            {
                var entry = context.VP_CPP_Immunization.Where(i => i.Id == vm.Id).FirstOrDefault();
                if (entry != null)
                {
                    entry.PatientRecordId = vm.PatientId;
                    entry.PhysicianId = vm.PhysicianId;
                    entry.AdministeredBy = vm.AdministeredBy;
                    entry.Table_Of_Doctors_Names = vm.Table_Of_Doctors_Names;
                    entry.ImmunizationDate = vm.ImmunizationDate;
                    entry.ImmunizationRefusedDate = vm.ImmunizationRefusedDate;
                    entry.Name = vm.Name;
                    entry.Notes = vm.Notes;
                    entry.Manufacturer = vm.Manufacturer;
                    entry.LotNumber = vm.LotNumber;
                    entry.Route = vm.Route;
                    entry.Site = vm.Site;
                    entry.Dose = vm.Dose;
                    entry.DoseUnit = vm.DoseUnit;
                    entry.Instructions = vm.Instructions;
                    entry.NextDate = vm.NextDate;
                    entry.NotRemind = vm.NotRemind;
                    entry.ReasonNextDate = vm.ReasonNextDate;
                    entry.CodingVocabulary = vm.CodingVocabulary;
                    entry.ImmunizationCode = vm.ImmunizationCode;
                    entry.ReasonForDel = vm.ReasonForDel;
                    entry.SubmitDate = vm.SubmitDate;
                    entry.isactive = vm.isactive;
                    entry.Colonoscopy = vm.Colonoscopy;
                    entry.VP_CPP_ImmunizationStatusId = vm.VP_CPP_ImmunizationStatusId;
                }
                context.SaveChanges(modifyingUserId, ipAddress);
            }
            catch (Exception)
            {
                succeeded = false;
            }

            return succeeded;
        }
        public void UpdateTestStatus(int appointmenttestID, int statusID, int userId, string ipaddress)
        {
            var appTest = context.AppointmentTests.Where(x => x.Id == appointmenttestID).FirstOrDefault();
            if (appTest.AppointmentTestStatusId <= statusID)
            {
                appTest.AppointmentTestStatusId = statusID;
                context.SaveChanges(userId, ipaddress);
            }
        }
        public void AssignTrainee(int appointmentTestID, int traineeID, int modifyingUserId, string ipAddress)
        {
            AppointmentTestResource appTestRes=context.AppointmentTestResources.Where(r => r.AppointmentTestId == appointmentTestID).FirstOrDefault();
            if (appTestRes != null)
            {
                appTestRes.performedByUserId = traineeID;
                context.SaveChanges(modifyingUserId,ipAddress);
            }
        }
        public VMUserDoctor GetTraineeUser(int appointmenttestid)
        {
            VMUserDoctor user = new VMUserDoctor();
            var entry = context.AppointmentTestResources.Where(r => r.AppointmentTestId == appointmenttestid).FirstOrDefault();
            if (entry != null)
            {
                var userID = entry.performedByUserId.HasValue ? entry.performedByUserId.Value : 0;
                if (userID > 0)
                {
                    var doc = (from u in context.Users
                               join p in context.PracticeDoctors on u.Id equals p.ApplicationUserId
                               where u.UserID == userID
                               select new { PracticeDoctorId = p.Id, u.Id, u.UserID, p.ExternalDoctorId, u.LastName, u.FirstName }).FirstOrDefault();
                    if (doc != null)
                    {
                        user = new VMUserDoctor { ExternalDoctorId = doc.ExternalDoctorId, PracticeDoctorId = doc.PracticeDoctorId, Id = doc.Id, userId = doc.UserID, LastName = doc.LastName, FirstName = doc.FirstName };
                    }

                }
            }
            return user;
        }
        public List<VP_Letter> GetLetterList(int patientID, DateTime start, DateTime end)
        {

            return (from a in context.Appointments
                    join p in context.PatientRecords on a.PatientRecordId equals p.Id
                    join at in context.AppointmentTests on a.Id equals at.AppointmentId
                    join t in context.Tests on at.TestId equals t.Id
                    where
                    a.appointmentTime >= start && a.appointmentTime <= end &&
                    p.Id == patientID &&
                    at.AppointmentTestStatusId >= (int)AppointmentTestStatuses.ReadyForDoctor
                    select new VP_Letter()
                    {
                        AppointmentID = a.Id,
                        TestID = at.TestId,
                        AppointmentTime = a.appointmentTime,
                        TestName = t.testShortName,
                        TestTime = at.startTime
                    }).ToList();

        }

        public List<VMLoinc> TestResultsByLOINC(int patientID, string dateFrom, string dateTo)
        {
            DateTime dtFrom = DateTime.MinValue;
            DateTime dtTo = DateTime.MaxValue;
            DateTime dummyDt;
            if (DateTime.TryParse(dateFrom, out dummyDt))
            {
                dtFrom = dummyDt;
            }
            if (DateTime.TryParse(dateTo, out dummyDt))
            {
                dtTo = dummyDt;
            }
            return (from p in context.HL7Patients
                    join r in context.HL7Reports on p.Id equals r.HL7PatientId
                    join v in context.HL7ReportVersions on r.Id equals v.HL7ReportId
                    join res in context.HL7Results on v.Id equals res.HL7ReportVersionId
                    join c in context.HL7Codings on res.testCodeIdentifier.Trim().ToLower() equals c.labCode.Trim().ToLower()
                    where
                    p.PatientRecordId == patientID
                    &&
                    (r.collectionDateTime.HasValue && r.collectionDateTime.Value.Date >= dtFrom.Date && r.collectionDateTime.Value.Date <= dtTo.Date)

                    select new VMLoinc()
                    {
                        Code = c.LOINC,
                        Value = res.testResult,
                        CollectionDate = r.collectionDateTime
                    }).ToList();
        }
        public List<VMTemplateLog> MergeLOINC(List<VMLoinc> lstLoinc,
                                            List<VMTemplatePatientDataItemHeader> lstHeader,
                                            List<VMTemplateLog> lstLog)
        {

            List<VMTemplateLog> newLogLst = new List<VMTemplateLog>();
            DateTime? CollectionDate = new DateTime?();

            if (lstLoinc.Count > 0)
            {
                var hl7grp = from h in lstLoinc
                             group h by h.CollectionDate into g
                             select new { g.Key, Other = g };
                int logCount = 0;
                foreach (var item in hl7grp)
                {
                    var templog = new VMTemplateLog();
                    CollectionDate = item.Key;
                    var logItem = new VMTemplateLog() { DateEntered = CollectionDate, Date = CollectionDate.Value, LogID = ++logCount };

                    foreach (var h in lstHeader)
                    {
                        var meas = item.Other.Where(x => x.Code == h.TestCode)
                                                   .ToList().FirstOrDefault();
                        if (meas != null)
                        {
                            var outNH = h.IsText ? false : GetFlag(meas.Value, h.NL, h.NH);
                            var outTH = h.IsText ? false : GetFlag(meas.Value, h.TL, h.TH);
                            var data = new VMTemplatePatientDataItem()
                            {
                                VPTemplateFieldId = h.VPTemplateFieldId,
                                VPTemplateFieldName = h.VPTemplateFieldName,
                                HeaderVPTemplateFieldId = h.VPTemplateFieldId,
                                Value = meas.Value,
                                OutSideNormal = outNH,
                                OutSideTarget = outTH,
                                OverDue = GetOverDue(CollectionDate, DateTime.Now, h.Frequency),
                                LogID = logItem.LogID
                            };
                            logItem.Items.Add(data);
                        }
                        else
                        {
                            var data = new VMTemplatePatientDataItem()
                            {
                                VPTemplateFieldId = h.VPTemplateFieldId,
                                VPTemplateFieldName = "",
                                HeaderVPTemplateFieldId = h.VPTemplateFieldId,
                                Value = "",
                                LogID = logItem.LogID
                            };
                            logItem.Items.Add(data);
                        }
                    }
                    if (logItem.Items.Count > 0)
                        newLogLst.Add(logItem);
                }
                if (newLogLst != null && newLogLst.Count > 0)
                    lstLog.AddRange(newLogLst);

                lstLog = lstLog.OrderByDescending(x => x.Date).ToList();
            }
            return lstLog;
        }
      
        public List<VMLoinc> AssignMeasurement(List<VMLoinc> lst)
        {
            lst.ForEach(l =>
            {
                var meas = context.Measurement.Where(m => m.measurementCode == l.Code).FirstOrDefault();
                if (meas != null)
                {
                    l.MeasurementID = meas.Id;
                }
            });

            return lst;
        }
       
        public LocalVP.VMCDFPatientMedication CDFPatientMedications(int patientID, DateTime dtFrom, DateTime dtTo)
        {
            var meds = this.GetMedications(patientID, dtFrom, dtTo).Where(m => m.IsDiscontinued == false).ToList();
            var cdftemplate = this.CDFTemplateTherapeuticClasses();
            return new LocalVP.VMCDFPatientMedication { CDFTemplateMedications = cdftemplate, Medications = meds };
        }
        public List<VMPatientMedication> GetMedications(int patientID, DateTime dtFrom, DateTime dtTo)
        {
            PatientMedicationRepository patRepo = new PatientMedicationRepository();

            return patRepo.GetPatientMedicationsInDateRange(patientID, dtFrom, dtTo);

        }
        public List<Cerebrum.ViewModels.VP.VMVisitMeasurement> GetMeasurements()
        {
            var meas = context.VPUniqueMeasurement.ToList();
            var cats = context.VPCategory.ToList();
            var lst = (from m in meas
                       orderby m.Name
                       select new Cerebrum.ViewModels.VP.VMVisitMeasurement()
                       {
                           Id = m.Id,
                           Name = m.Name,
                           LOINC = m.Testcode,
                           Gender = m.Gender
                       }).ToList();

            return lst;

        }        
        public int GetVPTestID()
        {
            return context.Tests.Where(t => t.testShortName == "VP").FirstOrDefault().Id;
        }
        public List<VMTemplateLog> ApplyFilters(List<VMTemplateLog> lst, bool OutSideTarget, bool OutSideNormal, bool OutSideInterval, VMTemplatePatientData vm)
        {

            List<int> idsToInclude = new List<int>();

            if (OutSideTarget == false && OutSideNormal == false && OutSideInterval == false)
                return lst;

            foreach (var l in lst)
            {

                foreach (var i in l.Items)
                {
                    if (OutSideNormal)
                    {
                        if (i.OutSideNormal)
                        {
                            if (!idsToInclude.Contains(l.ID))
                            {
                                idsToInclude.Add(l.ID);
                                break;
                            }
                        }
                        //else
                        //{
                        //    i.Value = string.Empty;
                        //}
                    }
                    //else
                    //{
                    //    idsToInclude.Add(l.ID);
                    //}

                    if (OutSideTarget)
                    {
                        if (i.OutSideTarget)
                        {
                            if (!idsToInclude.Contains(l.ID))
                            {
                                idsToInclude.Add(l.ID);
                                break;
                            }
                        }
                        //else
                        //{
                        //    i.Value = string.Empty;
                        //}
                    }
                    //else
                    //{
                    //    idsToInclude.Add(l.ID);
                    //}


                    if (OutSideInterval)
                    {
                        if (i.OverDue)
                        {
                            if (!idsToInclude.Contains(l.ID))
                            {
                                idsToInclude.Add(l.ID);
                                break;
                            }
                        }
                        //else
                        //{
                        //    i.Value = string.Empty;
                        //}
                    }
                }
            };

            lst = lst.Where(x => idsToInclude.Contains(x.ID)).ToList();

            return lst;

        }
        public List<VMTemplatePatientDataItemHeader> ApplyOutOfDateFilter(List<VMTemplatePatientDataItemHeader> lstHeader, List<VMTemplateLog> lstLog)
        {

            DateTime? minLog = lstLog.Min(x => x.Date);
            DateTime? maxLog = lstLog.Max(x => x.Date);

            var lastLog = lstLog.Where(l => l.Date == maxLog.Value).ToList().FirstOrDefault();

            if (lastLog != null)
            {
                lstHeader.ForEach(c =>
                {
                    //if (c.VPTemplateFieldName == "eGFR")
                    //{
                    //    string s = "";
                    //}
                    VMTemplatePatientDataItem lastNonEmpyMeas = new VMTemplatePatientDataItem();

                    //finding the last non empty value 
                    foreach (var l in lstLog.OrderByDescending(x => x.Date).ToList())
                    {
                        //lstLog.OrderByDescending(x => x.Date).ToList().ForEach(l =>
                        //{
                        var entry = l.Items
                                .Where(i =>
                                        i.VPTemplateFieldId == c.VPTemplateFieldId &&
                                       !string.IsNullOrEmpty(i.Value)).FirstOrDefault();

                        if (entry != null)
                        {

                            lastNonEmpyMeas = entry;
                            if (lastNonEmpyMeas != null && !string.IsNullOrEmpty(lastNonEmpyMeas.Value) && lastNonEmpyMeas.LogDate.HasValue && lastNonEmpyMeas.Frequency > 0)
                            {
                                if ((DateTime.Now - lastNonEmpyMeas.LogDate).Value.TotalDays > lastNonEmpyMeas.Frequency)
                                {
                                    c.OverDue = true;
                                }
                                else
                                {
                                    c.OverDue = false;
                                }
                                break;
                            }
                        }
                    }

                });
            }

            return lstHeader;
        }
        public int PatientCPPFilledCount(int doctorID, DateTime dtFrom, DateTime dtTo)
        {
            List<int> retVal = new List<int>();

            #region old
            //int count = (from a in context.VP_CPP_Problem_List
            //          join b in context.Demographics on a.PatientRecordId equals b.PatientRecordId
            //          join c in context.DemographicsMainResponsiblePhysicians on b.Id equals c.DemographicId
            //          where a.IsProblemList && c.PracticeDoctorId == doctorID && a.AddDate > dtFrom && a.AddDate < dtTo
            //             select a.ParentId).Distinct().Count(); 
            #endregion

            var count = (from a in context.VP_CPP_Problem_List
                      join b in context.Demographics on a.PatientRecordId equals b.PatientRecordId
                      join c in context.DemographicsMainResponsiblePhysicians on b.Id equals c.DemographicId
                      where a.IsProblemList && c.PracticeDoctorId == doctorID && a.AddDate > dtFrom && a.AddDate < dtTo
                      select a.ParentId).Distinct().Count();




            #region old
            // var patientLst =
            //     context.DemographicsMainResponsiblePhysicians
            //     .Where(x => x.PracticeDoctorId == doctorID)
            //     .Select(x => x.DemographicId)
            //     .ToList();
            // //add submitted dates and userid to all cpp elements
            //patientLst.ForEach(p =>
            // {
            //     bool hasCpp = false;

            //     hasCpp = this.GetCPPAlerts(p, dtFrom, dtTo).Count > 0;

            //     if (!hasCpp)
            //         hasCpp = this.GetRiskFactors(p, dtFrom, dtTo).Count > 0;
            //     if (!hasCpp)
            //         hasCpp = this.GetFamilyHistory(p, dtFrom, dtTo).Count > 0;
            //     if (!hasCpp)
            //         hasCpp = this.GetCPP_ProblemList(p, true, dtFrom, dtTo).Count > 0;
            //     if (!hasCpp)
            //         hasCpp = this.GetCPP_ProblemList(p, false, dtFrom, dtTo).Count > 0;

            //     if (hasCpp)
            //         retVal.Add(p);
            // }); 
            #endregion

            //return retVal.Count;

            return count;

        }
        public int GetMainDoctor(int appointmentID)
        {
            int retVal = 0;

            //var docLst = GetContactList(appointmentID);
            //var mainDoc = docLst.Where(e => e.DocType == DocType.Main).ToList().FirstOrDefault();

            var appt = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (appt != null)
            {
                var mainDoc = (from e in context.ExternalDoctors
                               join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                               where p.Id == appt.PracticeDoctorId
                               select e).FirstOrDefault();

                if (mainDoc != null)
                {
                    retVal = mainDoc.Id;
                }
            }

            return retVal;
        }
        public int GetMainDoctor(Appointment appt)
        {
            int retVal = 0;

            if (appt != null)
            {
                var mainDoc = (from e in context.ExternalDoctors
                               join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                               where p.Id == appt.PracticeDoctorId
                               select e).AsNoTracking().FirstOrDefault();

                if (mainDoc != null)
                {
                    retVal = mainDoc.Id;
                }
            }

            return retVal;
        }
        public ExternalDoctor GetAppointmentDoctor(int appointmentID)
        {
            ExternalDoctor appDoc = null;

            var appt = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (appt != null)
            {
                appDoc = (from e in context.ExternalDoctors
                          join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                          where p.Id == appt.PracticeDoctorId
                          select e).FirstOrDefault();
            }

            return appDoc;
        }
        public int GetPracticeDoctor(int appointmentID)
        {
            int retVal = 0;

            //var docLst = GetContactList(appointmentID);
            //var mainDoc = docLst.Where(e => e.DocType == DocType.Main).ToList().FirstOrDefault();

            var appt = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (appt != null)
            {
                var mainDoc = (from e in context.ExternalDoctors
                               join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                               where p.Id == appt.PracticeDoctorId
                               select p).FirstOrDefault();

                if (mainDoc != null)
                {
                    retVal = mainDoc.Id;
                }
            }

            return retVal;
        }
        public int GetPracticeDoctor(Appointment appt)
        {
            int retVal = 0;
            if (appt != null)
            {
                var mainDoc = (from e in context.ExternalDoctors
                               join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                               where p.Id == appt.PracticeDoctorId
                               select p).AsNoTracking().FirstOrDefault();

                if (mainDoc != null)
                {
                    retVal = mainDoc.Id;
                }
            }

            return retVal;
        }
        public Appointment GetPatientLatestAppointment(int practiceId, int patientRecordId)
        {
            var app = (from a in context.Appointments
                       join p in context.PatientRecords on a.PatientRecordId equals p.Id
                       where p.PracticeId == practiceId && p.Id == patientRecordId
                       select a).OrderByDescending(o => o.appointmentTime).FirstOrDefault();
            return app;
        }
        public Appointment GetAppointment(int appointmentID)
        {
            return context.Appointments.Where(a => a.Id == appointmentID).AsNoTracking().FirstOrDefault();
        }
        public DateTime? GetAppointmentDate(int appointmentID)
        {
            DateTime? dt = null;
            try
            {
                dt = context.Appointments.FirstOrDefault(a => a.Id == appointmentID).appointmentTime;
            }
            catch
            {

            }
            return dt;
        }
        public List<VMDoctorReport> GetAssociatedContactList(int patientID)
        {

            return (from doc in context.DemographicsAssociatedDoctors
                    join d in context.Demographics on doc.DemographicId equals d.Id
                    join e in context.ExternalDoctors on doc.ExternalDoctorId equals e.Id
                    where
                       d.PatientRecordId == patientID &&
                       doc.IsCC == true
                    select new VMDoctorReport
                    {
                        ID = e.Id,
                        Email = e.email,
                        Fax = e.fax,
                        HRM = e.HRM,
                        Mail = e.mail,
                        Name = e.lastName + " " + e.firstName,
                        LastName = e.lastName,
                        FirstName = e.firstName,
                        EmailAddress = e.emailAddress,
                        DocType = DocType.CC

                    }).ToList();
        }
        public List<SelectListItem> GetCPPProblemStatus()
        {
            List<SelectListItem> items = context.VP_CPP_Problem_Status.Select(a => new SelectListItem() { Value = a.Id.ToString(), Text = a.Text }).ToList(); ;
            return items;
        }
        public int GetProblemStatusId(string problemStatus)
        {
            var cppProblemStatus = context.VP_CPP_Problem_Status.ToList().Where(a => a.Text.ToLower() == problemStatus.ToLower()).FirstOrDefault();
            if (cppProblemStatus == null)
                return 0;

            return cppProblemStatus.Id;
        }
        // TODO: Moved to VPBLL. Delete!
        public void InsertCDSImportImmunization(VMCDSImportImmunization import)
        {
            var userId = import.UserId;
            var ipAddress = import.IpAddress;
            var hasDate = false;
            var patientId = import.PatientId;

            try
            {
                VP_CPP_Immunization immun = new VP_CPP_Immunization();


                DateTime? date = import.ImmunizationDate;

                immun.PatientRecordId = patientId;
                immun.PhysicianId = import.PhysicianId;
                immun.Name = import.ImmunizationName;
                immun.VP_CPP_ImmunizationTypeId = import.ImmunizationTypeId;
                immun.Manufacturer = !String.IsNullOrEmpty(import.Manufacturer) ? import.Manufacturer : null;
                immun.LotNumber = !String.IsNullOrEmpty(import.LotNumber) ? import.LotNumber : null;
                immun.Route = !String.IsNullOrEmpty(import.Route) ? import.Route : null;
                immun.Site = !String.IsNullOrEmpty(import.Site) ? import.Site : null;
                immun.Dose = !String.IsNullOrEmpty(import.Dose) ? import.Dose : null;
                immun.ImmunizationCode = !String.IsNullOrEmpty(import.ImmunizationCode) ? import.ImmunizationCode : null;
                immun.ImmunizationSystem = !String.IsNullOrEmpty(import.ImmunizationSystem) ? import.ImmunizationSystem : null;
                immun.refuse = import.Refuse;
                immun.Instructions = !String.IsNullOrEmpty(import.Instructions) ? import.Instructions : null;
                immun.Notes = !String.IsNullOrEmpty(import.Notes) ? import.Notes : null;
                immun.ResidualData = !String.IsNullOrEmpty(import.ResidualData) ? import.ResidualData : null;

                immun.PhysicianId = import.PhysicianId;

                immun.ImmunizationYear = import.ImmunizationYear;
                immun.ImmunizationMonth = import.ImmunizationMonth;
                immun.ImmunizationDay = import.ImmunizationDay;

                immun.isactive = true;
                immun.IsImported = true;

                if (date != null)
                {
                    hasDate = true;
                    immun.ImmunizationDate = date;
                    immun.SubmitDate = date;
                }

                if (import.Refuse)
                {
                    if (hasDate)
                    {
                        immun.ImmunizationRefusedDate = date;
                    }
                }


                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        context.VP_CPP_Immunization.Add(immun);
                        context.SaveChanges(userId, ipAddress);

                        var immRecall = new ImmunizationRecall();
                        immRecall.VP_CPP_Immunization_ID = immun.Id;
                        immRecall.VP_CPP_ImmunizationStatusId = immun.refuse ? (int)ImmunizationStatus.REFUSED : (int)ImmunizationStatus.COMPLETED;
                        immRecall.DateCreated = date;
                        immRecall.DateServicedDay = immun.ImmunizationDay;
                        immRecall.DateServicedMonth = immun.ImmunizationMonth;
                        immRecall.DateServicedYear = immun.ImmunizationYear;
                        immRecall.Active = true;

                        context.ImmunizationRecall.Add(immRecall);
                        context.SaveChanges(userId, ipAddress);

                        transaction.Commit();

                        import.Id = immun.Id;
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        _log.Error($"{ex.Message} {ex.Source}");
                        import.Errors.Add("An errror occurred while saving Immunization to database for patient " + patientId);
                    }

                }
            }
            catch (Exception ex)
            {
                _log.Error($"{ex.Message} {ex.Source}");
                import.Errors.Add("An errror occurred while saving Immunization to database for patient " + patientId);
            }
        }

        public List<VMRecallLetter> GetRecallLetters()
        {
            var letters = new List<VMRecallLetter>();

            letters.Add(new VMRecallLetter() { Code = 1001, TestName = "Mammogram", Description = "Mammogram (1st letter)", ViewName = "_mammogram_1" });
            letters.Add(new VMRecallLetter() { Code = 1002, TestName = "Mammogram", Description = "Mammogram (2nd letter)", ViewName = "_mammogram_2" });
            letters.Add(new VMRecallLetter() { Code = 1003, TestName = "Pap Smear", Description = "Pap (1st letter)", ViewName = "_pap_1" });
            letters.Add(new VMRecallLetter() { Code = 1004, TestName = "Pap Smear", Description = "Pap (2nd letter)", ViewName = "_pap_2" });
            letters.Add(new VMRecallLetter() { Code = 1005, TestName = "FOBT", Description = "FOBT (1st Letter)", ViewName = "_fobt_1" });
            letters.Add(new VMRecallLetter() { Code = 1006, TestName = "FOBT", Description = "FOBT (2nd Letter)", ViewName = "_fobt_2" });
            letters.Add(new VMRecallLetter() { Code = 1007, TestName = "Flu shot", Description = "Flu shot (1st letter)", ViewName = "_flushot_1" });
            letters.Add(new VMRecallLetter() { Code = 1008, TestName = "Flu shot", Description = "Flu shot (2nd letter)", ViewName = "_flushot_2" });
            letters.Add(new VMRecallLetter() { Code = 1009, TestName = "Childhood Immunization(s)", Description = "Childhood Immunizations (1st letter)", ViewName = "_child_imm_1" });
            letters.Add(new VMRecallLetter() { Code = 1010, TestName = "Childhood Immunization(s)", Description = "Childhood Immunizations (2nd letter)", ViewName = "_child_imm_2" });

            return letters;
        }

        public VMRecallLettersPrintView GetRecallLetterPrintView(VMRecallLetterRequest request)
        {
            var printView = new VMRecallLettersPrintView();
            var view = GetRecallLetters().Where(c => c.Code == request.LetterCode).FirstOrDefault();

            printView.RecallViewName = view.ViewName;
            printView.ProcedureType = request.ProcedureTypeName;
            printView.ProcedureTypeId = request.ProcedureTypeId;
            printView.PatientsRecallLetters = GetpatientsRecallLetters(request);

            return printView;
        }

        public List<VMPatientRecallLetter> GetpatientsRecallLetters(VMRecallLetterRequest request)
        {
            var patientsLetters = new List<VMPatientRecallLetter>();
            var currentDate = System.DateTime.Now;
            var doctor = GetRecallLetterDoctor(request.DoctorId);
            var letterType = GetRecallLetters().Where(c => c.Code == request.LetterCode).FirstOrDefault();
            var patientIds = request.Patients.Select(p => p.PatientId).ToList();

            var demographics = context.Demographics
                .Include("PatientRecord")
                .Include("addresses")
                .Include("phoneNumbers")
                .Include("healthcards")
                .Where(p => patientIds.Contains(p.PatientRecordId) && p.active == Active.Active).ToList();


            foreach (var dbDemo in demographics)
            {

                var address = dbDemo.addresses.FirstOrDefault();
                var phoneNumber = dbDemo.phoneNumbers.FirstOrDefault();
                var healthCard = dbDemo.healthcards.FirstOrDefault();
                var patient = new VMPatient()
                {
                    PatientId = dbDemo.PatientRecordId,
                    Salutation = dbDemo.namePrefix.ToString(), // need to fix this better
                    FirstName = dbDemo.firstName,
                    LastName = dbDemo.lastName,
                    FullName = dbDemo.lastName.ToUpper() + ", " + dbDemo.firstName,
                    DOB = dbDemo.dateOfBirth != null ? dbDemo.dateOfBirth.Value.ToShortDateString() : "",
                    Address = address != null ? address.addressLine1 : "",
                    City = address != null ? address.city : "",
                    Phone = phoneNumber != null ? phoneNumber.phoneNumber : "",
                    Ohip = healthCard != null ? healthCard.number : ""
                };

                patient.Salutation = patient.Salutation != "_" ? patient.Salutation : "";

                var patientLetter = new VMPatientRecallLetter();
                patientLetter.CurrentDate = currentDate;
                patientLetter.Doctor = doctor;
                patientLetter.Patient = patient;
                patientLetter.LetterCode = request.LetterCode;
                patientLetter.LetterName = request.LetterName;
                patientLetter.TestName = letterType.TestName;
                patientLetter.ProcedureTypeId = request.ProcedureTypeId;
                patientLetter.ProcedureTypeName = request.ProcedureTypeName;

                var requestPatient = request.Patients.Where(p => p.PatientId == dbDemo.PatientRecordId).FirstOrDefault();
                if (requestPatient != null)
                {
                    patientLetter.DateLastServiced = requestPatient.DateLastServiced;
                }


                patientsLetters.Add(patientLetter);
            }
            return patientsLetters;
        }

        public VMDoctor GetRecallLetterDoctor(int practiceDoctorId)
        {
            var doc = new VMDoctor();

            var extDoc = (from e in context.ExternalDoctors
                          join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                          where p.Id == practiceDoctorId
                          select new { e, p }).FirstOrDefault();

            if (extDoc != null)
            {

                var officeId = 0;
                officeId = extDoc.p.mainOfficeId > 0 ? extDoc.p.mainOfficeId
                    : context.Offices.First(o => o.PracticeId == extDoc.p.PracticeId).Id;

                var office = context.Offices.Find(officeId);
                var rep = new RepositoryForTemplates();
                OfficeOutlook outlook = context.OfficeOutlooks.
                                    Where(t => t.OfficeId == office.Id).FirstOrDefault();
                if (outlook != null)
                {
                    doc.OfficeLogoImageL = outlook.leftLogo;
                    doc.OfficeLogoImageR = outlook.rightLogo;
                    doc.OfficeLogoImageM = outlook.middleLogo;

                    if (rep.IsValidImage(outlook.leftLogo))
                    {
                        doc.OfficeLogoImageL = outlook.leftLogo;
                    }

                    if (rep.IsValidImage(outlook.rightLogo))
                    {
                        doc.OfficeLogoImageR = outlook.rightLogo;
                    }

                    if (rep.IsValidImage(outlook.middleLogo))
                    {
                        doc.OfficeLogoImageM = outlook.middleLogo;
                    }
                }

                byte[] signImg = rep.GetDoctorsSignature(extDoc.e.Id, extDoc.p.PracticeId);

                if (rep.IsValidImage(signImg))
                {
                    doc.Signature = signImg;
                }


                doc.DoctorId = extDoc.e.Id;
                doc.FullName = extDoc.e.FullName;
                doc.FirstName = extDoc.e.firstName;
                doc.LastName = extDoc.e.lastName;
                doc.PracticeDoctorId = extDoc.p.Id;
                doc.PracticeId = extDoc.p.PracticeId;
                doc.CPSO = extDoc.e.CPSO;
                doc.OfficeName = office.businessName;

                var addressStr = "";
                var phoneStr = "";
                var fax = "";

                addressStr += office.address1 + ", ";
                addressStr += !String.IsNullOrWhiteSpace(office.address2) ? office.address2 + ", " : "";
                addressStr += !String.IsNullOrWhiteSpace(office.city) ? office.city + ", " : "";
                addressStr += !String.IsNullOrWhiteSpace(office.postalCode) ? office.postalCode + " " : "";
                addressStr += GetOfficeProvinceName(office.province) + " ";
                addressStr += !String.IsNullOrWhiteSpace(office.country) ? office.country + " " : "";

                fax = office.fax;
                phoneStr += office.phone;

                doc.Address = addressStr;
                doc.Phone = phoneStr;
                doc.Fax = fax;


                //if (extDoc.p.mainOfficeId > 0)
                //{
                //    var office = context.Offices.Find(extDoc.p.mainOfficeId);

                //    addressStr += office.address1 + ", ";
                //    addressStr += !String.IsNullOrWhiteSpace(office.address2) ? office.address2 + ", " : "";
                //    addressStr += !String.IsNullOrWhiteSpace(office.city) ? office.city + ", " : "";
                //    addressStr += !String.IsNullOrWhiteSpace(office.postalCode) ? office.postalCode + " " : "";
                //    addressStr += !String.IsNullOrWhiteSpace(office.country) ? office.country + " " : "";

                //    fax = office.fax;
                //    phoneStr += office.phone;
                //}
                //else
                //{

                //    var address = extDoc.e.externalDoctorAddresses.LastOrDefault();
                //    var phone = extDoc.e.phoneNumbers.LastOrDefault();


                //    if (address != null)
                //    {
                //        addressStr += address.addressLine1 + ", ";
                //        addressStr += !String.IsNullOrWhiteSpace(address.addressLine2) ? address.addressLine2 + ", " : "";
                //        addressStr += !String.IsNullOrWhiteSpace(address.city) ? address.city + ", " : "";
                //        addressStr += !String.IsNullOrWhiteSpace(address.postalCode) ? address.postalCode + " " : "";
                //        addressStr += !String.IsNullOrWhiteSpace(address.country) ? address.country + " " : "";

                //        fax = address.faxNumber;
                //    }

                //    if (phone != null)
                //    {
                //        phoneStr += phone.phoneNumber;
                //    }

                //}

                //doc.Address = addressStr;
                //doc.Phone = phoneStr;
                //doc.Fax = fax;

            }


            return doc;
        }

        private string GetOfficeProvinceName(int province)
        {
            string name = "";
            try
            {
                //name = Enum.GetName(typeof(Province), province);
                name = Cerebrum.BLL.Common.EnumExtensions.GetDisplayName((Province)province);
            }
            catch (Exception ex)
            {
                _log.Error(ex);
            }

            return name;
        }

        private string GetCPPString(bool isVisible, string data)
        {
            if (!isVisible || string.IsNullOrEmpty(data) || string.IsNullOrEmpty(data.Trim()))
                return string.Empty;

            return data.Trim();
        }

        private string GetCPPString(bool isVisible, string data, string seperator)
        {
            string result = GetCPPString(isVisible, data);
            if (string.IsNullOrEmpty(result))
                return string.Empty;

            return seperator + result;
        }

        private string GetCPPString(bool isVisible, int cppDay, int cppMonth, int cppYear, string seperator)
        {
            if (!isVisible || (cppDay == 0 && cppMonth == 0 && cppYear == 0))
                return string.Empty;

            StringBuilder result = new StringBuilder();
            if (cppMonth != 0)
                result.Append(cppMonth.ToString());
            if (cppDay != 0)
                result.Append((string.IsNullOrEmpty(result.ToString()) ? string.Empty : "/") + cppDay.ToString());
            if (cppYear != 0)
                result.Append((string.IsNullOrEmpty(result.ToString()) ? string.Empty : "/") + cppYear.ToString());

            return seperator + result;
        }

        private string GetCPPString(bool isVisible, int cppYear, string unit, string lifeStage, string seperator)
        {
            if (!isVisible || (cppYear == 0 && string.IsNullOrEmpty(lifeStage)))
                return string.Empty;

            return seperator + (string.IsNullOrEmpty(lifeStage) ? string.Empty : lifeStage + " ") + (cppYear == 0 ? string.Empty : cppYear.ToString() + (unit == null ? string.Empty : " " + unit.ToLower()));
        }

        public string GetBuildDateFromPartial(int? day, int? month, int? year)
        {
            var dateStr = "";

            if (month != null)
            {
                dateStr += month + "/";
            }

            if (day != null)
            {
                dateStr += day + "/";
            }

            if (year != null)
            {
                dateStr += year;
            }

            return dateStr;
        }
    }
}