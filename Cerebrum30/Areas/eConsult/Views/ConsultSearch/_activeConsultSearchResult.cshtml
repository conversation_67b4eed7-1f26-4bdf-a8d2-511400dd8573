@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Econsult.VMConsultSearchResultPage
@{

}
@if (@Model.TotalRows > 0)
{
    foreach (var item in @Model.FoundConsults)
    {

        <tr data-id="@item.CaseId">
            <td class="text-nowrap">
                @if (item.PatientAssociatedFlag)
                {
                    <a href="javascript:void(null)" title="Click to view Patient Info" data-id="@item.CaseId" class="btn-view-patient">@item.CaseId</a>
                }
                else
                {
                    @item.CaseId

            </td>
            <td class="text-nowrap">
                @Html.Truncate(item.Title, 22, true, false)
            </td>
            <td class="text-nowrap">
                @patientName
            </td>
            @if (IsOtnSpecialist)
            {
                <td class="text-nowrap">
                    @item.RequesterName
                </td>
            }
            <td class="text-nowrap">
                @item.RecipientName
            </td>
            <td class="text-nowrap">
                @item.ConsultState
            </td>
            <td class="text-nowrap">
                @item.DateSubmitted.ToString("MMM dd, yyyy h:mm tt")
            </td>
            <td class="text-nowrap show-more-data">
                @item.DateLastUpdated.ToString("MMM dd, yyyy h:mm tt")
            </td>
            <td class="text-nowrap text-center show-more-data">
                @item.BillingCoverage
            </td>
            
            <td class="text-center">
                @if (item.SavedCompletedFlag)
                {
                    <input type="checkbox" onclick="return false;" checked readonly />
                }
            </td>
            <td class="text-center">
                @if (item.PatientNeedsToBeSeen)
                {
                    <input type="checkbox" onclick="return false;" checked readonly />
                }
            </td>
            <td class="text-center">
                @if (item.PatientAssociatedFlag)
                {
                    <input type="checkbox" onclick="return false;" checked readonly />
                }
            </td>
            @if (IsOtnSpecialist)
            {
                <td class="text-center">
                    @if (item.ConsultFlag != "NOFLAG" && item.RequesterName != Convert.ToString(ViewBag.OtnDoctorName))
                    {
                        <span class="glyphicon glyphicon-flag" style="color:@CustomHelpers.GetFlagColor(item.ConsultFlag);margin-left:10px;" data-toggle="tooltip" title="@item.ConsultFlag"></span>
                    }
                </td>

            <td class="text-center">
                @Html.ActionLink(linkText: "Edit", actionName: "LoadPatientConsult", controllerName: "Consult", routeValues: new { id = item.CaseId }, htmlAttributes: null)
            </td>
        </tr>

    <tr>
        <td colspan="14" style="padding-top:25px;">
            <input type="hidden" id="hdConsultFlag" value="@Model.Search.ConsultFlag" />
            <input type="hidden" id="hdConsultState" value="@Model.Search.ConsultState" />
            <input type="hidden" id="hdDateLastUpdated" value="@Model.Search.DateLastUpdated" />
            <input type="hidden" id="hdPatientAssociationFlag" value="@Model.Search.PatientAssociationFlag" />
            <input type="hidden" id="hdPatientLastName" value="@Model.Search.PatientLastName" />
            <input type="hidden" id="hdPatientNeedsToBeSeen" value="@Model.Search.PatientNeedsToBeSeen" />
            <input type="hidden" id="hdRecipientName" value="@Model.Search.RecipientName" />
            <input type="hidden" id="hdRequesterName" value="@Model.Search.RequesterName" />
            <input type="hidden" id="hdSavedCompletedFlag" value="@Model.Search.SavedCompletedFlag" />
            <input type="hidden" id="hdSortOrder" value="@Model.SortOrder" />
            <input type="hidden" id="hdPageSize" value="@Model.PageSize" />
            <input type="hidden" id="hdSortColumn" value="@Model.SortColumn" />

            <div class="panel panel-default">
                <div>

                </div>
                <div class="panel-body">
                    <div>
                        <ul class="pagination">

                            @for (int i = 1; i <= @Model.TotalPages; i++)
                            {

                                <li class="btn-search-consult-page @active" data-page-number="@i"><a href="javascript:void(null)">@i</a></li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </td>
    </tr>

else
{
    <tr>
        <td colspan="14">
            <div class="col-sm-12">
                <h4>There are no cases found according to search criteria</h4>
            </div>
        </td>
    </tr>

