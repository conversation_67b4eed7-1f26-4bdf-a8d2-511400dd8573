@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
@{

}
<tr>
    <td class="col-md-6 align-text-top" style="vertical-align:top;">
        @{
            foreach (var item in @Model.Notes)
            {

                {

                    {

                    }

                <div class="row" style="padding-bottom:10px;">
                    <div class="col-md-12 div-note">
                        <div class="col-md-8 pull-left note-heading">@message</div>
                        <div class="col-md-4 text-nowrap note-heading text-right">@item.Date.ToString("MMM dd, yyyy h:mm tt")</div>
                        @{
                            if (item.PatientNeedsToBeSeenAvailable)
                            {
                                if (item.PatientNeedsToBeSeen)
                                {
                                    <div class="col-md-12 text-danger note-needs-to-be-seen">Recommendation: patient should be seen (in person or by video)</div>
                                }
                                else
                                {
                                    <div class="col-md-12 note-needs-to-be-seen">Patient does not need to be seen (in person or by video)</div>

                        <div class="col-md-8 pull-left note-heading">@declineReason</div>
                        @{

                            {

                            }

                        <div class="col-md-12 note-item">@Html.Raw(note)</div>
                        @{
                            if (item.Attachments.Count > 0)
                            {
                                <div class="col-md-12 note-heading">File(s) attached:</div>
                            }
                            foreach (var file in item.Attachments)
                            {
@* j++; *@

                                <div class="col-md-12"><a herf="#" class="download-file c-pointer" data-file-id="@file.Id">@text</a>&nbsp;@Html.Truncate(title, 68, true, false)</div>
                            }

                    </div>
                </div>

    </td>
</tr>

