@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
@{

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Consultation Report</title>
    <link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />
 *@<style>
          #page-number:after {
/* content: "Page " counter(page); */
/* counter-increment: page; */
        }
          </style>
    @*<style>*@
              @@media print
          {
             @@page {
/* margin-top: 0; */
@* margin-bottom: 0; *@
             }
             body  {
@* padding-top: 0px; *@
@* padding-bottom: 32px ; *@

            #page-number:after  {
@* content: "Page " counter(page); *@
@* counter-increment: page; *@

        </style>
    <style>
        @@media print {
            .page {
@* page-break-after: always; *@
            }

                .page .pageEnd::after {
@* counter-increment: section; *@
                    /@* Increment the section counter*/*@
@* content: "Page " counter(section) " "; *@
                    /*  Display the counter  */
                }

    </style>
    <script>

        function addPageNumbers() {

@* alert("ok"); *@
 i - 10) + 'mm">' + i + '/' + count + '</div>'); }

    </script>
</head>

<body>
    <div class="page">
        <div class="page-header">
            <table style="width:100%">
                <tr>
                    <td style="text-align: left">*@<div>&nbsp;<strong>Patient Info</strong></div>* <div>&nbsp;</div>
                        <div>&nbsp;Name: <strong>@patientName</strong></div>
                        <div>&nbsp;DOB: <strong>@Model.Patient.BirthDate.ToString("MMM dd yyyy")</strong></div>
                        <div>&nbsp;Gender: <strong>@Model.Patient.Gender</strong></div>
                        <div>&nbsp;OHIP: <strong>@ohip</strong></div>
                    </td>
                    <td style="vertical-align:top;" nowrap>
                        <div style="text-align: right;padding-right:8px;">
@* &nbsp; *@
                            <button type="button" title="Click to Print" class="btn-primary" style="border-color:white" onClick="window.print()">
                                Print
                            </button>
                        </div>
                        <div style="text-align: right;padding-right:8px;">
                            Patient should be seen:&nbsp;<strong>@patientNeedsToBeSeen</strong>
                        </div>
                        <div style="text-align: right;padding-right:8px;">
                            Consult Flag:&nbsp;<strong>@Model.ConsultFlag</strong>
                        </div>
                        <div style="text-align: right;padding-right:8px;">
                            Case Id:&nbsp;<strong>@Model.CaseId</strong>
                        </div>
                        <div style="text-align: right;padding-right:8px;">
                            Report created: <strong>@printDate.ToString("MMM dd yyyy")</strong>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="page-footer">
            <table style="width:100%">
                <tr>
                    <td width="50%"><div>&nbsp;Print Date: @printDate.ToString("MMM dd, yyyy h:mm tt")</div></td>

                </tr>
            </table>
            <div class="pageEnd"></div>@* *@

        </div>
        <table>
            <thead>
                <tr>
                    <td>
                        <!--place holder for the fixed-position header-->
                        <div class="page-header-space"></div>
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div>
                            &nbsp;Referrer: <strong>@Model.RequesterName</strong>
                        </div>
                        <div>
                            &nbsp;Recipient: <strong>@Model.RecipientName</strong>
                        </div>
                        <div>
                            &nbsp;Subject: <strong>@Model.Title</strong>
                        </div>
                        <div class="page" style="padding:7px;">
                            @{
                                foreach (var item in notes)
                                {

                                    {

                                    }
                                    else
                                    {

                                        {

                                        }

                                    <div style="padding-bottom:10px;padding-left:10px;break-inside: avoid;">
                                        <div class="div-note" style="padding:5px;">
                                            <div class="note-heading-print" style="display:inline-block;">@message</div>
                                            <div class="note-heading-print" style="display:inline-block;">@item.Date.ToString("MMM dd, yyyy h:mm tt")</div>
                                            @if (item.PatientNeedsToBeSeen)
                                            {
                                                <div class="note-needs-to-be-seen" style="color:#a94442;">Recommendation: patient should be seen (in person or by video)</div>
                                            }
                                            <div class="note-heading-print">@declineReason</div>

                                            <div class="note-item">@note</div>
                                            @{
                                                if (item.Attachments.Count > 0)
                                                {
                                                    <div class="note-heading-print">@item.Attachments.Count File(s) attached</div>
                                                }
                                            }

                                        </div>
                                    </div>

                        </div>
                    </td>
                </tr>
            </tbody>

            <tfoot>
                <tr>
                    <td>
                        <!--place holder for the fixed-position footer-->
                        <div class="page-footer-space"></div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</body>
</html>
