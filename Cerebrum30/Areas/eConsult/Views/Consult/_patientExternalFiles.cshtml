@using Cerebrum.ViewModels.Patient
﻿@model  List<Cerebrum.ViewModels.Econsult.VMPatientClinicReport>
@{

}
<script src="~/Areas/eConsult/Scripts/patient-external-files.js"></script>

@Html.ModalHeader(header)
@if (!found)
{
    <div class="col-lg-12">
        <h4>There is no file for selected patient</h4>
    </div>
}
else
{
    <div class="col-lg-12" style="padding-bottom:5px;">
        <table class="table table-hover record_table">
            <thead>
                <tr>
                    <th class="text-center">#</th>
                    <th></th>
                    <th>File Name</th>
                    <th>Description</th>
                    <th>Received Date</th>
                </tr>
            </thead>
            @foreach (var item in Model)
            {

                {

                }

                {

                <tbody>
                    <tr>
                        <td align="center" @style>@count</td>
                        <td @style><input type="checkbox" name="chk" class="chk-file" data-file-report-received-id="@item.Id" 

                        <td @style>@Html.Truncate(item.FileName, 40, true, false)</td>
                        <td @style>@Html.Truncate(item.Description, 65, true, false)</td>
                        <td @style>@dt</td>
                    </tr>
                </tbody>

        </table>
    </div>

<div class="modal-footer">
    
    @if (found)
    {
        <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal" id="btnCloseClinicFile">Cancel</button>
        <button type="button" class="btn btn-info btn-sm pull-right" id="btnCloseAndSave" data-dismiss="modal">Close & Save</button>
    }
    else
    {
        <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Close</button>

</div>