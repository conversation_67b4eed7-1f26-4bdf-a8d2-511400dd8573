@Html.ModalHeader("Time Spent")

<div class="modal-body">

    <div class="row">
        <div class="form-group col-md-12" style="vertical-align: top;">
            <div class="row">
                <div class="col-md-9">
                    <span style="color:#a94442;">*</span> Time spent on consult:
                </div>
                <div class="col-md-3 pull-right">
                    @Html.DropDownList("TimeSpent", new SelectList(Model, "Value", "Text"), new { @class = "form-control" })
                </div>
            </div>
            <div class="row" style="margin-top:6px; display:none;" id="rowMinutesSpent">
                <div class="col-md-9">
                    <span style="color:#a94442;">*</span> Minutes spent:
                </div>
                <div class="col-md-3 pull-right">
                    <input type="number" id="TimeSpentMinutes" class="form-control" />
                </div>
            </div>
            <div class="row" style="margin-top:6px;">
                <div class="col-md-12"> 
                    <textarea class="form-control" id="time-spent-comments" rows="5" placeholder="Enter comments..." maxlength="400"></textarea>
                </div>
            </div>
            <div class="row" style="margin-top:6px;">
                <div class="col-md-12">
                    <span id="spanError" style="color:#a94442;"></span>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-info btn-sm" id="btnTimeSpentOk">OK</button>
    <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Cancel</button>
</div>