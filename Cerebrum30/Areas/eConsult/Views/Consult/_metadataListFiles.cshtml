﻿@model  List<Cerebrum.ViewModels.Econsult.Models.MetadataConsultNoteFile>
@{

}

<script src="~/Areas/eConsult/Scripts/metadata-list-files.js"></script>

@Html.ModalHeader(header)
@if (!found)
{
    <div class="col-lg-12">
        <h4>There is no file to save</h4>
    </div>
}
else
{
    <div class="col-lg-12" style="padding-bottom:5px;">
        <table class="table table-hover save-files-in-clinic-table">
            <thead>
                <tr>
                    <th class="text-center">#</th>
                    <th></th>
                    <th>File Name</th>
                    <th>File Description</th>
                </tr>
            </thead>
            @foreach (var item in Model)
            {

                {

                }

                <tbody>
                    <tr>
                        <td align="center" @style>@count</td>
                        <td @style>
                            <input type="checkbox" name="otnIds" class="chk-file" data-file-otn-id="@item.OtnId" />
                        </td>
                        <td @style>@Html.Truncate(item.FileName, 40, true, false)</td>
                        <td @style>@Html.Truncate(item.Title, 40, true, false)</td>
                    </tr>
                </tbody>

        </table>
    </div>

<div class="modal-footer">

    @if (found)
    {
        <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-info btn-sm pull-right" id="btnCloseAndSave" data-case-id="@ViewBag.CaseId">Close & Save</button>
    }
    else
    {
        <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Close</button>

</div>
