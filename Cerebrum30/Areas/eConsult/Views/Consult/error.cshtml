﻿@{

}
<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />
<style>
/@*.btn-primary {*@
/* color: #fff; */
/* background-color: #0495c9 !important; */
/* cursor: pointer; */
}
.btn-primary hover {
/* cursor: pointer; */
}*/
@* /*@input[type="file"] { *@ display: none; *@

.custom-file-upload {
@* display: inline-block; *@
@* padding: 6px 12px; *@
@* cursor: pointer; *@
}@*/*@
/*body {
@* background-color: #f8f8f8 !important; *@
}@*/*@
</style>
<h3>Error</h3>
@{

    {
        <h4 class="text-danger">@ViewBag.ErrMessage</h4>
    }
    else
    {
        <h4 class="text-danger">There was a problem. Please Contact Administrator!</h4>

        <a class='btn btn-primary btn-sm' href='javascript:;'>
            Choose File...
            <input type="file" multiple style='position:absolute;z-index:2;top:0;left:0;filter: alpha(opacity=0);-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";opacity:0;background-color:transparent;color:transparent;'>
        </a>
    </div>
    <br />
    <label for="file-upload" class="custom-file-upload">
        <a class="btn btn-primary btn-sm">Choose Files...</a> 
    </label>
    <input id="file-upload" multiple type="file" />

