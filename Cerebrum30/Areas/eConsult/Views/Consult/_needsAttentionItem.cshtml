@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
@{ 

}
<script src="~/Areas/eConsult/Scripts/upload-file-common.js"></script>
<form id="frm-needs-attention">
    <input type="hidden" id="hdCaseId" name="hdCaseId" value="@Model.CaseId" />
    <input type="hidden" id="hdValidFeedback" value="false" />
</form>
<input type="hidden" id="hdClinicFileIds" value="" />
<input type="hidden" id="hdClinicFileNames" value="" />
<input type="hidden" id="hdClinicFileUrls" value="" />
<input type="hidden" id="hdClinicOfficeIds" value="" />

<table class="table table-condensed table-responsive borderless">
    <tr>
        <td class="col-md-4" style="vertical-align:top;">
            @Html.PartialAsync("_consultInfoMain", Model)
        </td>
        <td class="col-md-8" style="vertical-align:top;">
            <table class="table-no-pointer">
                <tr>
                    <td class="col-md-6 align-text-top" style="vertical-align:top;">
                        <div class="row">
                            <div class="col-md-12 div-note" style="margin-bottom:10px;">
                                <div id="accordion-complete-consult" class="accordion">
                                    @{

                                        {
                                            <h3>Re-Assign</h3>
                                            <div>
                                                <div style="margin-top:6px;">
                                                    Are you sure you want to re-assign the case to a different recipient?
                                                    <br /><br />
                                                    <h5>The initial case data will be copied into a new draft. You will have an opportunity to review it and select a new recipient before submitting draft.</h5>
                                                    <br />
                                                    <h5>The current case will be cancelled.</h5>
                                                    <input type="button" class="btn btn-primary pull-right" id="btnReAssign" value="Re-Assign" />
                                                </div>
                                            </div>
                                            <h3 id="tabCancel">Cancel</h3>
                                            <div>
                                                <div style="margin-bottom:6px;">
                                                    Are you sure you want to cancel the case?
                                                </div>
                                                <textarea class="form-control" id="consult-cancel-note" rows="5" placeholder="Add note..." maxlength="4000"></textarea>
                                                <div style="margin-top:6px;">
                                                    <span id="consult-cancel-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" style="min-width:80px;" id="btnCancelConsult" value="Confirm Cancel" /><input type="button" class="btn btn-default pull-right" style="min-width:80px;" id="btnDiscardCancel" value="Abort Cancel" />
                                                </div>
                                            </div>

                                        {
                                            <h3>Provide More Info</h3>
                                            <div>
                                                <textarea class="form-control consult-note" id="consult-provide-more-info-note" rows="5" placeholder="Enter note..." maxlength="4000"></textarea>
                                                <div style="margin-top:6px;" id="div-consult-provide-more-info-spanError">
                                                    <span id="consult-provide-more-info-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnProvideMoreInfo" value="Send" /><input type="button" class="btn btn-default pull-right discard-all-data" value="Discard" />
                                                    <br />
                                                    <label for="FilesProvideMoreInfo" class="custom-file-upload">
                                                        <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                                                    </label>
                                                    <input style="margin-top:3px" type="file" id="FilesProvideMoreInfo" name="FilesProvideMoreInfo" class="form-control-file generate-description-box" multiple />

                                                    @if (Convert.ToInt32(ViewBag.PatientId) > 0)
                                                    {
                                                        <label for="btn" class="custom-file-upload">
                                                            <input type="button" data-patient-id="@ViewBag.PatientId" value="Choose EMR Files..." class="btn btn-primary btn-sm clinic-file-generate-description-box" />
                                                        </label>
                                                    }
                                                </div>
                                                <div class="col-md-12 text-left div-upload-file-description" id="div-FilesProvideMoreInfo"></div>
                                                <div class="col-md-12 text-left div-upload-file-description" id="div-clinic-Files"></div>
                                            </div>
                                            <h3 id="tabCancel">Cancel</h3>
                                            <div>
                                                <div style="margin-bottom:6px;">
                                                    Are you sure you want to cancel the case?
                                                </div>
                                                <textarea class="form-control" id="consult-cancel-note" rows="5" placeholder="Add note..." maxlength="4000"></textarea>
                                                <div style="margin-top:6px;">
                                                    <span id="consult-cancel-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" style="min-width:80px;" id="btnCancelConsult" value="Confirm Cancel" /><input type="button" class="btn btn-default pull-right" style="min-width:80px;" id="btnDiscardCancel" value="Abort Cancel" />
                                                </div>
                                            </div>

                                        else
                                        {
                                            <h3>Complete</h3>
                                            <div>
                                                <textarea class="form-control" id="consult-complete-note" rows="5" placeholder="Provide feedback to the specialist (optional)..." maxlength="4000"></textarea>
                                                <div style="margin-top:6px;">
                                                    <input type="button" class="btn btn-primary pull-right btn-complete-consult" data-consult-save="false" id="btnCompleteConsult" value="Complete" />
                                                    <input type="button" class="btn btn-primary pull-right btn-complete-consult" data-consult-save="true" data-number-of-files="@Model.NumberOfFiles" id="btnCompleteAndSaveConsult" value="Complete & Save" />
                                                </div>
                                            </div>
                                            <h3>Request Clarification</h3>
                                            <div>
                                                <textarea class="form-control consult-note" id="consult-request-clarification-note" rows="5" placeholder="Enter note..." maxlength="4000"></textarea>
                                                <div style="margin-top:6px;" id="div-consult-request-clarification-spanError">
                                                    <span id="consult-request-clarification-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnRequestClarification" value="Send" /><input type="button" class="btn btn-default pull-right discard-all-data" value="Discard" />
                                                    <br />
                                                    <label for="Files" class="custom-file-upload">
                                                        <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                                                    </label>
                                                    <input style="margin-top:3px" type="file" id="Files" name="Files" class="form-control-file generate-description-box" multiple />
                                                    @if (Convert.ToInt32(ViewBag.PatientId) > 0)
                                                    {
                                                        <label for="btn" class="custom-file-upload">
                                                            <input type="button" data-patient-id="@ViewBag.PatientId" value="Choose EMR Files..." class="btn btn-primary btn-sm clinic-file-generate-description-box" />
                                                        </label>
                                                    }
                                                </div>
                                                <div class="col-md-12 text-left div-upload-file-description" id="div-Files"></div>
                                                <div class="col-md-12 text-left div-upload-file-description" id="div-clinic-Files"></div>
                                            </div>

                                </div>
                            </div>

                        </div>
                    </td>
                </tr>
                @Html.PartialAsync("_consultInfoNotes", Model)
            </table>
        </td>
    </tr>
</table>
<br /><br />

