﻿@model Cerebrum.ViewModels.Econsult.Models.MetadataConsult
@using AwareMD.Cerebrum.Shared.Enums
@using Cerebrum.BLL.Utility
@using Cerebrum.ViewModels.Patient
@{ 

    if (@Model.CoverageId > 0)
    {

    }

<div style="margin-left:20px;">
    <table style="width:100%;" class="table table-condensed borderless">
        <tr>
            <td>
                Requester
            </td>
            <td>
                @Model.RequesterName
            </td>
        </tr>
        <tr>
            <td>
                Recipient
            </td>
            <td>
                @Model.RecipientName
            </td>
        </tr>
        <tr>
            <td style="vertical-align:top;">
                <a data-toggle="collapse" href="#patient-info">Patient</a>
            </td>
            <td class="col-md-12">
                <div>
                    <strong>@Model.PatientGiven @Model.PatientFamily</strong>
                    <div id="patient-info" class="panel-collapse collapse" style="margin-top:5px;">
                        <table class="table table-condensed table-bordered table-responsive" style="width:100%;">
                            <tr>
                                <td class="col-md-3 text-nowrap">First Name</td>
                                <td class="col-md-9">@Model.PatientGiven</td>
                            </tr>
                            <tr>
                                <td class="col-md-3 text-nowrap">Last Name</td>
                                <td class="col-md-9">@Model.PatientFamily</td>
                            </tr>
                            <tr>
                                <td class="col-md-3">DOB</td>
                                <td class="col-md-9">@Model.PatientBirthDate.ToString("MMM dd yyyy")</td>
                            </tr>
                            <tr>
                                <td class="col-md-3">Gender</td>
                                <td class="col-md-9">@Model.PatientGender</td>
                            </tr>
                            @{

                                {

                                }

                            <tr>
                                <td class="col-md-3">HIN</td>
                                <td class="col-md-9">@ohip</td>
                            </tr>
                            <tr>
                                <td colspan="2" class="col-md-12">Initial Request: @initialRequest</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                Case ID
            </td>
            <td>
                @Model.CaseId
            </td>
        </tr>
        <tr>
            <td>
                Subject
            </td>
            <td>
                @Html.Truncate(@Model.Title, 33, true, false)
            </td>
        </tr>
        <tr>
            <td colspan="2">
                Billing Coverage: @billingCoverage
            </td>
        </tr>
        @if (@Model.CoverageId == 2)
        {
            <tr>
                <td colspan="2">
                    Insurance Name: @Model.IssuerName
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    Insurance Group: @Model.IssuerGroup
                </td>
            </tr>

    </table>

</div>

