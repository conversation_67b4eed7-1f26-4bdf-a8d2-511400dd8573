﻿@model  List<Cerebrum.ViewModels.Econsult.VMPatientConsultDraft>
@using Cerebrum.ViewModels.Patient
@using Cerebrum.BLL.Utility

@{

    //var error = ViewBag.Error == null ? false : true;

<script src="~/Areas/eConsult/Scripts/search-patient-consults.js"></script>
<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />

<h4 style="margin-top:20px;"><i>Patient Info (EMR)</i></h4>
<table class="table table-condensed table-bordered table-responsive" style="width:70%">
    <thead>
        <tr>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Gender</th>
            <th>Date of Birth</th>
            <th class="text-nowrap">HIN</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="text-nowrap">
                @patientInfo.FirstName
            </td>
            <td class="text-nowrap">
                @patientInfo.LastName
            </td>
            <td class="text-nowrap">
                @UtilityHelper.GetDescriptionFromEnumValue(patientInfo.Gender)
            </td>
            <td class="text-nowrap">
                @patientInfo.DOB_MMM_dd_yyyy
            </td>
            <td class="text-nowrap">
                @ohip
            </td>
        </tr>
    </tbody>
</table>
<br /><br />
<h4><i>Patient Drafts</i></h4>
@if(@Model.Count == 0)
{
    <table>
        <tr>
            <td colspan="11">
                <div class="col-sm-12">
                    <h4>There are no drafts found</h4>
                </div>
            </td>
        </tr>
    </table>

else
{
    <table class="table table-condensed table-bordered table-responsive">
        <thead>
            <tr>
                <th>
                    Subject
                </th>
                <th>
                    Notes
                </th>
                <th class="text-nowrap">
                    Created Date
                </th>
                <th class="text-nowrap">
                    Last Updated
                </th>
                <th>
@* &nbsp; *@
                </th>
            </tr>
        </thead>
        <tbody>
            @for (int i = 0; i < @Model.Count; i++)
            {
                <tr>
                    <td>
                        @Model[i].SubjectLine
                    </td>
                    <td>
                        @Model[i].Notes
                    </td>
                    <td class="text-nowrap">
                        @Model[i].DateCreated.ToString("MMM dd, yyyy h:mm tt")
                    </td>
                    <td class="text-nowrap">
                        @Model[i].DateLastModified.ToString("MMM dd, yyyy h:mm tt")
                    </td>
                    <td>

                        *@Html.ActionLink("Edit", "DownloadFile", new { fileId = file.Id })
                        @if (showEdit)
                        { @Html.ActionLink(linkText: "Edit", actionName: "LoadPatientDraft", controllerName: "Consult", routeValues: new { id = Model[i].Id }, htmlAttributes: null)
                        }

                    </td>
                </tr>

        </tbody>

    </table>

