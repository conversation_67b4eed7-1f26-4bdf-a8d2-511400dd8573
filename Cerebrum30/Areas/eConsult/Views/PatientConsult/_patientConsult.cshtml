﻿@model Cerebrum.ViewModels.Econsult.VMMetadataConsult
@using Cerebrum.ViewModels.Patient
@using Cerebrum.BLL.Utility

@{

<script src="~/Areas/eConsult/Scripts/search-patient-consults.js"></script>
<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />

<h4 style="margin-top:20px;"><i>Patient Info (EMR)</i></h4>
<table class="table table-condensed table-bordered table-responsive" style="width:70%;font-size:11px;">
    <thead>
        <tr>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Gender</th>
            <th>Date of Birth</th>
            <th class="text-nowrap">HIN</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="text-nowrap">
                @patientInfo.FirstName
            </td>
            <td class="text-nowrap">
                @patientInfo.LastName
            </td>
            <td class="text-nowrap">
                @UtilityHelper.GetDescriptionFromEnumValue(patientInfo.Gender)
            </td>
            <td class="text-nowrap">
                @patientInfo.DOB_MMM_dd_yyyy
            </td>
            <td class="text-nowrap">
                @ohip
            </td>
        </tr>
    </tbody>
</table>
<br /><br />
<h4><i>Patient Consults</i></h4>

@if (@Model.Success)
{

    {
        <table style="width:80%;margin-top:5px;margin-bottom:40px; vertical-align:top;">
            <tr>
                <td>
                    <div class="col-sm-12">
                        <h4>There are no cases</h4>
                    </div>
                </td>
            </tr>
        </table>

    else
    {
        <script>
            $(document).ready(function () {

@* }); *@
        </script>
        <table style="width:100%;margin-top:5px;margin-bottom:40px;font-size:11px;">
            <tr>
                <td>
                    <div class="col-sm-12">
                        <br />
                        <table class="table table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>

                                    <th>
                                        Subject
                                    </th>
                                    <th>
                                        Requester
                                    </th>
                                    <th>
                                        Author Name
                                    </th>
                                    <th>
                                        Recipient
                                    </th>
                                    <th>
                                        State
                                    </th>
                                    <th class="text-nowrap">
                                        Submitted Date
                                    </th>
                                    <th class="text-nowrap">
                                        Last Updated
                                    </th>
                                    <th class="text-center">
                                        Saved<br /> Flag
                                    </th>
                                    <th class="text-center text-nowrap">
                                        Needs to<br /> be seen
                                    </th>
                                    <th class="text-center">
                                        Consult<br />Flag
                                    </th>
                                    <th>
@* &nbsp; *@
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in @Model.Consults)
                                {

                                    {

                                    }

                                    <tr data-id="@item.CaseId">

                                        <td>
                                            @Html.Truncate(item.Title, 50, true, false)
                                        </td>
                                        <td>
                                            @item.RequesterName
                                        </td>
                                        <td>
                                            @item.AuthorName
                                        </td>
                                        <td>
                                            @item.RecipientName
                                        </td>
                                        <td class="text-nowrap">
                                            @state
                                        </td>
                                        <td class="text-nowrap">
                                            @item.DateSubmitted.ToString("MMM dd, yyyy h:mm tt")
                                        </td>
                                        <td class="text-nowrap">
                                            @item.DateLastUpdated.ToString("MMM dd, yyyy h:mm tt")
                                        </td>
                                        <td class="text-center">
                                            @if (item.SavedCompletedFlag)
                                                {
                                                <input type="checkbox" onclick="return false;" checked readonly />
                                                }
                                        </td>
                                        <td class="text-center">
                                            @if (item.PatientNeedsToBeSeen)
                                                {
                                                <input type="checkbox" onclick="return false;" checked readonly />
                                                }
                                        </td>
                                        <td>
                                            @if (item.ConsultFlag != "NOFLAG")
                                                {
                                                <span class="glyphicon glyphicon-flag" style="color:@CustomHelpers.GetFlagColor(item.ConsultFlag);margin-left:10px;" data-toggle="tooltip" title="@item.ConsultFlag"></span>
                                                }
                                        </td>
                                        <td class="text-center">
                                            @if (item.ConsultState != "Completed" && showEdit)
                                            {

                                                {
                                                    @Html.ActionLink(linkText: "Edit", actionName: "LoadPatientConsult", controllerName: "Consult", routeValues: new { id = item.CaseId }, htmlAttributes: null)
                                                }

                                            else
                                            {
                                                if (item.SavedCompletedFlag)
                                                {
                                                    <a href="javascript:void(null)" class="btn-view-consult-details" data-case-id="@item.CaseId" data-id="@item.Id">View</a>
                                                }
                                            }
                                        </td>
                                    </tr>

                            </tbody>
                        </table>
                    </div>
                </td>
            </tr>
        </table>

else
{
    <table style="width:80%;margin-top:5px;margin-bottom:40px; vertical-align:top;">
        <tr>
            <td>
                <div class="col-sm-12">
                    <h4>There was a problem. Please Contact Administrator!</h4>
                </div>
            </td>
        </tr>
    </table>

