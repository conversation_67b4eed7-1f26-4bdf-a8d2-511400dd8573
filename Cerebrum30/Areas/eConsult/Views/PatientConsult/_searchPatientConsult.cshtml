@* ﻿@using Cerebrum.BLL.Utility; *@
@using AwareMD.Cerebrum.Shared.Enums
@using Cerebrum.ViewModels.Patient
@{

}
<script src="~/Areas/eConsult/Scripts/search-patient-consults.js"></script>
<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />
<h4><i>Search Consults in Metadata</i></h4> All consults for the last 3 months from selected '<b>Submitted Date</b>'

<table style="width:100%;margin-top:5px;margin-bottom:50px;font-size:11px;">
    <tr>
        <td>
            <div class="col-sm-12">
                <br />
                <table class="table table-condensed table-bordered table-responsive">
                    <thead>
                        <tr>
                            <th style="min-width:120px;">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="subject">Subject</a></td>
                                        <td>
                                            <table>
                                            <tr><td id="td-subject-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                            <tr><td id="td-subject-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                        </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th>
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" title="Sort By Last Name" class="btn-consult-sort-column" data-sort-column="patient">Patient</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-patient-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-patient-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th>
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="requester">Requester</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-requester-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-requester-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th>
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="recipient">Recipient</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-recipient-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-recipient-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th>
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="state">State</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-state-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-state-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-nowrap">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="submittedDate">Submitted Date</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-submittedDate-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-submittedDate-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-center">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="savedFlag">Saved<br /> Flag</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-savedFlag-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-savedFlag-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-center text-nowrap">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="needsToBeSeen">Needs to<br /> be seen</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-needsToBeSeen-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-needsToBeSeen-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-center text-nowrap">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="patientAssociated">Patient<br /> associated</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-patientAssociated-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-patientAssociated-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-center">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="consultFlag">Consult Flag</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-consultFlag-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-consultFlag-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th></th>
                        </tr>
                        <tr>
                            <td>

                            </td>
                            <td>
                                <input type="text" id="txtPatientLastName" placeholder="Patient Last Name" class="form-control input-mini" />
                            </td>

                            <td>
                                <input type="text" id="txtRequesterName" placeholder="Requester Name" class="form-control input-mini" />
                            </td>*@<td>@*e*@
                                </td>
                            <td>
                                <input type="text" id="txtRecipientName" placeholder="Recipient Name" class="form-control input-mini" />
                            </td>
                            <td>
                                <select class="form-control input-mini" id="ddlStatus" style="font-size:12px;">
                                    <option value="">All</option>
                                    <option value="Assigned">Assigned</option>
                                    <option value="Cancelled">Cancelled</option>
                                    <option value="Clarification Requested">Clarification Requested</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Consult Provided">Consult Provided</option>
                                    <option value="More Info Provided">More Info Provided</option>
                                    <option value="More Info Requested">More Info Requested</option>
                                    <option value="Requested">Requested</option>
                                    <option value="Returned">Returned</option>
                                    <option value="Submitted">Submitted</option>
                                    <option value="Unassigned">Unassigned</option>
                                </select>
                            </td>
                            <td class="text-nowrap">
                                <input id="SubmittedDate" name="SubmittedDate" style="font-size:12px;" type="text" class="form-control datepicker" value='@DateTime.Now.ToString("yyyy-MM-dd")' />

                            </td>
                            <td class="text-nowrap" style="min-width:85px;">
                                <select class="form-control input-mini" id="ddlSavedFlag" style="font-size:11px">
                                    <option value="0">All</option>
                                    <option value="1">Yes</option>
                                    <option value="2">No</option>
                                </select>
                            </td>
                            <td class="text-center text-nowrap" style="min-width:85px;">
                                <select class="form-control input-mini" id="ddlNeedsToBeSeen" style="font-size:11px">
                                    <option value="0">All</option>
                                    <option value="1">Yes</option>
                                    <option value="2">No</option>
                                </select>
                            </td>
                            <td class="text-center text-nowrap" style="min-width:85px;">
                                <select class="form-control input-mini" id="ddlPatientAssociated" style="font-size:11px">
                                    <option value="0">All</option>
                                    <option value="1">Yes</option>
                                    <option value="2">No</option>
                                </select>
                            </td>
                            <td style="min-width:125px;">
                                <select id="ddlSearchConsultFlag" class="form-control input-mini" style="font-size:10px">
                                    <option value="">All</option>
                                    @foreach (var item in Enum.GetValues(typeof(ConsultFlag)))
                                    {
                                        <option value="@item.ToString()">@UtilityHelper.GetDescriptionFromEnumValue((ConsultFlag)item)</option>
                                    }
                                </select>
                            </td>
                            <td>
                                <input type="button" class="btn btn-primary btn-sm" value="Search" id="btnSearchEconsult" />
                            </td>
                        </tr>
                    </thead>
                    <tbody id="tbody-consult-search-result"></tbody>
                </table>
            </div>
        </td>
    </tr>
</table>
