﻿@using Cerebrum.ViewModels.Econsult.Models
@{

}
<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />
<script>
    $(function () {

@* $('#btnUaoSubmit').prop('disabled', true); *@
        }
        else {
@* $('#btnUaoSubmit').prop('disabled', false); *@

        $("#ddlOneIdUaoId").change(function () {

@* $('#btnUaoSubmit').prop('disabled', true); *@

            else {
@* $('#btnUaoSubmit').prop('disabled', false); *@

@* }); *@
@* }); *@
</script>
<div class="col-sm-4">
    <h3>Select UAO</h3>
    @using (Html.BeginForm("SaveUserUao", "UaoSelect", Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post))
    {
        @Html.AntiForgeryToken()
        <div class="col-sm-6">
            <select class="form-control" id="ddlOneIdUaoId" name="ddlOneIdUaoId">
                <option value="0">Please Select UAO</option>
                @foreach (var item in listOfUao)
                {
                    <option value="@item.UAO">@item.UAOName</option>
                }
            </select>
        </div>
        <div><button type="submit" id="btnUaoSubmit" class="btn btn-primary">Submit</button></div>

</div>
