﻿using AwareMD.Eforms.Service;
using AwareMD.Eforms.Service.Models;
using AwareMD.Eforms.Service.Models.Components;
using Cerebrum.BLL.AdminUao;
using Cerebrum.BLL.Econsult;
using Cerebrum.BLL.Patient;
using Cerebrum.BLL.Practice;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using Cerebrum.Doctors;
using Cerebrum.DTO.Doctor;
using Cerebrum.DTO.Patient;
using Cerebrum.DTO.Practice;
using Cerebrum.ViewModels.AdminUAO.Dto;
using Cerebrum.ViewModels.Patient;
using Cerebrum30.Areas.EForms.Data;
using Cerebrum30.Controllers;
using Cerebrum30.Utility;
// using Hl7.Fhir.Model; // Commented out due to ambiguity - use fully qualified names
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using AwareMD.Cerebrum.Shared.Enums;

namespace Cerebrum30.Areas.EForms.Controllers.BLL
{
    public class EformsBLL
    {
        private log4net.ILog _log;

        public EformsBLL(log4net.ILog log)
        {
            _log = log;
        }
        public async System.Threading.Tasks.Task PatientCloseRequest(int patientId, string hubTopic, string accessToken, int userId)
        {
            if (patientId != 0 && !string.IsNullOrEmpty(hubTopic) && !string.IsNullOrEmpty(accessToken))
            {
                PatientOpenRequest pcr = CreatePatientCloseRequest(hubTopic, patientId);
                JsonSerializerSettings settings = GetJsonSerializerSettings();
                string patientCloseRequestBodyString = JsonConvert.SerializeObject(pcr, settings);

                _log.Info($"Service: eForms | Action: PatientClose | User: {userId.ToString()} | Status: Attempt | Content: Request Body: {Regex.Replace(patientCloseRequestBodyString, @"\s+", string.Empty)} | CMS Unique ID: {hubTopic}");

                try
                {
                    await HttpCaller.PatientOpenOrClose(patientCloseRequestBodyString, accessToken);
                    _log.Info(CreateLogParagraph("EForms", "Patient-Close", "Success", "N/A", hubTopic, userId));
                }
                catch (Exception e)
                {
                    _log.Error(CreateLogParagraph("EForms", "Patient-Close", "Fail", e.Message, hubTopic, userId));
                }
                HttpContextProvider.Current.Session.SetInt32("PatientId", 0);
            }
        }

        private PatientOpenRequest CreatePatientCloseRequest(string hubTopic, int patientId)
        {
            VMPatientInfo patient = new EconsultBLL().GetPatientInfo(patientId);
            PatientContactInfo patientContact = GetPatientContactInfo(patientId);

            PatientOpenRequest pcr = new PatientOpenRequest();
            pcr.id = Guid.NewGuid().ToString();
            pcr.timestamp = DateTime.UtcNow;
            pcr.Event.HubTopic = hubTopic;
            pcr.Event.HubEvent = "Patient-close";
            Context ctx = new Context();
            ctx.key = "patient";
            ctx.resource.resourceType = "Patient";
            ctx.resource.id = $"{patient.PatientId}";

            ctx.resource.meta = new AwareMD.Eforms.Service.Models.Components.Meta
            {
                profile = new List<string>
                {
                    "http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Patient|1.0.0"
                }
            };

            // Ticket 3305, if healthcard province is ON then set OHIP 
            if ((!string.IsNullOrWhiteSpace(patient.HealthCardProvince)) && patient.HealthCardProvince.ToLower().Equals("on"))
            {
                ctx.resource.identifier = new List<AwareMD.Eforms.Service.Models.Components.Identifier>
                {
                    new AwareMD.Eforms.Service.Models.Components.Identifier
                    {
                            system = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-patient-hcn",
                            value = patient.OHIP
                    }
                };
            }
            else
            {
                ctx.resource.identifier = null;
            }

            ctx.resource.name = new Name[]
            {
                new Name
                {
                    use = "official",
                    family = patient.LastName,
                    given = new string[] { patient.FirstName }
                }
                        };
            if (patientContact != null)
            {
                if (!string.IsNullOrEmpty(patientContact.PhoneNumber))
                {
                    ctx.resource.telecom = new List<Telecom>
                    {
                        new Telecom
                        {
                            system = "phone",
                            value = patientContact.PhoneNumber,
                            use = "home"
                        }
                    };
                }
                else
                    ctx.resource.telecom = null;

                ctx.resource.address = new List<AwareMD.Eforms.Service.Models.Components.Address>
                {
                    new AwareMD.Eforms.Service.Models.Components.Address
                    {
                        city = patientContact.City,
                        country = patientContact.Country,
                        postalCode = patientContact.PostalCode,
                        state = patientContact.Province,
                        use = "home",
                        type = "physical",
                        line = new string [] { patientContact.Address1 }
                    }
                };
            }
            // Gender
            switch (patient.Gender)
            {
                case Gender.M:
                case Gender.F:
                case Gender.O:
                case Gender.U:
                    {
                        ctx.resource.gender = HttpCaller.GetStringGender(patient.Gender);
                    }
                    break;
                case Gender.S:
                    ctx.resource.gender = null;
                    break;
            }

            try
            {
                ctx.resource.birthDate = patient.DateOfBirth.Value.ToString("yyyy-MM-dd");
            }
            catch (InvalidOperationException x)
            {
                ctx.resource.birthDate = null;
            }
            pcr.Event.context.Add(ctx);
            return pcr;
        }

        public JsonSerializerSettings GetJsonSerializerSettings()
        {
            JsonSerializerSettings settings = new JsonSerializerSettings();

            settings.NullValueHandling = NullValueHandling.Ignore;
            settings.DefaultValueHandling = DefaultValueHandling.Ignore;
            settings.MissingMemberHandling = MissingMemberHandling.Ignore;
            settings.PreserveReferencesHandling = PreserveReferencesHandling.None;
            settings.Formatting = Formatting.Indented;
            return settings;
        }

        private string CreateLogParagraph(string serviceName, string action, string status, string content, string cmsUniqueId, int userId)
        {
            string logText = $@"Service: {serviceName} | Action: {action} | DateTime: {DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm")} | 
                                User: {userId.ToString()} | Status: {status} | Content: {content} | CMS Unique ID: {cmsUniqueId}";
            return logText;
        }
        private PatientContactInfo GetPatientContactInfo(int patientId)
        {
            var ctx = new CerebrumContext();
            var addressList = ctx.DemographicsAddress.Where(a => a.Demographic.PatientRecordId == patientId && a.IsActive).ToList();
            // get last active for legacy data
            var address = addressList.LastOrDefault();
            var phone = ctx.PhoneNumber.FirstOrDefault(p => p.Demographic.PatientRecordId == patientId && p.IsActive && !p.IsRemoved);
            PatientContactInfo patientInfo = new PatientContactInfo();

            if (address != null)
            {
                patientInfo.Address1 = address.addressLine1;
                patientInfo.Address2 = address.addressLine2;
                patientInfo.City = address.city;
                patientInfo.Country = address.country;
                patientInfo.PostalCode = address.postalCode;
                patientInfo.Province = address.province;
            }
            ;

            if (phone != null)
            {
                patientInfo.PhoneNumber = phone.phoneNumber;
                patientInfo.Extension = phone.extention;
            }
            return patientInfo;
        }

        private PatientContactInfo GetPatientContactInfo(PatientDemographicInfoDTO patientDemo)
        {

            PatientContactInfo patientInfo = new PatientContactInfo();

            if (patientDemo != null)
            {
                patientInfo.Address1 = patientDemo.Address1;
                patientInfo.Address2 = patientDemo.Address2;
                patientInfo.City = patientDemo.City;
                patientInfo.Country = patientDemo.Country;
                patientInfo.PostalCode = patientDemo.PostalCode;
                patientInfo.Province = patientDemo.Province;
            }
            ;

            if (patientDemo.PatientPhoneNumbers != null && patientDemo.PatientPhoneNumbers.Count() > 0)
            {
                var phone = patientDemo.PatientPhoneNumbers.FirstOrDefault();
                patientInfo.PhoneNumber = phone.PhoneNumber;
                patientInfo.Extension = phone.Extention;
            }
            return patientInfo;
        }

        public PatientOpenRequest CreatePatientOpenRequest(string hubTopic, int patientId, Security.CerebrumUser CerebrumUser, IPatientBLL _patientBll)
        {
            int practiceId = CerebrumUser.PracticeId;
            var patient = _patientBll.GetPatientDemographicInfo(practiceId, patientId);

            //var patientContact = GetPatientContactInfo(patient);
            var patientContact = GetPatientContactInfo(patientId);

            var por = new PatientOpenRequest();
            por.id = Guid.NewGuid().ToString();
            por.timestamp = DateTime.UtcNow;
            por.Event.HubTopic = hubTopic;
            por.Event.HubEvent = "Patient-open";
            var ctx = new Context();
            ctx.key = "patient";
            ctx.resource.resourceType = "Patient";
            //ctx.resource.id = "cms-patient-example";
            ctx.resource.id = patientId.ToString();
            ctx.resource.meta = new AwareMD.Eforms.Service.Models.Components.Meta
            {
                profile = new List<string>
                {
                    "http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Patient|1.0.0"
                }
            };
            // fast fix for Ontario, needs to fix issue, since in DB it is ohip number for all provinces
            ctx.resource.identifier = new List<AwareMD.Eforms.Service.Models.Components.Identifier>
            {
                new AwareMD.Eforms.Service.Models.Components.Identifier
                {
                    system = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-patient-hcn",
                    value = patient.OHIP?.Trim().Length == 10 ? patient.OHIP : ""
                }
            };
            ctx.resource.name = new Name[]
            {
                new Name
                {
                    use = "official",
                    family = patient.LastName,
                    given = new string[] { patient.FirstName }
                }
            };
            if (patientContact != null)
            {
                if (!string.IsNullOrEmpty(patientContact.PhoneNumber))
                {
                    ctx.resource.telecom = new List<Telecom>
                    {
                        new Telecom
                        {
                            system = "phone",
                            value = patientContact.PhoneNumber,
                            use = "home"
                        }
                    };
                }
                else
                    ctx.resource.telecom = null;

                if (!string.IsNullOrEmpty(patientContact.Address1))
                {

                    ctx.resource.address = new List<AwareMD.Eforms.Service.Models.Components.Address>
                    {
                        new AwareMD.Eforms.Service.Models.Components.Address
                        {
                            city = patientContact.City,
                            country = patientContact.Country,
                            postalCode = patientContact.PostalCode,
                            state = patientContact.Province,
                            use = "home",
                            type = "physical",
                            line = new string [] { patientContact.Address1 ?? "non-existent" }
                        }
                    };
                }
                else
                    ctx.resource.address = null;
            }

            ctx.resource.gender = HttpCaller.GetStringGender(patient.Gender);
            ctx.resource.managingOrganization = null;
            try
            {
                ctx.resource.birthDate = patient.DateOfBirth.Value.ToString("yyyy-MM-dd");
            }
            catch (InvalidOperationException x)
            {
                ctx.resource.birthDate = null;
            }

            por.Event.context.Add(ctx);
            return por;
        }

        public OHuserLoginEvent CreateUserLoginEvent(string hubTopic, Security.CerebrumUser CerebrumUser, IOfficeBLL _officeBll, AdminUaoBLL _adminUaoBLL, IPracticeDoctorBLL practiceDoctorBLL)
        {
            var userLogin = new OHuserLoginEvent();
            userLogin.Id = Guid.NewGuid().ToString();
            userLogin.Timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH\\:mm\\:ss.ffZ");
            userLogin.Event.HubEvent = "OH.userLogin";
            userLogin.Event.HubTopic = hubTopic;

            IList<PracticeOfficeInfoDTO> offices = _officeBll.GetPracticeOffices(CerebrumUser.PracticeId);
            PracticeOfficeInfoDTO headOffice = offices.FirstOrDefault(f => f.IsHeadOffice);
            headOffice = headOffice == null ? offices.FirstOrDefault() : headOffice;
            UaoSettingsDto uaoSettings = _adminUaoBLL.GetUaoSettingsByUaoValue(CerebrumUser.OneIdUaoId);

            SetUserLoginOrganization(uaoSettings, ref userLogin);
            SetUserLoginPractitioner(headOffice, ref userLogin, CerebrumUser, practiceDoctorBLL);
            SetUserLoginLocation(headOffice, ref userLogin);
            SetUserLoginParameters(ref userLogin, CerebrumUser);
            return userLogin;
        }

        public OhConsentTargetChangeEvent CreateConsentTargetChangeEvent(string hubTopic, Security.CerebrumUser cerebrumUser)
        {
            var consentTargetChange = new OhConsentTargetChangeEvent();
            consentTargetChange.Id = Guid.NewGuid().ToString();
            consentTargetChange.Timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH\\:mm\\:ss.ffZ");
            consentTargetChange.Event.HubEvent = "OH.consentTargetChange";
            consentTargetChange.Event.HubTopic = hubTopic;
            SetConsentTargetChangeParameters(ref consentTargetChange, cerebrumUser);
            return consentTargetChange;
        }

        private void SetConsentTargetChangeParameters(ref OhConsentTargetChangeEvent _event, Security.CerebrumUser cerebrumUser)
        {
            var context = new OrgContext();
            context.key = "parameters";
            context.resource.resourceType = "Parameters";
            context.resource.id = "0c678a3d-3b71-446e-91b3-41541e1360af";
            context.resource.meta.versionId = null;
            context.resource.meta.profile.Add("http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Parameters|1.0.0");
            context.resource.identifier = null;
            context.resource.alias = null;
            context.resource._alias = null;
            context.resource.telecom = null;
            context.resource.address = null;
            context.resource.text = null;
            context.resource.status = null;
            context.resource.operationalStatus = null;
            context.resource.managingOrganization = null;
            context.resource.type = null;
            var parameter = new OrgParameter();
            parameter.name = "consentTarget";
            parameter.valueString = "http://ehealthontario.ca/fhir/StructureDefinition/ca-on-medications-profile-MedicationDispense,http://ehealthontario.ca/fhir/StructureDefinition/ca-on-immunizations-profile-retrieval-clinician-Immunization";
            parameter.valueCoding = null;
            context.resource.parameter.Add(parameter);
            _event.Event.context.Add(context);
        }

        private void SetUserLoginOrganization(UaoSettingsDto uaoSettings, ref OHuserLoginEvent _event)
        {
            var context = new OrgContext();
            context.key = "organization";
            context.resource = new OrgResource();
            context.resource.resourceType = "Organization";
            // ID: can be any value as long as is unique within the bundle,
            // EMR-Connectivity-ValScenarios_AMD__RS01_v01.xlsx, #1 
            context.resource.id = Helper.GenerateRequestId();
            context.resource.meta.versionId = null;
            context.resource.meta.profile.Add("http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Organization|1.0.0");

            var identifier = new OrgIdentifier();
            identifier.system = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-provider-upi";
            identifier.value = uaoSettings.UAO; //CerebrumUser.OneIdUaoId;

            //***** Ticket#3305
            var coding = new AwareMD.Eforms.Service.Models.Components.Coding();
            coding.system = "http://terminology.hl7.org/CodeSystem/organization-type";
            coding.code = "prov";
            identifier.type = null;
            //***** Ticket#3305

            context.resource.identifier.Add(identifier);
            context.resource.name = uaoSettings.LegalName;

            var address = new OrgAddress();
            // TODO: DSTU2 namespace doesn't exist in current FHIR library - commenting out
            // address.use = Hl7.Fhir.DSTU2.Model.Address.AddressUse.Work;
            // address.type = Hl7.Fhir.DSTU2.Model.Address.AddressType.Physical;
            address.line.Add(uaoSettings.Address1);
            if (!string.IsNullOrWhiteSpace(uaoSettings.Address2))
            {
                address.line.Add(uaoSettings.Address2);
            }
            address.city = uaoSettings.City;
            address.state = uaoSettings.Province.GetDisplayName();
            address.postalCode = uaoSettings.PostalCode;
            address.country = uaoSettings.Country == 0 ? "CAN" : "USA";

            context.resource.address = new OrgAddress[] { address };

            context.resource.text = null;
            context.resource.operationalStatus = null;
            //Ticket#3305
            context.resource.managingOrganization = null;
            context.resource.parameter = null;


            var t3 = new Type2
            {
                coding = new List<Coding2>
                {
                  new Coding2 {
                    system = "http://terminology.hl7.org/CodeSystem/organization-type",
                    code = "prov",
                    display = "Healthcare Provider"
                    }
                }
            };


            context.resource.type.Add(t3);

            if (!string.IsNullOrWhiteSpace(uaoSettings.Phone))
            {
                var telecom = new OrgTelecom();
                telecom.value = FormatPhone(uaoSettings.Phone);
                // TODO: FHIR DSTU2 namespace doesn't exist in newer FHIR library - commenting out during migration
                // telecom.system = Hl7.Fhir.DSTU2.Model.ContactPoint.ContactPointSystem.Phone;
                context.resource.telecom.Add(telecom);
            }

            context.resource.alias = null;
            context.resource._alias = null;


            _event.Event.context.Add(context);
        }

        private string FormatPhone(string phone)
        {
            if (phone.Length > 10)
            {
                return "1" + PhoneUtils.FormatPhone(phone.Substring(1));
            }
            else
            {
                return PhoneUtils.FormatPhone(phone);
            }
        }
        private void SetUserLoginPractitioner(PracticeOfficeInfoDTO headOffice, ref OHuserLoginEvent _event, Security.CerebrumUser CerebrumUser, IPracticeDoctorBLL _practiceDoctorBLL)
        {
            int practiceId = CerebrumUser.PracticeId;
            int practiceDoctorId = CerebrumUser.PracticeDoctorId;
            // if (practiceDoctorId > 0)
            //{
            IPracticeDoctorDTO practiceDoctor = _practiceDoctorBLL.GetPracticeDoctor(practiceId, practiceDoctorId);

            var context = new OrgContext();
            context.key = "practitioner";
            context.resource.resourceType = "Practitioner";
            context.resource.id = $"{CerebrumUser.UserId}";
            context.resource.meta.versionId = null;
            context.resource.meta.profile.Add("http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Practitioner|1.0.0");
            // TODO: FHIR DSTU2 namespace doesn't exist in newer FHIR library - commenting out during migration
            // context.resource.text.status = Hl7.Fhir.DSTU2.Model.Narrative.NarrativeStatus.Generated;
            //context.resource.text.div = "<div xmlns=\"http://www.w3.org/1999/xhtml\">Hospital XYZ, Bay Street, second floor, Neuro Radiology Operation Room 1</div>";
            // identifier 1
            var identifier = new AwareMD.Eforms.Service.Models.OrgIdentifier();

            /*
            var coding = new Cerebrum.EForms2.Models.Coding();
            coding.system = "http://terminology.hl7.org/CodeSystem/v2-0203";
            coding.code = "RRI";


            //identifier.type.coding.Add(coding);
            //identifier.type.text = "eHealth PPR UPI";
            //identifier.system = "http://ehealthontario.ca/fhir/NamingSystem/upi";
            //identifier.value = "24";

            context.resource.identifier.Add(identifier);

            // identifier 2
            identifier = new Cerebrum.EForms2.Models.UserLogin.OrgIdentifier();

            coding = new Cerebrum.EForms2.Models.Coding();
            coding.system = "http://terminology.hl7.org/CodeSystem/v2-0203";
            coding.code = "LN";

            identifier.type.coding.Add(coding);
            */
            //identifier = new Cerebrum.EForms2.Models.UserLogin.OrgIdentifier();
            //identifier.type.text = "Ontario Medical License Number";
            //identifier.system = practiceDoctor.PhysicianType.ToLower().Equals("md") ? "http://infoway-inforoute.ca/fhir/NamingSystem/ca-on-license-physician" : "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-nurse";
            //identifier.value = practiceDoctor.CPSO;
            //identifier.type = null;
            //context.resource.identifier.Add(identifier);
            var name = new OrgName();
            if (practiceDoctorId > 0 && !string.IsNullOrEmpty(practiceDoctor?.CPSO))
            {
                identifier = new OrgIdentifier();
                identifier.type.text = "Ontario Medical License Number";
                identifier.system = practiceDoctor.PhysicianType.ToLower().Equals("md") ? "http://infoway-inforoute.ca/fhir/NamingSystem/ca-on-license-physician" : "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-nurse";
                identifier.value = practiceDoctor.CPSO;
                identifier.type = null;
                context.resource.identifier.Add(identifier);

                name.family = practiceDoctor.LastName;
                name.given.Add(practiceDoctor.FirstName);
            }
            else
            {
                context.resource.identifier = null;
                name.family = CerebrumUser.LastName;
                name.given.Add(CerebrumUser.FirstName);
            }


            //name.use = HumanName.NameUse.Official;
            name.use = null;
            //name.family = practiceDoctor.LastName;
            //name.given.Add(practiceDoctor.FirstName);
            name.prefix = null;
            name.suffix = null;
            //name.prefix.Add("Dr.");
            //name.suffix.Add("III");
            //name.suffix.Add("PhD");
            //name.suffix.Add(practiceDoctor.Degrees);

            var listOfNames = new List<OrgName>();
            listOfNames.Add(name);
            context.resource.name = listOfNames;

            context.resource.type = null;

            //string province = headOffice.Province.GetDisplayName();

            context.resource.alias = null;
            context.resource._alias = null;
            context.resource.operationalStatus = null;
            context.resource.managingOrganization = null;
            context.resource.parameter = null;

            context.resource.address = null;
            context.resource.telecom = null;
            context.resource.text = null;

            _event.Event.context.Add(context);
            // }
        }

        private void SetUserLoginLocation(PracticeOfficeInfoDTO office, ref OHuserLoginEvent _event)
        {
            var context = new OrgContext();
            context.key = "location";
            context.resource = new OrgResource();
            context.resource.resourceType = "Location";
            context.resource.id = $"{office.OfficeId}";
            context.resource.meta.versionId = null;
            context.resource.meta.profile.Add("http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Location|1.0.0");

            var identifier = new OrgIdentifier();
            identifier.system = "https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-provider-upi";
            identifier.value = office.OfficeName;
            identifier.type = null;
            //context.resource.identifier.Add(identifier);
            context.resource.identifier = null;

            context.resource.status = null; //"suspended"; //ACTIVE | SUSPENDED | INACTIVE
            context.resource.operationalStatus = null;// .system = "http://terminology.hl7.org/CodeSystem/v2-0116";
            //context.resource.operationalStatus.code = "H";
            //context.resource.operationalStatus.display = "Housekeeping";

            context.resource.name = office.BusinessName;

            context.resource.description = null;// "Old South Wing, Neuro Radiology Operation Room 1 on second floor";
            context.resource.mode = null;

            var telecom = new OrgTelecom();
            // TODO: FHIR DSTU2 namespace doesn't exist in newer FHIR library - commenting out during migration
            // telecom.system = Hl7.Fhir.DSTU2.Model.ContactPoint.ContactPointSystem.Phone;
            telecom.value = office.Phone;
            context.resource.telecom.Add(telecom);

            string province = office.Province.GetDisplayName();
            var address = new OrgAddress();
            // TODO: FHIR DSTU2 namespace doesn't exist in newer FHIR library - commenting out during migration
            // address.use = Hl7.Fhir.DSTU2.Model.Address.AddressUse.Work;
            // address.type = Hl7.Fhir.DSTU2.Model.Address.AddressType.Physical;
            address.line.Add(office.Address1);
            if (!string.IsNullOrWhiteSpace(office.Address2))
            {
                address.line.Add(office.Address2);
            }
            address.city = office.City;
            address.state = province;
            address.postalCode = office.PostalCode;
            address.country = string.IsNullOrWhiteSpace(office.Country) ? "CAN" : office.Country;

            context.resource.address = address;
            context.resource.text = null;
            context.resource.parameter = null;

            var t3 = new Type2
            {
                coding = new List<Coding2>
                {
                    new Coding2
                    {
                        system = "http://terminology.hl7.org/CodeSystem/organization-type",
                        code = "team",
                        display = "Organizational Team"
                    }
                }
            };
            context.resource.type.Add(t3);

            context.resource.alias = null;
            context.resource._alias = null;
            context.resource.managingOrganization = null;
            _event.Event.context.Add(context);
        }

        private void SetUserLoginParameters(ref OHuserLoginEvent _event, Security.CerebrumUser CerebrumUser)
        {
            var context = new OrgContext();
            context.key = "parameters";
            context.resource.resourceType = "Parameters";
            //context.resource.id = "7336f5e9-b484-4e2e-9669-9491dd99ce3f";
            context.resource.id = Guid.NewGuid().ToString();
            context.resource.meta.versionId = null;
            context.resource.meta.profile.Add("http://ehealthontario.ca/fhir/StructureDefinition/ca-on-cms-profile-Parameters|1.0.0");

            context.resource.identifier = null;

            context.resource.alias = null;
            context.resource._alias = null;
            context.resource.telecom = null;

            context.resource.address = null;
            context.resource.text = null;
            context.resource.status = null;
            context.resource.operationalStatus = null;
            context.resource.managingOrganization = null;

            var parameter = new OrgParameter();
            parameter.name = "appLanguage";
            parameter.valueString = null;
            parameter.valueCoding.code = CerebrumUser.Language;
            parameter.valueCoding.system = "urn:ietf:bcp:47";

            context.resource.parameter.Add(parameter);
            context.resource.type = null;

            _event.Event.context.Add(context);
        }

        public string CreateLogParagraph(string serviceName, string action, string status, string content, string cmsUniqueId, Security.CerebrumUser CerebrumUser)
        {
            string logText = $@"Service: {serviceName} | Action: {action} | DateTime: {DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm")} | User: {CerebrumUser.UserId.ToString()} | Status: {status} | Content: {content} | CMS Unique ID: {cmsUniqueId}";
            return logText;
        }
    }
}