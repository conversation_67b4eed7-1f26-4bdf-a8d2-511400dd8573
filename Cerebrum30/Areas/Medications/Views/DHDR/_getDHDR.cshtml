﻿@using Cerebrum.DHDR.Services.Models
@using Cerebrum.ViewModels.MedicationDespense
@using Cerebrum.ViewModels.Patient
@using Cerebrum.ViewModels.Medications
@model VMPatientMedGetDHDR<DrugDispenseSummary, DrugDispense<Patient>, Patient>
@functions{
    public string formatDate(string arg)
    {
// return String.IsNullOrEmpty(arg) ? arg : (Convert.ToDateTime(arg)).ToString("MM/dd/yyyy"); 
    }
    public string getColor(int mod, string strClass)
    {
        if (string.IsNullOrEmpty(strClass))
        {
@* return "white"; *@
        }

        {
@* return "lightblue"; *@

        {
@* return "lightcyan"; *@

        else
        {
@* return "lightgreen"; *@

    public bool IsShowColumn(bool match1, bool match2, bool match3, bool match4, bool match5)
    {
@* return match1 && match2 && match3 && match4 && match5; *@

@{

    {
        try
        {

            {

            {

        catch (Exception ex)
        {
@* Cerebrum.BLL.Utility.UtilityHelper.WriteEconsultError(ex, CerebrumUser.UserId); *@
@* throw; *@

    {

    {

    }

    if (Model?.DrugDispenseSummary?.DrugDispenseList?.Count > 0)
    {
        try
        {

        catch (Exception ex)
        {
@* Cerebrum.BLL.Utility.UtilityHelper.WriteEconsultError(ex, CerebrumUser.UserId); *@
@* throw; *@

<link href="~/Areas/Medications/Content/get-dhdr.css" rel="stylesheet" />
<script src="~/Areas/Medications/Scripts/get-dhdr.js"></script>

<input type="hidden" id="hdshow-dhdr-disclaimer" value="@showDhdrDisclaimer.ToString().ToLower()" />
<div id="div-dhdr-disclaimer" class="row printNot">
    <div class="col-md-9" style="margin-left:20px; margin-top:8px;margin-bottom:8px; font-size:11px;">
        <span id="span-dhdr-disclaimer" class="text-danger">Warning:Limited to Drug and Pharmacy Service Information available in the Digital Health Drug Repository (DHDR) Service. To ensure a Best Possible Medication History, please review this information with the patient/family and use other available sources of medication information in addition to the DHDR EHR Service. </span>
        <span class="text-danger hidden-print printNot">For more details on the available in the DHDR EHR Service, please <a class="text-nowrap" href="https://www.forms.ssb.gov.on.ca/mbs/ssb/forms/ssbforms.nsf/FormDetail?OpenForm&ACT=RDR&TAB=PROFILE&SRCH=&ENV=WWE&TIT=5056-87E&NO=014-5056-8" target="_blank">click here</a></span>
    </div>
    <div class="col-md-2 hidden-print">
        <input type="button" value="Suppress Disclaimer" class="btn btn-info pull-right btn-sm" id="btn-suppress-disclaimer" />
    </div>
</div>
<div class="container" style="width:100% !important;">
    @{
        try
        {
            if (@Model.IsValid)
            {
                <div class="panel panel-default">
                    <div class="panel-heading panel-heading-nav printNot">
                        <ul class="nav nav-pills nav-justified dhdr-nav">
                            <li role="presentation" class="active">
                                <a href="#one" aria-controls="one" role="tab" data-toggle="tab">Summary</a>
                            </li>
                            <li role="presentation">
                                <a href="#two" aria-controls="two" role="tab" data-toggle="tab">Comparative</a>
                            </li>
                        </ul>
                    </div>
                    <div class="panel-body">
                        <div class="tab-content">
                            <div role="tabpanel" class="tab-pane fade in active" id="one">
                                <div>
                                    @if (!(@Model.DrugDispenseSummary != null && @Model.DrugDispenseSummary.Patient != null))
                                    {
                                        if (!showDrugsAndServices)
                                        {
                                            <div class="alert alert-danger" role="alert">
                                                @* TODO: Fix OtherResponse property access *@
                                                @Html.Raw(@Model.DrugDispenseSummary.OtherResponse.moreInformation.Replace("\n", "<br />"))
                                            </div>
                                        }
                                        if (accessBlockedByPatient)
                                        {
                                            <button class="btn btn-primary" type="button" onclick="unblock();">Patient Unblocks Access</button>
                                            <button class="btn btn-danger" type="button" onclick="refuse();">Patient Refuses Access</button>
                                            <button class="btn btn-default" type="button" onclick="cancel();">Cancel</button>
                                        }

                                </div>
                                @if (showDrugsAndServices)
                                {
                                    if (showDrugs)
                                    {
                                        <span class="pull-right printNot">show more data <input type="checkbox" id="chkShowMoreData"></span>
                                    }

                                    <h5><span><strong>DHDR Drugs</strong></span></h5>
                                    if (showOperationOutcomeIssues)
                                    {*@ foreach (var item in @Model.DrugDispenseSummary?.OperationOutcomeIssues.Where(c => c.code != "NotFound").ToList())
                                        {
                                            <span class="text-danger" style="font-size:11px;">
                                                @* @item.details.text*@
                                            </span>
                                            <br />
                                        }

                                    <span style="font-size:small;" id="span-dhdr-qty-result">@qtyResult</span>

                                    if (showDrugs)
                                    {
                                        <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                        <label for="dhdr-drugs-filter" class="printNot">Filter by:</label>
                                        <select id="ddldhdr-drugs-filter" class="dhdr-dropdown-common printNot">
                                            <option value="0">No filter</option>
                                            <option value="1">Generic Name</option>
                                            <option value="2">Brand Name</option>
                                            <option value="3">Dispensed Date</option>
                                            <option value="4">Pharmacy Name</option>
                                            <option value="5">Prescriber Name</option>
                                            <option value="6">Therapeutic Class</option>
                                        </select> @:&nbsp;
                                        @Html.DropDownList("listDrugDispenseGenericName", new SelectList(listDrugDispenseGenericName), "Please select Generic Name", new { @class = "filter-dhdr-dropdown dhdr-dropdown-common" })
                                        @Html.DropDownList("listDrugDispenseBrandName", new SelectList(listDrugDispenseBrandName), "Please select Brand Name", new { @class = "filter-dhdr-dropdown dhdr-dropdown-common" })
                                        @Html.DropDownList("listDrugDispenseDispensedDate", new SelectList(listDrugDispenseDispensedDate), "Please select Dispensed Date", new { @class = "filter-dhdr-dropdown dhdr-dropdown-common" })
                                        @Html.DropDownList("listDrugDispensePharmacyName", new SelectList(listDrugDispensePharmacyName), "Please select Pharmacy Name", new { @class = "filter-dhdr-dropdown dhdr-dropdown-common" })
                                        @Html.DropDownList("listDrugDispensePrescriberName", new SelectList(listDrugDispensePrescriberName), "Please select Prescriber", new { @class = "filter-dhdr-dropdown dhdr-dropdown-common" })
                                        @Html.DropDownList("listDrugDispenseTherapeuticClass", new SelectList(listDrugDispenseTherapeuticClass), "Please select Therapeutic Class", new { @class = "filter-dhdr-dropdown dhdr-dropdown-common" })
                                        <div class="row printNot">
                                            <div class="form-group form-group-sm col-sm-11">
                                                @Html.Raw(patient.PatientFullInfo)
                                            </div>
                                            <div class="form-group form-group-sm col-sm-1">
                                                <button id="print-report-summary" data-show-more="2" data-patient-id="@patient.PatientId" class="printNot btn btn-default btn-sm btn-primary" data-patient-info="@patient.PatientFullInfo">Print</button>
                                            </div>
                                        </div>
                                        <div class="top-scroll-container printNot">
                                            <div>
@* &nbsp; *@
                                            </div>
                                        </div>
                                        <div class="bottom-scroll-container">
                                            <table class="table table-striped-medication" id="dhdr-drug-table-summary-view">
                                                <thead>
                                                    <tr>
                                                        <th rowspan="2" class="@classHideColumn"></th>
                                                        <th rowspan="2" data-name="group" class="@classShowGroup"></th>
                                                        <th rowspan="2" class="text-center">
                                                            <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="DispensedDate">
                                                                Dispensed Date
                                                            </a><span class="span-sort-order" id="span-DispensedDate-up">&#9650;</span><span class="span-sort-order" id="span-DispensedDate-down">&#9660;</span>
                                                        </th>
                                                        <th rowspan="2" class="text-center">Pickup Date</th>
                                                        <th colspan="4" class="text-center">Dispensed Drug</th>
                                                        <th rowspan="2" class="text-center">Amount Of Medication per Dose</th>
                                                        <th rowspan="2" class="text-center">Freq</th>
                                                        <th colspan="2" class="text-center">Dispensed Quantity</th>
                                                        <th rowspan="2" class="text-center">Estimated Days Supply</th>
                                                        <th colspan="2" class="text-center">Remaining</th>
                                                        <th colspan="3" class="text-center">Prescriber</th>
                                                        <th colspan="2" class="text-center">Dispensing Pharmacy</th>
                                                        <th rowspan="2" class="show-more-data text-center">DIN</th>
                                                        <th class="show-more-data text-center" colspan="2">Therapeutic</th>
                                                        <th rowspan="2" class="show-more-data text-center">Rx Number</th>
                                                        <th class="show-more-data text-center" colspan="2">Pharmacist</th>
                                                        <th class="show-more-data text-center" colspan="2">Pharmacy</th>
                                                        <th class="show-more-data text-center" colspan="2">Prescriber</th>
                                                        <th rowspan="2" class="show-more-data text-center">Medical Condition</th>
                                                        <th class="text-center @classHideColumn" rowspan="2">
                                                            <span class="text-danger">Demorgaphics Mismatch</span>
                                                        </th>
                                                        <th rowspan="2" class="text-center">Rx Count<br><a href="javascript:void(null)" onclick="return false" data-expand="1" id="href-collapse-expand">Expand&nbsp;All</a></th>
                                                        <th rowspan="2" data-name="group"></th>
                                                    </tr>
                                                    <tr>
                                                        <th class="text-center text-nowrap">
                                                            <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="GenericName">
                                                                Generic Name
                                                            </a><span id="span-GenericName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-GenericName-up">&#9650;</span><span class="span-sort-order" id="span-GenericName-down">&#9660;</span>
                                                        </th>
                                                        <th class="text-center text-nowrap">
                                                            <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="BrandName">
                                                                Brand&nbsp;Name
                                                            </a><span id="span-BrandName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-BrandName-up">&#9650;</span><span class="span-sort-order" id="span-BrandName-down">&#9660;</span>
                                                        </th>
                                                        <th class="text-center">Strength</th>
                                                        <th class="text-center">Dosage Form</th>
                                                        <th class="text-center">Value</th>
                                                        <th class="text-center">Unit</th>
                                                        <th class="text-center">Refills</th>
                                                        <th class="text-center">Qty</th>
                                                        <th class="text-center">First Name</th>
                                                        <th class="text-center">
                                                            <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="PrescriberName">
                                                                Last
                                                            </a>&nbsp;&nbsp;&nbsp;&nbsp;
                                                            <span class="text-nowrap">
                                                                <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="PrescriberName">
                                                                    Name
                                                                </a><span id="span-PrescriberName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-PrescriberName-up">&#9650;</span><span class="span-sort-order" id="span-PrescriberName-down">&#9660;</span>
                                                            </span>
                                                        </th>
                                                        <th class="text-center">Phone Number</th>
                                                        <th class="text-center">
                                                            <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="PharmacyName">
                                                                Name
                                                            </a><span id="span-PharmacyName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-PharmacyName-up">&#9650;</span><span class="span-sort-order" id="span-PharmacyName-down">&#9660;</span>
                                                        </th>
                                                        <th class="text-center">Fax Number</th>
                                                        <th class="show-more-data text-center">class</th>
                                                        <th class="show-more-data text-center">sub-class</th>
                                                        <th class="show-more-data text-center"> Name</th>
                                                        <th class="show-more-data text-center"> License</th>
                                                        <th class="show-more-data text-center">Id</th>
                                                        <th class="show-more-data text-center">Phone</th>
                                                        <th class="show-more-data text-center">Id</th>
                                                        <th class="show-more-data text-center text-nowrap">Professional Id</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (DrugDispense<Patient> each in listDrugDispense.OrderBy(x => x.Group).ThenByDescending(x => x.DispensedDate))
                                                    {

                                                        if (!each.Show)
                                                        {

                                                        }
                                                        else
                                                        {

                                                            if (rxCount > 1)
                                                            {

@* groupNumber++; *@
@* dataGroupColor++; *@
                                                            }

                                                        if (groupCount > 1)
                                                        {

                                                        }

                                                        if (Convert.ToString(each.MedicationCondition).Length > 100)
                                                        {

                                                        }

                                                        {

                                                        <tr @strClass @dataGroup>
                                                            @if (!isShowColumn)
                                                            {
                                                                <td style="background-color: red; width: 1px;" class="padding0-vertical-align-middle @classHideColumn" title="Demorgaphics Mismatch">
                                                                    <span class="group group-font-10" style="color:white">Mismatch&nbsp;&nbsp;</span>
                                                                </td>
                                                            }
                                                            else
                                                            {
                                                                <td style="width: 1px;" class="padding0-vertical-align-middle @classTdGroupColor @classHideColumn"><span class="group group-font-10"></span></td>

                                                            <td class="padding0-vertical-align-middle @classTdGroupColor @classShowGroup" data-name="group"><span class="group group-font-10">@textGroup</span></td>
                                                            <td class="text-nowrap @classTdNotGroupColor"><strong>@each.DispensedDate</strong></td>
                                                            <td class="text-nowrap @classTdNotGroupColor">@each.PickupDate</td>
                                                            <td @withClassTdNotGroupColor>@each.GenericNameOfTheDispensedDrug</td>
                                                            <td @withClassTdNotGroupColor>@each.BrandNameOfTheDispensedDrug</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.DispensedDrugStrength</td>
                                                            <td class="text-nowrap text-center @classTdNotGroupColor">@each.DrugDosageForm</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.AmountOfMedicationperDose</td>
                                                            <td class="text-center text-nowrap @classTdNotGroupColor">@each.Frequency</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.DispensedQuantityValue</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.DispensedQuantityUnit</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.EstimatedDaysSupply</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.RefillsRemaining</td>
                                                            <td class="text-center @classTdNotGroupColor">@each.QuantityRemaining</td>
                                                            <td @withClassTdNotGroupColor>@each.PrescriberFirstName</td>
                                                            <td @withClassTdNotGroupColor>@each.PrescriberLastName</td>
                                                            <td class="text-nowrap @classTdNotGroupColor">@each.PrescriberPhoneNumber</td>
                                                            <td @withClassTdNotGroupColor>@each.DispensingPharmacy</td>
                                                            <td class="text-nowrap @classTdNotGroupColor">@each.DispensingPharmacyFaxNumber</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.DIN</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.TherapeuticClass</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.TherapeuticSubClass</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.RxNumber</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.PharmacistName</td>
                                                            <td class="show-more-data text-center @classTdNotGroupColor">@each.PharmacistLicense</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.PharmacyId</td>
                                                            <td class="show-more-data text-nowrap @classTdNotGroupColor">@each.DispensingPharmacyPhoneNumber</td>
                                                            <td class="show-more-data text-center @classTdNotGroupColor">@each.PrescriberLicense</td>
                                                            <td class="show-more-data @classTdNotGroupColor">@each.PrescriberDescription</td>
                                                            <td class="@classTdNotGroupColor show-more-data @showMedCond">
                                                                <div @strMedCondClass>
                                                                    @Html.Raw(Html.Encode(Convert.ToString(each.MedicationCondition)).Replace("\n", "<br style=\"display: block; content: ''; margin-top: 4px;\" />"))
                                                                </div>
                                                            </td>
                                                            <td class="@classTdNotGroupColor @classHideColumn">
                                                                @if (!each.Compare.LastName.Item3)
                                                                {
                                                                    <div class="text-danger">EMR&nbsp;&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item1</div>
                                                                    <div class="text-danger">DHDR&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item2</div>
                                                                }
                                                                @if (!each.Compare.FirstName.Item3)
                                                                {
                                                                    <div class="text-danger">EMR&nbsp;&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item1</div>
                                                                    <div class="text-danger">DHDR&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item2</div>

                                                                @if (!each.Compare.Gender.Item3)
                                                                {
                                                                    <div class="text-danger">EMR&nbsp;&nbsp;Gender:&nbsp;@each.Compare.Gender.Item1</div>
                                                                    <div class="text-danger">DHDR&nbsp;Gender:&nbsp;@each.Compare.Gender.Item2</div>

                                                                @if (!each.Compare.BirthDate.Item3)
                                                                {
                                                                    <div class="text-danger">EMR&nbsp;&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item1.ToString())</div>
                                                                    <div class="text-danger">DHDR&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item2.ToString())</div>

                                                                @if (!each.Compare.HealthNumber.Item3)
                                                                {
                                                                    <div class="text-danger">EMR&nbsp;&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item1</div>
                                                                    <div class="text-danger">DHDR&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item2</div>

                                                            </td>
                                                            @if (rxCount > 0)
                                                            {
                                                                <td class="text-center @classTdNotGroupColor"><a>@rxCount</a></td>
                                                            }
                                                            else
                                                            {
                                                                <td @withClassTdNotGroupColor></td>

                                                            <td class="padding0-vertical-align-middle @classTdGroupColor" data-name="group"><span class="group group-font-10">@textGroup</span></td>
                                                        </tr>

                                                </tbody>
                                            </table>
                                        </div>
                                        <br />

                                    <div id="dhdr-pharmacy-service" class="dhdr-horizontal-scroll">
                                        <h5><strong>DHDR Pharmacy Service</strong></h5>
                                        <span style="font-size: small;">@serviceQtyResult</span>
                                        @if (listPharmacyServices.Count > 0)
                                        {

                                            <table class="table table-striped-medication">
                                                <thead>
                                                    <tr>
                                                        <th></th>
                                                        <th class="text-center">Packaged Date</th>
                                                        <th class="text-center">Pickup Date</th>
                                                        <th class="text-center">Pharmacy Service Type</th>
                                                        <th class="text-center">Pharmacy Service Description</th>
                                                        <th class="text-center">Pharmacy Name</th>
                                                        <th class="text-center">Pharmacist Name</th>
                                                        <th class="text-center">Pharmacy Fax</th>
                                                        <th class="text-center">Service Count<br> <a href="javascript:void(null)" onclick="return false" data-expand="1" id="href-collapse-expand-service">Expand&nbsp;All</a></th>
                                                        <th class="text-center @classHideColumnPharmacyService">
                                                            <span class="text-danger">Demorgaphics Mismatch</span>
                                                        </th>
                                                        <th></th>
                                                    </tr>
                                                </thead>

                                                @foreach (var each in listPharmacyServices.OrderBy(x => x.Group).ThenByDescending(x => x.DispensedDate))
                                                {

                                                    if (!each.Show)
                                                    {

                                                    }
                                                    else
                                                    {

                                                        if (serviceCount > 1)
                                                        {

@* groupNumber++; *@
@* dataGroupColor++; *@
                                                        }

                                                    if (groupCount > 1)
                                                    {

                                                    }

                                                    {

                                                    <tr @strClass @dataGroup>
                                                        <td class="padding0-vertical-align-middle @classTdGroupColor"><span class="group group-font-10">@textGroup</span></td>
                                                        <td class="text-nowrap @classTdNotGroupColor"><strong>@each.DispensedDate</strong></td>
                                                        <td class="text-nowrap @classTdNotGroupColor">@each.PickupDate</td>
                                                        <td @withClassTdNotGroupColor>@each.BrandNameOfTheDispensedDrug</td>
                                                        <td @withClassTdNotGroupColor>@each.GenericNameOfTheDispensedDrug</td>
                                                        <td @withClassTdNotGroupColor>@each.DispensingPharmacy</td>
                                                        <td @withClassTdNotGroupColor>@each.PharmacistName</td>
                                                        <td @withClassTdNotGroupColor>@each.DispensingPharmacyFaxNumber</td>
                                                        @if (serviceCount > 0)
                                                        {
                                                            <td class="text-center @classTdNotGroupColor"><a>@serviceCount</a></td>
                                                        }
                                                        else
                                                        {
                                                            <td @withClassTdNotGroupColor></td>

                                                        <td class="@classHideColumnPharmacyService">
                                                            @if (!each.Compare.LastName.Item3)
                                                            {
                                                                <div class="text-danger">EMR&nbsp;&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item1</div>
                                                                <div class="text-danger">DHDR&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item2</div>
                                                            }
                                                            @if (!each.Compare.FirstName.Item3)
                                                            {
                                                                <div class="text-danger">EMR&nbsp;&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item1</div>
                                                                <div class="text-danger">DHDR&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item2</div>

                                                            @if (!each.Compare.Gender.Item3)
                                                            {
                                                                <div class="text-danger">EMR&nbsp;&nbsp;Gender:&nbsp;@each.Compare.Gender.Item1</div>
                                                                <div class="text-danger">DHDR&nbsp;Gender:&nbsp;@each.Compare.Gender.Item2</div>

                                                            @if (!each.Compare.BirthDate.Item3)
                                                            {
                                                                <div class="text-danger">EMR&nbsp;&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item1.ToString())</div>
                                                                <div class="text-danger">DHDR&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item2.ToString())</div>

                                                            @if (!each.Compare.HealthNumber.Item3)
                                                            {
                                                                <div class="text-danger">EMR&nbsp;&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item1</div>
                                                                <div class="text-danger">DHDR&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item2</div>

                                                        </td>
                                                        <td class="padding0-vertical-align-middle @classTdGroupColor"><span class="group group-font-10">@textGroup</span></td>
                                                    </tr>

                                            </table>

                                    </div>

                            </div>
                            <div role="tabpanel" class="tab-pane fade" id="two">
                                <div class="row printNot">
                                    <div class="form-group form-group-sm col-sm-11">
                                        @Html.Raw(patient.PatientFullInfo)
                                    </div>
                                    <div class="form-group form-group-sm col-sm-1">
                                        <button id="print-report-comparative" data-patient-id="@patient.PatientId" class="printNot btn btn-default btn-sm btn-primary" data-patient-info="@patient.PatientFullInfo">Print</button>
                                    </div>
                                </div>

                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="col-lg-6 td-medication-despense-view" style="padding-left:1px; padding-right:1px;">
                                            <div class="panel panel-default">
                                                @if (showDrugsAndServices)
                                                {
                                                    <div class="panel-heading"><span><strong>DHDR Drugs</strong></span></div>
                                                    if (showOperationOutcomeIssues)
                                                    {

                                                        {
                                                            <span class="text-danger" style="font-size:11px;">
                                                                @* @item.details.text*@
                                                            </span>
                                                            <br />
                                                        }

                                                    <span style="font-size:small;" id="span-dhdr-comparative-qty-result">@qtyResult</span>
                                                    if (showDrugs)
                                                    {
                                                        <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                        <label for="dhdr-drugs-filter-comparative">Filter by:</label>
                                                        <select id="ddldhdr-drugs-filter-comparative" class="dhdr-dropdown-common">
                                                            <option value="0">No filter</option>
                                                            <option value="1">Generic Name</option>
                                                            <option value="2">Brand Name</option>
                                                            <option value="3">Dispensed Date</option>
                                                            <option value="4">Pharmacy Name</option>
                                                            <option value="5">Prescriber Name</option>
                                                        </select> @:&nbsp;
                                                        @Html.DropDownList("listDrugDispenseGenericName-comparative", new SelectList(listDrugDispenseGenericName), "Please select Generic Name", new { @class = "filter-dhdr-dropdown-comparative dhdr-dropdown-common" })
                                                        @Html.DropDownList("listDrugDispenseBrandName-comparative", new SelectList(listDrugDispenseBrandName), "Please select Brand Name", new { @class = "filter-dhdr-dropdown-comparative dhdr-dropdown-common" })
                                                        @Html.DropDownList("listDrugDispenseDispensedDate-comparative", new SelectList(listDrugDispenseDispensedDate), "Please select Dispensed Date", new { @class = "filter-dhdr-dropdown-comparative dhdr-dropdown-common" })
                                                        @Html.DropDownList("listDrugDispensePharmacyName-comparative", new SelectList(listDrugDispensePharmacyName), "Please select Pharmacy Name", new { @class = "filter-dhdr-dropdown-comparative dhdr-dropdown-common" })
                                                        @Html.DropDownList("listDrugDispensePrescriberName-comparative", new SelectList(listDrugDispensePrescriberName), "Please select Prescriber", new { @class = "filter-dhdr-dropdown-comparative dhdr-dropdown-common" })

                                                else
                                                {
                                                    <div class="alert alert-danger" role="alert">
                                                        @* TODO: Fix OtherResponse property access * @Html.Raw(@Model.DrugDispenseSummary.OtherResponse.moreInformation.Replace("\n", "<br />"))
                                                    </div>
                                                }
                                                <div class="">
                                                    @if (showDrugsAndServices)
                                                    {
                                                        <div class="dhdr-horizontal-scroll dhdr-vertical-scroll">
                                                            @if (showDrugs)
                                                            {
                                                                <table id="medication-despense-view" class="table-medication-despense-view">
                                                                    <thead>
                                                                        <tr>
                                                                            <th rowspan="2" class="text-center">
                                                                                <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="DispensedDate">
                                                                                    Dispensed Date
                                                                                </a><span class="span-sort-order" id="span-DispensedDate-up">&#9650;</span><span class="span-sort-order" id="span-DispensedDate-down">&#9660;</span>
                                                                            </th>
                                                                            <th rowspan="2" class="text-center">Pickup Date</th>
                                                                            <th colspan="4" class="text-center">Dispensed Drug</th>
                                                                            <th rowspan="2" class="text-center">Amount Of Medication per Dose</th>
                                                                            <th rowspan="2" class="text-center">Freq</th>
                                                                            <th colspan="2" class="text-center">Dispensed Quantity</th>
                                                                            <th rowspan="2" class="text-center">Estimated Days Supply</th>
                                                                            <th colspan="2" class="text-center">Remaining</th>
                                                                            <th colspan="3" class="text-center">Prescriber</th>
                                                                            <th colspan="2" class="text-center">Dispensing Pharmacy</th>
                                                                            <th class="text-center @classHideColumn" rowspan="2">
                                                                                <span class="text-danger">Demorgaphics Mismatch</span>
                                                                            </th>
                                                                        </tr>
                                                                        <tr>
                                                                            <th class="text-center text-nowrap">
                                                                                <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="GenericName">
                                                                                    Generic Name
                                                                                </a><span id="span-GenericName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-GenericName-up">&#9650;</span><span class="span-sort-order" id="span-GenericName-down">&#9660;</span>
                                                                            </th>
                                                                            <th class="text-center text-nowrap">
                                                                                <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="BrandName">
                                                                                    Brand Name
                                                                                </a><span id="span-BrandName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-BrandName-up">&#9650;</span><span class="span-sort-order" id="span-BrandName-down">&#9660;</span>
                                                                            </th>
                                                                            <th class="text-center">Strength</th>
                                                                            <th class="text-center">Dosage Form</th>
                                                                            <th class="text-center">Value</th>
                                                                            <th class="text-center">Unit</th>
                                                                            <th class="text-center">Refills</th>
                                                                            <th class="text-center">Qty</th>
                                                                            <th class="text-center">First Name</th>
                                                                            <th class="text-center">
                                                                                <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="PrescriberName">
                                                                                    Last
                                                                                </a>&nbsp;&nbsp;&nbsp;&nbsp;
                                                                                <span class="text-nowrap">
                                                                                    <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="PrescriberName">
                                                                                        Name
                                                                                    </a><span id="span-PrescriberName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-PrescriberName-up">&#9650;</span><span class="span-sort-order" id="span-PrescriberName-down">&#9660;</span>
                                                                                </span>
                                                                            </th>
                                                                            <th class="text-center">Phone Number</th>
                                                                            <th class="text-center text-nowrap">
                                                                                <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="PharmacyName">
                                                                                    Name
                                                                                </a><span id="span-PharmacyName-empty-space" class="span-empty-space">&nbsp;&nbsp;&nbsp;</span><span class="span-sort-order" id="span-PharmacyName-up">&#9650;</span><span class="span-sort-order" id="span-PharmacyName-down">&#9660;</span>
                                                                            </th>
                                                                            <th class="text-center">Fax Number</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        @foreach (DrugDispense<Patient> each in listDrugDispense.OrderByDescending(x => x.DispensedDate))
                                                                        {
                                                                            <tr>
                                                                                <td class="text-nowrap"><strong>@each.DispensedDate</strong></td>
                                                                                <td class="text-nowrap">@each.PickupDate</td>
                                                                                <td>@each.GenericNameOfTheDispensedDrug</td>
                                                                                <td>@each.BrandNameOfTheDispensedDrug</td>
                                                                                <td class="text-center">@each.DispensedDrugStrength</td>
                                                                                <td class="text-nowrap text-center">@each.DrugDosageForm</td>
                                                                                <td>@each.AmountOfMedicationperDose</td>
                                                                                <td class="text-center text-nowrap">@each.Frequency</td>
                                                                                <td class="text-center">@each.DispensedQuantityValue</td>
                                                                                <td class="text-center">@each.DispensedQuantityUnit</td>
                                                                                <td class="text-center">@each.EstimatedDaysSupply</td>
                                                                                <td class="text-center">@each.RefillsRemaining</td>
                                                                                <td class="text-center">@each.QuantityRemaining</td>
                                                                                <td>@each.PrescriberFirstName</td>
                                                                                <td>@each.PrescriberLastName</td>
                                                                                <td class="text-nowrap">@each.PrescriberPhoneNumber</td>
                                                                                <td>@each.DispensingPharmacy</td>
                                                                                <td class="text-nowrap">@each.DispensingPharmacyFaxNumber</td>
                                                                                <td class="@classHideColumn">
                                                                                    @if (!each.Compare.LastName.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item2</div>
                                                                                    }
                                                                                    @if (!each.Compare.FirstName.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item2</div>

                                                                                    @if (!each.Compare.Gender.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;Gender:&nbsp;@each.Compare.Gender.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;Gender:&nbsp;@each.Compare.Gender.Item2</div>

                                                                                    @if (!each.Compare.BirthDate.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item1.ToString())</div>
                                                                                        <div class="text-danger">DHDR&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item2.ToString())</div>

                                                                                    @if (!each.Compare.HealthNumber.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item2</div>

                                                                                </td>
                                                                            </tr>

                                                                    </tbody>
                                                                </table>

                                                        </div>
                                                        <div style="padding-left:2px;">
                                                            @if (showDrugs)
                                                            {
                                                                <br />
                                                            }
                                                            <h5><strong>DHDR Pharmacy Service</strong></h5>
                                                            <span style="font-size:small;">@serviceQtyResult</span>
                                                            <div class="dhdr-horizontal-scroll dhdr-vertical-scroll">

                                                                @if (listPharmacyServices.Count > 0)
                                                                {
                                                                    <table class="table table-striped-medication" style="max-width: 640px;">
                                                                        <thead>
                                                                            <tr>
                                                                                <th rowspan="2" class="text-center">Packaged Date</th>
                                                                                <th rowspan="2" class="text-center">Pickup Date</th>
                                                                                <th colspan="2" class="text-center">Pharmacy Service </th>
                                                                                <th colspan="2" class="text-center">Pharmacy </th>
                                                                                <th rowspan="2" class="text-center">Pharmacist Name</th>
                                                                                <th rowspan="2" class="text-center @classHideColumnPharmacyService">
                                                                                    <span class="text-danger">Demorgaphics Mismatch</span>
                                                                                </th>
                                                                            </tr>
                                                                            <tr>
                                                                                <th class="text-center">Type</th>
                                                                                <th class="text-center">Description</th>
                                                                                <th class="text-center">Name</th>
                                                                                <th class="text-center">Fax</th>
                                                                            </tr>
                                                                        </thead>

                                                                        @foreach (var each in listPharmacyServices.OrderByDescending(x => x.DispensedDate))
                                                                        {
                                                                            <tr>
                                                                                <td class="text-nowrap"><strong>@each.DispensedDate</strong></td>
                                                                                <td class="text-nowrap">@each.PickupDate</td>
                                                                                <td>@each.BrandNameOfTheDispensedDrug</td>
                                                                                <td>@each.GenericNameOfTheDispensedDrug</td>
                                                                                <td>@each.DispensingPharmacy</td>
                                                                                <td class="text-nowrap">@each.DispensingPharmacyFaxNumber</td>
                                                                                <td>@each.PharmacistName</td>
                                                                                <td class="@classHideColumnPharmacyService">
                                                                                    @if (!each.Compare.LastName.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;L.&nbsp;Name:&nbsp;@each.Compare.LastName.Item2</div>
                                                                                    }
                                                                                    @if (!each.Compare.FirstName.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;F.&nbsp;Name:&nbsp;@each.Compare.FirstName.Item2</div>

                                                                                    @if (!each.Compare.Gender.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;Gender:&nbsp;@each.Compare.Gender.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;Gender:&nbsp;@each.Compare.Gender.Item2</div>

                                                                                    @if (!each.Compare.BirthDate.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item1.ToString())</div>
                                                                                        <div class="text-danger">DHDR&nbsp;DoB:&nbsp;@formatDate(each.Compare.BirthDate.Item2.ToString())</div>

                                                                                    @if (!each.Compare.HealthNumber.Item3)
                                                                                    {
                                                                                        <div class="text-danger">EMR&nbsp;&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item1</div>
                                                                                        <div class="text-danger">DHDR&nbsp;OHIP#:&nbsp;@each.Compare.HealthNumber.Item2</div>

                                                                                </td>
                                                                            </tr>

                                                                    </table>

                                                            </div>
                                                        </div>

                                                </div>
                                            </div>

                                        </div>
                                        <div class="col-lg-6" style="padding-left:1px; padding-right:1px;">
                                            <div class="panel panel-default">
                                                <div class="panel-heading"><span><strong>EMR Local Data (Active medications)</strong></span><span class="pull-right">Hide DHDR Info <input type="checkbox" id="chkHideDhdrInfo"></span></div>
                                                <div class="panel-body">
                                                    @if (showOperationOutcomeIssues)
                                                    {
                                                        <br />
                                                    }
                                                    <span style="font-size:small;" id="span-emr-result-returned"></span>
                                                    <div class="dhdr-horizontal-scroll dhdr-vertical-scroll">
                                                        @* TODO: Convert Html.Action to ViewComponent or controller call *@
                                                        <div class="alert alert-info">GetPatientComparativeMedList functionality temporarily disabled during .NET 8 migration</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

            else
            {
                <div class="text-danger">
                    @Html.Raw(Html.Encode(@Model.ErrorMessage).Replace("\n", "<br />"))
                    @if (@Model.ErrorMessage.Contains("Session Timeout! OneId token expired."))
                    {
                        <br /><a href="@retRedirect">Login to OneId</a>
                    }
                </div>

        catch (Exception ex)
        {
@* Cerebrum.BLL.Utility.UtilityHelper.WriteEconsultError(ex, CerebrumUser.UserId); *@
@* throw; *@

</div>
@*Behrooz, please move all of js and css to js and css file*@
<script type="text/javascript">

    function refuse() {
@* log("success", "Patient @Model.EMRPatient.PatientId refused consent.", "N/A"); *@

    }

    function cancel() {
@* log("success", "Consent process cancelled for patient @Model.EMRPatient.PatientId.", "N/A"); *@

    }

    function log(status, content, hubTopic) {

    function unblock() {

@* console.log(pcoi); *@
        function childCheck() {
            if (pcoi.closed) {
@* console.log("Yaroo closed!"); *@
@* clearInterval(timer); *@
                //document.medSearchForm.submit();
@* document.getElementById("btn-med-dhdr-search-2").click(); *@
            }

    function closeModal() {

@* window.close(); *@

</script>

<style type="text/css">
    #container {
@* width: 100%; *@
@* height: 100%; *@
@* top: 0; *@
@* position: absolute; *@
@* visibility: visible; *@
        display: none;@* *@
        background-color: rgba(22,22,22,0.5); / complimenting your modal colors *@/

</style>

<div id="container">
    <div style="margin: 300px auto; background-color:cornsilk; width:400px; height:200px; border-radius:20px; padding:30px;">
        <h3 id="modalMessage">
        </h3>
        <div class="center-block">
            <button type="button" class="btn btn-primary" onclick="closeModal();">OK</button>
        </div>
    </div>
</div>