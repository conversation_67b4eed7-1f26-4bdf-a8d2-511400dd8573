@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.Medications.VMPrescribedSet>

@{ 

}
<div class="content-height500"><!-- panel panel-info  -->
    <div class="panel-heading">
        <h3 class="panel-title">Prescriptions <span class="badge cbadge">@Model.Count()</span> <!-- class="panel-title" -->
        
        @*<spans>(@Model.Count())</spans>*@</h3>
    </div>
    <table id="tbl-prescribedsets" data-total-prescribed="@Model.Count()" class="table ">
        <thead>
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.PrescriptionSetIdStr)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Medications)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.UserFullName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.CreatedByUserFullName)
                </th>
                @*<th>)*@
                <th>
                    @Html.DisplayNameFor(model => model.DateEntered)
                </th>
                <th>
                    Notes / Prints @* @Html.DisplayNameFor(model => model.Prints) *@
                </th>
                <th></th>
            </tr>
        </thead>
            @foreach (var item in Model)
            {
                <tr>
                    <td>

                        @Html.DisplayFor(modelItem => item.PrescriptionSetIdStr)
                    </td>
                    <td style="width:40%;">
                        @Html.DisplayFor(modelItem => item.Medications)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.UserFullName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.CreatedByUserFullName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.DatePrescribed)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.DateEntered)
                    </td>
                    <td class="td-total-prints">
                        @{var totalPrints = item.Prints.Count();}
                        @if (item.Prints.Any())
                        {
                            var printCount = 1;
                            <div class="btn-popover-container">
                                <span class="text-primary popover-btn c-pointer">@totalPrints</span>
                                <div class="btn-popover-title">
                                    Notes
                                </div>
                                <div class="btn-popover-content">
                                    <div style="overflow:auto;font-size:10px;">
                                        <table class="table table-bordered table-condensed">
                                            <tr>
                                                <th></th>
                                                <th>Name</th>
                                                <th>Notes</th>
                                                <th>Date</th>
                                            </tr>
                                            @foreach (var p in item.Prints)
                                            {

                                                <tr>
                                                    <td>@printCount</td>
                                                    <td>@p.UserFullName</td>
                                                    <td>@notes</td>
                                                    <td>@p.Date</td>
                                                </tr>
                                                @{ printCount++; }

                                        </table>
                                    </div>
                                </div>

                            </div>

                        else
                        {
                            <span class="text-primary">@totalPrints</span>
                        }
                    </td>
                    <td>
                        <a href="#" class="btn btn-default btn-xs btn-view-prescriptionset" data-load-url="@Url.Action("ViewPrescriptionSet","patientmedications", new { area="medications", prescriptionSetId = item.PrescriptionSetId, patientId = item.PatientId })">View</a>
                        <a href="#" class="btn btn-default btn-xs btn-represcribe" data-load-url="@Url.Action("RePrescribePrescriptions","patientmedications", new { area="medications", prescriptionSetId = item.PrescriptionSetId, patientId = item.PatientId })">Represcribe</a>

                    </td>
                </tr>
            }
</table>
</div>
