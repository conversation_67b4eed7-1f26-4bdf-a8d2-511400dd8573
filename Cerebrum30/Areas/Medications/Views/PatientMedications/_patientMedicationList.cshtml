@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.Medications.VMPatientMedication>

@{

foreach (var item in therGroup)
{

@* drpClasses.Add(lookUp); *@

foreach(var item in treatGroup)
{

@* drpTreatmentTypes.Add(lookUp); *@
}

if (medsForInteractions.Count() > 1)
{
//this is for when we use our interactions database

foreach(var item in medsForInteractions)
{

@* interactionsRequestList.Add(request); *@

//for sorting

<!-- ############## Medication (interactions) ############## -->

@if (interactionsRequestList.Any())
{

{
@Html.AntiForgeryToken()
@Html.Hidden("patientId",patId)

{
@Html.EditorFor(model => interactionsRequestList[i], "VMHiddenInteractionRequest", String.Format("{0}[{1}]", "interactionRequestList", i))
}

<script type="text/javascript">
$(function () {

@* interactionsDiv.data('reload-interactions', @reloadInteractions); *@
@* loadInterActions(); *@
@* }); *@
</script>

} if (!String.IsNullOrWhiteSpace(medicationDins))
{*@ var patId = @Model.First().PatientId;
<div id="interactionsWarning" data-patient-id="@patId" data-medication-ids="@medicationDins" data-reload-interactions="@reloadInteractions"></div>
<script type="text/javascript">
$(function () {
@* setTimeout(function () { loadInterActions(); }, 0); *@

@* interactionsDiv.data('patient-id', @patId); *@
//interactionsDiv.data('medication-ids', '@medicationDins');
@* interactionsDiv.data('medication-ids', ''+JSON.stringify(@Html.Raw(Json.Serialize(interactionsRequestList)))); *@
@* interactionsDiv.data('reload-interactions', @reloadInteractions); *@
@* loadInterActions(); *@
@* }); *@
</script>@* *@
<!-- ############## /interactions ############## -->

<div>
<h3 class="panel-title spacer-top-35">Medications <span id="patientmedication-count" class="badge cbadge">@Model.Count()</span></h3>

<div class="pull-right">@* @Html.DropDownList("drpTherapeuticClasses", new SelectList(drpClasses), "All", new { @class = "form-control" }) *@
@{if (drpClasses.Any()) { @* var allClasses = new Cerebrum.ViewModels.Common.VMLookupItem(); 
    allClasses.Value = "All Classes"; *@

@* drpClasses.Insert(0,allClasses); *@

<div id="drpTherapeuticClasses" class="dropdown hover-menu">
<button class="btn btn-default btn-sm" type="button">
<span id="drpTherapeuticClasses-title">@allClasses.Value</span>
<span class="caret"></span>
</button>
<ul class="dropdown-menu dropdown-menu-right _34534">
@foreach (var item in drpClasses)
{

if (item.Text.Equals(allClasses.Text))
{

//classCountColor = "text-warning";
}
<li class="@active">
<a data-therapeutic-class-value="@item.Value" class="therapeutic-class" href="#">@item.Value <span class="therapeutic-class-count @classCountColor">(@item.Text)</span></a>
</li>

</ul>
</div>

</div>

<div class="pull-left" style="margin-top: 3px; margin-bottom:3px">

@{if (drpTreatmentTypes.Any())
{

@* drpTreatmentTypes.Insert(0, allTypes); *@

<div id="drpTreatmentTypes" style="margin-right:5px;" class="dropdown hover-menu">
<button class="btn btn-default btn-sm" type="button">
<span id="drpTreatmentTypes-title">@allTypes.Value</span>
<span class="caret"></span>
</button>
<ul class="dropdown-menu dropdown-menu-left">
@foreach (var item in drpTreatmentTypes)
{

if (item.Text.Equals(allTypes.Text))
{

}
<li class="@active">
<a data-treatmentype-value="@item.Value" class="therapeutic-class" href="#">@item.Value <span class="therapeutic-class-count @classCountColor">(@item.Text)</span></a>
</li>

</ul>
</div>@* *@

</div>
@if (@Model.Any())
{

<table data-patient-id ="@patientId" data-sort-load-url="@Url.Action("getpatientmedications", "patientmedications", new { area = "medications"})" id="tbl-patientmedications" class="table spacer-top-4">

<tr class="medication-code">
<th class="hidden"></th>
<th class="hidden"></th>
<th class="th-expand-row"></th>
<th>
<input name="selectAllMedications" id="selectAllMedications" type="checkbox" /> All
</th>
<th style="width:20%" class="th-sortable-header" data-sort-key="@sortMedName">
@Html.DisplayNameFor(model => model.MedicationName)
</th>
<th style="width:15%" class="th-sortable-header" data-sort-key="@sortMedClass">
@Html.DisplayNameFor(model => model.Classes)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedForm">
@Html.DisplayNameFor(model => model.Form)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedDose">
@Html.DisplayNameFor(model => model.Dose)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedStrength">
@Html.DisplayNameFor(model => model.Strength)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedSig">
@Html.DisplayNameFor(model => model.SIG)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedRoute">
@Html.DisplayNameFor(model => model.Route)
</th>
<th class="th-sortable-header" data-sort-key="@sortMedLast">
@Html.DisplayNameFor(model => model.DateLastPrescribed)
</th>
<th style="width:8%" class="th-sortable-header" data-sort-key="@sortMedStart">
@Html.DisplayNameFor(model => model.DateStarted)
</th>
<th>
@Html.DisplayNameFor(model => model.DateStartedStr)
</th>
<th style="width:8%">
@Html.DisplayNameFor(model => model.DateExpired)
</th>
<th>
@Html.DisplayNameFor(model => model.DateDiscontinued)
</th>
@if (isDoctor)
{
<th>
@Html.DisplayNameFor(model => model.CPPVisibility)
</th>
}
<th >Action</th>
</tr>
@foreach (var item in Model)
{

<tr id="<EMAIL>" class="patient-med-item @cssDiscontinue">
<td class="hidden">@itemClasses</td>
<td class="hidden">@treatType </td>
<td>
@if (showExpandLink)

</td>
<td>
@if (!item.IsDiscontinued)

</td>
<td>
<span class="@cssDiscontinue">@item.MedicationName</span>

<div>Ingredients: @item.Ingredients</div>
@if (!String.IsNullOrWhiteSpace(item.LU))
{
<div>LU: @item.LU</div>
}
</td>
<td>
@ Html.DisplayFor(modelItem => item.Classes)
    @Html.Truncate(item.Classes,50,true,false)
</td>
<td>
@Html.DisplayFor(modelItem => item.Form)
</td>
<td>
@Html.DisplayFor(modelItem => item.Dose)
</td>
<td>
@Html.DisplayFor(modelItem => item.Strength)
</td>
<td>
@Html.DisplayFor(modelItem => item.SIG)
</td>
<td>
@Html.DisplayFor(modelItem => item.Route)
</td>
<td>
@if (!String.IsNullOrWhiteSpace(item.DateLastPrescribedPartial))
{
<span>@item.DateLastPrescribedPartial</span>
}*@ else if (item.DateLastPrescribed != null)
{
<span>@item.DateLastPrescribed.Value.ToShortDateString()</span>
}

@if (item.HasOutidePrescriber)
{
<small> by @Html.DisplayFor(modelItem => item.OutsideProviderFirstName) @Html.DisplayFor(modelItem => item.OutsideProviderLastName)</small>

else if(!String.IsNullOrWhiteSpace(item.LastPrescribedName))
{
<small> by @Html.DisplayFor(modelItem => item.LastPrescribedName)</small>

</td>
<td>
@Html.DisplayFor(modelItem => item.DateStarted)
</td>
<td>
@Html.DisplayFor(modelItem => item.DateStartedStr)
</td>
<td>
@Html.DisplayFor(modelItem => item.DateExpired)
</td>
<td class="td-discontinue">
@if (item.IsDiscontinued || item.DateDiscontinued != null)
{

if (!String.IsNullOrWhiteSpace(item.DiscontinueComment))
{

}
else if (!String.IsNullOrWhiteSpace(item.DoseChangeComment))
{

//else
//{
//    changeCommentItem = item.InternalPrescriptionNotes;
//}

@Html.DisplayFor(modelItem => item.DateDiscontinued)
if (!String.IsNullOrWhiteSpace(item.DiscontinuedByFirstName) || !String.IsNullOrWhiteSpace(item.DiscontinuedByLastName))
{
<small> by @Html.DisplayFor(modelItem => item.DiscontinuedByFirstName) @Html.DisplayFor(modelItem => item.DiscontinuedByLastName)</small>
}

<a data-toggle="tooltip" data-placement="bottom" title="Edit Discontinued Medication" data-modal-id="discontinueReasonModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("editdiscontinuereason", "patientmedications", new { area = "medications", id= item.Id})"

if (!String.IsNullOrWhiteSpace(changeCommentItem))
{
<div class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<div class="btn-popover-title">
Comments
</div>
<div class="btn-popover-content">
<div style="width:200px;max-height:200px;overflow-y:auto;">
@changeCommentItem
</div>
</div>

</div>

else
{

<a data-modal-id="discontinueReasonModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("creatediscontinuereason", "patientmedications", new { area = "medications", id= item.Id})" class="italic cb-modal btn btn-default btn-xs" href="#"><small>Discontinue</small></a>

}

@if (isDoctor)
{
<td>
<div>
@{

}
<div data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="CPP Visibility" data-pat-med-id="@item.Id" data-url="@Url.Action("ChangeCPPVisibility", "patientmedications", new { area = "medications" })" class="c-pointer btn-cpp-visibility @cppVisibilityColor">@cppVisiibiltyText</div>
</div>
</td>

<td class="td-edit-med">
<div class="btn-popover-container">
<button type="button" class="btn btn-default btn-xs popover-btn">
<span class="glyphicon glyphicon-option-vertical text-primary"></span>
</button>
<div class="btn-popover-title">
Medication Menu
</div>
<div class="btn-popover-content">
<ul class="ul-app-menu">
<li>
<a data-modal-id="editDoseModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("editdose", "patientmedications", new { area = "medications", id= item.Id})"

</li>
<li>
@if (item.CanDelete)
{
<a data-modal-id="deleteMedicationModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("delete", "patientmedications", new { area = "medications", id = item.Id })"

}
else
{
<a data-toggle="tooltip" data-placement="bottom" title="Cannot Delete because there are prescriptions"

<span class="disabled glyphicon glyphicon-trash text-danger"></span> Delete Medication
</a>
}
</li>
<li>
<a data-modal-id="allergyModal" data-result-div="patient-history-content" data-modal-url="@Url.Action("create", "patientallergies", new { area = "medications", din = item.DIN, patientId = item.PatientId, medicationNoDinId = item.MedicationNoDinId, isDin = item.IsDin})"

</li>
<li>
<a data-modal-id="templateModal" data-modal-url="@Url.Action("create", "medicationtemplates", new { area = "medications", id= item.Id})"

</li>
<li>
<a data-patient-medication-id="@item.Id" data-medication-set-id="@item.MedicationSetId" data-modal-url="@Url.Action("printmedication", "patientmedications", new { area = "medications" })"

</li>
</ul>
</div>

</div>

@if (!String.IsNullOrWhiteSpace(item.OutsideProviderFirstName) || !String.IsNullOrWhiteSpace(item.OutsideProviderLastName))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-tag"></span>
<span class="btn-popover-title">
Outside Provider
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@item.OutsideProviderFirstName @item.OutsideProviderLastName
</span>
</span>

</span>
} if (!String.IsNullOrWhiteSpace(item.InternalPrescriptionNotes))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<span class="btn-popover-title">
Internal Notes
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@item.InternalPrescriptionNotes
</span>
</span>

</span>

@* var changeReasonMain = ""; if (!String.IsNullOrWhiteSpace(item.DiscontinueReason))
{

else if (!String.IsNullOrWhiteSpace(item.DoseChangeReason))
{

@if (!String.IsNullOrWhiteSpace(item.InternalPrescriptionNotes) || !String.IsNullOrWhiteSpace(changeReasonMain))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<span class="btn-popover-title">
Notes
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@if (!String.IsNullOrWhiteSpace(item.InternalPrescriptionNotes))
{
<strong>Internal Notes: </strong> @item.InternalPrescriptionNotes

}
@if (!String.IsNullOrWhiteSpace(changeReasonMain))
{
<strong>Reason: </strong> @changeReasonMain

</span>
</span>

</span>

@if (!String.IsNullOrWhiteSpace(item.ResidualData))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer">R</span>
<span class="btn-popover-title">Residual Data</span>
<span class="btn-popover-content"><div style="width:200px;max-height:200px;overflow-y:auto;">@item.ResidualData</div>
</span>
</span>

</td>
</tr>
foreach (var disconItem in item.DiscontinuedMedications)
{

<tr id="<EMAIL><EMAIL>" class="expand collapse child-row @disconItemColor">
<td class="child-row-td-noborder"></td>
<td></td>
<td>
@Html.DisplayFor(modelItem => disconItem.MedicationName)
<div>Ingredients: @disconItem.Ingredients</div>
@if (!String.IsNullOrWhiteSpace(disconItem.LU))
{
<div>LU: @disconItem.LU</div>
}
</td>
<td>

@Html.Truncate(disconItem.Classes, 50, true, false)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.Form)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.Dose)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.Strength)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.SIG)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.Route)
</td>
<td>
@if (disconItem.DateLastPrescribed != null)
{
<span>@disconItem.DateLastPrescribed.Value.ToShortDateString()</span>
}
@if (disconItem.HasOutidePrescriber)
{
<small> by @Html.DisplayFor(modelItem => disconItem.OutsideProviderFirstName) @Html.DisplayFor(modelItem => disconItem.OutsideProviderLastName)</small>

else if (!String.IsNullOrWhiteSpace(disconItem.LastPrescribedName))
{
<small> by @Html.DisplayFor(modelItem => disconItem.LastPrescribedName)</small>

</td>
<td>
@Html.DisplayFor(modelItem => disconItem.DateStarted)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.DateStartedStr)
</td>
<td>
@Html.DisplayFor(modelItem => disconItem.DateExpired)
</td>
<td class="td-discontinue">
@Html.DisplayFor(modelItem => disconItem.DateDiscontinued)
@if (!String.IsNullOrWhiteSpace(disconItem.DiscontinuedByFirstName) || !String.IsNullOrWhiteSpace(disconItem.DiscontinuedByLastName))
{
<small> by @Html.DisplayFor(modelItem => disconItem.DiscontinuedByFirstName) @Html.DisplayFor(modelItem => disconItem.DiscontinuedByLastName)</small>
}
@{

if (!String.IsNullOrWhiteSpace(disconItem.DiscontinueComment))
{

}
else if (!String.IsNullOrWhiteSpace(disconItem.DoseChangeComment))
{

//else
//{
//    changeComment = disconItem.InternalPrescriptionNotes;
//}

@if (!String.IsNullOrWhiteSpace(changeComment))
{
<div class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<div class="btn-popover-title">
Comments
</div>
<div class="btn-popover-content">
<div style="width:200px;max-height:200px;overflow-y:auto;">
<div>@changeComment</div>
@ if (!String.IsNullOrWhiteSpace(disconItem.DiscontinueComment))
{
<div>@disconItem.DiscontinueComment</div>
}
else if (!String.IsNullOrWhiteSpace(disconItem.DoseChangeComment))
{
<div>@disconItem.DoseChangeComment</div>

</div>
</div>

</div>

</td>
@if (isDoctor)
{
<td>
<div>
@{

}
<div data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="CPP Visibility">@cppVisiibiltyTextDis</div>
</div>
</td>

<td class="td-edit-med">
@{

if (!String.IsNullOrWhiteSpace(disconItem.DiscontinueReason))
{

}
else if (!String.IsNullOrWhiteSpace(disconItem.DoseChangeReason))
{

@if (!String.IsNullOrWhiteSpace(disconItem.InternalPrescriptionNotes) || !String.IsNullOrWhiteSpace(changeReason))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer glyphicon glyphicon-comment"></span>
<span class="btn-popover-title">
Notes
</span>
<span class="btn-popover-content">
<span style="width:200px;max-height:200px;overflow-y:auto;">
@if (!String.IsNullOrWhiteSpace(disconItem.InternalPrescriptionNotes))
{
<strong>Internal Notes: </strong> @disconItem.InternalPrescriptionNotes

}
@if (!String.IsNullOrWhiteSpace(changeReason))
{
<strong>Reason: </strong> @changeReason

</span>
</span>

</span>

@if (!String.IsNullOrWhiteSpace(disconItem.ResidualData))
{
<span class="btn-popover-container">
<span class="text-danger popover-btn c-pointer">R</span>
<span class="btn-popover-title">Residual Data</span>
<span class="btn-popover-content">
<div style="width:200px;max-height:200px;overflow-y:auto;">@disconItem.ResidualData</div> <!-- or use 'pre' tag-->
</span>
</span>

</td>
</tr>

</table>
<div class="panel-footer">
<a data-is-print="0" data-patient-id="@patientId" data-modal-url="@Url.Action("PrescriptionsCheck", "patientmedications", new { area = "medications" })" class="btn btn-primary btn-sm @disableRXFooterbtns">Prescribe</a>
<a data-is-print="1" data-patient-id="@patientId" data-modal-url="@Url.Action("PrescriptionsCheck", "patientmedications", new { area = "medications" })" class="btn btn-primary btn-sm @disableRXFooterbtns">Quick Prescribe</a>

<a data-patient-id="@patientId" data-modal-url="@Url.Action("printprescriptions", "patientmedications", new { area = "medications" })" id="btn-quick-prescribe" class="btn btn-primary btn-xs @disableRXbtns">Quick Prescribe</a>
</div>
<div style="margin-bottom:30px"></div>

else
{@* *@
<div class="">@*panel-body*@
<h5 class="text-info">
<span>No medications.</span>
</h5>
</div>

</div>

