@* ﻿@using Cerebrum.ViewModels.Medications; *@

@model VMPatientMedHistory
@using Cerebrum.ViewModels.Medications
@using Cerebrum.ViewModels.Patient

@{

<style>
    @@media screen and (min-width: 768px) {
        #prescriptionSet-modal-container .modal-lg {
/* width: 85% !important; */
        }
    }

    @@media screen and (min-width: 768px) {
        #print-rx-modal-container .modal-lg {
/* width: 85% !important; */
        }
    }

    @@media screen and (min-width: 300px) {
        #print-patientmedications-modal-container .modal-lg {
@* width: 85% !important; *@
        }
    }
</style>

<script>
$(document).ready(function () {
@* setHeaderTitle('@@page'); *@
@* }); *@
</script>

@section patientinfo{

    @Html.GetPatientInfo(@Model.Patient.PatientId)

}
<div class="row">
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-12">
                <div class="float_l">
                    <a class="btn btn-default btn-sm" id="btn-view-prescriptions" href="#">Prescriptions <span id="prescription-set-count"></span></a> if (Cerebrum.DHDR.Services.Config.GetConfiguration?.CerebrumConfig.Enabled == true)
                    @if (CerebrumUser.IsDhdrEnabled && eConsultActive)
                    {
                        @Html.ActionLink("DHDR", "Index", "DHDR", new { patientId = @Model.Patient.PatientId }, new { target = "blank_", @class = "btn btn-default btn-sm" })
                    }
                    <a class="btn btn-default btn-sm" href="Help/Drugs_Renal_Failure_AmFamPhysician2007.pdf" style="margin-left:4px !important;" target="_blank">Renal Dose Adjustments</a>
                    <a class="btn btn-default btn-sm" href="http://www.lucodes.ca/LU.html" target="_blank">LU Codes</a>
                    <a class="btn btn-default btn-sm" href="https://www.canada.ca/en/health-canada/services/drugs-health-products/drug-products/drug-product-database.html" target="_blank">Dormant Dins</a>
                    <a href="#" data-modal-url="@Url.Action("createnodinmedication","medications", new { area="medications" })" id="btn-noDin" class="btn btn-sm btn-default">Add No Din Medication</a>
                    @if (isDoctor)
                    {
                        <a class="btn btn-default btn-sm" id="btn-interation-settings" href="#">Interactions Settings</a>
                    }

                    <a class="btn btn-default btn-sm" id="btn-print-medications" href="#">Print Medications</a>

                    <a data-interactions-off="@interactionsOffMsg" data-interactions-on="@interactionsOnMsg" data-load-url="@Url.Action("ChangeInteractionsCheck", "patientmedications", new { area = "medications" })"

                </div>
                <div class="float_r">
                    @if (!String.IsNullOrWhiteSpace(@Model.MedicationDBLastUpdated))
                    {
                        <span>Drug Database Last Updated: @Model.MedicationDBLastUpdated</span>
                    }
                    <a href="@Html.GetReferrerUrl()" class="btn btn-default btn-sm">Go Back</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row spacer-top-7">
    <div class="col-md-7">
        <div id="med-search-container">
            @await Html.PartialAsync("_medicationSearchForm", ViewBag.MedicationSearchData)
        </div>
        @*<br />*@
            @if (Cerebrum.DHDR.Services.Config.GetConfiguration?.CerebrumConfig.Enabled == true)
            {
            if (!string.IsNullOrWhiteSpace(@Model.Patient.Ohip))
            {
            <div id="dhdr-med-search-container">
            Ohip being searched @Model.Patient.Ohip
            @* TODO: Convert Html.RenderAction to ViewComponent - Convert Html.RenderAction to ViewComponent - @ * </div>
            }
            else
            {
            <div id="dhdr-med-cannot-search">
            Ohip is missing, DHDR is not avaiable.
            </div>
            }
</div>
    <div class="col-md-2 text-right">
        @*<button data-modal-url="@Url.Action("createnodinmedication","medications", new { area="medications" })" id="btn-noDin" class="btn btn-sm btn-default">Add No Din Medication</button>* </div>
    <div class="col-md-3 text-right">
        @if (canPrescribe)
        {
            <div class="dropdown hover-menu">
                <button class="btn btn-default btn-sm" type="button">
                    Medication Templates
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-right med-templ-lst __345326">
                    @foreach (var item in @Model.MedicationTemplateClasses)
                    {
                        if (item.TotalTemplates > 0) {*  double menuColsCount = Math.Ceiling((item?.UserTemplates?.Count ?? 0) / 30.0);

                            <li class="dropdown-submenu">
                                <a href="#" class="submenu-header" tabindex="-1">@Html.DisplayFor(model => item.ClassName) (@Html.DisplayFor(model => item.TotalTemplates)) <span class="caret"></span></a>
                                <ul class="dropdown-menu drp-right-align" style="@menuCols">
                                    @foreach (var subItem in item.UserTemplates)
                                    {
                                        <li>

                                            <span class="cb-modal glyphicon glyphicon-pencil c-pointer" data-modal-id="editMedTempateModal"

@* &nbsp; *@
                                            </span>

                                            <span class="cb-modal c-pointer" data-modal-id="createMedModal"

                                                @subItem.Name
                                            </span>

@* *@
                                            @*</a>*@
                                        </li>

                                </ul>
                            </li>

                        else
                        {
                            <li class="disabled">
                                <a href="#">
                                    @Html.DisplayFor(model => item.ClassName) (@Html.DisplayFor(model => item.TotalTemplates))
                                </a>
                            </li>

                </ul>
            </div>

    </div>
</div>

<!-- ########### Allergies ###########-->
<div style="margin-top:15px;" id="patient-allergy-history-content" data-load-url="@Url.Action("getpatientAllergymain", "patientalleriess", new { area = "medications", patientId= @Model.Patient.PatientId })">
    @Html.PartialAsync("_patientAllergyViewList", @Model.PatientAllergyGet)
</div>*@if (@Model.PatientAllergies.Any(a => a.IsDin == false) || @Model.PatientAllergyIngredients.Any())
    {
    <div style="padding-top:15px;" class="row">
    <div class="col-md-12">
    <div ><!-- class="alert alert-danger" role="alert" -->
    <div ><strong>Allergies</strong></div><!-- class="text-danger" -->
    @if (@Model.PatientAllergies.Any(a=> a.IsDin == false))
    {
    <div id="allergyWarning" style="margin-bottom:0;">
    @Html.PartialAsync("_patientAllergyList", @Model.PatientAllergies)
    </div>
    }
    @if (@Model.PatientAllergyIngredients.Any())
    {*  var margin = @Model.PatientAllergies.Any() ? "0px" : "7px";

    <div id="allergyIngredientWarning" style="margin-top:@margin;">
    @Html.PartialAsync("_patientAllergyIngredList", @Model.PatientAllergyIngredients)
    </div>
    }
    </div>
    </div>
    </div>

<!-- ########### interaction and Medication ###########-->

<div id="interactionsWarning" data-patient-id="" data-medication-ids="" data-reload-interactions="0"></div>

<div style="margin-top:15px;" id="patient-history-content" data-load-url="@Url.Action("getpatientmedications", "patientmedications", new { area = "medications", patientId= @Model.Patient.PatientId })">
    @Html.PartialAsync("_patientMedicationList", @Model.PatientMedications)
</div>

<div data-patient-id="@Model.Patient.PatientId" data-load-url="@Url.Action("getprescriptionsets", "patientmedications", new { area = "medications"})" class="modal fade" id="prescriptionSet-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">@Model.Patient.FullName</h4>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer">
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="printMedications-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">Print Medications</h4>
            </div>
            <div class="modal-body">
                <div class="form-group form-group-sm">
                    <div>                        
                        <div>
                            @Html.Label("ddlViewPatientMedicationFilter", "Medications", htmlAttributes: new { @class = "control-label col-md-5" })
                            @Html.DropDownList("ddlViewPatientMedicationFilter")
                                new List<SelectListItem>
                                {

                        </div>
                        <div>
                            @Html.Label("txtViewPatientMedicationsDateFrom", "From", htmlAttributes: new { @class = "control-label col-md-5 required-label" })
                            @Html.TextBox("txtViewPatientMedicationsDateFrom", String.Format("{0:MM/dd/yyyy}", String.string.IsNullOrEmpty(@Model.Patient.DOB) ? DateTime.Now.ToString("MM/dd/yyyy") : Convert.ToDateTime(@Model.Patient.DOB).ToString("MM/dd/yyyy")), new { @class = "form-control date-picker input-sm", @style = "width: 60pt" })
                        </div>
                        <div>
                            @Html.Label("txtViewPatientMedicationsDateTo", "To", htmlAttributes: new { @class = "control-label col-md-5 required-label" })
                            @Html.TextBox("txtViewPatientMedicationsDateTo", String.Format("{0:MM/dd/yyyy}", DateTime.Now.ToString("MM/dd/yyyy")), new { @class = "form-control date-picker input-sm", @style = "width: 60pt" })
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="btn-view-printmedications" class="btn btn-default btn-sm" data-load-url="@Url.Action("ViewPrintMedications","patientmedications", new { area="medications", patientId = @Model.Patient.PatientId, dateFrom = "pDateFrom", dateTo = "pDateTo", filterType = "pMedicationFilterType" })">View</button>
                    <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                </div>
            </div><!--End modal content-->
        </div><!--End modal dialog-->
    </div>
</div>

<div class="modal fade" id="print-rx-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="print-rx-title">Prescription Print Preview</span></h4>
            </div>

            <div class="modal-body">
                <div id="print-rx-message-header"></div>
                <div id="divPrescriptionPrint">
                    <iframe id="print-rx-frame" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                @if (CerebrumUser.HasPermission("ReprintOrRefaxPrescription"))
                {
                    <button id="btn-fax-prescription" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-phone-alt"></span> Fax</button>
                    <button id="btn-print-prescription" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                }
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="print-patientmedications-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="patient-medication-title">Medications Print Preview</span></h4>
            </div>

            <div class="modal-body">
                <div id="print-patientmedications-message-header"></div>
                <div id="divPatientMedicationsPrint">
                    <iframe id="print-patientmedications-frame" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-print-patientmedications" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="print-med-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span id="saved-title">Dose History Print</span></h4>
            </div>

            <div class="modal-body">
                <div id="divMedicationPrint">
                    <iframe id="print-med-frame" style="width:100%;background-color:#ffffff;border:none; height:100%;" src="@Url.Action("print","patientmedications",new { area="medications" })"></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-print-medication-view" class="btn btn-default btn-sm"><span class="glyphicon glyphicon-print"></span> Print</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

<div class="modal fade" id="fax-rx-modal-container" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><span>Fax Prescription for: @Model.Patient.FullName</span></h4>
            </div>

            <div class="modal-body">
                <div id="divPrescriptionFax">
                    <form id="frm-fax-rx" method="post" role="form" action="@Url.Action("faxprescription", "patientmedications", new { area = "medications" })">
                        <div class="form-horizontal">
                            @Html.AntiForgeryToken()
                            @Html.Hidden("PracticeDoctorId", 0)
                            @Html.Hidden("PracticeDoctor", "")
                            @Html.Hidden("PatientId", @Model.Patient.PatientId)
                            @Html.Hidden("PatientFirstName", @Model.Patient.FirstName)
                            @Html.Hidden("PatientLastName", @Model.Patient.LastName)
                            @Html.Hidden("PrescriptionDate", "")
                            @Html.Hidden("FaxPrescriptionSetId", 0)
                            @Html.Hidden("FaxPrescriptionSetIdStr", "")
                            @Html.Hidden("FaxHtml", "")
                            @Html.Hidden("FaxHtmlHeader", "")
                            @Html.Hidden("FaxHtmlFooter", "")

                            <div class="form-group form-group-sm">

                                @Html.Label("AccessionNumber", "Fax Number", new { @class = "control-label col-md-2 required-label" })
                                <div class="col-md-2">
                                    @Html.TextBox("FaxPharmacyNumber", @Model.Patient.PharmacyFax)
                                </div>
                            </div>
                            <div class="form-group form-group-sm">
                                <div class="col-md-offset-2 col-md-10">
                                    <div id="rxfaxerror" class="text-danger"></div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button id="btn-submit-fax-prescription" class="btn btn-default btn-sm modal-submit-btn"><span class="glyphicon glyphicon-phone-alt"></span> Fax</button>
                <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div><!--End modal content-->
    </div><!--End modal dialog-->
</div>

@if (isDoctor)
{
    <!--interactions settings modal-->
    <div data-patient-id="@Model.Patient.PatientId" class="modal fade" id="interactions-settings-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <form id="frm-interaction-settings" class="" method="post" role="form" action="@Url.Action("EditInterationSettings", "patientmedications", new { area = "medications" })">
                    @Html.ModalHeader("Interaction Settings")
                    <div class="modal-body">
                        @* Html.RenderPartial("_interactionSettings", settings); *@
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-default btn-sm modal-submit-btn btn-spacing">Save Settings</button>
                        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                    </div>
                </form>

            </div><!--End modal content-->
        </div><!--End modal dialog-->
    </div>

