﻿@{

    ////Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
    //Layout = "~/Views/Shared/_Layout.cshtml";

}

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet" />
<link href="~/Areas/Schedule/Content/password-reveal.css" rel="stylesheet" />
<link href="~/Content/jquery-ui-1.12.1.min.css" rel="stylesheet" />
<script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>
<!-- 1. Load libraries -->
<!-- Polyfill(s) for older browsers -->
<script src="node_modules/core-js/client/shim.min.js"></script>

<script src="node_modules/zone.js/dist/zone.js"></script>
<script src="node_modules/reflect-metadata/Reflect.js"></script>
<script src="node_modules/systemjs/dist/system.src.js"></script>

<!-- 2. Configure SystemJS -->
<script>System.packageWithIndex = true;</script>
<script src="~/app/adminManager/config/systemjs.config.admin.js"></script>
<!-- ... -->

<script src="Scripts/rxjs.js"></script>

<script>
@* System.import('app').catch(function (err) { console.error(err); }); *@
</script>

<style>
    .forActiveChB input {
/* display: inline-block; */
/* float: left; */
/* margin-top: 14px !important; */
/* margin-left: 5px; */
    }

    .forActiveChB span {
/* display: inline-block; */
/* float: left; */
@* margin-top: 10px !important; *@
@* margin-left: 5px; *@
    }

    .bcl {
@* position: relative; *@
@* float: left; *@
@* font-weight: 700; *@
@* margin-top: 9px; *@
    }

    .capp1 {
@* background-color: rgb(201, 207, 211); *@
@* width: 500px; *@
@* height: 37px; *@
@* position: relative; *@
@* float: left; *@

    .width_117px {
@* width: 117px; *@

    tbody {
@* overflow-y: scroll; *@
@* height: 665px; *@
@* width: 100%; *@
@* position: absolute; *@
    }

    .forSearch {
@* position: relative; *@
@* float: right; *@
@* margin-top: 8px; *@
    }

        .forSearch input {
@* padding: 0px; *@
@* background-color: rgb(239, 245, 251); *@
@* width: 300px; *@
        }

    .head_ span {
@* position: relative; *@
@* float: left; *@
@* font-size: xx-large; *@
@* padding: 0px; *@
    }

    .head2_ span {
@* position: relative; *@
@* float: left; *@
@* font-size: large; *@
@* padding: 0px; *@
@* margin-top: 10px; *@
@* font-weight: 700; *@

    .userBar {
@* background-color: rgb(189, 195, 200); *@
@* float: left; *@
@* width: 290px; *@
@* height: 37px; *@
    }

    .label_ellipsis {
@* text-overflow: ellipsis; *@
@* white-space: nowrap; *@
@* overflow: hidden; *@
    }

        .label_ellipsis label {
@* display: inline !important; *@

    .usersHed {
@* background-color: rgb(189, 195, 200); *@
@* width: 100%; *@
@* position: relative; *@
@* float: left; *@
@* height: 37px; *@

    /@*.usersHed H2 {*@
@* margin-bottom: 0px; *@
@* color: white; *@
@* margin-top: 0px; *@
        }@*/*@

    /*.usersHed {
@* background-color: rgb(189, 195, 200); *@
@* width: 1167px !important; *@
@* margin-left: 20px; *@
    }

        .usersHed H2 {
@* margin-bottom: 0px; *@
@* color: white; *@
@* margin-top: 30px; *@
        }*/

    .red {
@* background-color: rgb(168, 175, 180); *@

    .green_ {
@* color: #4b9e02 !important; *@

    .rem_right_padd {
@* padding-right: 0px; *@

    .rem_left_padd {
@* padding-left: 0px; *@

    .input_ {
@* width: 100%; *@
@* height: 21px; *@
@* padding-top: 0px; *@
@* padding-bottom: 0px; *@
    }

    .input_chb {
@* display: inline-block; *@
@* float: right; *@

    .marg_bott {
@* margin-bottom: 2px; *@

    .divButt {
@* -webkit-border-radius: 4px 4px 4px 4px; *@
@* -moz-border-radius: 4px 4px 4px 4px; *@
@* border-radius: 4px 4px 4px 4px; *@
@* margin-top: 7px; *@
@* float: right; *@
@* margin-right: 5px; *@

    .newDiv {
@* background-color: rgb(220, 226, 231); *@

    .scroll_div {
@* overflow-y: scroll; *@
@* overflow-x: hidden; *@

    #ph_provider {
@* text-overflow: ellipsis; *@
@* position: relative; *@
@* float: left; *@
@* -webkit-border-radius: 4px 4px 4px 4px; *@
@* -moz-border-radius: 4px 4px 4px 4px; *@
@* border-radius: 4px 4px 4px 4px; *@
@* border: 1px solid white; *@
@* margin-left: 5px; *@
@* margin-right: 5px; *@
@* width: 285px; *@
@* height: 83px; *@

    #ph_provider_1 {
@* position: relative; *@
@* float: left; *@
        /* width: 198px; */
@* width: 281px; *@
@* height: 75px; *@
@* margin-top: 4px; *@

    #ph_provider_2 {
@* position: relative; *@
@* float: left; *@
@* width: 85px; *@
@* height: 60px; *@
    }

    #ph_provider_1 label {
@* margin-left: 2px; *@
@* font-weight: 100; *@
@* width: 280px; *@
@* white-space: nowrap; *@
@* overflow: hidden; *@
@* text-overflow: ellipsis; *@

    .ph_docs_1 {
@* position: relative; *@
@* float: left; *@
@* width: 198px; *@
        /@*width: 281px;*/*@
@* height: 60px; *@
@* margin-top: 4px; *@

        .ph_docs_1 label {
@* margin-left: 2px; *@
@* font-weight: 100; *@
@* width: 180px; *@
        }

    #ph_loles {
@* position: relative; *@
@* float: left; *@
@* -webkit-border-radius: 4px 4px 4px 4px; *@
@* -moz-border-radius: 4px 4px 4px 4px; *@
@* border-radius: 4px 4px 4px 4px; *@
@* border: 1px solid white; *@
@* margin-left: 5px; *@
@* margin-right: 5px; *@
@* width: 285px; *@
@* height: 180px; *@

    #ph_loles_1 {
@* position: relative; *@
@* float: left; *@
        /* width: 198px; */
@* width: 281px; *@
@* height: 172px; *@
@* margin-top: 4px; *@

    #ph_loles_2 {
@* position: relative; *@
@* float: left; *@
@* width: 85px; *@
@* height: 240px; *@
    }

    #ph_loles_1 label {
@* margin-left: 2px; *@
@* font-weight: 100; *@
@* width: 180px; *@
    }

    .width_100 {
@* width: 100%; *@

    .chb_right {
@* float: left; *@

    .form-group {
@* margin-bottom: 3px; *@

    .wrapper:after {
@* content: ''; *@
@* display: block; *@
@* clear: both; *@
    }

    .txt_under span {
@* font-weight: 600; *@

    #adminUser_L {
@* background-color: rgb(239, 245, 251); *@
@* width: 500px; *@
@* min-height: 745px; *@
@* display: table; *@
@* position: relative; *@
@* border-radius: 0px; *@
@* float: left; *@
@* margin-left: 10px; *@

    #adminUser_M {
@* width: 440px; *@
@* background-color: rgb(239, 245, 251); *@
@* min-height: 745px; *@
@* position: relative; *@
@* float: left; *@
        /@*margin-left: 3px;*/*@
@* border-radius: 0px; *@
@* border-right-width: 0px; *@

    #adminUser_R {
@* width: 290px; *@
@* background-color: rgb(239, 245, 251); *@
@* min-height: 745px; *@
@* position: relative; *@
@* float: left; *@
@* border-radius: 0px; *@
@* border-left-width: 0px; *@

    #mainContent {
@* width: 1250px; *@
@* height: 720px; *@
@* margin-left: 10px; *@
    }

    .newUserClass {
@* height: 745px; *@
@* background-color: rgb(220, 226, 231); *@

    #newRoleDiv {
@* height: 400px; *@
@* background-color: rgb(220, 226, 231); *@
@* clear: both; *@
    }

    #updateUserDiv_R {
@* height: 580px; *@
@* margin-top: 5px; *@
@* margin-top: 3px; *@
@* float: left; *@
    }

    #updateUserDiv_L {
@* height: 600px; *@
@* background-color: rgb(239, 245, 251); *@
@* margin-top: 5px; *@
    }

    .usAdButton {
@* border-radius: 4px 4px 4px 4px; *@
@* margin-top: 7px; *@
@* float: right; *@
@* margin-right: 5px; *@
@* height: 20px; *@
@* padding-top: 0px; *@
@* width: 80px; *@

    .usAdm_div1 {
@* width: 85px; *@
@* float: left; *@

        .usAdm_div1 select {
@* width: 81px; *@

    .usAdm_div2 {
@* width: 85px; *@
@* float: left; *@
@* margin-top: 3px; *@
    }

        .usAdm_div2 select {
@* width: 81px; *@

    .rem_top_bot_pad input {
@* padding-top: 0px; *@
@* padding-bottom: 0px; *@

</style>
@*<h2>Users</h2>*@

<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading"></div>
            <div class="panel-body">
                @*----------------*@
                <div id="mainContent">
                    <adminUserList requestVerificationToken="@requestVerificationToken">Loading...</adminUserList>
                </div>@* *@
                @*-------------------*@
            </div>
        </div>
    </div>
</div>
