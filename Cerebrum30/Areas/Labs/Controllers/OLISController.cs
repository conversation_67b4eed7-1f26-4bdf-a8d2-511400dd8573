using Cerebrum.BLL.Patient;
using Cerebrum.ViewModels.OLIS;
// TODO: Re-enable when Cerebrum.Labs project is fixed for .NET 8
// using Cerebrum.Labs.OLIS.services;
using Cerebrum30.Controllers;
using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using Cerebrum.ViewModels.Common;
using System.Web;
using Cerebrum.Doctors;
// TODO: Replace System.Web.Routing with Microsoft.AspNetCore.Routing
// using System.Web.Routing;
// TODO: Re-enable when Cerebrum.Labs project is fixed for .NET 8
// using Cerebrum.Labs.OLISAPILayer;
using System.Net.Http;
using Cerebrum30.Utility;
using Cerebrum.BLL.Documents;

namespace Cerebrum30.Areas.Labs.Controllers
{
#if ENABLE_CEREBRUM_LABS
    [SessionState(System.Web.SessionState.SessionStateBehavior.ReadOnly)]
    public class OLISController : BaseController
    {
        private IOLISBuildQuery _olisbll;
        private OLISClinicalViewerService _cvService;
        private IPatientBLL _patientBLL;
        private IPracticeDoctorBLL _practiceDoctorBLL;
        private IOlisApiWrapper _apiWrapper;
        private IDocumentsBLL _documentsBLL;
        private IHttpClientFactory _httpClientFactory;
        private bool _isProductionEnv = false;
        public OLISController(IOlisApiWrapper apiWrapper, IPracticeDoctorBLL practiceDoctorBLL, IPatientBLL patientBLL, IOLISBuildQuery olisbll, IDocumentsBLL documentsBLL, IHttpClientFactory httpClientFactory)
#else
    // TODO: OLIS functionality disabled until Cerebrum.Labs project is fixed for .NET 8
    // The Cerebrum.Labs project has CAPICOM dependencies that need to be resolved
    public class OLISController : BaseController
    {
        private IPatientBLL _patientBLL;
        private IPracticeDoctorBLL _practiceDoctorBLL;
        private IDocumentsBLL _documentsBLL;
        private IHttpClientFactory _httpClientFactory;
        private bool _isProductionEnv = false;
        public OLISController(IPracticeDoctorBLL practiceDoctorBLL, IPatientBLL patientBLL, IDocumentsBLL documentsBLL, IHttpClientFactory httpClientFactory)
#endif
        {
#if ENABLE_CEREBRUM_LABS
            _httpClientFactory = httpClientFactory;
            _olisbll = olisbll;
            _cvService = new OLISClinicalViewerService();
            _patientBLL = patientBLL;
            _practiceDoctorBLL = practiceDoctorBLL;
            _apiWrapper = apiWrapper;
            _documentsBLL = documentsBLL;
            _isProductionEnv = Helper.IsProdEnvironment();
#else
            _httpClientFactory = httpClientFactory;
            _patientBLL = patientBLL;
            _practiceDoctorBLL = practiceDoctorBLL;
            _documentsBLL = documentsBLL;
            _isProductionEnv = Helper.IsProdEnvironment();
#endif
        }

#if ENABLE_CEREBRUM_LABS
        [HttpGet]
        [ActionName("OLISReportSearch")]
        public ActionResult Index(int? patientId)
        {
            VMOLISSearch s = new VMOLISSearch { patientId = patientId, PracticeId = CerebrumUser.PracticeId, IsDoctor = CerebrumUser.IsDoctor | CerebrumUser.IsNurse, PracticeDoctorId = CerebrumUser.PracticeDoctorId };
            s = _olisbll.GetSearch(s);
            TryValidateModel(s);

            return View("Index", s);
        }
        [HttpGet]
        [ActionName("OLISReportConsentReinstatement")]
        public ActionResult Index(VMOLISConsentReinstatement c)
        {
            VMOLISSearch s = new VMOLISSearch { Query = "Z02", placerGroupNumber = c.OrderId, patientId = c.PatientRecordId, PracticeId = CerebrumUser.PracticeId, IsDoctor = CerebrumUser.IsDoctor | CerebrumUser.IsNurse, PracticeDoctorId = CerebrumUser.PracticeDoctorId, ValidateDateRange = _isProductionEnv };
            s = _olisbll.GetSearch(s);
            return View("Index", s);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Index(VMOLISSearch s)
        {
            _log.Debug($"Environment: {Helper.Environment()}, OLIS Request Received: {s.ToString()}");
            string ipAddress = IPAddress(Request);
            List<OLISQueryResponse> reporturls = new List<OLISQueryResponse>();
            DateTime? frmDate = s.fromDate;
            DateTime? toDate = s.toDate;

            s.PracticeId = CerebrumUser.PracticeId;
            s.UserId = CerebrumUser.UserId;
            s.UserFirstName = CerebrumUser.FirstName;
            s.UserLastName = CerebrumUser.LastName;
            s.IPAddress = ipAddress;
            s.IsDoctor = CerebrumUser.IsDoctor | CerebrumUser.IsNurse;
            s.OfficeId = CerebrumUser.OfficeId;
            s.ValidateDateRange = _isProductionEnv;

            ModelState.Clear();
            bool isValid = TryValidateModel(s, nameof(VMOLISSearch));

            _log.Debug($"Is OLIS request cleaned model valid ? {isValid} Original ModelState:{ModelState.IsValid}");

            try
            {
                if (ModelState.IsValid)
                {
                    if (s.Query.ToLower().Equals("z04") || s.Query.ToLower().Equals("preload"))
                    {
                        s.PreloadQuery = true;
                        if (s.requestingPractitioner.IndexOf(',') > 0)
                        {
                            string[] ds = s.requestingPractitioner.Split(',');
                            foreach (var d in ds)
                            {
                                s.requestingPractitioner = d;
                                //var resp = await _olisbll.SendZ04(s);
                                var resp = await _apiWrapper.SendOLISZ04RequestAsync(s);
                                if (resp != null)
                                {
                                    foreach (var rptid in resp)
                                    {
                                        reporturls.Add(rptid);
                                    }
                                }
                                else
                                {
                                    ModelState.AddModelError("", "Please contact admin.");
                                }
                            }
                        }
                        else
                        {
                            //var resp = await _olisbll.SendZ04(s);
                            var resp = await _apiWrapper.SendOLISZ04RequestAsync(s);
                            if (resp != null)
                            {
                                foreach (var rptid in resp)
                                {
                                    reporturls.Add(rptid);
                                }
                            }
                            else
                            {
                                ModelState.AddModelError("", "Please contact admin.");
                            }
                        }
                    }
                    else
                    {
                        _log.Debug($"Send Z01 request.");
                        //var resp = await _olisbll.SendRequest(s);
                        var resp = await _apiWrapper.SendOLISZ01RequestAsync(s);
                        if (resp != null)
                        {
                            foreach (var rptid in resp)
                            {
                                reporturls.Add(rptid);
                            }
                        }
                        else
                        {
                            ModelState.AddModelError("", "Please contact admin.");
                        }
                    }
                }
                else
                {
                    s = _olisbll.GetSearch(s);
                    s.fromDate = frmDate;
                    s.toDate = toDate;

                    TryValidateModel(s);
                    string errors = Cerebrum.BLL.Utility.UtilityHelper.GetValidationErrors(ModelState);
                    ModelState.AddModelError("*", errors);
                    _log.Debug(errors);
                    return View(s);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                ModelState.AddModelError("", ex.Message);
            }

            _log.Debug($"Request Response count: {reporturls.Count()}");

            s.IsDoctor = CerebrumUser.IsDoctor;
            s = _olisbll.GetSearch(s);
            s.fromDate = frmDate;
            s.toDate = toDate;
            s.QueryResponse = reporturls;
            if (s.ExternalQuery)
            {
                if (s.patientId != null)
                {
                    var pat = _olisbll.GetPatientDemographic((int)s.patientId);
                    s.patient = pat.FullName;
                }
            }
            return View(s);
        }
        public ActionResult PracticeDoctors()
        {
            var docs = _practiceDoctorBLL.GetAllPracticeDoctorsForOLIS(CerebrumUser.PracticeId);
            return PartialView("PracticeDoctorsLookup", docs);
        }


        [HttpGet]
        public ActionResult SearchLabs(string search)
        {
            var codes = _olisbll.LabLookup(search);
            var json = JsonConvert.SerializeObject(codes);
            return Content(json);
        }

        [HttpGet]
        public ActionResult SearchTestRequestCodes(string search)
        {
            var codes = _olisbll.GetTestRequestCodes(search);
            var json = JsonConvert.SerializeObject(codes);
            return Content(json);
        }
        [HttpGet]
        public async Task<ActionResult> SearchTestResultCodes(string search)
        {
            var codes = await _olisbll.GetTestResultCodesAsync(search);
            var json = JsonConvert.SerializeObject(codes);
            return Content(json);
        }
        public async Task<ActionResult> TestResultCodes()
        {
            var codes = await _olisbll.GetTestResultCodesAsync();
            //var json = JsonConvert.SerializeObject(codes);
            return PartialView("TestResultCodes", codes);
        }
        [HttpGet]
        public ActionResult ConsentBlockResult(string accessionNumber, string physicianNo, int HL7PatientId)
        {
            var consent = _olisbll.GetConsentInfo(CerebrumUser.PracticeId, accessionNumber, physicianNo, HL7PatientId);
            return PartialView("PatientConsentForm", consent);
        }
        [HttpPost]
        public async Task<ActionResult> ConsentBlockOverride(VMOLISConsentReinstatement c)
        {
            c.UserId = CerebrumUser.UserId;
            c.IPAddress = IPAddress(Request);

            //var reports = await _olisbll.SendRequest(c, user);
            var reports = await _apiWrapper.SendOLISZ01ConsentBlockOverrideRequestAsync(c);
            var r = _cvService.GetReport(reports);
            return PartialView(r);
        }
        [HttpGet]
        public ActionResult Preloads()
        {
            var preloads = _cvService.Preloads(CerebrumUser.PracticeId);

            return PartialView(preloads);
        }
        [HttpGet]
        public ActionResult PreloadQuery()
        {
            var preloads = _cvService.Preloads(CerebrumUser.PracticeId);

            return View(preloads);
        }

        [HttpPost]
        public async Task<ActionResult> SaveAll(int reportReceivedId, bool markseen)
        {
            int practiceId = CerebrumUser.PracticeId;
            int userid = CerebrumUser.UserId;
            int practiceDoctorId = CerebrumUser.PracticeDoctorId;
            string ipAddress = IPAddress(Request);
            markseen = (CerebrumUser.IsDoctor || CerebrumUser.IsNurse);
            bool isDoctor = markseen;

            List<VMOLISReportSave> reportsaved = await _apiWrapper.ReportsSaveAllAsync(reportReceivedId, practiceId, practiceDoctorId, isDoctor, userid, ipAddress, markseen);
            //HostingEnvironment.QueueBackgroundWorkItem(async cancellationToken =>
            //{
            //    reportsaved= await apiWrapper.ReportsSaveAll(reportReceivedId, practiceId, practiceDoctorId, isDoctor, userid, ipaddress, markseen);
            //});

            return PartialView("OLISReportSave", reportsaved);
        }

        [HttpPost]
        public async Task<ActionResult> SaveOLISReport(int reportReceivedId, int pid_set, string filename, List<string> requestedtests, List<string> results, bool markseen)
        {
            int practiceId = CerebrumUser.PracticeId;
            int userid = CerebrumUser.UserId;
            int practiceDoctorId = CerebrumUser.PracticeDoctorId;
            string ipAddress = IPAddress(Request);
            markseen = (CerebrumUser.IsDoctor || CerebrumUser.IsNurse);
            bool isDoctor = markseen;
            VMOLISReportSave reportsaved = null;
            try
            {
                reportsaved = await _apiWrapper.ReportSaveAsync(reportReceivedId, practiceId, practiceDoctorId, isDoctor, pid_set, userid, ipAddress, markseen, requestedtests, results);
                return Json(reportsaved);
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
            }
            return Json(new { success = false, Message = "Report Save Error" });
        }

        //[HttpPost]
        //public ActionResult SaveAll(int reportReceivedId,bool markseen)
        //{
        //    int practiceId = CerebrumUser.PracticeId;
        //    int userid = CerebrumUser.UserId;
        //    string ipaddress = Request.UserHostAddress;
        //    VMOLISMarkSeen _markseen = new VMOLISMarkSeen { MarkSeen = markseen };

        //    if (markseen)
        //    {
        //        markseen = (CerebrumUser.IsDoctor || CerebrumUser.IsNurse);
        //        if (markseen)
        //        {
        //            _markseen.MarkSeen = markseen;
        //            _markseen.IPAddress = ipaddress;
        //            _markseen.SeenAt = DateTime.Now;
        //            _markseen.UserId = CerebrumUser.UserId;
        //            _markseen.PracticeDoctorId = CerebrumUser.PracticeDoctorId;
        //        }
        //    }
        //    var reportsaved = _cvService.SaveOLISReport(reportReceivedId, 0, null, null, practiceId, userid, ipaddress, _markseen);
        //    var json = JsonConvert.SerializeObject(reportsaved);
        //    return Content(json);
        //}

        //[HttpPost]
        //public ActionResult SaveOLISReport(int reportReceivedId, int pid_set, string filename, List<string> requestedtests, List<string> results, bool markseen)
        //{
        //    int practiceId = CerebrumUser.PracticeId;
        //    int userid = CerebrumUser.UserId;
        //    string ipaddress = Request.UserHostAddress;
        //    VMOLISMarkSeen _markseen = new VMOLISMarkSeen {MarkSeen=markseen };

        //    if (markseen)
        //    {
        //        markseen = (CerebrumUser.IsDoctor || CerebrumUser.IsNurse);
        //        if(markseen)
        //        {
        //            _markseen.MarkSeen = markseen;
        //            _markseen.IPAddress = ipaddress;
        //            _markseen.SeenAt = DateTime.Now;
        //            _markseen.UserId = CerebrumUser.UserId;
        //            _markseen.PracticeDoctorId = CerebrumUser.PracticeDoctorId;
        //        }
        //    }
        //    var reportsaved = _cvService.SaveOLISReport(reportReceivedId, pid_set, requestedtests, results, practiceId, userid, ipaddress, _markseen);
        //    var json = JsonConvert.SerializeObject(reportsaved);
        //    return Content(json);
        //}

        [HttpPost]
        public async Task<ActionResult> RejectOLISReport(int reportReceivedId, int pid_setid, string reason)
        {
            int practiceId = CerebrumUser.PracticeId;
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);

            var reportsaved = await _apiWrapper.RejectReportAsync(reportReceivedId, pid_setid, reason, practiceId, userid, ipAddress);
            return Content(reportsaved);
        }

        [HttpGet]
        public async Task<ActionResult> ReportPreview(int? report)
        {
            if (report != null)
            {
                string ipAddress = IPAddress(Request);
                var rpt = await _apiWrapper.GetReportPreviewAsync((int)report, CerebrumUser.PracticeId, CerebrumUser.UserId, ipAddress);
                return View(rpt);
            }
            return View();
        }
        [HttpPost]
        public async Task<ActionResult> ReportPreview(VMOLISReportFilter request)
        {
            if (request != null)
            {
                string ipAddress = IPAddress(Request);
                request.PracticeId = CerebrumUser.PracticeId;
                request.UserId = CerebrumUser.UserId;
                request.IPAddress = ipAddress;

                var rpt = await _apiWrapper.GetReportPreviewFilterAsync(request);

                return View(rpt);
            }
            return View();
        }

        //[HttpGet]
        //public ActionResult ReportPreview(int? report)
        //{
        //    if (report != null)
        //    {
        //        var rpt = _cvService.GetReport((int)report, CerebrumUser.PracticeId, CerebrumUser.UserId, Request.UserHostAddress);
        //        rpt.Filter = rpt.Fill_Filters();
        //        return View(rpt);
        //    }
        //    return View();
        //}
        //[HttpPost]
        //public ActionResult ReportPreview(VMOLISReportFilter s)
        //{
        //    if (s != null)
        //    {
        //        s.PracticeId = CerebrumUser.PracticeId;
        //        s.UserId = CerebrumUser.UserId;
        //        s.IPAddress = Request.UserHostAddress;
        //        var rpt = _cvService.GetReportWithFilter(s);
        //        rpt.Filter = s;
        //        return View(rpt);
        //    }
        //    return View();
        //}
        // ChildActionOnly is not used in ASP.NET Core - child actions are handled differently
        [HttpGet]
        public ActionResult OLISReportFilter(int id)
        {
            var f = new VMOLISReportFilter { PracticeId = CerebrumUser.PracticeId, ReportId = id };
            return PartialView(f);
        }
        [HttpPost]
        public ActionResult OLISReportFilter(VMOLISReportFilter s)
        {
            if (s != null)
            {
                string ipAddress = IPAddress(Request);
                s.PracticeId = CerebrumUser.PracticeId;
                s.UserId = CerebrumUser.UserId;
                s.IPAddress = ipAddress;
                var rpt = _cvService.GetReportWithFilter(s);
                rpt.Filter = s;
                return View("ReportPreview", rpt);
            }
            return View();
        }

        [HttpGet]
        public ActionResult ReportTestsSummary(int? report)
        {
            if (report != null)
            {
                string ipAddress = IPAddress(Request);
                var rpt = _cvService.GetReport((int)report, CerebrumUser.PracticeId, CerebrumUser.UserId, ipAddress);
                return View(rpt);
            }
            return View();
        }
        [HttpGet]
        public ActionResult ReportTabularSummary(int? report)
        {
            if (report != null)
            {
                string ipAddress = IPAddress(Request);
                var rpt = _cvService.GetReport((int)report, CerebrumUser.PracticeId, CerebrumUser.UserId, ipAddress);
                return View(rpt.tabularReports);
            }
            return View();
        }
        [HttpGet]
        public ActionResult ReportDetails(int? report)
        {
            if (report != null)
            {
                string ipAddress = IPAddress(Request);
                var rpt = _cvService.GetReport((int)report, CerebrumUser.PracticeId, CerebrumUser.UserId, ipAddress);
                return View(rpt);
            }
            return View();
        }
        [HttpGet]
        public async Task<ActionResult> Report(int id)
        {
            if (id > 0)
            {
                string ipAddress = IPAddress(Request);
                var rpt = await _apiWrapper.GetOLISSavedReportAsync(id, CerebrumUser.PracticeId, CerebrumUser.UserId, ipAddress);
                var json = JsonConvert.SerializeObject(rpt, Newtonsoft.Json.Formatting.None,
                                new JsonSerializerSettings
                                {
                                    NullValueHandling = NullValueHandling.Ignore
                                });
                return Content(rpt);
            }
            return Content("Report not exists");
            //VMOlisReport rpt = _cvService.GetOLISSavedReport(id, CerebrumUser.PracticeId,CerebrumUser.UserId, Request.UserHostAddress);

            //if (rpt==null)
            //    return Content("Report not exists");
            //var json = JsonConvert.SerializeObject(rpt, Newtonsoft.Json.Formatting.None,
            //                new JsonSerializerSettings
            //                {
            //                    NullValueHandling = NullValueHandling.Ignore
            //                });
            //return Content(json);
        }
        [HttpGet]
        public ActionResult ClinicalViewer(int id)
        {
            return View();
        }
        [HttpGet]
        public ActionResult ClinicalViewer2(int id)
        {
            return View();
        }

        public ActionResult GeneratePDFReport(string htmlStr, string pageTitle)
        {
            // string html = Server.UrlDecode(htmlStr);            
            string html = System.Uri.UnescapeDataString(htmlStr);

            string fileName = "";

            if (!String.IsNullOrWhiteSpace(html))
            {
                fileName = _documentsBLL.GenerateOlisViewerFile(CerebrumUser.UserId, CerebrumUser.FullName, html, pageTitle);
                if (!String.IsNullOrWhiteSpace(fileName))
                {
                    return Json(new { success = true, message = "", fileName = fileName });
                }
            }

            return Json(new { success = false, message = "An error occured" });
        }

        public FileResult PDFReport(string fileName)// sql
        {
            var pdfBuilder = new Cerebrum.BLL.Documents.PDFBuilder();

            string errMsg = "There is no report data!";
            byte[] contents = null;

            if (!String.IsNullOrWhiteSpace(fileName))
            {
                string filePath = _documentsBLL.GetOlisViewerFilePath(fileName: fileName);
                contents = System.IO.File.ReadAllBytes(filePath);

                if (contents != null)
                {
                    _documentsBLL.DeleteTempFile(filePath);
                    return File(contents, "application/pdf");
                }
            }

            contents = pdfBuilder.GetErrorPdfBytes(errMsg);
            Response.Charset = "utf-8";
            Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
            return File(contents, "application/pdf");

        }

        [HttpGet]
        public FileResult ClinicalViewerED(string url, string type)
        {
            string path = string.Empty;
            try
            {
                path = Server.MapPath(url);
            }
            catch
            {
                path = url;
            }
            byte[] content = System.IO.File.ReadAllBytes(path);
            string returntype = string.Empty;
            switch (type.ToLower())
            {
                case "jpeg":
                case "jpg":
                    returntype = "image/jpeg";
                    break;
                case "pdf":
                    returntype = "application/pdf";
                    break;
                case "png":
                    returntype = "application/png";
                    break;
                case "tiff":
                    returntype = "image/bmp";
                    break;
                case "XML":
                    returntype = "application/xml";
                    break;
                case "rtf":
                    return File(content, System.Net.Mime.MediaTypeNames.Application.Rtf, (new FileInfo(path)).Name);

            }
            return File(content, returntype, Path.GetFileName(path));
        }

        public JsonResult SearchPatients(string term_)
        {
            if (term_ != null && term_ != "")
            {
                int practiceId = CerebrumUser.PracticeId; ;


                List<VMLookupItem> list = _patientBLL.SearchPatients(term_, practiceId);

                return Json(list, JsonRequestBehavior.AllowGet);
            }
            else
            { return null; }
        }

        public JsonResult SearchPracticeDoctors(string term_)
        {
            if (term_ != null && term_ != "")
            {
                int practiceId = CerebrumUser.PracticeId; ;


                List<VMLookupItem> list = _patientBLL.SearchPracticeDoctors(term_, practiceId);

                return Json(list, JsonRequestBehavior.AllowGet);
            }
            else
            { return null; }
        }

        public JsonResult SearchPracticeUsers(string term_)
        {
            if (term_ != null && term_ != "")
            {
                int practiceId = CerebrumUser.PracticeId;


                List<VMLookupItem> list = _patientBLL.SearchPracticeUsers(term_, practiceId);

                return Json(list, JsonRequestBehavior.AllowGet);
            }
            else
            { return null; }
        }

        public string RenderViewToString(string controllerName, string viewName, object viewData)
        {
            var context = HttpContextProvider.Current;
            var contextBase = new HttpContextWrapper(context);
            var routeData = new RouteData();
            routeData.Values.Add("controller", controllerName);

            var controllerContext = new ControllerContext(contextBase,
                                                          routeData,
                                                          new OLISController(_apiWrapper, _practiceDoctorBLL, _patientBLL, _olisbll, _documentsBLL, _httpClientFactory));

            var razorViewEngine = new RazorViewEngine();
            var razorViewResult = razorViewEngine.FindView(controllerContext,
                                                           viewName,
                                                           "",
                                                           false);

            var writer = new StringWriter();
            var viewContext = new ViewContext(controllerContext,
                                              razorViewResult.View,
                                              new ViewDataDictionary(viewData),
                                              new TempDataDictionary(),
                                              writer);
            razorViewResult.View.Render(viewContext, writer);

            return writer.ToString();
        }

        private static string RenderViewToString(ControllerContext context,
                                    string viewPath,
                                    object model = null,
                                    bool partial = false)
        {
            // first find the ViewEngine for this view
            ViewEngineResult viewEngineResult = null;
            if (partial)
                viewEngineResult = ViewEngines.Engines.FindPartialView(context, viewPath);
            else
                viewEngineResult = ViewEngines.Engines.FindView(context, viewPath, null);

            if (viewEngineResult == null)
                throw new FileNotFoundException("View cannot be found.");

            // get the view and attach the model to view data
            var view = viewEngineResult.View;
            context.Controller.ViewData.Model = model;

            string result = null;

            using (var sw = new StringWriter())
            {
                var ctx = new ViewContext(context, view,
                                            context.Controller.ViewData,
                                            context.Controller.TempData,
                                            sw);
                view.Render(ctx, sw);
                result = sw.ToString();
            }

            return result;
        }
#else
        // Stub implementations for when Cerebrum.Labs is not available
        [HttpGet]
        [ActionName("OLISReportSearch")]
        public ActionResult Index(int? patientId)
        {
            ViewBag.Message = "OLIS functionality is currently disabled. Please contact administrator.";
            return View("Disabled");
        }

        [HttpGet]
        [ActionName("OLISReportConsentReinstatement")]
        public ActionResult Index(VMOLISConsentReinstatement c)
        {
            ViewBag.Message = "OLIS functionality is currently disabled. Please contact administrator.";
            return View("Disabled");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Index(VMOLISSearch s)
        {
            ViewBag.Message = "OLIS functionality is currently disabled. Please contact administrator.";
            return View("Disabled");
        }

        // Add other stub methods as needed
        public ActionResult Preloads()
        {
            ViewBag.Message = "OLIS functionality is currently disabled. Please contact administrator.";
            return View("Disabled");
        }
#endif

    }
}
