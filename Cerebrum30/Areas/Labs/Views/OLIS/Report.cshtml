﻿@model Cerebrum.ViewModels.OLIS.VMOlisReport
@using Cerebrum30.Utility
@using Cerebrum.ViewModels.Patient
<div style="width: 100%;" >
    
    <div class="row">
        <div class="col-sm-4"> @Html.PartialAsync("Patient", @Model.Patient)</div>
        <div class="col-sm-4"> @Html.PartialAsync("Provider", @Model.Provider)</div>
        <div class="col-sm-4"> @Html.PartialAsync("ReportDetails", @Model.ReportDetails)</div>
    </div>
    @if (!string.IsNullOrWhiteSpace(@Model.PatientReportBlockConsentNote))
    {
    <div class="alert alert-danger text-center" role="alert">
        <p class="red">@Html.Raw(@Model.PatientReportBlockConsentNote)</p>
    </div>
    }
    <div class="row">
        <div class="col-md-12">@*width: 100%; display: block; clear: both; background: #4cff00*@
            <hr />
            <div class="dont-break-out fixedWidthfont" style="float: left;">
               @foreach (var c in @Model.ReportNotes)
               {*@ string comment = c;
                <div>@Html.Raw(comment)</div><br />
               }            
            </div>            
        </div>
    </div>

    @if (@Model.Patient != null && (!string.IsNullOrWhiteSpace(@Model.Patient.OrderNotes)))
    {
        <div class="row">
            <div class="col-md-12">
                <h4>Comments (orderNote)</h4>
                <div class="dont-break-out fixedWidthfont">@Html.Raw(Cerebrum.ViewModels.Extension.FormatHL7Report(@Model.Patient.OrderNotes))</div>
                <hr />
            </div>
        </div>

</div>

<div class="requested-tests" style="width: 100%; margin-bottom: 70px">
    @Html.PartialAsync("TestCategories", @Model.TestCategories)
</div>

<hr />
<div class="row">
    <div class="col-md-3">@Html.PartialAsync("OLISCCList", @Model.CCList)</div>
    <div class="col-md-3">@Html.PartialAsync("OLISOrderingFacility", @Model.OrderingFacility)</div>
    <div class="col-md-3">
        @if (@Model.AdmittingProvider != null)
        {
        <div>
            <h4>Admitting Provider</h4>
            <hr />
            @Html.PartialAsync("PractitionerProvider", @Model.AdmittingProvider)
        </div>

        @if (@Model.AttendingProvider != null)
        {
        <div>
            <h4>Attending Provider</h4>
            <hr />
            *@ @Html.PartialAsync("PractitionerProvider", @Model.AdmittingProvider)
        </div>

    </div>
</div>