﻿@model Cerebrum.ViewModels.Triage.VMWaitlistMain
@{

}

@section customcss{
    <!-- TODO: Replace Styles.Render with direct link references for ASP.NET Core -->

@section scripts{
    <script src="~/Areas/Schedule/Scripts/appointments.js"></script>
    <script src="~/Areas/Triage/Scripts/triage.js"></script>

 <script src="~/Areas/Schedule/Scripts/sharedFunctions.js"></script> }

<div style="margin-left:auto;margin-right:auto;margin-top:10px;" id="div-searchWaitList">
    @{Html.RenderPartial("_waitlistSearch", @Model.WaitListSearch);}
</div>
<hr/>

        @Html.PartialAsync("_waitlistPaging", @Model.WaitListSearch)
    </div>
<div id="waitlist-container">
    @{Html.RenderPartial("_waitingList", @Model.WaitList);}
</div>
<br />
*@<div id="waitlist-triage-urgencies" class="hidden">

        <ul class="ul-appstatus">
            @foreach (var item in wlTriageUrgencies)
            {
                <li class="app-wailist-item" data-triage-urgency-id="@item.Value">
                    <div>@item.Text</div>
                </li>
            }

        </ul>
        <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>@* *@
    </div>