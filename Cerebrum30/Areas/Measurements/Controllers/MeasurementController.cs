﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Admin;
using Cerebrum.BLL.Measurements;
using Cerebrum.BLL.Practice;
using Cerebrum.BLL.RadDicom;
using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using Cerebrum.ViewModels.Admin;
using Cerebrum.ViewModels.Measurements;
using Cerebrum.ViewModels.RadDicom;
using Cerebrum.ViewModels.VP;
using Cerebrum30.Areas.Measurements.DataAccess;
using Cerebrum30.Areas.Measurements.Models.DataObjects;
using Cerebrum30.Areas.Measurements.Models.ViewModels;
using Cerebrum30.Areas.VP.DataAccess;
using Cerebrum30.Controllers;
using Cerebrum30.DAL.DataAccess.Repositories;
using Cerebrum30.Services.WorkSheet;
using Cerebrum30.Utility;
using Cerebrum30.WebAPI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using AwareMD.WorkSheet.Dto.PlaceHolder;
using System.Data.Entity;
using System.Configuration;
using Cerebrum.BLL.TestBase.Validation;
using Cerebrum.ViewModels.Documents;
// TODO: Replace with ASP.NET Core view rendering
// using RenderPartialToStringExtensions = Cerebrum.BLL.Utility.RenderPartialToStringExtensions;
using Cerebrum.ContactManager;

namespace Cerebrum30.Areas.Measurements.Controllers
{
    // Note: ValidateInput attribute not needed in ASP.NET Core - input validation is handled differently
    //[CheckRoles(Roles = "")]
    //[CheckPermissions(PermissionType = "worksheet")]
    public class MeasurementController : BaseController
    {
        readonly string MODEL_FRM_PREV_POST = "MODEL_FRM_PREV_POST";
        readonly string NEW_LOG = "NEW_LOG";
        readonly string NEW_SHOW_MODE = "SHOW_MODE_CHANGED";

        //readonly string NEW_OPTIONS = "NEW_OPTIONS";
        //readonly string ADD_ALERT_ERROR = "ADD_ALERT_ERROR";
        readonly string ADD_CUSTOMIZE_RP = "ADD_CUSTOMIZE_RP";
        readonly string SCRIPT_TO_EXECUTE = "SCRIPT_TO_EXECUTE";
        //readonly string MODEL_WITH_VALIDATION_ERRORS = "MODEL_WITH_VALIDATION_ERRORS";
        //readonly string REPORT_SENT = "REPORT_SENT";
        readonly string OPENRAWDATA_MSSG = "OPENRAWDATA_MSSG";
        readonly string OPENRAWDATA_SCRIPT = "OPENRAWDATA_SCRIPT";

        private Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository measurementRepo;
        private UserRepository userRepo;
        private VPRepository vpRepo;
        private MeasurementBLL _measurementBll;
        private HrmValidator _hrmValidator;
        private IPracticeBLL _practiceBll;
        private IRadDicomStudyBLL _radStudyBll;
        private IHttpClientFactory _httpClientFactory;
        private IWorkSheetApiToggle _wsToggle;
        private int _appointmentTestLogID;
        private readonly IAppointmentPriorityConfigurationBLL _appPriorityBll;
        private readonly IContactManagerService _contactManagerService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private ITokenHelper UtilityTokenHelper => new TokenHelper(_httpClientFactory, HttpContext);

        public MeasurementController(Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository measurementRepo, UserRepository userRepo, VPRepository vpRepo, IRadDicomStudyBLL radStudyBll, IHttpClientFactory httpClientFactory)
            : this(measurementRepo, userRepo, vpRepo, radStudyBll, httpClientFactory, null)
        { }

        public MeasurementController(Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository measurementRepo, UserRepository userRepo, VPRepository vpRepo, IRadDicomStudyBLL radStudyBll, IHttpClientFactory httpClientFactory, IWorkSheetApiToggle wsToggle)
            : this(measurementRepo, userRepo, vpRepo, new MeasurementBLL(), null, radStudyBll, httpClientFactory, null, null, null)
        { }

        public MeasurementController(Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository _measRepo, UserRepository _userRepo, VPRepository _vpRepo, MeasurementBLL measurementBll, IPracticeBLL practiceBll, IRadDicomStudyBLL radStudyBll, IHttpClientFactory httpClientFactory, IWorkSheetApiToggle wsToggle, IAppointmentPriorityConfigurationBLL appPriorityBll, IContactManagerService contactManagerService, IWebHostEnvironment webHostEnvironment = null)
        {
            measurementRepo = _measRepo;
            userRepo = _userRepo;
            vpRepo = _vpRepo;
            _measurementBll = measurementBll;
            _hrmValidator = new HrmValidator();
            _practiceBll = practiceBll;
            _radStudyBll = radStudyBll;
            _httpClientFactory = httpClientFactory;
            _wsToggle = wsToggle;
            _appPriorityBll = appPriorityBll;
            _contactManagerService = contactManagerService;
            _webHostEnvironment = webHostEnvironment;
        }

        #region Methods
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetTestList()
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            var tests = measurementRepo.GetTestNames();

            tests.ForEach
               (
                   l => lst.Add(new SelectListItem
                   {
                       Value = l.Id.ToString(),
                       Text = l.Name
                   }
              ));

            return lst;
        }
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetAppointmentList()
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            var appointments = measurementRepo.GetAppointments();

            appointments.ForEach
               (
                   l => lst.Add(new SelectListItem
                   {
                       Value = l.ToString(),
                       Text = l.ToString()
                   }
              ));
            return lst;
        }
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetPracticeList()
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            var appointments = measurementRepo.GetPractices();

            appointments.ForEach
               (
                   l => lst.Add(new SelectListItem
                   {
                       Value = l.ID.ToString(),
                       Text = l.Name.ToString()
                   }
              ));

            return lst;
        }
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetShowModeList()
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem() { Text = "Visible AND Filled", Value = "1" });
            lst.Add(new SelectListItem() { Text = "Visible", Value = "2" });
            lst.Add(new SelectListItem() { Text = "All", Value = "3" });

            return lst;
        }
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetOperatorList()
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            measurementRepo.GetAllMeasurementOperators().ForEach(x => lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem() { Text = x.name, Value = x.Id.ToString() }));

            return lst;
        }
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetOperatorList(List<MeasurementOperator> moList)
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            moList.ForEach(x => lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem() { Text = x.name, Value = x.Id.ToString() }));

            return lst;
        }
        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> GetLogList(int apptID, int testID)
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem() { Value = "-1", Text = "---" });

            var logs = measurementRepo.GetLogsWithName(apptID, testID)
                        .OrderByDescending(L => L.Id).ToList();

            logs.ForEach
                (
                    l => lst.Add(new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem
                    {
                        Value = l.Id.ToString(),
                        Text = l.Date.Value.ToString() + "---" + l.UserName
                    }
               ));

            return lst;
        }
        private int GetMaxLogID(List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst)
        {
            int retVal = -1;

            lst = (from c in lst
                   where c.Value != "-1"
                   select c).ToList();

            if (lst != null && lst.Count > 0)
            {
                var maxID = lst.OrderByDescending(l => Int32.Parse(l.Value)).ToList()[0];

                if (maxID != null)
                {
                    retVal = Int32.Parse(maxID.Value);
                }
            }

            return retVal;
        }
        private int GetMaxID(List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> lst)
        {
            int retVal = -1;

            retVal = lst.Max(x => Int32.Parse(x.Value));
            return retVal;
        }

        private WorkSheetVM GetReportPhraseValues(WorkSheetVM vm)
        {
            var lst = measurementRepo.GetReportPhraseSavedValues(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

            //clear all values
            vm.ReportPhraseViewModel.MainList.ReportPhraseSavedText.ForEach(x => x.Value = string.Empty);

            foreach (var item in lst)
            {
                var entry = vm.ReportPhraseViewModel.MainList.ReportPhraseSavedText.FirstOrDefault(r => r.TopLevelReportPhraseID == item.TopLevelReportPhraseID);

                if (entry != null)
                {
                    entry.Value = item.Value;
                }
                else
                {
                    entry.Value = string.Empty;
                }
            }

            return vm;
        }

        public ActionResult ReportPhraseCustomize(int reportPhraseID, int userID)
        {
            ReportPhrase_Val vm = measurementRepo.GetReportPhraseCustomValue(reportPhraseID, userID);

            return View(vm);
        }

        [HttpPost]
        public ActionResult ReportPhraseCustomize(ReportPhrase_Val vm)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {

                if (string.IsNullOrEmpty(vm.Text))
                {
                    errored = true;
                    message = "Custom Text Value required";
                }
                else
                {
                    measurementRepo.SaveReportPhraseCustomValue(vm, CerebrumUser.UserId, GetIPAddress());
                    message = "Changes Saved";
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Errored = errored ? "1" : "0", Result = message });
        }

        [Obsolete("not using anymore, see ticket 12153")]
        public ActionResult _PreviousTestsByPatient(int patientrecordid, int appointmentid)
        {
            List<VMPreviousTestItem> lstPrevTests = measurementRepo.GetPatientsPreviousTests(CerebrumUser.PracticeId, patientrecordid, appointmentid);

            // var url = new UrlHelper(Request.RequestContext); // TODO: Replace with ASP.NET Core URL generation
            //exclude vps
            lstPrevTests = lstPrevTests.Where(t => t.TestName.ToLower() != (Utility.Helper.VP_TEST_NAME.ToLower())).ToList();
            lstPrevTests.ForEach(x =>
            {
                x.URL = Url.Action("Index", "Measurement",
                        new
                        {
                            AppointmentID = x.AppointmentID,
                            Testid = x.TestID,
                            AppointmentTestID = x.AppointmentTestID,
                            area = "MeasurementS"
                        });
            });

            return PartialView("_PreviousTestsByPatient", lstPrevTests);
        }

        public JsonResult GetPreviousTestsByPatient(int patientID)
        {
            List<VMPreviousTestItem> lstPrevTests =
           measurementRepo.GetPatientsPreviousTests(CerebrumUser.PracticeId, patientID);
            // var url = new UrlHelper(Request.RequestContext); // TODO: Replace with ASP.NET Core URL generation
            //exclude vps
            lstPrevTests = lstPrevTests.Where(t => t.TestName.ToLower() != (Utility.Helper.VP_TEST_NAME.ToLower())).ToList();
            lstPrevTests.ForEach(x =>
            {
                x.URL = Url.Action("Index", "Measurement",
                        new
                        {
                            AppointmentID = x.AppointmentID,
                            Testid = x.TestID,
                            AppointmentTestID = x.AppointmentTestID,
                            area = "MeasurementS"
                        });
            });

            return Json(new { Result = lstPrevTests });
        }


        public JsonResult GetPreviousTestsAll(int appointmentID, int testID, int patientId)
        {
            var practiceId = CerebrumUser.PracticeId;
            var userId = CerebrumUser.UserId;
            var practiceDoctorId = CerebrumUser.PracticeDoctorId;
            // TODO: Implement GetPreviousTestsMain method in MeasurementBLL
            var testMain = new Cerebrum.ViewModels.TestBase.VMPreviousTestsMain(); // _measurementBll.GetPreviousTestsMain(patientId, practiceId, null, System.DateTime.Now);
            // commented out for ticket #12159
            //var openPreviousReport = OpenPreviousReport(practiceId, userId, appointmentID, testID, testMain.PreviousTests);
            //return Json(new { Result = testMain.PreviousTests, ResultVP = testMain.PreviousVPTests, AutoOpenPreviousReport = openPreviousReport });
            return Json(new { Result = testMain.PreviousTests, ResultVP = testMain.PreviousVPTests });
        }
        private WorkSheetVM Get_ReportPhrases(WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID)
        {
            return Get_ReportPhrases(CerebrumUser.PracticeId, vm, appointmentID, testID, appointmentTestLogID);
        }
        public Dictionary<string, long> Get_ReportPhrases_ForPerformance(int practiceId, WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID)
        {
            Dictionary<string, long> result = new Dictionary<string, long>();
            var watch = System.Diagnostics.Stopwatch.StartNew();
            result.Add("Start=", watch.ElapsedMilliseconds);

            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            result.Add("GetUserID=", watch.ElapsedMilliseconds);

            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            result.Add("GetPatientByAppointment=", watch.ElapsedMilliseconds);

            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);
            result.Add("GetGroupIdByTest=", watch.ElapsedMilliseconds);

            vm.PracticeID = practiceId;
            vm.DoctorID = vm.DoctorID != 0 ? vm.DoctorID : vpRepo.GetMainDoctor(vm.AppointmentID);
            result.Add("GetMainDoctor=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.GetReportPhrasesByGroup(vm.TestID, vm.DoctorID, vm.GroupID);
            result.Add("GetReportPhrasesByGroup=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.GetCustomReportPhrases(vm.ReportPhrases, vm.DoctorID, vm.PracticeID, vm.TestID);
            result.Add("GetCustomReportPhrases=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            result.Add("LoadReportPhraseSavedValues=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.LoadReportPhraseNormals(vm.ReportPhrases);
            result.Add("LoadReportPhraseNormals=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.LoadReportPhraseeMeasurementsScroll(vm.ReportPhrases);
            result.Add("LoadReportPhraseeMeasurementsScroll=", watch.ElapsedMilliseconds);

            vm.ImpressionBoxID = measurementRepo.GetImpressionBoxID(vm.ReportPhrases);
            result.Add("GetImpressionBoxID=", watch.ElapsedMilliseconds);

            vm = measurementRepo.RemoveIllegalChars(vm);
            result.Add("RemoveIllegalChars=", watch.ElapsedMilliseconds);

            return result;
        }

        private WorkSheetVM Get_ReportPhrases(int practiceId, WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID)
        {
            vm.DoctorID = vm.DoctorID != 0 ? vm.DoctorID : vpRepo.GetMainDoctor(appointmentID);
            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(testID);
            return Get_ReportPhrases(practiceId, vm, measurementRepo.GetReportPhrases(testID, vm.DoctorID, vm.GroupID), appointmentID, testID, appointmentTestLogID);
        }

        private WorkSheetVM Get_ReportPhrases(int practiceId, WorkSheetVM vm, List<ReportPhrase> rp, int appointmentID, int testID, int appointmentTestLogID)
        {
            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);
            vm.PracticeID = practiceId;
            vm.DoctorID = vm.DoctorID != 0 ? vm.DoctorID : vpRepo.GetMainDoctor(vm.AppointmentID);


            //if (_wsToggle.GetPracticeConfigList().Where(x => x.PracticeId == practiceId && x.IsConsumeApi).Any())
            //{
            //    vm.ReportPhrases = Cerebrum30.Services.WorkSheet.WorkSheetClients.GetReportPhrasesAsync(_httpClientFactory, vm.PracticeID, vm.TestID, vm.DoctorID, vm.GroupID, vm.AppointmentID, vm.AppointmentTestLogID).Result;
            //}
            //else
            //{
            vm.ReportPhrases = measurementRepo.GetReportPhrasesByGroup(rp, practiceId, vm.TestID, vm.DoctorID, vm.GroupID);
            vm.ReportPhrases = measurementRepo.GetCustomReportPhrases(vm.ReportPhrases, vm.DoctorID, vm.PracticeID, vm.TestID); // being called inside service.GetReportPhrasesByGroup
            vm.ReportPhrases = measurementRepo.LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID); // being called inside service.GetReportPhrasesByGroup
            vm.ReportPhrases = measurementRepo.LoadReportPhraseNormals(vm.ReportPhrases); // being called inside service.GetReportPhrasesByGroup
            vm.ReportPhrases = measurementRepo.LoadReportPhraseeMeasurementsScroll(vm.ReportPhrases);
            //vm.ImpressionBoxID = measurementRepo.GetImpressionBoxID(vm.ReportPhrases);

            vm = measurementRepo.RemoveIllegalChars(vm);
            //}

            vm.ImpressionBoxID = measurementRepo.GetImpressionBoxID(vm.ReportPhrases);

            return vm;
        }

        private WorkSheetVM Get_ReportPhrases_Vertical(WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID)
        {
            return Get_ReportPhrases_Vertical(CerebrumUser.PracticeId, vm, appointmentID, testID, appointmentTestLogID);
        }

        private WorkSheetVM Get_ReportPhrases_Vertical(int practiceId, WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID)
        {
            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(testID);
            vm.DoctorID = vm.DoctorID != 0 ? vm.DoctorID : vpRepo.GetMainDoctor(appointmentID);

            return Get_ReportPhrases_Vertical(practiceId, vm, measurementRepo.GetReportPhrases(testID, vm.DoctorID, vm.GroupID), appointmentID, testID, appointmentTestLogID);
        }

        private WorkSheetVM Get_ReportPhrases_Vertical(int practiceId, WorkSheetVM vm, List<ReportPhrase> rp, int appointmentID, int testID, int appointmentTestLogID)
        {
            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);
            vm.PracticeID = practiceId;
            vm.DoctorID = vm.DoctorID != 0 ? vm.DoctorID : vpRepo.GetMainDoctor(vm.AppointmentID);

            //if (_wsToggle.GetPracticeConfigList().Where(x => x.PracticeId == practiceId && x.IsConsumeApi).Any())
            //{
            //    vm.ReportPhrases = Cerebrum30.Services.WorkSheet.WorkSheetClients.GetReportPhrasesAsync(_httpClientFactory, vm.PracticeID, vm.TestID, vm.DoctorID, vm.GroupID, vm.AppointmentID, vm.AppointmentTestLogID).Result;
            //}
            //else
            //{
            //vm.ReportPhrases = measurementRepo.GetReportPhrasesByGroup(vm.TestID, vm.DoctorID);
            vm.ReportPhrases = measurementRepo.GetReportPhrasesByGroup(rp, practiceId, vm.TestID, vm.DoctorID, vm.GroupID);
            vm.ReportPhrases = measurementRepo.GetCustomReportPhrases(vm.ReportPhrases, vm.DoctorID, vm.PracticeID, vm.TestID);
            vm.ReportPhrases = measurementRepo.LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            vm.ReportPhrases = measurementRepo.LoadReportPhraseNormals(vm.ReportPhrases);
            vm.ReportPhrases = measurementRepo.LoadReportPhraseeMeasurementsScroll(vm.ReportPhrases);
            //vm.ImpressionBoxID = measurementRepo.GetImpressionBoxID(vm.ReportPhrases);
            vm = measurementRepo.RemoveIllegalChars(vm);
            //}

            vm.ImpressionBoxID = measurementRepo.GetImpressionBoxID(vm.ReportPhrases);

            return vm;
        }

        public Dictionary<string, long> Get_ReportPhrases_Vertical_ForPerformance(int practiceId, WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID)
        {
            Dictionary<string, long> result = new Dictionary<string, long>();
            var watch = System.Diagnostics.Stopwatch.StartNew();
            result.Add("Start=", watch.ElapsedMilliseconds);

            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            result.Add("GetPatientByAppointment=", watch.ElapsedMilliseconds);

            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);
            result.Add("GetGroupIdByTest=", watch.ElapsedMilliseconds);

            vm.PracticeID = practiceId;
            vm.DoctorID = vm.DoctorID != 0 ? vm.DoctorID : vpRepo.GetMainDoctor(vm.AppointmentID);
            result.Add("GetMainDoctor=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.GetReportPhrasesByGroup(practiceId, vm.TestID, vm.DoctorID);
            result.Add("GetReportPhrasesByGroup=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.GetCustomReportPhrases(vm.ReportPhrases, vm.DoctorID, vm.PracticeID, vm.TestID);
            result.Add("GetCustomReportPhrases=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            result.Add("LoadReportPhraseSavedValues=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.LoadReportPhraseNormals(vm.ReportPhrases);
            result.Add("LoadReportPhraseNormals=", watch.ElapsedMilliseconds);

            vm.ReportPhrases = measurementRepo.LoadReportPhraseeMeasurementsScroll(vm.ReportPhrases);
            result.Add("LoadReportPhraseeMeasurementsScroll=", watch.ElapsedMilliseconds);

            vm.ImpressionBoxID = measurementRepo.GetImpressionBoxID(vm.ReportPhrases);
            result.Add("GetImpressionBoxID=", watch.ElapsedMilliseconds);

            vm = measurementRepo.RemoveIllegalChars(vm);
            result.Add("RemoveIllegalChars=", watch.ElapsedMilliseconds);

            return result;
        }


        private WorkSheetVM Get_Measurements_Vertical(WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(testID);
            var q = measurementRepo.GetMeasurementCategory(vm.GroupID, appointmentID, testID, appointmentTestLogID);

            return Get_Measurements_Vertical(vm, q, GetOperatorList(), appointmentID, testID, appointmentTestLogID, practiceID, Mode);
        }

        private WorkSheetVM Get_Measurements_Vertical(WorkSheetVM vm, List<MeasurementCategory> mc, List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> mo, int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.PracticeID = practiceID; //todo

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            //vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);

            vm.ShowModeList = GetShowModeList();

            vm.MeasurementOperators = mo;

            vm.ShowMode = Mode;

            //get categories and their measurements
            //if (_wsToggle.GetPracticeConfigList().Where(x => x.PracticeId == vm.PracticeID && x.IsConsumeApi).Any())
            //{
            //    var lst = Cerebrum30.Services.WorkSheet.WorkSheetClients.Get_Measurements(_httpClientFactory, vm.PracticeID, vm.TestID, vm.GroupID, vm.AppointmentID, vm.AppointmentTestLogID, vm.ShowMode).Result;
            //    vm.MeasurementCategories = lst;
            //    vm = this.measurementRepo.GetBSAValue(vm);
            //}
            //else
            //{
            var lst = measurementRepo.GetCategoriesAndMeasurements(mc, vm.GroupID, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            //var lst = measurementRepo.GetCategoriesAndMeasurements(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            //lst = measurementRepo.FilterCategoriesByGroup(vm.GroupID, lst);

            //get saved values for each measurement 
            vm.MeasurementCategories = measurementRepo.LoadSavedValues(lst, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PatientID);

            vm = this.measurementRepo.GetBSAValue(vm);

            //Filter meas by Practice
            if (vm.ShowMode != MeasurementShowMode.All)
            {
                vm.MeasurementCategories = this.measurementRepo.FilterMeasurementsByPractice(vm.PracticeID, lst, vm.ShowMode);
            }

            //filter according to show mode
            vm.MeasurementCategories = measurementRepo.FilterByShowMode(lst, vm.ShowMode);
            //}

            return vm;
        }

        public Dictionary<string, long> Get_Measurements_Vertical_ForPerformance(WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            Dictionary<string, long> result = new Dictionary<string, long>();
            var watch = System.Diagnostics.Stopwatch.StartNew();
            result.Add("Start=", watch.ElapsedMilliseconds);

            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.PracticeID = practiceID; //todo

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            result.Add("GetUserID=", watch.ElapsedMilliseconds);

            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            result.Add("GetPatientByAppointment=", watch.ElapsedMilliseconds);

            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);
            result.Add("GetGroupIdByTest=", watch.ElapsedMilliseconds);

            vm.ShowModeList = GetShowModeList();
            result.Add("GetShowModeList=", watch.ElapsedMilliseconds);

            vm.MeasurementOperators = GetOperatorList();
            result.Add("GetOperatorList=", watch.ElapsedMilliseconds);

            vm.ShowMode = Mode;

            //get categories and their measurements
            var lst = measurementRepo.GetCategoriesAndMeasurements(vm.GroupID, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            result.Add("GetCategoriesAndMeasurements=", watch.ElapsedMilliseconds);
            //var lst = measurementRepo.GetCategoriesAndMeasurements(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            //result.Add("GetCategoriesAndMeasurements=", watch.ElapsedMilliseconds);

            //lst = measurementRepo.FilterCategoriesByGroup(vm.GroupID, lst);
            //result.Add("FilterCategoriesByGroup=", watch.ElapsedMilliseconds);

            //get saved values for each measurement 
            vm.MeasurementCategories = measurementRepo.LoadSavedValues(lst, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PatientID);
            result.Add("LoadSavedValues=", watch.ElapsedMilliseconds);

            vm = this.measurementRepo.GetBSAValue(vm);
            result.Add("GetBSAValue=", watch.ElapsedMilliseconds);

            //Filter meas by Practice
            if (vm.ShowMode != MeasurementShowMode.All)
            {
                vm.MeasurementCategories = this.measurementRepo.FilterMeasurementsByPractice(vm.PracticeID, lst, vm.ShowMode);
                result.Add("FilterMeasurementsByPractice=", watch.ElapsedMilliseconds);
            }

            //filter according to show mode
            vm.MeasurementCategories = measurementRepo.FilterByShowMode(lst, vm.ShowMode);
            result.Add("FilterByShowMode=", watch.ElapsedMilliseconds);

            watch.Stop();

            return result;
        }

        private WorkSheetVM Get_Measurements(WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(testID);
            var q = measurementRepo.GetMeasurementCategory(vm.GroupID, appointmentID, testID, appointmentTestLogID);

            return Get_Measurements(vm, q, GetOperatorList(), appointmentID, testID, appointmentTestLogID, practiceID, Mode);
        }

        private WorkSheetVM Get_Measurements(WorkSheetVM vm, List<MeasurementCategory> mc, List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> mo, int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.PracticeID = practiceID; //todo

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            //vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);

            vm.ShowModeList = GetShowModeList();

            //vm.MeasurementOperators = GetOperatorList();
            vm.MeasurementOperators = mo;

            vm.ShowMode = Mode;

            //get categories and their measurements
            //if (_wsToggle.GetPracticeConfigList().Where(x => x.PracticeId == vm.PracticeID && x.IsConsumeApi).Any())
            //{
            //    var lst = Cerebrum30.Services.WorkSheet.WorkSheetClients.Get_Measurements(_httpClientFactory, vm.PracticeID, vm.TestID, vm.GroupID, vm.AppointmentID, vm.AppointmentTestLogID, vm.ShowMode).Result;
            //    vm.MeasurementCategories = lst;
            //    vm = this.measurementRepo.GetBSAValue(vm);
            //}
            //else
            //{
            var lst = measurementRepo.GetCategoriesAndMeasurements(mc, vm.GroupID, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            //var lst = measurementRepo.GetCategoriesAndMeasurements(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

            //lst = measurementRepo.FilterCategoriesByGroup(vm.GroupID, lst);

            //get saved values for each measurement 
            vm.MeasurementCategories = measurementRepo.LoadSavedValues(lst, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PatientID);

            vm = this.measurementRepo.GetBSAValue(vm);

            //Filter meas by Practice
            if (vm.ShowMode != MeasurementShowMode.All)
            {
                vm.MeasurementCategories = this.measurementRepo.FilterMeasurementsByPractice(vm.PracticeID, lst, vm.ShowMode);
            }

            //filter according to show mode
            vm.MeasurementCategories = measurementRepo.FilterByShowMode(lst, vm.ShowMode);
            //}
            return vm;
        }

        public Dictionary<string, long> Get_Measurements_ForPerformance(WorkSheetVM vm, int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            Dictionary<string, long> result = new Dictionary<string, long>();
            var watch = System.Diagnostics.Stopwatch.StartNew();
            result.Add("Start=", watch.ElapsedMilliseconds);

            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.PracticeID = practiceID; //todo

            vm.UserID = vm.UserID != 0 ? vm.UserID : userRepo.GetUserID(HttpContext?.User?.Identity?.Name);
            result.Add("GetUserID=", watch.ElapsedMilliseconds);

            vm.PatientID = vm.PatientID != 0 ? vm.PatientID : userRepo.GetPatientByAppointment(vm.AppointmentID);
            result.Add("GetPatientByAppointment=", watch.ElapsedMilliseconds);

            vm.GroupID = vm.GroupID != 0 ? vm.GroupID : userRepo.GetGroupIdByTest(vm.TestID);
            result.Add("GetGroupIdByTest=", watch.ElapsedMilliseconds);

            vm.ShowModeList = GetShowModeList();
            result.Add("GetShowModeList=", watch.ElapsedMilliseconds);

            vm.MeasurementOperators = GetOperatorList();
            result.Add("GetOperatorList=", watch.ElapsedMilliseconds);

            vm.ShowMode = Mode;

            //get categories and their measurements
            var lst = measurementRepo.GetCategoriesAndMeasurements(vm.GroupID, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            result.Add("GetCategoriesAndMeasurements=", watch.ElapsedMilliseconds);
            //var lst = measurementRepo.GetCategoriesAndMeasurements(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            //result.Add("GetCategoriesAndMeasurements=", watch.ElapsedMilliseconds);

            //lst = measurementRepo.FilterCategoriesByGroup(vm.GroupID, lst);
            //result.Add("FilterCategoriesByGroup=", watch.ElapsedMilliseconds);

            //get saved values for each measurement 
            vm.MeasurementCategories = measurementRepo.LoadSavedValues(lst, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PatientID);
            result.Add("LoadSavedValues=", watch.ElapsedMilliseconds);

            vm = this.measurementRepo.GetBSAValue(vm);
            result.Add("GetBSAValue=", watch.ElapsedMilliseconds);

            //Filter meas by Practice
            if (vm.ShowMode != MeasurementShowMode.All)
            {
                vm.MeasurementCategories = this.measurementRepo.FilterMeasurementsByPractice(vm.PracticeID, lst, vm.ShowMode);
                result.Add("FilterMeasurementsByPractice=", watch.ElapsedMilliseconds);
            }

            //filter according to show mode
            vm.MeasurementCategories = measurementRepo.FilterByShowMode(lst, vm.ShowMode);
            result.Add("FilterByShowMode=", watch.ElapsedMilliseconds);

            return result;
        }

        #endregion


        public ActionResult Index(int AppointmentID, int TestID, int AppointmentTestID = 0, int practiceID = 1, bool isWorkList = false)
        {
            WorkSheetVM vm;
            int userId = CerebrumUser.UserId;
            int practiceId = CerebrumUser.PracticeId;
            if (Config.Enabled && _wsToggle.IsPracticeWSApi(practiceId))
            {
                try
                {
                    vm = Cerebrum30.Services.WorkSheet.Api.Client.WorkSheetClients.GenerateWorkSheetAsync(_httpClientFactory, practiceId, TestID, userId, AppointmentID, AppointmentTestID, isWorkList).Result;
                }
                catch (Exception ex)
                {
                    _log.Error("WorkSheet API Error (GenerateWorkSheetAsync) - " + ex.GetBaseException().Message);
                    throw new Exception("Error consuming WorkSheet microservices, please contact administrators.");
                }
            }
            else
            {
                vm = GenerateWorkSheet(userId, practiceId, AppointmentID, TestID, AppointmentTestID = 0, practiceID = 1, isWorkList);
            }

            vm.DoctorNotes = measurementRepo.GetDoctorNotes(vm.AppointmentID, vm.TestID);
            vm.TechNotes = measurementRepo.GetTechNotes(vm.AppointmentID, vm.TestID);

            return View(vm);
        }

        internal WorkSheetVM GenerateWorkSheet_Old2(int UserId, int PracticeId, int AppointmentID, int TestID, int AppointmentTestID = 0, int practiceID = 1, bool isWorkList = false)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm.AppointmentID = AppointmentID;
            vm.TestID = TestID;
            AppointmentTest apptest = AppointmentTestID == 0 ? userRepo.GetAppointmentTest(AppointmentID, TestID) : userRepo.GetAppointmentTest(AppointmentTestID);
            vm.AppointmentTestID = apptest.Id;
            vm.UserID = UserId;
            vm.ReportPhraseFormatTable = RootPhraseFormat.Table.ToString();

            Appointment appt = vpRepo.GetAppointment(AppointmentID);
            TestGroup tg = userRepo.GetTestGroupByTest(vm.TestID);
            Test test = measurementRepo.GetTest(vm.TestID);

            vm.PatientID = appt.PatientRecordId;
            vm.GroupID = tg.GroupId;
            vm.PracticeID = PracticeId;
            //vm.DoctorID = vpRepo.GetMainDoctor(vm.AppointmentID);
            vm.DoctorID = vpRepo.GetMainDoctor(appt);
            //vm.PracticeDoctorID = vpRepo.GetPracticeDoctor(vm.AppointmentID);
            vm.PracticeDoctorID = vpRepo.GetPracticeDoctor(appt);
            vm.OfficeId = appt.OfficeId;
            vm.RootPhraseFormat = this.userRepo.GetReportTemplate(vm.AppointmentID); //TODO
            vm.DateStr = appt.appointmentTime.ToShortDateString();
            vm.TestName = test.testFullName;
            vm.HasRawData = HasRawData(vm.AppointmentID, vm.TestID);
            vm.OpenRawData = OpenRawData(vm.AppointmentID, vm.TestID);
            vm.OpenImage = OpenImage(vm.AppointmentID, vm.TestID);
            vm.AutoOpenReferralDocuments = OpenReferralDocument(vm.AppointmentID, vm.TestID);
            vm.IsWorkList = isWorkList;
            vm.IsClassicAppointment = apptest.IsImported;
            var testStatusId = apptest.AppointmentTestStatusId;
            vm.AppointmentTestStatusId = testStatusId;
            vm.IsAmended = testStatusId > 0 && (testStatusId == (int)AppointmentTestStatuses.ReportCompleted || testStatusId == (int)AppointmentTestStatuses.BeingSent) ? true : false;

            if (TempData[MODEL_FRM_PREV_POST] != null) //make sure a new log was NOT created
            {
                //load model from Store
                vm = TempData[MODEL_FRM_PREV_POST] as WorkSheetVM;
            }
            else
            {
                vm.ShowModeList = GetShowModeList();

                vm.MeasurementOperators = GetOperatorList();

                vm.AppointmentTestLogs = GetLogList(vm.AppointmentID, vm.TestID);
                vm.AppointmentTestLastLogID = GetMaxLogID(vm.AppointmentTestLogs);

                //diff revision log was selected
                if (TempData[NEW_LOG] != null)
                    vm.AppointmentTestLogID = (int)TempData[NEW_LOG];
                else
                    vm.AppointmentTestLogID = vm.AppointmentTestLastLogID;

                vm.RangeTypes = measurementRepo.GetMeasurementRangeTypes();
            }

            if (vm.GroupID == 1)
            {
                vm = Get_Measurements_Vertical(vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                vm = Get_ReportPhrases_Vertical(PracticeId, vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            }
            else
            {
                vm = Get_Measurements(vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                vm = Get_ReportPhrases(PracticeId, vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            }

            return vm;
        }

        internal WorkSheetVM GenerateWorkSheet_Old(int UserId, int PracticeId, int AppointmentID, int TestID, int AppointmentTestID = 0, int practiceID = 1, bool isWorkList = false)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm.AppointmentID = AppointmentID;
            vm.TestID = TestID;
            vm.AppointmentTestID = AppointmentTestID == 0 ? userRepo.GetAppointmentTestID(AppointmentID, TestID) : AppointmentTestID;
            vm.UserID = UserId;
            vm.ReportPhraseFormatTable = RootPhraseFormat.Table.ToString();
            vm.PatientID = userRepo.GetPatientByAppointment(vm.AppointmentID);
            vm.GroupID = userRepo.GetGroupIdByTest(vm.TestID);
            vm.PracticeID = PracticeId;
            vm.DoctorID = (new Cerebrum30.Areas.VP.DataAccess.VPRepository()).GetMainDoctor(vm.AppointmentID);
            vm.PracticeDoctorID = (new Cerebrum30.Areas.VP.DataAccess.VPRepository()).GetPracticeDoctor(vm.AppointmentID);
            //vm.OfficeId = CerebrumUser.OfficeId;
            vm.OfficeId = userRepo.GetOfficeIDByAppointment(vm.AppointmentID);
            vm.RootPhraseFormat = this.userRepo.GetReportTemplate(vm.AppointmentID); //TODO
            //vm.Date = this.userRepo.GetppointmentDate(vm.AppointmentID).Value;
            vm.DateStr = this.userRepo.GetAppointmentDate(vm.AppointmentID).Value.ToShortDateString();
            vm.TestName = this.measurementRepo.GetTestName(vm.TestID);
            vm.HasRawData = HasRawData(vm.AppointmentID, vm.TestID);
            vm.OpenRawData = OpenRawData(vm.AppointmentID, vm.TestID);
            vm.OpenImage = OpenImage(vm.AppointmentID, vm.TestID);
            vm.AutoOpenReferralDocuments = OpenReferralDocument(vm.AppointmentID, vm.TestID);
            vm.IsWorkList = isWorkList;
            vm.IsClassicAppointment = this.measurementRepo.IsClassicAppointment(vm.AppointmentTestID);
            var testStatusId = _measurementBll.GetTestStatusId(AppointmentTestID);
            vm.IsAmended = testStatusId > 0 && testStatusId == (int)AppointmentTestStatuses.ReportCompleted ? true : false;

            if (TempData[MODEL_FRM_PREV_POST] != null) //make sure a new log was NOT created
            {
                //load model from Store
                vm = TempData[MODEL_FRM_PREV_POST] as WorkSheetVM;
            }
            else
            {
                vm.ShowModeList = GetShowModeList();

                vm.MeasurementOperators = GetOperatorList();

                vm.AppointmentTestLogs = GetLogList(vm.AppointmentID, vm.TestID);
                vm.AppointmentTestLastLogID = GetMaxLogID(vm.AppointmentTestLogs);

                //diff revision log was selected
                if (TempData[NEW_LOG] != null)
                    vm.AppointmentTestLogID = (int)TempData[NEW_LOG];
                else
                    vm.AppointmentTestLogID = GetMaxLogID(vm.AppointmentTestLogs);

                vm.RangeTypes = measurementRepo.GetMeasurementRangeTypes();
            }

            if (vm.GroupID == 1)
            {
                vm = Get_Measurements_Vertical(vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                vm = Get_ReportPhrases_Vertical(PracticeId, vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            }
            else
            {
                vm = Get_Measurements(vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                vm = Get_ReportPhrases(PracticeId, vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
            }

            return vm;
        }

        internal WorkSheetVM GenerateWorkSheet(int UserId, int PracticeId, int AppointmentID, int TestID, int AppointmentTestID = 0, int practiceID = 1, bool isWorkList = false)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm.AppointmentID = AppointmentID;
            vm.TestID = TestID;
            AppointmentTest apptest = AppointmentTestID == 0 ? userRepo.GetAppointmentTest(AppointmentID, TestID) : userRepo.GetAppointmentTest(AppointmentTestID);
            vm.AppointmentTestID = apptest.Id;
            vm.UserID = UserId;
            vm.ReportPhraseFormatTable = RootPhraseFormat.Table.ToString();

            WorkSheetData wsdata = measurementRepo.GetWorkSheetData(AppointmentID, vm.TestID);

            //Appointment appt = vpRepo.GetAppointment(AppointmentID);
            //TestGroup tg = userRepo.GetTestGroupByTest(vm.TestID);
            //Test test = measurementRepo.GetTest(vm.TestID

            Appointment appt = wsdata.Appointment;
            Group group = wsdata.Group;
            TestGroup tg = wsdata.TestGroup;
            Test test = wsdata.Test;

            vm.PatientID = appt.PatientRecordId;
            vm.GroupID = tg.GroupId;
            vm.TestGroupLayoutType = group.TestGroupLayoutType;
            vm.PracticeID = PracticeId;
            //vm.DoctorID = vpRepo.GetMainDoctor(vm.AppointmentID);
            //vm.DoctorID = vpRepo.GetMainDoctor(appt);
            if (appt != null)
            {
                vm.DoctorID = wsdata.ExternalDoctor != null ? wsdata.ExternalDoctor.Id : 0;
                ////vm.PracticeDoctorID = vpRepo.GetPracticeDoctor(vm.AppointmentID);
                //vm.PracticeDoctorID = vpRepo.GetPracticeDoctor(appt);
                vm.PracticeDoctorID = wsdata.PracticeDoctor != null ? wsdata.PracticeDoctor.Id : 0;
                //Tuple<int, int> doc = vpRepo.GetMainAndPracticeDoctor(appt);
                //vm.DoctorID = doc.Item1;
                //vm.PracticeDoctorID = doc.Item2;
            }
            vm.OfficeId = appt.OfficeId;
            //vm.RootPhraseFormat = this.userRepo.GetReportTemplate(vm.AppointmentID); //TODO
            vm.RootPhraseFormat = this.userRepo.GetReportTemplate(wsdata.AppointmentProvider, wsdata.ExternalDoctor);
            vm.DateStr = appt.appointmentTime.ToShortDateString();
            vm.TestName = test.testFullName;
            //vm.HasRawData = HasRawData(vm.AppointmentID, vm.TestID);
            vm.HasRawData = wsdata.HasRawData;
            vm.OpenRawData = OpenRawData(vm.AppointmentID, vm.TestID);
            vm.OpenImage = OpenImage(vm.AppointmentID, vm.TestID);
            vm.AutoOpenReferralDocuments = OpenReferralDocument(vm.AppointmentID, vm.TestID);
            vm.IsWorkList = isWorkList;
            vm.IsClassicAppointment = apptest.IsImported;
            var testStatusId = apptest.AppointmentTestStatusId;
            vm.AppointmentTestStatusId = testStatusId;
            vm.IsAmended = testStatusId > 0 && (testStatusId == (int)AppointmentTestStatuses.ReportCompleted || testStatusId == (int)AppointmentTestStatuses.BeingSent) ? true : false;

            if (TempData[MODEL_FRM_PREV_POST] != null) //make sure a new log was NOT created
            {
                //load model from Store
                vm = TempData[MODEL_FRM_PREV_POST] as WorkSheetVM;
            }
            else
            {
                vm.ShowModeList = GetShowModeList();

                //vm.MeasurementOperators = GetOperatorList();
                vm.MeasurementOperators = GetOperatorList(wsdata.MeasurementOperatorList);

                vm.AppointmentTestLogs = GetLogList(vm.AppointmentID, vm.TestID);
                vm.AppointmentTestLastLogID = GetMaxLogID(vm.AppointmentTestLogs);

                //diff revision log was selected
                if (TempData[NEW_LOG] != null)
                    vm.AppointmentTestLogID = (int)TempData[NEW_LOG];
                else
                    vm.AppointmentTestLogID = vm.AppointmentTestLastLogID;

                //vm.RangeTypes = measurementRepo.GetMeasurementRangeTypes();
                vm.RangeTypes = measurementRepo.GetMeasurementRangeTypes(wsdata.MeasurementRangeTypeList);
            }

            if (vm.TestGroupLayoutType == TestGroupLayoutType.SideBySide)
            {
                vm = Get_Measurements_Vertical(vm, wsdata.MeasurementCategoryList, GetOperatorList(wsdata.MeasurementOperatorList), vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                vm = Get_ReportPhrases_Vertical(PracticeId, vm, wsdata.ReportPhraseList, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID); // note: exactly the same codes as Get_ReportPhrases
            }
            else
            {
                vm = Get_Measurements(vm, wsdata.MeasurementCategoryList, GetOperatorList(wsdata.MeasurementOperatorList), vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                vm = Get_ReportPhrases(PracticeId, vm, wsdata.ReportPhraseList, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID); // note: exactly the same codes as Get_ReportPhrases_Vertical
            }

            return vm;
        }


        internal Dictionary<string, long> Index_ForPerformance(int userId, int practiceId, int AppointmentID, int TestID, int AppointmentTestID = 0, int practiceID = 1, bool isWorkList = false)
        {
            Dictionary<string, long> result = new Dictionary<string, long>();

            var watch = System.Diagnostics.Stopwatch.StartNew();
            result.Add("Start=", watch.ElapsedMilliseconds);

            WorkSheetVM vm = new WorkSheetVM();
            result.Add("WorkSheetVM=", watch.ElapsedMilliseconds);

            vm.AppointmentID = AppointmentID;
            vm.TestID = TestID;

            AppointmentTest apptest = AppointmentTestID == 0 ? userRepo.GetAppointmentTest(AppointmentID, TestID) : userRepo.GetAppointmentTest(AppointmentTestID);
            result.Add("GetAppointmentTest=", watch.ElapsedMilliseconds);
            vm.AppointmentTestID = apptest.Id;
            //vm.AppointmentTestID = AppointmentTestID == 0 ? userRepo.GetAppointmentTestID(AppointmentID, TestID) : AppointmentTestID;
            //result.Add("GetAppointmentTestID=", watch.ElapsedMilliseconds);

            Appointment appt = vpRepo.GetAppointment(AppointmentID);
            result.Add("GetAppointment=", watch.ElapsedMilliseconds);

            TestGroup tg = userRepo.GetTestGroupByTest(vm.TestID);
            result.Add("GetTestGroupByTest=", watch.ElapsedMilliseconds);

            Test test = measurementRepo.GetTest(vm.TestID);
            result.Add("GetTest=", watch.ElapsedMilliseconds);

            vm.UserID = userId;
            vm.ReportPhraseFormatTable = RootPhraseFormat.Table.ToString();

            vm.PatientID = appt.PatientRecordId;
            //vm.PatientID = userRepo.GetPatientByAppointment(vm.AppointmentID);
            //result.Add("GetPatientByAppointment=", watch.ElapsedMilliseconds);

            vm.GroupID = tg.GroupId;
            //vm.GroupID = userRepo.GetGroupIdByTest(vm.TestID);
            //result.Add("GetGroupIdByTest=", watch.ElapsedMilliseconds);

            vm.PracticeID = practiceId;
            vm.DoctorID = vpRepo.GetMainDoctor(appt);
            result.Add("GetMainDoctor=", watch.ElapsedMilliseconds);

            vm.PracticeDoctorID = vpRepo.GetPracticeDoctor(appt);
            result.Add("GetPracticeDoctor=", watch.ElapsedMilliseconds);
            // note: I looked into GetMainDoctor and GetPracticeDoctor, the logic are exactly the same, so don't need to run it twice
            //vm.PracticeDoctorID = vpRepo.Value.GetPracticeDoctor(vm.AppointmentID); 
            //result.Add("GetPracticeDoctor=", watch.ElapsedMilliseconds);

            //vm.OfficeId = CerebrumUser.OfficeId;
            vm.OfficeId = appt.OfficeId;
            //vm.OfficeId = userRepo.GetOfficeIDByAppointment(vm.AppointmentID);
            //result.Add("GetOfficeIDByAppointment=", watch.ElapsedMilliseconds);

            vm.RootPhraseFormat = this.userRepo.GetReportTemplate(vm.AppointmentID); //TODO
            result.Add("GetReportTemplate=", watch.ElapsedMilliseconds);

            //vm.Date = this.userRepo.GetppointmentDate(vm.AppointmentID).Value;
            vm.DateStr = appt.appointmentTime.ToShortDateString();
            //vm.DateStr = this.userRepo.GetAppointmentDate(vm.AppointmentID).Value.ToShortDateString();
            //result.Add("GetAppointmentDate=", watch.ElapsedMilliseconds);

            vm.TestName = test.testFullName;
            //vm.TestName = this.measurementRepo.GetTestName(vm.TestID);
            //result.Add("GetTestName=", watch.ElapsedMilliseconds);

            vm.HasRawData = HasRawData(vm.AppointmentID, vm.TestID);
            result.Add("HasRawData=", watch.ElapsedMilliseconds);

            vm.OpenRawData = OpenRawData(vm.AppointmentID, vm.TestID);
            result.Add("OpenRawData=", watch.ElapsedMilliseconds);

            vm.OpenImage = OpenImage(vm.AppointmentID, vm.TestID);
            result.Add("OpenImage=", watch.ElapsedMilliseconds);

            vm.AutoOpenReferralDocuments = OpenReferralDocument(vm.AppointmentID, vm.TestID);
            result.Add("OpenReferralDocument=", (watch.ElapsedMilliseconds));

            vm.IsWorkList = isWorkList;
            vm.IsClassicAppointment = apptest.IsImported;
            //vm.IsClassicAppointment = this.measurementRepo.IsClassicAppointment(vm.AppointmentTestID);
            //result.Add("IsClassicAppointment=", (watch.ElapsedMilliseconds));

            var testStatusId = apptest.AppointmentTestStatusId;
            //var testStatusId = _measurementBll.GetTestStatusId(AppointmentTestID);
            //result.Add("GetTestStatusId=", watch.ElapsedMilliseconds);
            vm.IsAmended = testStatusId > 0 && testStatusId == (int)AppointmentTestStatuses.ReportCompleted ? true : false;

            if (TempData[MODEL_FRM_PREV_POST] != null) //make sure a new log was NOT created
            {
                //load model from Store
                vm = TempData[MODEL_FRM_PREV_POST] as WorkSheetVM;
            }
            else
            {
                vm.ShowModeList = GetShowModeList();
                result.Add("GetShowModeList=", watch.ElapsedMilliseconds);

                vm.MeasurementOperators = GetOperatorList();
                result.Add("GetOperatorList=", watch.ElapsedMilliseconds);

                vm.AppointmentTestLogs = GetLogList(vm.AppointmentID, vm.TestID);
                result.Add("GetLogList=", watch.ElapsedMilliseconds);

                vm.AppointmentTestLastLogID = GetMaxLogID(vm.AppointmentTestLogs);
                result.Add("GetMaxLogID=", watch.ElapsedMilliseconds);

                //diff revision log was selected
                if (TempData[NEW_LOG] != null)
                    vm.AppointmentTestLogID = (int)TempData[NEW_LOG];
                else
                    vm.AppointmentTestLogID = vm.AppointmentTestLastLogID;

                vm.RangeTypes = measurementRepo.GetMeasurementRangeTypes();
                result.Add("GetMeasurementRangeTypes=", watch.ElapsedMilliseconds);
            }

            if (vm.GroupID == 1)
            {
                vm = Get_Measurements_Vertical(vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                result.Add("Get_Measurements_Vertical=", watch.ElapsedMilliseconds);

                vm = Get_ReportPhrases_Vertical(practiceId, vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
                result.Add("Get_ReportPhrases_Vertical=", watch.ElapsedMilliseconds);
            }
            else
            {
                vm = Get_Measurements(vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PracticeID, vm.ShowMode);
                result.Add("Get_Measurements=", watch.ElapsedMilliseconds);

                vm = Get_ReportPhrases(practiceId, vm, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);
                result.Add("Get_ReportPhrases=", watch.ElapsedMilliseconds);
            }
            watch.Stop();

            return result;
        }

        [HttpPost]
        public ActionResult Index(WorkSheetVM model)
        {
            ////new log record selected in dropdown
            TempData[NEW_LOG] = model.AppointmentTestLogID;
            return RedirectToAction("Index", new { AppointmentID = model.AppointmentID, TestID = model.TestID, AppointmentTestID = model.AppointmentTestID, practiceID = model.PracticeID });
        }

        #region Ajaxed

        public JsonResult GetRangeToolTip(string mesCode, string catCode, int appointmentID)
        {
            bool errored = false;
            string retStr = string.Empty;
            try
            {
                var gender = measurementRepo.GetPatientGenderByAppointment(appointmentID);
                retStr = this.measurementRepo.GetRangeStr(mesCode, catCode, gender.ToString());
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Message = retStr,
                Errored = errored ? "1" : "0"
            });

        }

        [HttpPost]
        public JsonResult GetLogs(int appointmentID, int testID)
        {
            bool errored = false;
            List<SelectListItem> appointmentTestLogs = new List<SelectListItem>();
            int appointmentTestLastLogID = -1;
            try
            {
                appointmentTestLogs = GetLogList(appointmentID, testID);
                appointmentTestLastLogID = GetMaxLogID(appointmentTestLogs);
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                AppointmentTestLogs = appointmentTestLogs,
                AppointmentTestLastLogID = appointmentTestLastLogID,
                Errored = errored ? "1" : "0"
            });
        }

        public JsonResult GetBSARangeToolTip(string mesCode, string catCode, int appointmentID)
        {
            bool errored = false;
            string retStr = string.Empty;
            try
            {
                var gender = measurementRepo.GetPatientGenderByAppointment(appointmentID);
                retStr = this.measurementRepo.GetBSARangeStr(mesCode, catCode, gender.ToString());
            }
            catch (Exception)
            {
                errored = true;
            }

            return Json(new
            {
                Message = retStr,
                Errored = errored ? "1" : "0"
            });
        }
        [ResponseCache(NoStore = true, Duration = 0, VaryByQueryKeys = new[] { "*" })]
        public ActionResult _ReportPhrases(int appointmentID, int testID, int appointmentTestLogID)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm = Get_ReportPhrases(vm, appointmentID, testID, appointmentTestLogID);

            return View(vm);
        }
        [ResponseCache(NoStore = true, Duration = 0, VaryByQueryKeys = new[] { "*" })]
        public ActionResult _ReportPhrases_Vertical(int appointmentID, int testID, int appointmentTestLogID)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm = Get_ReportPhrases_Vertical(vm, appointmentID, testID, appointmentTestLogID);

            return View(vm);
        }
        [ResponseCache(NoStore = true, Duration = 0, VaryByQueryKeys = new[] { "*" })]
        public ActionResult _Measurements(int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm = Get_Measurements(vm, appointmentID, testID, appointmentTestLogID, practiceID, Mode);

            return View(vm);
        }

        [ResponseCache(NoStore = true, Duration = 0)]
        public ActionResult _Measurements_Vertical(int appointmentID, int testID, int appointmentTestLogID, int practiceID, MeasurementShowMode Mode)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm = Get_Measurements_Vertical(vm, appointmentID, testID, appointmentTestLogID, practiceID, Mode);

            return View(vm);
        }

        //[OutputCache(NoStore = true, Duration = 0, VaryByParam = "*")]
        public ActionResult _Notes(int appointmentID, int testID, int appointmentTestLogID)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.AppointmentTestLogID = appointmentTestLogID;

            vm.DoctorNotes = measurementRepo.GetDoctorNotes(vm.AppointmentID, vm.TestID);

            vm.TechNotes = measurementRepo.GetTechNotes(vm.AppointmentID, vm.TestID);

            return PartialView(vm);
        }
        public JsonResult AutoPhrases(
                     int appointmentID,
                     int testID,
                     List<Measurement_Value_VM> measurementValues,
                     List<Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM> phrases,
                     string TechNotes,
                     string DoctorsNotes)
        {
            WorkSheetVM model = new WorkSheetVM();
            List<ReportPhraseMeasurmentCategoryVM> lstCatReportPhrases = measurementRepo.GetAllReportPhraseMeasurementCategories();

            List<MeasurementRangeTextVM> lstRangeText = measurementRepo.GetMeasurementRangeTextList();

            List<MeasurementVM> measurements = new List<MeasurementVM>();

            if (lstCatReportPhrases != null && lstCatReportPhrases.Count > 0 && lstRangeText != null && lstRangeText.Count > 0)
            {
                //load ranges
                model.MeasurementCategories = measurementRepo.LoadRanges(model.AppointmentID, model.MeasurementCategories);

                #region  prepare all measurements 
                foreach (var c in model.MeasurementCategories)
                {
                    foreach (var m in c.Measurements)
                    {
                        m.MeasurementCategoryID = c.Id;
                        measurements.Add(m);
                    }
                }
                #endregion 

                //group by ReportPhraseID
                var reportPhraseGropLst = (from p in lstCatReportPhrases
                                           group p by p.ReportPhraseID into g

                                           select new ReportPhraseMeasurmentCategoryVM() { ReportPhraseID = g.Key })
                                           .OrderBy(m => m.ReportPhraseID)
                                           .ToList();
                //go through each report phrase
                foreach (var reportPhrase in reportPhraseGropLst)
                {
                    var reportPhraseLst = lstCatReportPhrases.Where(x => x.ReportPhraseID == reportPhrase.ReportPhraseID).ToList();

                    foreach (var rp in reportPhraseLst)
                    {
                        var category = model.MeasurementCategories.FirstOrDefault(c => c.Id == rp.MeasurementCategoryID);
                        if (category != null)
                        {
                            foreach (var measureVM in category.Measurements)
                            {
                                var ranges = lstRangeText.Where(m => m.MeasurementID == measureVM.Id).ToList();

                                if (ranges != null && ranges.Count > 0)
                                {
                                    foreach (var range in ranges)
                                    {
                                        foreach (MeasurementSavedValueVM savedValue in measureVM.MeasurementSavedValues)
                                        {
                                            if (savedValue.MeasurementRange != MeasurementRanges.Normal)
                                            {
                                                if (range.MeasurementRangeID == (int)savedValue.MeasurementRange)
                                                {
                                                    //var toUpdateRPLst = model.ReportPhraseViewModel.MainList.ReportPhraseSavedText.Where(x => x.TopLevelReportPhraseID == rp.ReportPhraseID).ToList();

                                                    var toUpdateRPLst = model.ReportPhrases.Where(x => x.Id == rp.ReportPhraseID).ToList();

                                                    foreach (var toUpdate in toUpdateRPLst)
                                                    {
                                                        //toUpdate.RangeMessage += " " + Environment.NewLine + range.Text;
                                                        toUpdate.Value += " " + Environment.NewLine + range.Text;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
            }

            return Json(new
            {
                Errored = "1"
            });
        }
        private bool SaveData(WorkSheetVM vm, List<Measurement_Value_VM> retErrorList)
        {
            bool errored;

            var ip = GetIpAddress(Request);

            int appointmentID = vm.AppointmentID;
            int testID = vm.TestID;
            List<Measurement_Value_VM> measurementValues = new List<Measurement_Value_VM>();
            List<Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM> phrases = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM>();

            #region   extracting saved values and phrases from vm and getting them in the follwoing shape

            vm.MeasurementCategories.ForEach(c =>
            {
                c.Measurements.ForEach(m =>
                {
                    if (m.MeasurementSavedValues[0].Discard)
                    {
                        measurementValues.Add(new Measurement_Value_VM()
                        {
                            MeasureID = m.MeasurementSavedValues[0].MeasurementId,
                            Value = m.MeasurementSavedValues[0].Value,
                            MeasurementOperator = m.MeasurementSavedValues[0].MeasurementOperatorId,
                            Discard = m.MeasurementSavedValues[0].Discard
                        });
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(m.MeasurementSavedValues[0].Value))
                        {
                            measurementValues.Add(new Measurement_Value_VM()
                            {
                                MeasureID = m.MeasurementSavedValues[0].MeasurementId,
                                Value = m.MeasurementSavedValues[0].Value,
                                MeasurementOperator = m.MeasurementSavedValues[0].MeasurementOperatorId,
                                Discard = m.MeasurementSavedValues[0].Discard
                            });
                        }
                    }
                });
            });

            vm.ReportPhrases.ForEach(r =>
            {
                if (!string.IsNullOrEmpty(r.Value))
                {
                    phrases.Add(new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                    {
                        Id = r.Id,
                        Value = r.Value
                    });
                }

            });

            #endregion

            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> topLevelPhrasesToSave = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();
            List<MeasurementSavedValueVM> lstMeasToSave = new List<MeasurementSavedValueVM>();

            errored = (vm.MeasurementCategories.Count == 0 && vm.ReportPhrases.Count == 0) || retErrorList.Count > 0;

            //if no error occurred prepare data
            if (!errored)
            {
                //preparing measuremnts 
                measurementValues.ForEach(m =>
                {
                    //if (double.TryParse(m.Value, out result))
                    {
                        //  item.MeasurementSavedValues[0].Value = measurementRepo.GetMaskedValue(item.mask, item.MeasurementSavedValues[0].Value);
                        lstMeasToSave.Add(new MeasurementSavedValueVM()
                        {
                            MeasurementId = m.MeasureID,
                            Value = m.Value,
                            AppointmentID = appointmentID,
                            TestID = testID,
                            MeasurementOperatorId = m.MeasurementOperator,
                            Discard = m.Discard
                        });
                    }
                });

                //preparing report phrases 
                if (phrases != null)
                {
                    phrases.ForEach(p =>
                    {
                        topLevelPhrasesToSave.Add(new Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase()
                        {
                            Id = p.Id,
                            Value = p.Value
                        });
                    });
                }



                using (TransactionScope scope = new TransactionScope(TransactionScopeOption.RequiresNew))
                {
                    try
                    {
                        int newLogID = measurementRepo.CreateLog(appointmentID, testID, CerebrumUser.UserId, ip);
                        lstMeasToSave = measurementRepo.Merge(lstMeasToSave, newLogID, testID, appointmentID, vm.AppointmentTestLastLogID);

                        var savePhrases = new VMSaveReportPhrase { ReportPhrases = ConvertToGlobalTopLevelReportPhrase(topLevelPhrasesToSave), appointmentID = appointmentID, testID = testID, appointmentTestLogId = newLogID, UserId = CerebrumUser.UserId, IPAddress = ip };

                        measurementRepo.Save_ReportPhrases(savePhrases);

                        measurementRepo.SaveMeasurements(lstMeasToSave, CerebrumUser.UserId, ip);

                        var note = new VMSaveMeasurmentNote { AppointmentId = vm.AppointmentID, TestId = vm.TestID, DoctorComment = vm.DoctorNotes, TechnicianNote = vm.TechNotes, UserId = CerebrumUser.UserId, IPAddress = ip };
                        measurementRepo.UpdateNotes(note);

                        scope.Complete();
                        scope.Dispose();
                        vm.AppointmentTestLogID = newLogID;
                        _appointmentTestLogID = newLogID;
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex.ToString());
                        errored = true;
                        scope.Dispose();
                    }
                }
            }

            return errored;
        }
        private string GetIpAddress(HttpRequest rb)
        {
            #region IP Address
            // Avoid Helper method ambiguity by using ASP.NET Core approach directly
            var ip = rb.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(ip))
            {
                return ip.Split(',')[0].Trim();
            }

            ip = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            if (ip.Equals("::1"))
            {
                try
                {
                    ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork).ToString();
                }
                catch { }
            }
            return ip;
            #endregion
        }
        public ActionResult Save_WS_Data(WorkSheetVM vm)
        {
            bool errored = false;
            List<Measurement_Value_VM> retErrorList = new List<Measurement_Value_VM>();

            try
            {
                errored = SaveData(vm, retErrorList);

            }
            catch (Exception ex)
            {
                _log.Error(ex);
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                retErrorList = retErrorList,
                AppointmentTestLogID = errored ? 0 : _appointmentTestLogID
            });

        }

        public JsonResult Save_Draft(WorkSheetVM vm)
        {
            bool errored = false;
            List<Measurement_Value_VM> retErrorList = new List<Measurement_Value_VM>();
            try
            {
                var ip = IPAddress(Request);
                errored = SaveData(vm, retErrorList);
                if (!errored)
                {
                    this.measurementRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.ReadyForDoctor, CerebrumUser.UserId, ip);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });

        }


        public JsonResult SendInterimReport(WorkSheetVM vm)
        {
            return SendReport(false, vm);
        }

        public JsonResult SendReport(WorkSheetVM vm)
        {
            return SendReport(true, vm);
        }

        public JsonResult SaveReportSendingOptions(VMFileResendToDoctors vm)
        {
            var isDoctor = CerebrumUser.PracticeDoctorId > 0;
            var bullsEyeId = _measurementBll.GetBullsEyeId(vm.AppointmentId, vm.TestId);
            var testStatus = _measurementBll.GetTestStatus(vm.AppointmentTestId);
            var validationMessageResult = _hrmValidator.ValidateHrmReportAlternativeSendingOptions(vm, testStatus);
            var sent = false;
            var userId = CerebrumUser.UserId;
            var ipaddress = CerebrumUser.IpAddress;

            if (!string.IsNullOrEmpty(validationMessageResult))
            {
                return Json(new { Errored = "1", Message = validationMessageResult });
            }

            var doctors = vm.Doctors.Where(a => a.HRM || a.Fax || a.Email).ToList();
            if (doctors.Count != 0)
            {
                sent = _measurementBll.SaveReportInQueue(vm.AppointmentTestId, vm.AppointmentTestLogId, isDoctor, AwareMD.Cerebrum.Shared.Enums.DataType.Report, VPLetterType.None,
                CerebrumUser.UserId, CerebrumUser.FullName, CerebrumUser.IpAddress, false, bullsEyeId, vm.ChangeStatus, vm.IsAmended, doctors, CerebrumUser.PracticeSendOutReportMethod);
            }

            if (sent && !vm.ChangeStatus)
            {
                this.measurementRepo.UpdateTestStatus(vm.AppointmentTestId, (int)AppointmentTestStatuses.Test_Started, userId, ipaddress);
            }

            return Json(new { Errored = "0", Message = "The report was saved in the queue and will be sent by fax or email" });
        }

        /// <summary>
        /// Submission from ws.js click #btn-set-abnormal
        /// </summary>
        /// <param name="appointmentID"></param>
        /// <param name="testId"></param>
        /// <param name="unset"></param>
        /// <returns></returns>
        //public ActionResult SetActionOnAbnormal(int appointmentID) 
        public ActionResult SetActionOnAbnormal(int appointmentID, int testId, bool unset = false) // redmine #12567
        {
            bool errored = false;

            try
            {
                //this.measurementRepo.SetActionOnAbnormal(appointmentID, true, CerebrumUser.UserId, GetIPAddress());
                this.measurementRepo.SetActionOnAbnormal(appointmentID, testId, unset, CerebrumUser.UserId, GetIPAddress()); // redmine #12567
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                errored = true;
            }

            return Json(new
            {
                Errored = errored ? "1" : "0"
            });
        }

        [HttpPost]
        public ActionResult SetForReview(WorkSheetVM vm)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                this.measurementRepo.SetForReview(vm.AppointmentID, vm.AppointmentTestID, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                _log.Error(exc);
                errored = true;
                message = exc.ToString();
            }

            return Json(new
            {
                Errored = errored ? "1" : "0",
                Message = message
            });
        }
        #endregion

        [HttpPost]
        public ActionResult MeasMultipleValues(WorkSheetVM vm)
        {
            //get categories and their measurements
            var lst = measurementRepo.GetCategoriesAndMeasurements(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

            lst = measurementRepo.FilterCategoriesByTest(vm.TestID, lst);

            //get saved values for each measurement 
            vm.MeasurementCategories = measurementRepo.LoadSavedValues(lst, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID, vm.PatientID);

            vm = this.measurementRepo.GetBSAValue(vm);

            //Filter meas by Practice
            if (vm.ShowMode != MeasurementShowMode.All)
            {
                vm.MeasurementCategories = this.measurementRepo.FilterMeasurementsByPractice(vm.PracticeID, lst, vm.ShowMode);
            }

            //filter according to show mode
            vm.MeasurementCategories = measurementRepo.FilterByShowMode(lst, vm.ShowMode);
            vm.MeasurementCategories.ForEach(c =>
            {
                c.Measurements.ForEach(m =>
                {
                    m.MeasurementSavedValues.Add(new MeasurementSavedValueVM() { Value = "1" });
                    m.MeasurementSavedValues.Add(new MeasurementSavedValueVM() { Value = "2" });
                    m.MeasurementSavedValues.Add(new MeasurementSavedValueVM() { Value = "5" });
                });
            });

            return PartialView("_Measurements_Vertical", vm);
        }

        [HttpPost]
        public ActionResult AutoPhrases(WorkSheetVM vm)
        {
            Dictionary<string, string> lst = new Dictionary<string, string>();

            vm.GroupID = userRepo.GetGroupIdByTest(vm.TestID);

            if (vm.GroupID == 1)
            {
                //load rp and categories
                List<ReportPhraseMeasurmentCategoryVM> lstCatReportPhrases = measurementRepo.GetAllReportPhraseMeasurementCategories();
                //load measures and and their ranges list
                List<MeasurementRangeTextVM> lstRangeText = measurementRepo.GetMeasurementRangeTextList();

                List<MeasurementVM> measurements = new List<MeasurementVM>();

                if (
                    lstCatReportPhrases != null && lstCatReportPhrases.Count > 0 &&
                    lstRangeText != null && lstRangeText.Count > 0
                   )
                {                    //load normal and IndexRanges ranges for all measurements 
                    vm.MeasurementCategories = measurementRepo.LoadRanges(vm.AppointmentID, vm.MeasurementCategories);

                    #region  prepare all measurements 
                    foreach (var c in vm.MeasurementCategories)
                    {
                        foreach (var m in c.Measurements)
                        {
                            m.MeasurementCategoryID = c.Id;
                            measurements.Add(m);
                        }
                    }
                    #endregion

                    //from rp and cat table , create list of rp ids
                    var reportPhraseGropLst = (from p in lstCatReportPhrases
                                               group p by p.ReportPhraseID into g

                                               select new ReportPhraseMeasurmentCategoryVM() { ReportPhraseID = g.Key })
                                               .OrderBy(m => m.ReportPhraseID)
                                               .ToList();
                    //go through each report phrase id

                    reportPhraseGropLst.ForEach(reportPhrase =>
                    {
                        //find all entries for specefic report phrase 
                        var reportPhraseLst = lstCatReportPhrases.Where(x => x.ReportPhraseID == reportPhrase.ReportPhraseID).ToList();

                        reportPhraseLst.ForEach(rp =>
                        {
                            //find corresponding category
                            var category = vm.MeasurementCategories.FirstOrDefault(c => c.Id == rp.MeasurementCategoryID);
                            if (category != null)
                            {
                                category.Measurements.ForEach(measureVM =>
                                {
                                    measureVM.MeasurementSavedValues.ForEach(savedValue =>
                                    {
                                        if (savedValue.MeasurementRange != MeasurementRanges.Normal && savedValue.MeasurementRange != MeasurementRanges.NoRangeAvailable)
                                        {
                                            // var rang = lstRangeText.Where(m => m.MeasurementID == measureVM.Id && m.MeasurementRangeID == (int)savedValue.MeasurementRange).FirstOrDefault();
                                            var toUpdateRPLst = vm.ReportPhrases.Where(x => x.Id == rp.ReportPhraseID).ToList();
                                            toUpdateRPLst.ForEach(toUpdate =>
                                            {
                                                toUpdate.Value +=
                                                (string.IsNullOrEmpty(toUpdate.Value) ? string.Empty : Environment.NewLine) + measureVM.name + " is " + savedValue.MeasurementRange.ToString();
                                            });
                                        }
                                    });
                                });
                            }
                        });
                    });
                }

                vm.ReportPhrases.ForEach(r =>
                {
                    lst.Add(r.Id.ToString(), r.Value);
                });
            }

            return Json(new { Items = lst.ToList(), Errored = false });
        }
        [HttpPost]
        public ActionResult InsertAutoPhrases(WorkSheetVM model)
        {
            List<ReportPhraseMeasurmentCategoryVM> lstCatReportPhrases = measurementRepo.GetAllReportPhraseMeasurementCategories();

            List<MeasurementRangeTextVM> lstRangeText = measurementRepo.GetMeasurementRangeTextList();

            List<MeasurementVM> measurements = new List<MeasurementVM>();

            if (lstCatReportPhrases != null && lstCatReportPhrases.Count > 0 && lstRangeText != null && lstRangeText.Count > 0)
            {
                //load ranges
                model.MeasurementCategories = measurementRepo.LoadRanges(model.AppointmentID, model.MeasurementCategories);

                #region  prepare all measurements 
                foreach (var c in model.MeasurementCategories)
                {
                    foreach (var m in c.Measurements)
                    {
                        m.MeasurementCategoryID = c.Id;
                        measurements.Add(m);
                    }
                }
                #endregion 

                //group by ReportPhraseID
                var reportPhraseGropLst = (from p in lstCatReportPhrases
                                           group p by p.ReportPhraseID into g
                                           select new ReportPhraseMeasurmentCategoryVM() { ReportPhraseID = g.Key })
                                           .OrderBy(m => m.ReportPhraseID)
                                           .ToList();
                //go through each report phrase
                foreach (var reportPhrase in reportPhraseGropLst)
                {
                    var reportPhraseLst = lstCatReportPhrases.Where(x => x.ReportPhraseID == reportPhrase.ReportPhraseID).ToList();

                    foreach (var rp in reportPhraseLst)
                    {
                        var category = model.MeasurementCategories.FirstOrDefault(c => c.Id == rp.MeasurementCategoryID);

                        if (category != null)
                        {
                            foreach (var measureVM in category.Measurements)
                            {
                                var ranges = lstRangeText.Where(m => m.MeasurementID == measureVM.Id).ToList();

                                if (ranges != null && ranges.Count > 0)
                                {
                                    foreach (var range in ranges)
                                    {
                                        foreach (MeasurementSavedValueVM savedValue in measureVM.MeasurementSavedValues)
                                        {
                                            if (savedValue.MeasurementRange != MeasurementRanges.Normal)
                                            {
                                                if (range.MeasurementRangeID == (int)savedValue.MeasurementRange)
                                                {
                                                    //var toUpdateRPLst = model.ReportPhraseViewModel.MainList.ReportPhraseSavedText.Where(x => x.TopLevelReportPhraseID == rp.ReportPhraseID).ToList();

                                                    var toUpdateRPLst = model.ReportPhrases.Where(x => x.Id == rp.ReportPhraseID).ToList();

                                                    foreach (var toUpdate in toUpdateRPLst)
                                                    {
                                                        //toUpdate.RangeMessage += " " + Environment.NewLine + range.Text;
                                                        toUpdate.Value += " " + Environment.NewLine + range.Text;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
            }

            TempData[MODEL_FRM_PREV_POST] = model;

            return RedirectToAction("Index", new { AppointmentID = model.AppointmentID, TestID = model.TestID, AppointmentTestID = model.AppointmentTestID, practiceID = model.PracticeID });
        }

        [HttpPost]
        public ActionResult Go(WorkSheetVM model)
        {
            //appid and testid changed 
            return RedirectToAction("Index", new { AppointmentID = model.AppointmentID, TestID = model.TestID, AppointmentTestID = model.AppointmentTestID, practiceID = model.PracticeID });
        }

        [HttpPost]
        public ActionResult ChangeShowMode(WorkSheetVM model)
        {
            //show mode changed , so remember the revision log selected
            TempData[NEW_SHOW_MODE] = model.ShowMode;
            TempData[NEW_LOG] = model.AppointmentTestLogID;

            return RedirectToAction("Index", new { AppointmentID = model.AppointmentID, TestID = model.TestID, AppointmentTestID = model.AppointmentTestID, dt = model.Date, practiceID = model.PracticeID });
        }

        [HttpPost]
        public ActionResult Import()
        {
            List<MData> data = new List<MData>();

            data.Add(new MData() { catCode = "AORTIC VALVE", meaCode = "20304-2", units = "mmHG", value = "21.43" });
            data.Add(new MData() { catCode = "AORTA", meaCode = "122197", units = "cm2", value = "1.9987" });
            data.Add(new MData() { catCode = "LVOT", meaCode = "F-32120", units = "mmHG", value = "101" });
            data.Add(new MData() { catCode = "Tricuspid Valve", meaCode = "F - 32110", units = "bpm", value = "45.987" });
            data.Add(new MData() { catCode = "Right Atrium", meaCode = "F-32100", units = "%", value = "11.99" });
            string lnk = "http://localhost:65062/API/MeasurementImporter/Save?appointmentID=1&testID=1";

            // TODO: Replace with System.Text.Json or Newtonsoft.Json
            // System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            string json = System.Text.Json.JsonSerializer.Serialize(data);

            var httpWebRequest = (HttpWebRequest)WebRequest.Create(lnk); // need to replace with HttpClientFactory, but since this is just a testing method, I am not going to spend time to change here
            httpWebRequest.ContentType = "text/json";
            httpWebRequest.Method = "POST";
            using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
            {
                streamWriter.Write(json);
                streamWriter.Flush();
            }
            var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
            bool status = false;
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var result = streamReader.ReadToEnd();
                status = bool.Parse(result.ToString());
            }
            ViewBag.Status = status;

            return View();
        }

        [HttpPost]
        public ActionResult Customize(WorkSheetVM model)
        {
            return View(model);
        }

        public ActionResult GetRanges(int mid, string mesCode, string catCode, int appointmentID)
        {
            RangeListVM vm = new RangeListVM();

            vm.Measurement = measurementRepo.GetMeasurement(mid);

            if (string.IsNullOrEmpty(mesCode) || string.IsNullOrEmpty(catCode))
            {
                vm.Message = "Missing measurementCode and categoryCode";
                vm.MissingValues = true;
            }
            else
            {
                vm.Ranges = measurementRepo.GetRanges(mesCode, catCode);
                vm.BSARanges = measurementRepo.GetBSARanges(mesCode, catCode);
            }

            return View(vm);
        }

        public ActionResult GetBSARanges(int mid, string mesCode, string catCode, int appointmentID)
        {
            RangeListVM vm = new RangeListVM();

            vm.Measurement = measurementRepo.GetMeasurement(mid);

            if (string.IsNullOrEmpty(mesCode) || string.IsNullOrEmpty(catCode))
            {
                vm.Message = "Missing measurementCode and categoryCode";
                vm.MissingValues = true;
            }
            else
            {
                vm.Ranges = measurementRepo.GetBSARanges(mesCode, catCode);

                if (vm.Ranges.Count > 0)
                {
                    var gender = measurementRepo.GetPatientGenderByAppointment(appointmentID);
                    vm.Ranges = vm.Ranges.Where(x => x.gender == gender.ToString() && x.RangeName == MeasurementRanges.Normal.ToString()).ToList();
                }
            }

            return View("GetRanges", vm);
        }

        /// <summary>
        /// Gets History of a particular measurement 
        /// </summary>
        /// <param name="measurementID"></param>
        /// <param name="appointmentID"></param>
        /// <param name="testID"></param>
        /// <returns></returns>
        public ActionResult History(int measurementID, int appointmentID, int testID)
        {
            var model = measurementRepo.GetMeasurementHistory(measurementID, appointmentID, testID);
            ViewBag.MeasureName = this.measurementRepo.GetMeasurement(measurementID).name;

            return View(model);
        }

        public ActionResult MeasurementHistory(int measurementID, int appointmentID, int testID)
        {

            var model = measurementRepo.GetMeasurementHistory(measurementID, appointmentID, testID);
            ViewBag.MeasureName = this.measurementRepo.GetMeasurement(measurementID).name;
            return View("_measurementHistory", model);
        }

        public ActionResult ReportPhraseHistory(int reportPhraseID, int appointmentID, int testID)
        {

            var model = measurementRepo.GetReportPhraseHistory(reportPhraseID, appointmentID, testID);
            ViewBag.ReportPhraseName = this.measurementRepo.GetReportPhraseByID(reportPhraseID);

            return View(model);
        }

        [HttpGet]
        public JsonResult GetValueForOperator(string values, int Operator)
        {
            // TODO: Replace with System.Text.Json
            var deserializedMenuItems = System.Text.Json.JsonSerializer.Deserialize<object[]>(values);

            List<double> lst = new List<double>();

            foreach (var item in deserializedMenuItems)
            {
                lst.Add(double.Parse(item.ToString()));
            }

            MeasurementOperators oper = (MeasurementOperators)Operator;
            bool errored = false;
            string message = string.Empty;
            double result = 0;

            switch (oper)
            {
                case MeasurementOperators.NONE:
                    message = "Please select a valid operation";
                    errored = true;
                    break;

                case MeasurementOperators.MAX:
                    result = lst.Max();
                    break;

                case MeasurementOperators.MIN:
                    result = lst.Min();
                    break;

                case MeasurementOperators.AVG:
                    result = lst.Average();
                    break;

                default:
                    break;
            }
            return Json(new { Errored = errored, Result = result, Message = message });
        }

        [HttpGet]
        public JsonResult GetRangeForMeasurement(string val, string mesCode, string catCode, int apptID)
        {
            var gender = userRepo.GetGender(apptID);
            bool errored = false;

            string message = string.Empty;
            int result = 0;
            double value;

            if (
                !string.IsNullOrEmpty(val) &&
                !string.IsNullOrEmpty(mesCode) &&
                !string.IsNullOrEmpty(catCode)
              )
            {
                if (double.TryParse(val, out value))
                {
                    MeasurementRanges rang = measurementRepo.GetRangeTypes(value, mesCode, catCode, gender);
                    result = (int)rang;
                }
            }

            return Json(new { Errored = errored, Result = result, Message = message });
        }

        [HttpGet]
        public JsonResult AddReportPhraseAction(int apptID, int testID, int reportPhraseID, int topLevelReportPhraseID)
        {
            bool errored = false;
            string message = string.Empty;

            try
            {
                measurementRepo.AddReportPhraseSavedValue(apptID, testID, reportPhraseID, topLevelReportPhraseID, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                _log.Error(exc);
                errored = true;
                message = exc.Message;
            }
            return Json(new { Errored = errored, Result = message });
        }

        [HttpGet]
        public ActionResult GetZScore(int apptLogID)
        {
            List<WSZScore> result = null;

            try
            {
                result = _measurementBll.GetZScore(apptLogID);
            }
            catch (Exception exc)
            {
                _log.Error("Error Getting Zscore: " + exc.Message);
                result = new List<WSZScore>();
            }
            return PartialView("_zscoreList", result);
        }

        [HttpGet]
        public JsonResult RemoveReportPhraseAction(int apptID, int testID, int reportPhraseID, int topLevelReportPhraseID)
        {
            bool errored = false;
            string message = string.Empty;

            try
            {
                measurementRepo.RemoveReportPhraseSavedValue(apptID, testID, reportPhraseID, topLevelReportPhraseID, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }
            return Json(new { Errored = errored, Result = message });
        }

        public ActionResult EditReportPhraseSetting(int patientID, int practiceID, int userID, int testID, int DocID)
        {
            ReportPhraseSetting_VM vm = new
                                ReportPhraseSetting_VM(patientID, practiceID, userID);

            vm.ReportPhraseSettings = measurementRepo.GetReportPhraseSettings(practiceID, testID, DocID);
            vm.ReportPhraseSettings = measurementRepo.GetReportPhraseSettingsCustom(vm.ReportPhraseSettings, testID, DocID);
            vm.ReportPhraseSettingMode = measurementRepo.GetReportPhraseSettingsMode(practiceID, DocID, testID);

            for (int i = 1; i < vm.ReportPhraseSettings.Count + 1; i++)
            {
                vm.RankList.Add(new SelectListItem() { Value = i.ToString(), Text = i.ToString() });
            }

            if (TempData[ADD_CUSTOMIZE_RP] != null)
            {
                vm.ErrorMessage = TempData[ADD_CUSTOMIZE_RP].ToString();
            }

            if (TempData[SCRIPT_TO_EXECUTE] != null)
                vm.ScriptToExecute = TempData[SCRIPT_TO_EXECUTE].ToString();

            vm.TestID = testID;
            vm.PatientID = patientID;
            vm.PracticeID = practiceID;
            vm.UserID = userID;
            vm.DocID = DocID;

            return View(vm);
        }

        public ActionResult SaveReportPhraseSetting(ReportPhraseSetting_VM model)
        {
            bool errored = false;
            string message = string.Empty;

            try
            {
                // get all the text boxes that are empty. We cannot save those.
                var emptyTexts = model.ReportPhraseSettings
                    .Where(x => x.Text == null || (x.Text != null && x.Text.Trim() == ""))
                    .ToList();
                if (!emptyTexts.Any())
                {
                    measurementRepo.SaveReportPhraseSetting(model, CerebrumUser.UserId, GetIPAddress());
                }
                else
                {
                    errored = true;
                    string categories = String.Join(",", emptyTexts.Select(x => x.OriginalText).ToList());
                    message = "Custom text cannot be empty. Please enter values. " + categories;
                }
            }
            catch (Exception exc)
            {
                _log.Error(exc);
                errored = true;
                message = exc.Message;
            }

            return Json(new { Errored = errored ? "1" : "0", Result = message });
        }

        public ActionResult AddPhraseSubItem(int RootPhraseID, int ParentID, int testID, int DocID)
        {
            AddPhraseSubItem_VM vm = new AddPhraseSubItem_VM();
            vm.DrID = DocID;

            return View(vm);
        }

        [HttpPost]
        public ActionResult AddPhraseSubItem(AddPhraseSubItem_VM vm)
        {
            bool errored = false;
            string message = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(vm.Name))
                {
                    message = "Missing Name";
                    errored = true;
                }
                else
                {
                    measurementRepo.AddPhraseSubItem(vm, CerebrumUser.UserId, GetIPAddress());
                }
            }
            catch (Exception exc)
            {
                errored = true;
                message = exc.Message;
            }

            return Json(new { Errored = errored ? "1" : "0", Result = message });

        }


        [HttpPost]
        [ValidateAntiForgeryToken()]
        public ActionResult PhraseVisible(Models.RequestModels.PhraseVisibleRequest request)
        {
            if (ModelState.IsValid)
            {
                int result = measurementRepo.UpdatePhraseVisible(CerebrumUser.PracticeId, request.PhraseId, CerebrumUser.UserId, GetIPAddress());
                if (result == 0)
                {
                    return Json(new { Errored = 1, Result = "Failed to update phrase visibility." });
                }
                else
                {
                    return Json(new { Errored = 0 });
                }
            }
            else
            {
                string errorMsg = ValidationError(this.ModelState);
                return Json(new { Errored = 1, Result = errorMsg });
            }
        }

        public string GetPracticeUs2AiSubdomain()
        {
            return _practiceBll.GetPractice(CerebrumUser.PracticeId)?.Us2AiSubdomain;
        }

        public FileResult ShowImages(int appointmentID, int testID)
        {
            string extension = "xaf";
            string message = string.Empty;

            try
            {
                var xafFile = _radStudyBll.GenerateViewerFile(_httpClientFactory, CerebrumUser.PracticeId, appointmentID, testID, CerebrumUser.UserId);

                if (xafFile != null)
                {
                    byte[] contents = xafFile.ToArray();

                    Response.Headers.Add("Content-Disposition", string.Format("inline; filename=test.{0}", extension));
                    return File(contents, string.Format("application/{0}", extension));
                }
                else
                {
                    UTF8Encoding encoding = new UTF8Encoding();
                    byte[] contentAsBytes = encoding.GetBytes("No Data Found in database");
                    Response.Headers.Add("Content-Disposition", "inline; filename=NoDataFound.txt");
                    return File(contentAsBytes, "application/txt");
                }
            }
            catch (Exception exc)
            {
                message = exc.Message;
            }

            return null;
        }
        public ActionResult ShowImagesWeb_OLD(int appointmentID, int testID)
        {
            VMShowWebImagesMain vm = _measurementBll.GetWebImagesMain(_httpClientFactory, appointmentID, testID, CerebrumUser.UserId, CerebrumUser.PracticeId);
            return View(vm);
        }
        public ActionResult ShowImagesWeb(int appointmentID, int testID)
        {
            VMShowWebImagesMain vm = _radStudyBll.GetImages(_httpClientFactory, CerebrumUser.PracticeId, appointmentID, testID, CerebrumUser.UserId);
            return View(vm);
        }
        public async Task<FileResult> ShowPDF(int appointmentID, int testID)
        {
            string message = string.Empty;

            try
            {
                var pdf = await _radStudyBll.GetPDFFile(_httpClientFactory, CerebrumUser.PracticeId, appointmentID, testID, CerebrumUser.UserId);

                if (pdf != null && pdf.contents.Length > 0)
                {
                    Response.Headers.Add("Content-Disposition", string.Format("inline; filename={0}.pdf", pdf.imageName));
                    return File(pdf.contents, "application/pdf");
                }
                else
                {
                    UTF8Encoding encoding = new UTF8Encoding();
                    byte[] contentAsBytes = encoding.GetBytes("No Data Found in database");
                    Response.Headers.Add("Content-Disposition", "inline; filename=NoDataFound.txt");
                    return File(contentAsBytes, "application/txt");
                }
            }
            catch (Exception exc)
            {
                message = exc.Message;
            }

            return null;
        }

        [HttpGet]
        public JsonResult GetPDFInfo(int appointmentID, int testID)
        {
            VMRadPdfAccessData pdf = new VMRadPdfAccessData();
            try
            {
                pdf = _radStudyBll.GetPDFInfo(_httpClientFactory, CerebrumUser.PracticeId, appointmentID, testID, CerebrumUser.UserId);
            }
            catch
            {
            }

            return Json(pdf);
        }

        public ActionResult WS_Links(WorkSheetVM vm)
        {
            vm.ActionOnAbnormal = this.measurementRepo.GetActionOnAbnormal(vm.AppointmentID);
            vm.IsTestAbnormal = this.measurementRepo.GetIsTestAbnormal(vm.AppointmentID, vm.TestID);
            vm.HasRawData = HasRawData(vm.AppointmentID, vm.TestID);
            vm.OpenRawData = OpenRawData(vm.AppointmentID, vm.TestID);
            vm.OpenImage = OpenImage(vm.AppointmentID, vm.TestID);
            vm.PracticeUs2AiSubdomain = GetPracticeUs2AiSubdomain();
            vm.AutoOpenReferralDocuments = OpenReferralDocument(vm.AppointmentID, vm.TestID);
            vm.SetForReview = measurementRepo.GetForReview(vm.AppointmentID, vm.AppointmentTestID);
            vm.HasBullsEye = measurementRepo.HasBullsEye(vm.TestID);
            return View(vm);
        }

        public ActionResult BullEye(int appointmentId, int testId)
        {
            BullEyeResponse response = new BullEyeResponse();
            try
            {
                response = measurementRepo.GetBullEyeInfo(appointmentId, testId);
            }
            catch (Exception ex)
            {
                _log.Error(ex.ExceptionDetails());
                response.errorMessage = "There was a problem.Please Contact Administrator!";
            }

            return PartialView("_BullEye", response);
        }

        [HttpPost]
        public ActionResult SaveSvgImage(BullEyeRequest request)
        {
            try
            {
                measurementRepo.SaveBullsEyeInfo(request, CerebrumUser.UserId, GetIPAddress());
            }
            catch (Exception exc)
            {
                _log.Error(exc.ExceptionDetails());
                return Content("There was a problem.Please Contact Administrator!");
            }

            return Content(string.Empty);
        }
        public ActionResult PreviousExams(int AppointmentID, int TestID)
        {
            VMPreviousTestItem vm = new VMPreviousTestItem();
            vm.AppointmentID = AppointmentID;
            vm.TestID = TestID;
            return PartialView("_PreviousTests", vm);
        }
        #region Worklist

        public ActionResult WorkList(int DoctorID = 0)
        {
            var practiceId = CerebrumUser.PracticeId;
            var workList = _measurementBll.GetWorkList(practiceId);
            var filterRequest = CerebrumUser.WorkListFilterRequest;
            // for signalR
            // TODO: In ASP.NET Core, Session needs to be configured and accessed differently
            // Session["PracticeId"] = practiceId;
            filterRequest.PracticeID = practiceId;
            filterRequest.IsPracticeDoctor = IsUserAPracticeDoctor();

            if (filterRequest.SelectedDoctorID <= 0 && DoctorID > 0 && !filterRequest.IsPracticeDoctor)
            {
                filterRequest.SelectedDoctorID = DoctorID;
            }
            else if (filterRequest.SelectedDoctorID <= 0 && filterRequest.IsPracticeDoctor)
            {
                filterRequest.SelectedDoctorID = CerebrumUser.PracticeDoctorId;
            }

            if (filterRequest.SelectedDoctorID > 0)
            {
                var practiceDoc = workList.Doctors.Where(x => x.Value == filterRequest.SelectedDoctorID.ToString()).FirstOrDefault();
                if (practiceDoc != null)
                {
                    filterRequest.DoctorName = practiceDoc.Text;
                }
            }

            CerebrumUser.WorkListFilterRequest = filterRequest;

            ViewBag.Offices = workList.Offices;
            ViewBag.Tests = workList.Tests;
            ViewBag.Statuses = workList.Statuses;
            ViewBag.Doctors = workList.Doctors;
            ViewBag.TestGroups = workList.TestGroups;
            LoadPrioritiesData();
            return View(workList);
        }

        public ActionResult WS_List_Data(VMWorklistRequest filterRequest)
        {
            if (ModelState.IsValid)
            {
                var workList = _measurementBll.GetWorkDataMainList(filterRequest);
                filterRequest.FilterSet = true;
                filterRequest.DoctorName = filterRequest.SelectedDoctorID > 0 ? _measurementBll.GetDoctorNameByID((int)filterRequest.SelectedDoctorID) : "";
                CerebrumUser.WorkListFilterRequest = filterRequest;
                LoadPrioritiesData();
                return View(workList);
            }
            else
            {
                string errorMsg = ValidationError(this.ModelState);
                return Json(new { success = false, message = errorMsg });
            }
        }

        public ActionResult WL_Reassign()
        {
            return View();

        }

        public JsonResult Reassign_WL_Item(int appointmentTestID, int docID, int patientID)
        {
            string ipAddress = IPAddress(Request);
            bool errored = false;
            string message = string.Empty;
            string mssg = string.Empty;
            try
            {
                message = measurementRepo.Reassign(appointmentTestID, docID, CerebrumUser.UserId, GetIPAddress());

                string name = userRepo.GetNameByDocID(docID);
                //string guid = userRepo.GetGuidByUserName("<EMAIL>");
                string guid = userRepo.GetUserNameByDocID(docID);
                //string guid = userRepo.GetGuidByUserName("<EMAIL>");
                //string guid = userRepo.GetGuidByDocID(docID);

                mssg = _contactManagerService.AddNewTask(new Cerebrum.ViewModels.ContactManagerNew.ContactManagerTaskData()
                {
                    patientRecordId = patientID.ToString(),
                    subject = "Reassignment",
                    recipients = guid,
                    urgency = "2",
                    dueDate = DateTime.Now.ToString("MM/dd/yyyy"),
                    message = string.Format("Test was reassigned to {0}", name),
                    office = "",
                    markDone = 0
                }, CerebrumUser.PracticeId, CerebrumUser.GetUserIdGuid(), CerebrumUser.UserId, ipAddress);
            }
            catch (Exception exc)
            {
                errored = true;
                mssg = exc.Message;
            }

            if (!string.IsNullOrEmpty(mssg))
            {
                errored = true;
                message = mssg;
            }

            return Json(new { Errored = errored, Result = message });

        }

        #endregion

        #region Reports

        public ActionResult GetDocContactList(int appointmentID, int TestID, int appointmentTestId = 0)
        {
            VMPatientAppointement vm = new VMPatientAppointement();

            vm.AppointmentID = appointmentID;
            vm.TestID = TestID;
            vm.PatientID = userRepo.GetPatientByAppointment(vm.AppointmentID);
            vm.AppointmentTestId = appointmentTestId;

            vm.Doctors = _measurementBll.GetDoctorReportList(appointmentID);

            return View(vm);

        }
        //[CheckPermissions(Permissions = "SendReport")]
        public ActionResult SendReportList(int appointmentID, int testID, int patientID, int appointmentTestId = 0)
        {
            Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM vm = new Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM() { AppointmentId = appointmentID, TestId = testID, PatientId = patientID, AppointmentTestId = appointmentTestId };
            return View(vm);
        }

        public async Task<ActionResult> ReportHistory(int appointmentID, int TestID, int patientID)
        {
            var vm = measurementRepo.GetReportHistory(appointmentID, TestID, patientID);
            // Create a list of tasks for GenerateTokenAsync
            var tasks = vm.Select(async v =>
            {
                v.Token = await UtilityTokenHelper.GenerateTokenAsync(
                    CerebrumUser.UserId,
                    CerebrumUser.PracticeId,
                    this.measurementRepo.GetDownloadURL(userRepo.GetOfficeIDByAppointment(appointmentID))
                );
            }).ToList();

            // Wait for all tasks to complete
            await Task.WhenAll(tasks);

            return View(vm);
        }
        #endregion

        #region Raw Data
        // [CheckPermissions(Permissions = "ViewImagesRawData")]
        public async Task<ActionResult> OpenRawDataClassic(int appointmentId, int testID, int appointmentTestID)
        {
            try
            {
                var vm = this.measurementRepo.GetLegacyURLs(appointmentTestID);
                vm.AppointmentID = appointmentId;
                vm.Token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId,
                    this.measurementRepo.GetDownloadURL(userRepo.GetOfficeIDByAppointment(appointmentId)));

                return View(vm);
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                _log.Error(ex.InnerException.ToString());
                _log.Error(ex.Source);
            }
            return View();
        }
        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        public async Task<ActionResult> OpenRawDataClassicByPatient(int appointmentId, int patientID)
        {
            try
            {
                var vm = this.measurementRepo.GetLegacyURLList(patientID);

                if (appointmentId == 0)
                {
                    appointmentId = this.userRepo.GetLastAppointmentsByPatient(patientID, CerebrumUser.PracticeId);
                }

                // Run token generation in parallel for performance
                var tasks = vm.Select(async v =>
                {
                    v.AppointmentID = appointmentId;
                    var officeId = userRepo.GetOfficeIDByAppointment(appointmentId);
                    var downloadUrl = this.measurementRepo.GetDownloadURL(officeId);

                    v.Token = await UtilityTokenHelper.GenerateTokenAsync(
                        CerebrumUser.UserId,
                        CerebrumUser.PracticeId,
                        downloadUrl
                    );
                });

                await Task.WhenAll(tasks);

                return View(vm);
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                _log.Error(ex.InnerException?.ToString());
                _log.Error(ex.Source);
            }
            return View();
        }

        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        public async Task<JsonResult> OpenRawDataClassic2(int appointmentId, int testID, int appointmentTestID)
        {
            try
            {
                var vm = this.measurementRepo.GetLegacyURLs(appointmentTestID);
                vm.AppointmentID = appointmentId;

                var officeId = userRepo.GetOfficeIDByAppointment(appointmentId);
                var downloadUrl = this.measurementRepo.GetDownloadURL(officeId);

                vm.Token = await UtilityTokenHelper.GenerateTokenAsync(
                    CerebrumUser.UserId,
                    CerebrumUser.PracticeId,
                    downloadUrl
                );

                return Json(new { Result = vm });
            }
            catch (Exception ex)
            {
                _log.Error("OpenRawDataClassic2 Error", ex);
                return Json(new { Result = ex.ToString() });
            }
        }

        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        public async Task<ActionResult> OpenRawData(int appointmentId, int testID, int? openRawData)
        {
            RawData_VM vm = new RawData_VM() { AppointmentID = appointmentId, TestID = testID };

            ServerLocationProvider locationProvider = new ServerLocationProvider(appointmentId, testID);
            string uploadPath = locationProvider.GetLocation(AwareMD.Cerebrum.Shared.Enums.DataType.RawData, Module.WS);
            vm.RelativePath = locationProvider.GetURL(AwareMD.Cerebrum.Shared.Enums.DataType.RawData, Module.WS);
            //TODO, hardcoded
            uploadPath = Path.Combine(_webHostEnvironment.WebRootPath, "Areas/Measurements/uploads/", uploadPath);

            if (!Directory.Exists(uploadPath))
            {
                Directory.CreateDirectory(uploadPath);
            }

            string clinicServerDownloadUrl = this.measurementRepo.GetDownloadURL(userRepo.GetOfficeIDByAppointment(appointmentId));
            string token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, clinicServerDownloadUrl);
            foreach (var file in measurementRepo.GetRawFiles(appointmentId, testID))
            {
                vm.Files.Add(new RawDataFile()
                {
                    FileName = Path.GetFileName(file),
                    Token = token,
                    Path = file.Replace("\\", "|")
                });
            }
            vm.clinicServerUrl = $"{clinicServerDownloadUrl.TrimEnd('/')}/{Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH.TrimStart('/').TrimEnd('/')}/";

            if (TempData[OPENRAWDATA_MSSG] != null)
                ViewBag.Message = TempData[OPENRAWDATA_MSSG].ToString();

            if (TempData[OPENRAWDATA_SCRIPT] != null)
                vm.ScriptToExecute = TempData[OPENRAWDATA_SCRIPT].ToString();

            if (openRawData != null && openRawData > 0)
                vm.openRawData = 1;

            return View(vm);
        }
        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        //overloding method because of typo

        [HttpPost]
        public async Task<JsonResult> ShowRawData(int appointmentId, int testID)
        {
            RawData_VM vm = new RawData_VM() { AppointmentID = appointmentId, TestID = testID };
            int officeId = userRepo.GetOfficeIDByAppointment(appointmentId);
            string clinicServerDownloadUrl = this.measurementRepo.GetDownloadURL(officeId);
            string token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, clinicServerDownloadUrl);
            foreach (var file in measurementRepo.GetRawFiles(appointmentId, testID))
            {
                vm.Files.Add(new RawDataFile()
                {
                    FileName = Path.GetFileName(file),
                    Token = token,
                    Path = Cerebrum.BLL.Utility.UtilityHelper.GetDocumentUrl(file, officeId, true)
                });
            }
            vm.clinicServerUrl = $"{clinicServerDownloadUrl.TrimEnd('/')}/{Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH.TrimStart('/').TrimEnd('/')}/";

            return Json(vm);
        }

        public async Task<FileResult> RetreiveFile(string path, int? appointmentID = 0, string token = "", int officeID = 0)
        {
            return await RetrieveFile(path, appointmentID, token, officeID);
        }

        public async Task<FileResult> RetrieveFile(string path, int? appointmentID = 0, string token = "", int officeID = 0, int numberOfTry = 0)
        {
            try
            {
                if (numberOfTry > 0)
                    _log.Debug($"RetrieveFile: After try {numberOfTry} time(s) to read from clinic server, read from main server");

                if (officeID == 0)
                    officeID = userRepo.GetOfficeIDByAppointment((int)appointmentID);
                if (string.IsNullOrEmpty(token))
                {
                    //_log.Debug($"Token doesn't exists");
                    token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, this.measurementRepo.GetDownloadURL(officeID));
                    _log.Debug("Token generated");
                }
                else
                {
                    _log.Debug($"Token exists");
                }


                path = path.TrimStart("\\".ToCharArray());
                path = path.Replace("\\", "|");

                string serverURL = this.measurementRepo.GetDownloadURL(officeID);
                string extension = Path.GetExtension(path.Replace("|", "//"));
                extension = extension.Replace(".", string.Empty);
                var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
                string baseUrl = serverURL + Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH + Uri.EscapeDataString(path);
                byte[] contents;
                System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
                stopwatch.Start();
                try
                {
                    _log.Info($"RetrieveFile Started - path: {path}, baseUrl: {baseUrl}");
                    //contents = await Cerebrum.BLL.Utility.CerebrumHTTPClient.GetByteAsync(_httpClientFactory, baseUrl, token);
                    var httpClient = _httpClientFactory.CreateClient("download");
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);

                    var response = await httpClient.GetAsync(baseUrl, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        _log.Info($"RetrieveFile Succeed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                    }
                    else
                    {
                        _log.Info($"RetrieveFile Failed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                    }
                    contents = await response.Content.ReadAsByteArrayAsync();
                }
                catch (Exception ex)
                {
                    _log.Error($"RetrieveFile Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        _log.Error($"RetrieveFile Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                    }
                    throw ex;
                }
                _log.Debug($"path: {path} BaseURL: {baseUrl} Response content{contents.Length}");

                Response.Headers.Add("Content-Disposition", string.Format("inline; filename={1}.{0}", extension, Uri.EscapeDataString(filename)));

                return File(contents, string.Format("application/{0}", extension));
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
            }
            return null;
        }
        public async Task<byte[]> RetreiveFileBytes(string path, int appointmentID, string token = "", int officeID = 0)
        {
            return await RetrieveFileBytes(path, appointmentID, token, officeID);
        }

        public async Task<byte[]> RetrieveFileBytes(string path, int appointmentID, string token = "", int officeID = 0)
        {
            try
            {
                if (officeID == 0)
                    officeID = userRepo.GetOfficeIDByAppointment(appointmentID);
                _log.Info($"Token exists {string.IsNullOrEmpty(token)}");
                if (string.IsNullOrEmpty(token))
                {
                    _log.Info($"Token doesn't exists");
                    token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, this.measurementRepo.GetDownloadURL(officeID));
                    _log.Info("Token generated");
                }
                else
                {
                    _log.Info($"Token exists");
                }

                path = path.TrimStart("\\".ToCharArray());
                path = path.Replace("\\", "|");
                _log.Debug($"path: {path}");
                string serverURL = this.measurementRepo.GetDownloadURL(officeID);
                string extension = Path.GetExtension(path.Replace("|", "//"));
                extension = extension.Replace(".", string.Empty);
                var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
                string baseUrl = serverURL + Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH + Uri.EscapeDataString(path);
                System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
                stopwatch.Start();
                try
                {
                    _log.Info($"RetrieveFileBytes Started - path: {path}, baseUrl: {baseUrl}");
                    HttpClient httpClient = _httpClientFactory.CreateClient("download");
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);

                    var response = await httpClient.GetAsync(baseUrl, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        _log.Info($"RetrieveFileBytes Succeed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                    }
                    else
                    {
                        _log.Info($"RetrieveFileBytes Failed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                    }
                    var contents = await response.Content.ReadAsByteArrayAsync();

                    return contents;
                }
                catch (Exception ex)
                {
                    _log.Error($"RetrieveFileBytes Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        _log.Error($"RetrieveFileBytes Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex);
            }
            return null;
        }
        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        //overloading method to correct typo
        public async Task<string> RetreiveFileString(string path, int appointmentID, string token = "", int officeID = 0)
        {
            return await RetrieveFileString(path, appointmentID, token, officeID);
        }
        public async Task<string> RetrieveFileString(string path, int appointmentID, string token = "", int officeID = 0, int numberOfTry = 0)
        {
            try
            {
                if (numberOfTry > 0)
                    _log.Debug($"RetreiveFileString: After try {numberOfTry} time(s) to read from clinic server, read from main server");

                if (officeID == 0)
                    officeID = userRepo.GetOfficeIDByAppointment(appointmentID);

                string serverURL = this.measurementRepo.GetDownloadURL(officeID);

                if (string.IsNullOrEmpty(token))
                {
                    token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, serverURL);
                }

                path = path.TrimStart("\\".ToCharArray());
                path = path.Replace("\\", "|");


                string extension = Path.GetExtension(path.Replace("|", "//"));
                extension = extension.Replace(".", string.Empty);
                var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
                string baseUrl = serverURL + Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH + Uri.EscapeDataString(path);

                _log.Debug($"path: {path} BaseURL: {baseUrl} Token:{token.Length}");

                string contents = string.Empty; //await Cerebrum.BLL.Utility.CerebrumHTTPClient.GetStringAsync(_httpClientFactory, baseUrl, token);
                System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
                stopwatch.Start();
                try
                {
                    _log.Info($"RetrieveFileString Started - path: {path}, baseUrl: {baseUrl}");
                    var httpClient = _httpClientFactory.CreateClient("download");
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);

                    var response = await httpClient.GetAsync(baseUrl, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        _log.Info($"RetrieveFileString Succeed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                    }
                    else
                    {
                        _log.Info($"RetrieveFileString Failed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                    }
                    contents = await response.Content.ReadAsStringAsync();
                }
                catch (Exception ex)
                {
                    _log.Error($"RetrieveFileString Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        _log.Error($"RetrieveFileString Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                    }
                    throw ex;
                }
                //    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);
                //    var response = await httpClient.GetAsync(baseUrl);
                //    contents = await response.Content.ReadAsStringAsync();                    
                //}
                return contents;
            }
            catch (Exception ex)
            {
                _log.Error(ex);
            }
            return null;
        }

        // overloading method to correct a typo
        public async Task<byte[]> RetreiveFilePDF(int appointmentId, int testId)
        {
            return await RetrieveFilePDF(appointmentId, testId);
        }
        public async Task<byte[]> RetrieveFilePDF(int appointmentId, int testId)
        {
            byte[] contents = await _radStudyBll.RetriveFile(_httpClientFactory, CerebrumUser.PracticeId, appointmentId, testId, CerebrumUser.UserId, RadFile_Extension.pdf);

            return contents;
        }
        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        public async Task<byte[]> RetrieveFilePDF_OLD(RadImage rad, int appointmentId)
        {
            string path = rad.ImageName;

            var officeID = userRepo.GetOfficeIDByAppointment(appointmentId);
            string baseUrl = measurementRepo.GetDownloadURL(officeID) + "/api/exam/" + string.Format("/{0}/{1}/{2}", rad.Study_Date.Value.ToString("yyyyMMdd"), rad.Study_UID, rad.ImageName);
            string token = await UtilityTokenHelper.GenerateTokenAsync(((Cerebrum30.Security.CerebrumUser)HttpContextProvider.Current.User).UserId,
                ((Cerebrum30.Security.CerebrumUser)HttpContextProvider.Current.User).PracticeId,
                measurementRepo.GetDownloadURL(userRepo.GetOfficeIDByAppointment(appointmentId)));

            string extension = Path.GetExtension(path);
            extension = extension.Replace(".", string.Empty);
            var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            try
            {
                _log.Info($"RetrieveFilePDF_OLD Started - path: {path}, baseUrl: {baseUrl}");
                HttpClient httpClient = _httpClientFactory.CreateClient("download");
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);

                var response = await httpClient.GetAsync(baseUrl, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    _log.Info($"RetrieveFilePDF_OLD Succeed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                }
                else
                {
                    _log.Info($"RetrieveFilePDF_OLD Failed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                }
                var contents = await response.Content.ReadAsByteArrayAsync();

                return contents;
            }
            catch (Exception ex)
            {
                _log.Error($"RetrieveFilePDF_OLD Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                if (ex.InnerException != null)
                {
                    _log.Error($"RetrieveFilePDF_OLD Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                }
                throw ex;
            }
        }
        // [CheckPermissions(Permissions = "ViewImagesRawData")]
        //overloading method to correct a typo
        public async Task<FileResult> RetreiveFilePDF2(RadImage rad, int appointmentID)
        {
            return await RetrieveFilePDF2(rad, appointmentID);
        }
        public async Task<FileResult> RetrieveFilePDF2(RadImage rad, int appointmentID)
        {
            try
            {
                string path = rad.ImageName;
                string token = "";
                int officeID = 0;

                if (officeID == 0)
                    officeID = userRepo.GetOfficeIDByAppointment(appointmentID);
                _log.Info($"Token exists {string.IsNullOrEmpty(token)}");
                if (string.IsNullOrEmpty(token))
                {
                    _log.Info($"Token doesn't exists");
                    token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, this.measurementRepo.GetDownloadURL(officeID));
                    _log.Info("Token generated");
                }
                else
                {
                    _log.Info($"Token exists");
                }

                string serverURL = this.measurementRepo.GetDownloadURL(officeID);

                string extension = Path.GetExtension(path.Replace("|", "//"));
                extension = extension.Replace(".", string.Empty);
                var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
                string baseUrl = serverURL + "/api/exam/" + string.Format("/{0}/{1}/{2}", rad.Study_Date.Value.ToString("yyyyMMdd"), rad.Study_UID, rad.ImageName);
                System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
                stopwatch.Start();
                try
                {
                    _log.Info($"RetrieveFilePDF2 Started - path: {path}, baseUrl: {baseUrl}");
                    HttpClient httpClient = _httpClientFactory.CreateClient("download");
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);
                    var response = await httpClient.GetAsync(baseUrl);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        _log.Info($"RetrieveFilePDF2 Succeed - Time: {stopwatch.ElapsedMilliseconds}");
                    }
                    else
                    {
                        _log.Info($"RetrieveFilePDF2 Failed - Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                    }
                    var contents = await response.Content.ReadAsByteArrayAsync();
                    _log.Info($"Response content{contents.Length}");
                    Response.Headers.Add("Content-Disposition", string.Format("inline; filename={1}.{0}", extension, Uri.EscapeDataString(filename)));
                    _log.Info($"Hearder added to response");
                    return File(contents, string.Format("application/{0}", extension));
                }
                catch (Exception ex)
                {
                    _log.Error($"RetrieveFilePDF2 Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        _log.Error($"RetrieveFilePDF2 Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                    }
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex.ExceptionDetails());
            }
            return null;
        }
        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        //overloading method to correct a typo
        public async Task<FileResult> RetreiveFileClassic(string path, string token, int appointmentID)
        {
            return await RetrieveFileClassic(path, token, appointmentID);
        }
        public async Task<FileResult> RetrieveFileClassic(string path, string token, int appointmentID)
        {
            var officeID = userRepo.GetOfficeIDByAppointment(appointmentID);
            string serverURL = this.measurementRepo.GetDownloadURL(officeID);

            // try find 'fn=' text to retrieve file path
            // when not found the path used as is
            int pos = path.IndexOf("fn=");
            if (pos >= 0)
            {
                path = path.Substring(path.IndexOf("fn=") + 3);
            }

            string extension = Path.GetExtension(path.Replace("|", "//"));
            extension = extension.Replace(".", string.Empty);
            var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
            path = path.Replace("\\", "|").Replace(":", "||");
            _log.Debug($"path: {path}");
            string baseUrl = serverURL + Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH + Uri.EscapeDataString(path);
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            try
            {
                _log.Info($"RetrieveFileClassic Started - path: {path}, baseUrl: {baseUrl}");
                HttpClient httpClient = _httpClientFactory.CreateClient("download");
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);
                var response = await httpClient.GetAsync(baseUrl);
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    _log.Info($"RetrieveFileClassic Succeed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                }
                else
                {
                    _log.Info($"RetrieveFileClassic Failed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                }
                var contents = await response.Content.ReadAsByteArrayAsync();
                Response.Headers.Add("Content-Disposition", string.Format("inline; filename={1}.{0}", extension, Uri.EscapeDataString(filename)));
                return File(contents, string.Format("application/{0}", extension));
            }
            catch (Exception ex)
            {
                _log.Error($"RetrieveFileClassic Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                if (ex.InnerException != null)
                {
                    _log.Error($"RetrieveFileClassic Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                }
                throw ex;
            }
        }

        [HttpPost]
        //[CheckPermissions(Permissions = "ViewImagesRawData")]
        public ActionResult OpenRawData(IFormFile file, int appointmentId, int testID)
        {
            int userid = CerebrumUser.UserId;
            string ipAddress = IPAddress(Request);
            try
            {
                if (file != null && file.Length > 0)
                {
                    var fileName = Path.GetFileName(file.FileName);
                    var extension = Path.GetExtension(file.FileName);

                    if (extension.ToLower() != ".pdf".ToLower())
                    {
                        TempData[OPENRAWDATA_MSSG] = "Only pdf files allowed";
                    }
                    else
                    {
                        var documentsBLL = new Cerebrum.BLL.Documents.DocumentsBLL();
                        List<string> allowedExtenstions = new List<string> { ".pdf" };
                        if (!documentsBLL.IsAllowedFile(file.OpenReadStream(), allowedExtenstions))
                        {
                            TempData[OPENRAWDATA_MSSG] = "Only pdf files allowed";
                        }
                        else
                        {
                            ServerLocationProvider locationProvider = new ServerLocationProvider(appointmentId, testID);
                            string uploadPathOrg = locationProvider.GetLocation(AwareMD.Cerebrum.Shared.Enums.DataType.RawData, Module.WS);
                            string uploadPath = Path.Combine(_webHostEnvironment.WebRootPath, "Areas/Measurements/uploads/", uploadPathOrg);
                            if (!Directory.Exists(uploadPath))
                            {
                                DirectoryInfo dir = Directory.CreateDirectory(uploadPath);
                            }

                            var newFileName = documentsBLL.GenerateInternalFileName(fileName, null, CerebrumUser.UserId, ipAddress);
                            var newPath = Path.Combine(uploadPath, newFileName);
                            var path = Path.Combine(uploadPath, fileName);

                            if (System.IO.File.Exists(path))
                            {
                                System.IO.File.Delete(path);
                            }

                            path = newPath;
                            fileName = newFileName;
                            using (var stream = new FileStream(path, FileMode.Create))
                            {
                                file.CopyTo(stream);
                            }

                            Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM sendVM = new Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM()
                            {
                                AppointmentId = appointmentId,
                                TestId = testID,
                                DateEntered = DateTime.Now,
                                SendType = (Cerebrum30.Areas.Measurements.Models.ViewModels.SendType)(int)AwareMD.Cerebrum.Shared.Enums.SendType.Raw,
                                Location = uploadPathOrg + fileName,
                                Sent = false,
                                Amended = false,
                                PhysicalPath = uploadPathOrg + fileName,
                                URL = uploadPathOrg + fileName,
                                EmailTo = string.Empty

                            };

                            //appointmentId
                            var officeID = userRepo.GetOfficeIDByAppointment(appointmentId);
                            //string serverURL = this.measurementRepo.GetUploadURL(officeID);

                            //TODO, putting file on clinic server
                            documentsBLL.FileSendToClinic(_httpClientFactory, CerebrumUser.UserId, officeID, path, uploadPathOrg, false);
                            // Convert local SendReport_VM to the expected Cerebrum.ViewModels.VP.SendReport_VM
                            var convertedSendVM = new Cerebrum.ViewModels.VP.SendReport_VM
                            {
                                Id = sendVM.Id,
                                EmailTo = sendVM.EmailTo,
                                FaxTo = sendVM.FaxTo,
                                Location = sendVM.Location,
                                DateEntered = sendVM.DateEntered,
                                Sent = sendVM.Sent,
                                Amended = sendVM.Amended,
                                IsVP = sendVM.IsVP,
                                AppointmentId = sendVM.AppointmentId,
                                TestId = sendVM.TestId,
                                PatientId = sendVM.PatientId,
                                SendType = (AwareMD.Cerebrum.Shared.Enums.SendType)(int)sendVM.SendType,
                                PhysicalPath = sendVM.PhysicalPath,
                                URL = sendVM.URL,
                                DocName = sendVM.DocName,
                                ErrorMessage = sendVM.ErrorMessage
                            };
                            this.measurementRepo.AddSendReport_RawData(convertedSendVM, userid, ipAddress);

                            System.IO.File.Delete(path);
                            TempData[OPENRAWDATA_MSSG] = "File Uploaded";
                            TempData[OPENRAWDATA_SCRIPT] = "if(opener!= null){opener.LoadMenu();}";
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                TempData[OPENRAWDATA_MSSG] = "Error occurred " + exc.Message;
                _log.Error(exc.ToString());
            }

            return RedirectToAction("OpenRawData",

                new
                {
                    appointmentId = appointmentId,
                    testID = testID
                });
        }

        #endregion

        private JsonResult SendReport(bool changeStatus, WorkSheetVM vm)
        {
            bool errored = false;
            string ipaddress = IPAddress(Request);
            string message = "";

            //saving data and draft 
            List<Measurement_Value_VM> retErrorList = new List<Measurement_Value_VM>();
            errored = SaveData(vm, retErrorList);
            if (!errored)
            {
                var appointmentTestId = vm.AppointmentTestID;
                var appointmentTestLogId = vm.AppointmentTestLogID;
                var userId = CerebrumUser.UserId;
                var userFullName = CerebrumUser.FullName;
                var isDoctor = CerebrumUser.PracticeDoctorId > 0 ? true : false;
                var ipAddress = CerebrumUser.IpAddress;
                var isAmended = vm.IsAmended;

                if (CerebrumUser.IsTrainee)
                {
                    //if user is trainee, change status ONLY 
                    this.measurementRepo.UpdateTestStatus(appointmentTestId, (int)AppointmentTestStatuses.TraineeReportReady, userId, ipaddress);
                    message = "Trainee report is ready.";
                }
                else
                {
                    var allowToSendReport = IsAllowToSendReport(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

                    if (allowToSendReport)
                    {
                        var practiceSendOutReportMethod = CerebrumUser.PracticeSendOutReportMethod;
                        var sent = false;
                        var bullsEyeId = _measurementBll.GetBullsEyeId(vm.AppointmentID, vm.TestID);
                        switch (practiceSendOutReportMethod)
                        {
                            case (SendOutReportMethod.Queue):
                                var dataValidation = _measurementBll.ValidateHrmReport(vm.PracticeID, vm.AppointmentTestID, vm.AppointmentTestLogID);
                                if (!string.IsNullOrEmpty(dataValidation.ErrorMessage))
                                {
                                    var dataVm = new VMFileResendToDoctors()
                                    {
                                        AppointmentId = vm.AppointmentID,
                                        AppointmentTestId = vm.AppointmentTestID,
                                        AppointmentTestLogId = vm.AppointmentTestLogID,
                                        PatientId = vm.PatientID,
                                        TestId = vm.TestID,
                                        IsAmended = vm.IsAmended,
                                        ChangeStatus = changeStatus,
                                        ErrorMessage = dataValidation.ErrorMessage,
                                        Doctors = dataValidation.Doctors
                                    };

                                    // TODO: Replace with ASP.NET Core view rendering
                                    string validationInfo = "Validation info temporarily disabled - needs ASP.NET Core view rendering";
                                    return Json(new { Errored = "2", Message = validationInfo });
                                }

                                _log.Info("Report will be sent by queue");
                                // new method SaveReportInQueue, SendReport calls in task (Cerebrum.SendReport.Console)
                                sent = _measurementBll.SaveReportInQueue(appointmentTestId, appointmentTestLogId,
                                    isDoctor, AwareMD.Cerebrum.Shared.Enums.DataType.Report, VPLetterType.None, userId, userFullName, ipAddress,
                                    false, bullsEyeId, changeStatus, isAmended, null, practiceSendOutReportMethod);
                                break;
                            case SendOutReportMethod.Regular:
                                sent = _measurementBll.SendReport(_httpClientFactory, appointmentTestId,
                                    appointmentTestLogId, isDoctor, AwareMD.Cerebrum.Shared.Enums.DataType.Report, VPLetterType.None,
                                    userId, userFullName, ipAddress, false, null, practiceSendOutReportMethod, true, changeStatus, out message);
                                break;
                            case SendOutReportMethod.HrmService:
                                sent = _measurementBll.SendReport(_httpClientFactory, appointmentTestId,
                                    appointmentTestLogId, isDoctor, AwareMD.Cerebrum.Shared.Enums.DataType.Report, VPLetterType.None,
                                    userId, userFullName, ipAddress, false, null, practiceSendOutReportMethod, true, changeStatus, out message);

                                var messageParts = message?.Split(new[] { "||" }, StringSplitOptions.None);
                                if (messageParts?.Length == 2)
                                {
                                    VMFileResendToDoctors dataHRM = _measurementBll.GetHrmServiceErrorMessage(vm.PracticeID, vm.AppointmentTestID, vm.AppointmentTestLogID);
                                    dataHRM.AppointmentId = vm.AppointmentID;
                                    dataHRM.AppointmentTestId = vm.AppointmentTestID;
                                    dataHRM.AppointmentTestLogId = vm.AppointmentTestLogID;
                                    dataHRM.PatientId = vm.PatientID;
                                    dataHRM.TestId = vm.TestID;
                                    dataHRM.IsAmended = vm.IsAmended;
                                    dataHRM.ChangeStatus = changeStatus;
                                    dataHRM.ErrorMessage = messageParts[1];

                                    // TODO: Replace with ASP.NET Core view rendering
                                    string validationInfo = "HRM validation info temporarily disabled - needs ASP.NET Core view rendering";
                                    return Json(new { Errored = "2", Message = validationInfo });
                                }

                                //hrmService only send the report via hrm, the others ways like fax and email will send in task (Cerebrum.SendReport.Console)
                                _measurementBll.SaveReportInQueue(appointmentTestId, appointmentTestLogId,
                                    isDoctor, AwareMD.Cerebrum.Shared.Enums.DataType.Report, VPLetterType.None, userId, userFullName, ipAddress,
                                    false, bullsEyeId, changeStatus, isAmended, null, practiceSendOutReportMethod);

                                break;
                            default:
                                throw new ArgumentException("Invalid practice send out report method");
                        }

                        if (sent)
                        {
                            // change status only if its not amended
                            if (!isAmended)
                            {
                                //change to completed
                                if (changeStatus)
                                {
                                    // change ReportCompleted to BeingSent. When run Cerebrum.SendReport.Console app
                                    // status will be changed to ReportCompleted
                                    if (practiceSendOutReportMethod != SendOutReportMethod.Queue && practiceSendOutReportMethod != SendOutReportMethod.HrmService)
                                    {
                                        this.measurementRepo.UpdateTestStatus(appointmentTestId, (int)AppointmentTestStatuses.ReportCompleted, userId, ipaddress);
                                    }
                                }
                                else // for interim report, go back to test started
                                {
                                    this.measurementRepo.UpdateTestStatus(vm.AppointmentTestID, (int)AppointmentTestStatuses.Test_Started, userId, ipaddress);
                                }
                            }
                        }

                        errored = sent == false;
                    }
                    else
                    {
                        message = "Please attach raw data or DICOM before sending.";
                        errored = true;
                    }
                }
            }
            else
            {
                message = "An error occurred while saving data.";
            }

            return Json(new { Errored = errored ? "1" : "0", Message = message });


        }

        private string ValidationError(ModelStateDictionary ms)
        {
            return Cerebrum.ViewModels.Utility.ValidationError(ms);
        }

        private bool IsUserAPracticeDoctor()
        {
            bool isPracticeDoctor = CerebrumUser.PracticeDoctorId > 0 ? true : false;

            return isPracticeDoctor;
        }
        private bool IsDocumentAutoOpen(int appointmentID, int testID)
        {
            return false;   //disable "auto open" for performance issue

            //var bll = new Cerebrum.BLL.User.UserBLL();
            //var practiceDoctor = bll.GetPracticeDoctorData(CerebrumUser.PracticeId, CerebrumUser.UserId);
            //if (practiceDoctor == null || !practiceDoctor.autoOpenDocument)
            //    return false;

            //var testStatus = _measurementBll.GetTestStatus(appointmentID, testID);
            //if (testStatus.ToLower() != "ready for doctor")
            //    return false;

            //return true;
        }

        private bool HasRawData(int appointmentID, int testID)
        {
            bool hasRawData = false;
            try
            {
                hasRawData = measurementRepo.GetRawFiles(appointmentID, testID).Count > 0;
            }
            catch
            {
                hasRawData = false;
            }

            return hasRawData;
        }

        private bool OpenRawData(int appointmentID, int testID)
        {
            bool openRawData = false;
            try
            {
                if (!IsDocumentAutoOpen(appointmentID, testID))
                    return false;

                openRawData = HasRawData(appointmentID, testID);
            }
            catch
            {
                openRawData = false;
            }

            return openRawData;
        }

        private bool OpenImage(int appointmentID, int testID)
        {
            bool openImage = false;
            try
            {
                if (!IsDocumentAutoOpen(appointmentID, testID))
                    return false;

                var xafFile = _radStudyBll.GenerateViewerFile(_httpClientFactory, CerebrumUser.PracticeId, appointmentID, testID, CerebrumUser.UserId);
                if (xafFile != null)
                    openImage = true;
                else
                    openImage = false;
            }
            catch
            {
                openImage = false;
            }

            return openImage;
        }

        private List<Cerebrum.ViewModels.Documents.VMReferralDocument> OpenReferralDocument(int appointmentID, int testID)
        {
            List<Cerebrum.ViewModels.Documents.VMReferralDocument> autoOpenReferralDocuments = new List<Cerebrum.ViewModels.Documents.VMReferralDocument>();
            try
            {
                if (!IsDocumentAutoOpen(appointmentID, testID))
                    return autoOpenReferralDocuments;

                Cerebrum.BLL.Documents.DocumentsBLL bll = new Cerebrum.BLL.Documents.DocumentsBLL();
                autoOpenReferralDocuments = bll.GetReferralDocuments(_httpClientFactory, CerebrumUser.PracticeId, CerebrumUser.UserId, appointmentId: appointmentID);
            }
            catch
            {
            }

            return autoOpenReferralDocuments;
        }

        private string GetIPAddress()
        {
            string ipAddress = IPAddress(Request);
            return ipAddress;
        }

        private bool IsAllowToSendReport(int appointmentID, int testID, int appointmentTestLogID)
        {
            if (testID != 4)
            {
                return true;
            }

            // ECG only
            var rawFiles = _measurementBll.GetRawFiles(appointmentID, testID);
            if (rawFiles.Count > 0)
            {
                return true;
            }

            return _measurementBll.IsDicomExists(appointmentID, testID, appointmentTestLogID);
        }

        private List<VMAppointmentPriority> GetPracticeActivePriorityList()
        {
            return _appPriorityBll.GetPracticePriorityList(CerebrumUser.PracticeId, false);
        }
        private void LoadPrioritiesData()
        {
            var priorities = GetPracticeActivePriorityList();

            if (priorities.Any())
            {
                ViewBag.AppointmentPriorities = MapPriorityItems(priorities);
                ViewBag.ShowPriority = CerebrumUser.IsAppointmentPriorityEnabled && CerebrumUser.IsPracticeAppointmentPriorityEnabled;
            }
        }

        private List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> MapPriorityItems(List<VMAppointmentPriority> priorities)
        {
            List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> list = new List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>();

            var activeGroup = new Microsoft.AspNetCore.Mvc.Rendering.SelectListGroup { Name = "Active", Disabled = false };
            var inActiveGroup = new Microsoft.AspNetCore.Mvc.Rendering.SelectListGroup { Name = "Inactive", Disabled = false };

            bool allInActivePriority = priorities.All(p => p.IsActive);

            foreach (var priority in priorities.OrderBy(r => r.Rank).ThenBy(p => p.PriorityName))
            {
                Microsoft.AspNetCore.Mvc.Rendering.SelectListItem item = new Microsoft.AspNetCore.Mvc.Rendering.SelectListItem()
                {
                    Text = priority.PriorityName,
                    Value = priority.Id.ToString(),
                    Selected = priority.IsActive,
                    Group = allInActivePriority ? null : (priority.IsActive ? activeGroup : inActiveGroup),
                };

                list.Add(item);
            }

            list.Insert(0, new SelectListItem() { Value = "", Text = "None" });

            return list;
        }
        /// <summary>
        /// Convert from local TopLevelReportPhrase to global TopLevelReportPhrase
        /// </summary>
        private List<Cerebrum.ViewModels.Measurements.TopLevelReportPhrase> ConvertToGlobalTopLevelReportPhrase(List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> localPhrases)
        {
            if (localPhrases == null) return new List<Cerebrum.ViewModels.Measurements.TopLevelReportPhrase>();

            return localPhrases.Select(lp => new Cerebrum.ViewModels.Measurements.TopLevelReportPhrase
            {
                Id = lp.Id,
                Name = lp.Name,
                Value = lp.Value,
                RangeMessage = lp.RangeMessage,
                NormalPhraseID = lp.NormalPhraseID,
                NormalPhraseText = lp.NormalPhraseText,
                AutoScrollCategoryID = lp.AutoScrollCategoryID,
                type = lp.type,
                field = lp.field,
                ordernumber = lp.ordernumber,
                Visible = true, // Default value since local type doesn't have this property
                Phrases = ConvertReportPhrasesToGlobal(lp.Phrases)
            }).ToList();
        }

        /// <summary>
        /// Convert ReportPhrase_VM list from local to global type
        /// </summary>
        private List<Cerebrum.ViewModels.Measurements.ReportPhrase_VM> ConvertReportPhrasesToGlobal(List<Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM> localPhrases)
        {
            if (localPhrases == null) return new List<Cerebrum.ViewModels.Measurements.ReportPhrase_VM>();

            return localPhrases.Select(phrase => new Cerebrum.ViewModels.Measurements.ReportPhrase_VM
            {
                Id = phrase.Id,
                Name = phrase.Name,
                Value = phrase.Value,
                CustomText = phrase.CustomText,
                type = phrase.type,
                field = phrase.field,
                Visible = true, // Default value since local type doesn't have this property
                Phrases = ConvertReportPhrasesToGlobal(phrase.Phrases) // Recursive conversion
            }).ToList();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _measurementBll.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}