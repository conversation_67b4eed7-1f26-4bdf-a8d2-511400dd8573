﻿using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Patient;
using Cerebrum.Caching;
using Cerebrum.Data;
using Cerebrum.DTO.Worksheet.SPDtos;
using Cerebrum.ViewModels.Measurements;
using Cerebrum.ViewModels.Test;
using Cerebrum.ViewModels.VP;
using Cerebrum.VisitPage;
using Cerebrum3.Infrastructure;
using Cerebrum30.Areas.Measurements.Models.DataObjects;
using Cerebrum30.Areas.Measurements.Models.ViewModels;
using Cerebrum30.Areas.PdfConversions.Models.ViewModels;
using Cerebrum30.DAL.DataAccess.Repositories;
using Cerebrum30.Utility;
using Cerebrum30.Utility.HL7;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Common;
// EF6 using statements removed - using EF Core instead
// using System.Data.Entity;
// using System.Data.Entity.Infrastructure;
using Microsoft.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Xml;
using Svg; // SVG.NET package for SVG processing

namespace Cerebrum30.Areas.Measurements.DataAccess
{
    public class MeasurementRepository : GenericRepository<CerebrumContext, Measurement>, IMeasurementRepository
    {
        readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private SendReportBLL _sendReport;
        //readonly string MEASURE_FORMAT_DECIMAL = "{0:0.##}";
        readonly string MEASURE_FORMAT_DECIMAL = "{0:F2}";
        private PatientBLL _patientBll;
        public MeasurementRepository()
        {
            _patientBll = new PatientBLL(context);
            _sendReport = new SendReportBLL(context);
        }

        #region Private methods
        private DateTime? GetAppointmentDate(int appointmentID)
        {
            DateTime? retDT = null;

            var appointment = (from a in context.Appointments
                               where a.Id == appointmentID
                               select a).FirstOrDefault();

            if (appointment != null)
            {
                retDT = appointment.appointmentTime;
            }

            return retDT;
        }

        private bool HasActiveProstaticValve(int patientID, int appointmentID)
        {
            bool retVal = false;

            var patient = context.PatientProstaticValve.Where(p => p.PatientRecordId == patientID).FirstOrDefault();

            if (patient != null)
            {
                DateTime? addedDate = patient.AddedDate;
                DateTime? statusChangeDate = patient.EndDate;
                DateTime? appointmentTime = this.GetAppointmentDate(appointmentID);

                if (appointmentTime >= addedDate && appointmentTime <= statusChangeDate)
                {
                    retVal = true;
                }
            }
            return retVal;
        }
        private int GetProstaticValveID(int patientID, int appointmentID)
        {
            int retVal = 0;

            if (patientID > 0 && appointmentID > 0)
            {
                var patientLst = context.PatientProstaticValve.Where(p => p.PatientRecordId == patientID).ToList();

                if (patientLst != null && patientLst.Count > 0)
                {
                    var patient = patientLst[0];
                    if (patient != null)
                    {
                        DateTime? addedDate = patient.AddedDate;
                        DateTime? statusChangeDate = patient.EndDate;
                        DateTime? appointmentTime = this.GetAppointmentDate(appointmentID);

                        if (appointmentTime >= addedDate && appointmentTime <= statusChangeDate)
                        {
                            retVal = patient.ProstaticValveId;
                        }
                    }
                }
            }
            return retVal;
        }
        private static decimal TruncateToTwoDigits(decimal Value)
        {
            int adjusted = (int)Math.Truncate(Value * 100m);
            return adjusted / 100m;
        }

        private List<MeasurementSavedValueVM> GetSavedValues(int AppointmentID, int TestID, int prevLogID, bool has1LogBefore = false)
        {
            var prevCatLst = GetCategoriesAndMeasurements(AppointmentID, TestID, prevLogID);
            prevCatLst = LoadSavedValues(prevCatLst, AppointmentID, TestID, prevLogID, 0);
            var allsavedMeasurment = prevCatLst.SelectMany(s => s.Measurements).ToList().SelectMany(m => m.MeasurementSavedValues.Where(w => !string.IsNullOrWhiteSpace(w.Value))).ToList();

            bool hasMeasurmentInPreviousLog = allsavedMeasurment != null && allsavedMeasurment.Count > 0;
            return GetSavedValueList(prevCatLst, hasMeasurmentInPreviousLog);

        }
        //private List<MeasurementSavedValue> GetSavedValues(int appointmentID, int testID, int appointmentTestLogID, int measureID)
        //{
        //    return (
        //                from s in context.MeasurementSavedValue
        //                where
        //                s.MeasurementId == measureID &&
        //                s.AppointmentTestLogID == appointmentTestLogID &&
        //                s.TestID == testID &&
        //                s.AppointmentID == appointmentID
        //                select s
        //            ).ToList();

        //}
        private List<MeasurementSavedValue> GetSavedValueSet(int appointmentID, int testID, int appointmentTestLogID)
        {
            return (
                        from s in context.MeasurementSavedValue.Include("Measurement")
                        where
                            s.AppointmentTestLogID == appointmentTestLogID &&
                            s.TestID == testID &&
                            s.AppointmentID == appointmentID
                        select s

                    ).AsNoTracking().ToList();

        }

        /// <summary>
        /// Returns a 'MeasurementRanges' object from table 'MeasurementRange' based on 'mesCode' and 'catCode' for a particular value
        /// </summary>
        /// <param name="val"></param>
        /// <param name="mesCode"></param>
        /// <param name="catCode"></param>
        /// <returns></returns>
        public MeasurementRanges GetRangeTypes(double val, string mesCode, string catCode, Gender gender)
        {
            MeasurementRanges range = new MeasurementRanges();

            range = MeasurementRanges.NoRangeAvailable;

            if (val != 0 && !string.IsNullOrEmpty(mesCode) && !string.IsNullOrEmpty(catCode))
            {
                if ((
                    from r in context.MeasurementRange
                    where
                    (r.measurementCode == mesCode && r.categoryCode == catCode) &&
                    r.gender == (gender == Gender.M ? "M" : "F")
                    select r
                    ).Any())
                {
                    var rangeType = (from r in context.MeasurementRange
                                     where
                                     (r.measurementCode == mesCode && r.categoryCode == catCode) &&
                                     (val >= r.Range1 && val < r.Range2) &&
                                     r.gender == (gender == Gender.M ? "M" : "F")
                                     select r).ToList().FirstOrDefault();
                    if (rangeType != null)
                    {
                        range = (MeasurementRanges)rangeType.MeasurementRangeTypeId;

                    }
                    else
                    {
                        range = MeasurementRanges.OutOfRange;
                    }

                }
            }

            return range;

        }

        public MeasurementRanges GetMeasurementRange(List<MeasurementRange> lst, double val, string mesCode, string catCode, int prostaticValveId, Gender gender)
        {
            MeasurementRanges range = MeasurementRanges.NoRangeAvailable;

            if (val != 0 && !string.IsNullOrEmpty(mesCode) && !string.IsNullOrEmpty(catCode))
            {
                var find = (from r in lst
                            where (r.measurementCode == mesCode && r.categoryCode == catCode)
                            && r.gender == (gender == Gender.M ? "M" : "F")
                            select new { r.Range1, r.Range2, r.MeasurementRangeTypeId });
                //if ((from r in lst
                //     where (r.measurementCode == mesCode && r.categoryCode == catCode)
                //     &&
                //     r.gender == (gender == Gender.M ? "M" : "F")
                //     select r).Any())
                if (find.Any())
                {
                    //var rangeType = (from r in lst
                    //                 where (r.measurementCode == mesCode && r.categoryCode == catCode) && (val >= r.Range1 && val < r.Range2)
                    //                 &&
                    //                 r.gender == (gender == Gender.M ? "M" : "F")
                    //                 //&& r.ProstaticValveID == prostaticValveId
                    //                 select r.MeasurementRangeTypeId).FirstOrDefault();
                    var rangeType = (from r in find
                                     where (val >= r.Range1 && val < r.Range2)
                                     select r.MeasurementRangeTypeId).FirstOrDefault();
                    if (rangeType > 0)
                    {
                        range = (MeasurementRanges)rangeType;

                    }
                    else
                    {
                        range = MeasurementRanges.OutOfRange;
                    }
                }
            }

            return range;

        }

        public MeasurementRanges? GetMeasurementBSARange(List<MeasurementBSARange> lst, double val, string mesCode, string catCode, int prostaticValveId, Gender gender)
        {
            MeasurementRanges? range = null;

            if (val != 0 && !string.IsNullOrEmpty(mesCode) && !string.IsNullOrEmpty(catCode))
            {
                var find = (from r in lst
                            where
                            (r.measurementCode == mesCode && r.categoryCode == catCode) &&
                             r.gender == (gender == Gender.M ? "M" : "F")
                            //&& r.ProstaticValveID == prostaticValveId
                            select new { r.IndexedRange1, r.IndexedRange2, r.MeasurementRangeTypeId });
                //if ((from r in lst
                //     where
                //     (r.measurementCode == mesCode && r.categoryCode == catCode) &&
                //      r.gender == (gender == Gender.M ? "M" : "F")
                //     //&& r.ProstaticValveID == prostaticValveId
                //     select r).Any())
                if (find.Any())
                {
                    //var rangeType = (from r in lst
                    //                 where
                    //                 (r.measurementCode == mesCode && r.categoryCode == catCode) &&
                    //                 (val >= r.IndexedRange1 && val < r.IndexedRange2) &&
                    //                  r.gender == (gender == Gender.M ? "M" : "F")
                    //                 //&& r.ProstaticValveID == prostaticValveId
                    //                 select r).ToList().FirstOrDefault();
                    var rangeType = (from r in find
                                     where (val >= r.IndexedRange1 && val < r.IndexedRange2)
                                     select r).FirstOrDefault();
                    if (rangeType != null)
                    {
                        range = (MeasurementRanges)rangeType.MeasurementRangeTypeId;

                    }
                    else
                    {
                        range = MeasurementRanges.OutOfRange;
                    }
                }

            }

            return range;

        }
        public string GetMeasurementBSARange(double val, string mesCode, string catCode, string gender)
        {
            string range = string.Empty;
            var lst = context.MeasurementBSARange.ToList();
            if (val != 0 && !string.IsNullOrEmpty(mesCode) && !string.IsNullOrEmpty(catCode))
            {
                var rangeTypes = (from r in lst
                                  where
                                  (r.measurementCode == mesCode && r.categoryCode == catCode)
                                  //(val >= r.IndexedRange1 && val < r.IndexedRange2)
                                  && r.gender == gender &&
                                  r.MeasurementRangeTypeId == 1
                                  select r).ToList();

                if (rangeTypes != null && rangeTypes.Count > 0)
                {
                    range = "(" + rangeTypes[0].IndexedRange1 + "-" + rangeTypes[0].IndexedRange2 + ")";
                }
                //else
                //{
                //    range = "NO RANGE FOUND";
                //}
            }
            return range;
        }

        #endregion

        #region Report Phrases

        public string GetReportPhraseByID(int rpid)
        {
            string retStr = string.Empty;
            var rp = context.ReportPhrase.Where(r => r.Id == rpid).FirstOrDefault();
            if (rp != null)
            {
                retStr = rp.name;
            }
            return retStr;
        }

        public void AddPhraseSubItem(AddPhraseSubItem_VM vm, int userId, string ipAddress)
        {
            var newItem = new ReportPhrase()
            {
                //Id = maxID,
                name = vm.Name,
                root = vm.RootPhraseID,
                parent = vm.ParentID,
                value = vm.Value,
                dr = vm.DrID
                //ordernumber = 999999
            };

            //adding report phrase
            context.ReportPhrase.Add(newItem);
            context.SaveChanges(userId, ipAddress);

            if (vm.ItemType == Cerebrum30.Areas.Measurements.Models.ViewModels.ItemType.Category)
            {
                //adding child element if type is "category" and not "item"
                context.ReportPhrase.Add(new ReportPhrase()
                {
                    name = string.Empty,
                    root = vm.RootPhraseID,
                    parent = newItem.Id,
                    value = string.Empty,
                    dr = vm.DrID
                    //ordernumber = 999999
                });
            }
            context.SaveChanges(userId, ipAddress);
        }

        public void SaveReportPhraseSetting(ReportPhraseSetting_VM model, int userId, string ipAddress)
        {
            model.PatientID = 0;
            //model.TestID = model.TestID;
            //if (model.ReportPhraseSettingMode == ReportPhraseSetting_SaveMode.TestPractice)
            //{
            //    model.PracticeID = model.PracticeID;
            //    model.UserID = 0;
            //}
            //else
            //if (model.ReportPhraseSettingMode == ReportPhraseSetting_SaveMode.Practice)
            //{
            //    model.PracticeID = model.PracticeID;
            //    model.UserID = 0;
            //}
            //else
            //    if (model.ReportPhraseSettingMode == ReportPhraseSetting_SaveMode.User)
            //{
            //    model.PracticeID = 0;
            //    model.UserID = model.UserID;
            //}

            //deleting already existing entries
            var lstToDelByUser = context.ReportPhrase_Custom.Where(p => p.UserID == model.DocID && p.TestID == model.TestID).OrderBy(m => m.Rank).ToList();
            //var lstToDelByPrac = context.ReportPhrase_Custom.Where(p => p.PracticeID > 0  && p.PracticeID == model.PracticeID && p.TestID == model.TestID).OrderBy(m => m.Rank).ToList();
            //var lstToDelByPracTest = context.ReportPhrase_Custom.Where(p => p.PracticeID > 0 &&  p.PracticeID == model.PracticeID && p.TestID == model.TestID).OrderBy(m => m.Rank).ToList();
            context.ReportPhrase_Custom.RemoveRange(lstToDelByUser);
            //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            context.SaveChanges(userId, ipAddress);

            //context.ReportPhrase_Custom.RemoveRange(lstToDelByPrac);
            //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");

            //context.ReportPhrase_Custom.RemoveRange(lstToDelByPracTest);

            List<ReportPhrase_Custom> lst = new List<ReportPhrase_Custom>();

            model.ReportPhraseSettings = model.ReportPhraseSettings.OrderBy(m => m.Rank).ToList();

            foreach (var setting in model.ReportPhraseSettings)
            {
                lst.Add(new ReportPhrase_Custom()
                {
                    ReportPhraseId = setting.ReportPhraseID,
                    Text = setting.Text,
                    Visible = setting.Visible,
                    Rank = setting.Rank,
                    PatientRecordId = model.PatientID,
                    PracticeID = model.PracticeID,
                    UserID = model.DocID,
                    TestID = model.TestID,
                });
            }

            context.ReportPhrase_Custom.AddRange(lst);
            context.SaveChanges(userId, ipAddress);
        }

        public ReportPhrase_Val GetReportPhraseCustomValue(int reportPhraseID, int userID)
        {
            ReportPhrase_Val vm = new ReportPhrase_Val();

            vm.ReportPhraseID = reportPhraseID;
            vm.UserID = userID;
            vm.Text = string.Empty;
            vm.OriginalText = string.Empty;

            var entryOrg = context.ReportPhrase.Where(x => x.Id == reportPhraseID).FirstOrDefault();
            if (entryOrg != null)
            {
                vm.OriginalText = entryOrg.name;
                vm.Value = entryOrg.value;
            }

            var entry = context.ReportPhraseByDoctor
                .Where(x => x.ReportPhraseID == reportPhraseID && x.DrID == userID).FirstOrDefault();

            if (entry != null)
            {
                vm.Text = entry.Text;
            }

            return vm;
        }

        public void SaveReportPhraseCustomValue(ReportPhrase_Val vm, int userId, string ipAddress)
        {
            var entry = context.ReportPhraseByDoctor
                .Where(x => x.ReportPhraseID == vm.ReportPhraseID && x.DrID == vm.UserID).FirstOrDefault();

            if (entry != null)
            {
                context.ReportPhraseByDoctor.Remove(entry);
                context.SaveChanges(userId, ipAddress);
            }

            context.ReportPhraseByDoctor.Add(new ReportPhraseByDoctor()
            {
                DrID = vm.UserID,
                Text = vm.Text,
                ReportPhraseID = vm.ReportPhraseID
            });
            context.SaveChanges(userId, ipAddress);
        }

        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetReportPhrases23(int groupID, int userID)
        {
            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();
            var phrases = context.ReportPhrase.ToList();

            var topLevelPhrases =

                (from p in phrases
                 join t in context.TestGroupDetail on p.Id equals t.ReportPhraseId
                 where p.parent == -1 && p.status == 0 && t.TestGroupId == groupID
                 orderby p.ordernumber
                 select p).ToList();

            foreach (var item in topLevelPhrases)
            {
                Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase newRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.name
                };

                var secLst = phrases.Where(x =>
                                            x.parent == item.Id &&
                                            x.root == item.Id &&
                                            x.status == 0)
                                            .OrderBy(x => x.ordernumber).ToList();

                if (secLst.Count > 0)
                {
                    foreach (var subItem in secLst)
                    {
                        var secLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                        {
                            Id = subItem.Id,
                            Name = subItem.name,
                            CustomText = string.Empty
                        };

                        var thirdLst = phrases.Where(x =>
                                                        x.parent == subItem.Id &&
                                                        x.status == 0)
                                                        .OrderBy(x => x.ordernumber).ToList();
                        foreach (var thirdLevelItem in thirdLst)
                        {
                            var thirdLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                            {
                                Id = thirdLevelItem.Id,
                                Name = thirdLevelItem.name,
                                CustomText = string.Empty

                            };

                            var fourthLst = phrases.Where(x =>
                                                             x.parent == thirdLevelItem.Id &&
                                                             x.status == 0)
                                                             .OrderBy(x => x.ordernumber).ToList();

                            foreach (var fourthLevelItem in fourthLst)
                            {
                                var fourthLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                                {
                                    Id = fourthLevelItem.Id,
                                    Name = fourthLevelItem.name,
                                    CustomText = string.Empty
                                };
                                thirdLevelRP.Phrases.Add(fourthLevelRP);
                            }

                            secLevelRP.Phrases.Add(thirdLevelRP);
                        }

                        newRP.Phrases.Add(secLevelRP);
                    }

                    lst.Add(newRP);
                }
            }
            var phrasesWithCustomText = context.ReportPhraseByDoctor.Where(x => x.DrID == userID).ToList();

            foreach (var item in lst)
            {
                foreach (var level1 in item.Phrases)
                {
                    level1.CustomText = GetCustomPhrase(level1.Id, phrasesWithCustomText);
                    foreach (var level2 in level1.Phrases)
                    {
                        level2.CustomText = GetCustomPhrase(level2.Id, phrasesWithCustomText);
                        foreach (var level3 in level2.Phrases)
                        {
                            level3.CustomText = GetCustomPhrase(level3.Id, phrasesWithCustomText);
                        }
                    }
                }
            }
            return lst;
        }


        [Obsolete("Moved to Cerebrum30.Services.WorkSheet.WorkSheetClients.GetReportPhrasesByGroupAsync")]
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetReportPhrasesByGroup(int practiceId, int testID, int userID)
        {
            int groupID = (new UserRepository()).GetGroupByTestID(testID);

            return GetReportPhrasesByGroup(practiceId, testID, userID, groupID);
        }


        public class ReportPhrase_VM_Extend : Cerebrum.ViewModels.Measurements.ReportPhrase_VM
        {
            internal int? parent { get; set; }
            internal int? root { get; set; }
            internal int? dr { get; set; }
            internal string name { get; set; }
            internal string grp { get; set; }
            internal int? GroupID { get; set; }
            internal string value { get; set; }
        }


        public class ReportPhraseExtend : ReportPhrase
        {
            public bool Visible { get; set; }
        }

        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetReportPhrasesByGroup(int testID, int userID, int groupID, List<ReportPhraseExtend> topLevelPhrases, List<ReportPhrase_VM_Extend> phrases)
        {
            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();
            var phrasesWithCustomText = context.ReportPhraseByDoctor.Where(x => x.DrID == userID).ToList();

            foreach (var item in topLevelPhrases)
            {
                Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase newRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.name,
                    field = item.field,
                    type = item.type,
                    ordernumber = item.ordernumber,
                    // TODO: Map Visible property if available
                    // Visible = item.Visible
                };

                var secLst = phrases.Where(x =>
                                                 x.parent == item.Id &&
                                                 x.root == item.Id &&
                                                 //x.status == 0 &&
                                                 (!x.dr.HasValue || x.dr == userID)

                                            )
                                            .OrderBy(x => x.name.Trim())
                                            .ToList();
                if (groupID == 4)
                {
                    secLst = secLst.Where(xp =>
                                                 (string.IsNullOrEmpty(xp.grp) || xp.grp == ",0,"
                                                 ||
                                                 //hardcoded for ECG
                                                 (xp.GroupID == 4 ? xp.grp.Contains(string.Format(",{0},", groupID.ToString())) : 1 == 2))
                                            )
                                            .OrderBy(x => x.name.Trim())
                                            .ToList();
                }

                if (secLst.Count > 0)
                {
                    foreach (var subItem in secLst)
                    {
                        var secLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                        {
                            Id = subItem.Id,
                            Name = subItem.Name,
                            Value = subItem.Value,
                            CustomText = subItem.CustomText,
                            type = subItem.type,
                            field = subItem.field
                        };
                        var thirdLst = phrases.Where(x =>
                                                        x.parent == subItem.Id
                                                        //&& x.status == 0
                                                        )
                                                        .OrderBy(x => x.name.Trim())
                                                        .ToList();

                        foreach (var thirdLevelItem in thirdLst)
                        {
                            var thirdLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                            {
                                Id = thirdLevelItem.Id,
                                Name = thirdLevelItem.Name,
                                Value = thirdLevelItem.Value,
                                CustomText = thirdLevelItem.CustomText,
                                type = thirdLevelItem.type,
                                field = thirdLevelItem.field
                            };
                            var fourthLst = phrases.Where(x =>
                                                             x.parent == thirdLevelItem.Id
                                                             //&& x.status == 0
                                                             )
                                                             .OrderBy(x => x.name.Trim())
                                                             .ToList();

                            foreach (var fourthLevelItem in fourthLst)
                            {
                                fourthLevelItem.CustomText = GetCustomPhrase(fourthLevelItem.Id, phrasesWithCustomText);

                                // Convert ReportPhrase_VM_Extend to local ReportPhrase_VM
                                var fourthLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                                {
                                    Id = fourthLevelItem.Id,
                                    Name = fourthLevelItem.Name,
                                    Value = fourthLevelItem.Value,
                                    CustomText = fourthLevelItem.CustomText,
                                    type = fourthLevelItem.type,
                                    field = fourthLevelItem.field
                                };

                                thirdLevelRP.Phrases.Add(fourthLevelRP);
                            }
                            thirdLevelRP.CustomText = GetCustomPhrase(thirdLevelRP.Id, phrasesWithCustomText);
                            secLevelRP.Phrases.Add(thirdLevelRP);
                        }
                        secLevelRP.CustomText = GetCustomPhrase(secLevelRP.Id, phrasesWithCustomText);
                        newRP.Phrases.Add(secLevelRP);
                    }
                }
                lst.Add(newRP);
            }

            return lst;
        }

        /*
        internal Tuple<List<ReportPhrase>, List<ReportPhrase_VM_Extend>> GetReportPhrasesByGroup_Query(int testID, int userID, int groupID)
        {
            var topLevelPhrases =
              (from p in context.ReportPhrase
               where p.parent == -1 &&
                     p.status == 0 &&
                     p.GroupID == groupID
               orderby p.ordernumber
               select p).ToList();

            return GetReportPhrasesByGroup_Query(topLevelPhrases, testID, userID, groupID);
        }
        */

        internal Tuple<List<ReportPhraseExtend>, List<ReportPhrase_VM_Extend>> GetReportPhrasesByGroup_Query(List<ReportPhrase> topLevelPhrases, int practiceId, int testID, int userID, int groupID)
        {
            //var topLevelPhrases =
            //              (from p in context.ReportPhrase
            //               where p.parent == -1 &&
            //                     p.status == 0 &&
            //                     p.GroupID == groupID
            //               orderby p.ordernumber
            //               select p).ToList();

            var rootIDs = topLevelPhrases.Select(x => x.Id).ToList();
            //var phrases = context.ReportPhrase
            //    .Where(x =>
            //        x.status == 0
            //        && (x.dr == null || x.dr == userID)
            //        && (x.root == null || rootIDs.Contains(x.root.Value))
            var phrases = (from x in context.ReportPhrase
                           from p in context.ReportPhraseByPractice
                           .Where(ppp => ppp.ReportPhraseID == x.Id && ppp.PracticeID == practiceId)
                           .DefaultIfEmpty()
                               //on x.Id equals p.ReportPhraseID into ppp
                               //from p in ppp.DefaultIfEmpty()
                           where x.status == 0
                            && (x.dr == null || x.dr == userID)
                            && (x.root == null || rootIDs.Contains(x.root.Value))
                           select new { ReportPhrase = x, Visible = p == null ? true : p.Visible }
                ).AsNoTracking().Select(subItem => new ReportPhrase_VM_Extend
                {
                    Id = subItem.ReportPhrase.Id,
                    Name = subItem.ReportPhrase.name,
                    CustomText = string.Empty,
                    Value = subItem.ReportPhrase.value,
                    field = subItem.ReportPhrase.field,
                    type = subItem.ReportPhrase.type,
                    dr = subItem.ReportPhrase.dr,
                    parent = subItem.ReportPhrase.parent,
                    root = subItem.ReportPhrase.root,
                    name = subItem.ReportPhrase.name,
                    grp = subItem.ReportPhrase.grp,
                    GroupID = subItem.ReportPhrase.GroupID,
                    value = subItem.ReportPhrase.value,
                    Visible = subItem.Visible
                }).ToList();
            var topLevelPhrasesVisible = (from ppp in context.ReportPhraseByPractice
                                          where rootIDs.Contains(ppp.Id)
                                            && ppp.PracticeID == practiceId
                                          select ppp).AsNoTracking().ToList();

            var result1 = (from p in topLevelPhrases
                           join ppp in topLevelPhrasesVisible on p.Id equals ppp.ReportPhraseID into final
                           from ppp in final.DefaultIfEmpty()
                           select new ReportPhraseExtend
                           {
                               Id = p.Id,
                               Index = p.Index,
                               OldId = p.OldId,
                               name = p.name,
                               value = p.value,
                               parent = p.parent,
                               root = p.root,
                               ordernumber = p.ordernumber,
                               TestID = p.TestID,
                               OLDTESTID = p.OLDTESTID,
                               GroupID = p.GroupID,
                               type = p.type,
                               field = p.field,
                               status = p.status,
                               grp = p.grp,
                               dr = p.dr,
                               ReportPhraseSavedText = p.ReportPhraseSavedText,
                               ReportPhraseSavedValue = p.ReportPhraseSavedValue,
                               ReportPhraseByDoctor = p.ReportPhraseByDoctor,
                               ReportPhraseByPractice = p.ReportPhraseByPractice,
                               Visible = ppp == null ? true : ppp.Visible
                           }).ToList();

            return new Tuple<List<ReportPhraseExtend>, List<ReportPhrase_VM_Extend>>(result1, phrases);
        }


        internal Tuple<List<ReportPhrase>, List<ReportPhrase>> GetReportPhrasesByGroup_Query_old(int testID, int userID, int groupID)
        {
            var topLevelPhrases = (from p in context.ReportPhrase
                                   where p.parent == -1 &&
                                         p.status == 0 &&
                                         p.GroupID == groupID
                                   orderby p.ordernumber
                                   select p).ToList();

            var rootIDs = topLevelPhrases.Select(x => x.Id).ToList();

            var phrases = context.ReportPhrase.Where(x => x.status == 0 && (x.dr == null || x.dr == userID) && (x.root == null || rootIDs.Contains(x.root.Value))).ToList();

            return new Tuple<List<ReportPhrase>, List<ReportPhrase>>(topLevelPhrases, phrases);
        }

        public List<ReportPhrase> GetReportPhrases(int testID, int userID, int groupID)
        {
            var topLevelPhrases = (from p in context.ReportPhrase
                                   where p.parent == -1 &&
                                         p.status == 0 &&
                                         p.GroupID == groupID
                                   orderby p.ordernumber
                                   select p).ToList();
            return topLevelPhrases;
        }

        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetReportPhrasesByGroup(int practiceId, int testID, int userID, int groupID)
        {
            return GetReportPhrasesByGroup(GetReportPhrases(testID, userID, groupID), practiceId, testID, userID, groupID);
        }

        [Obsolete("Moved to WS microservices")]
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetReportPhrasesByGroup(List<ReportPhrase> rp, int practiceId, int testID, int userID, int groupID)
        {
            var lists = GetReportPhrasesByGroup_Query(rp, practiceId, testID, userID, groupID);
            var topLevelPhrases = lists.Item1;
            var phrases = lists.Item2;

            return GetReportPhrasesByGroup(testID, userID, groupID, topLevelPhrases, phrases);
        }

        [Obsolete("This method is for unit test purpose, can be deleted later")]
        public List<ReportPhrase_VM_Extend> GetReportPhrasesByGroup_phrases(List<ReportPhrase> rp, int practiceId, int testID, int userID, int groupID)
        {
            var lists = GetReportPhrasesByGroup_Query(rp, practiceId, testID, userID, groupID);
            var topLevelPhrases = lists.Item1;
            var phrases = lists.Item2;
            return lists.Item2;
        }

        /*
        public List<TopLevelReportPhrase> GetReportPhrasesByGroup_old(int testID, int userID, int groupID, List<ReportPhrase> topLevelPhrases, List<ReportPhrase> phrases)
        {
            List<TopLevelReportPhrase> lst = new List<TopLevelReportPhrase>();

            //var topLevelPhrases =
            //  (from p in context.ReportPhrase
            //   where p.parent == -1 &&
            //         p.status == 0 &&
            //         p.GroupID == groupID
            //   orderby p.ordernumber
            //   select p).ToList();

            //var rootIDs = topLevelPhrases.Select(x => x.Id).ToList();

            //var phrases = context.ReportPhrase.Where(x => x.status == 0 && (x.dr == null || x.dr == userID) && (x.root == null || rootIDs.Contains(x.root.Value))).ToList();

            foreach (var item in topLevelPhrases)
            {
                TopLevelReportPhrase newRP = new TopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.name,
                    field = item.field,
                    type = item.type,
                    ordernumber = item.ordernumber,
                    Visible = item.Visible
                };

                var secLst = phrases.Where(x =>
                                                 x.parent == item.Id &&
                                                 x.root == item.Id &&
                                                 x.status == 0 &&
                                                 (!x.dr.HasValue || x.dr == userID)

                                            )
                                            .OrderBy(x => x.name).ToList();
                if (groupID == 4)
                {
                    secLst = secLst.Where(xp =>
                                                 (string.IsNullOrEmpty(xp.grp) || xp.grp == ",0,"
                                                 ||
                                                 //hardcoded for ECG
                                                 (xp.GroupID == 4 ? xp.grp.Contains(string.Format(",{0},", groupID.ToString())) : 1 == 2))
                                            )
                                            .OrderBy(x => x.name).ToList();
                }

                if (secLst.Count > 0)
                {
                    foreach (var subItem in secLst)
                    {
                        var secLevelRP = new Cerebrum.ViewModels.Measurements.ReportPhrase_VM()
                        {
                            Id = subItem.Id,
                            Name = subItem.name,
                            CustomText = string.Empty,
                            Value = subItem.value,
                            field = subItem.field,
                            type = subItem.type,
                            Visible = subItem.Visible
                        };

                        var thirdLst = phrases.Where(x =>
                                                        x.parent == subItem.Id &&
                                                        x.status == 0)
                                                        .OrderBy(x => x.name).ToList();

                        foreach (var thirdLevelItem in thirdLst)
                        {
                            var thirdLevelRP = new Cerebrum.ViewModels.Measurements.ReportPhrase_VM()
                            {
                                Id = thirdLevelItem.Id,
                                Name = thirdLevelItem.name,
                                CustomText = string.Empty,
                                Value = thirdLevelItem.value,
                                field = thirdLevelItem.field,
                                type = thirdLevelItem.type,
                                Visible = thirdLevelItem.Visible
                            };

                            var fourthLst = phrases.Where(x =>
                                                             x.parent == thirdLevelItem.Id &&
                                                             x.status == 0)
                                                             .OrderBy(x => x.name).ToList();

                            foreach (var fourthLevelItem in fourthLst)
                            {
                                var fourthLevelRP = new Cerebrum.ViewModels.Measurements.ReportPhrase_VM()
                                {
                                    Id = fourthLevelItem.Id,
                                    Name = fourthLevelItem.name,
                                    CustomText = string.Empty,
                                    Value = fourthLevelItem.value,
                                    field = fourthLevelItem.field,
                                    type = fourthLevelItem.type,
                                    Visible = fourthLevelItem.Visible
                                };
                                thirdLevelRP.Phrases.Add(fourthLevelRP);
                            }
                            secLevelRP.Phrases.Add(thirdLevelRP);
                        }
                        newRP.Phrases.Add(secLevelRP);
                    }

                }
                lst.Add(newRP);
            }

            var phrasesWithCustomText = context.ReportPhraseByDoctor.Where(x => x.DrID == userID).ToList();
            foreach (var item in lst)
            {
                foreach (var level1 in item.Phrases)
                {
                    level1.CustomText = GetCustomPhrase(level1.Id, phrasesWithCustomText);
                    foreach (var level2 in level1.Phrases)
                    {
                        level2.CustomText = GetCustomPhrase(level2.Id, phrasesWithCustomText);
                        foreach (var level3 in level2.Phrases)
                        {
                            level3.CustomText = GetCustomPhrase(level3.Id, phrasesWithCustomText);
                        }
                    }
                }
            }

            return lst;
        }*/

        /*
        public List<TopLevelReportPhrase> GetReportPhrasesByGroup_Old(int testID, int userID)
        {
            List<TopLevelReportPhrase> lst = new List<TopLevelReportPhrase>();
            var phrases = context.ReportPhrase.ToList();

            int groupID = (new UserRepository()).GetGroupByTestID(testID);

            var topLevelPhrases =

              (from p in phrases
               where p.parent == -1 &&
                     p.status == 0 &&
                     p.GroupID == groupID
               orderby p.ordernumber
               select p).ToList();

            foreach (var item in topLevelPhrases)
            {
                TopLevelReportPhrase newRP = new TopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.name,
                    field=item.field,
                    type=item.type,
                    ordernumber = item.ordernumber,
                    Visible = item.Visible
                };

                var secLst = phrases.Where(x =>
                                                 x.parent == item.Id &&
                                                 x.root == item.Id &&
                                                 x.status == 0 &&
                                                 (!x.dr.HasValue || x.dr == userID)

                                            )
                                            .OrderBy(x => x.name).ToList();
                if (groupID == 4)
                {
                    secLst = secLst.Where(xp =>
                                                 (string.IsNullOrEmpty(xp.grp) || xp.grp == ",0,"
                                                 ||
                                                 //hardcoded for ECG
                                                 (xp.GroupID == 4 ? xp.grp.Contains(string.Format(",{0},", groupID.ToString())) : 1 == 2))
                                            )
                                            .OrderBy(x => x.name).ToList();
                }

                if (secLst.Count > 0)
                {
                    foreach (var subItem in secLst)
                    {
                        var secLevelRP = new Cerebrum.ViewModels.Measurements.ReportPhrase_VM()
                        {
                            Id = subItem.Id,
                            Name = subItem.name,
                            CustomText = string.Empty,
                            Value = subItem.value,
                            field=subItem.field,
                            type=subItem.type,
                            Visible = subItem.Visible
                        };

                        var thirdLst = phrases.Where(x =>
                                                        x.parent == subItem.Id &&
                                                        x.status == 0)
                                                        .OrderBy(x => x.name).ToList();

                        foreach (var thirdLevelItem in thirdLst)
                        {
                            var thirdLevelRP = new Cerebrum.ViewModels.Measurements.ReportPhrase_VM()
                            {
                                Id = thirdLevelItem.Id,
                                Name = thirdLevelItem.name,
                                CustomText = string.Empty,
                                Value = thirdLevelItem.value,
                                field = thirdLevelItem.field,
                                type = thirdLevelItem.type,
                                Visible = thirdLevelItem.Visible
                            };

                            var fourthLst = phrases.Where(x =>
                                                             x.parent == thirdLevelItem.Id &&
                                                             x.status == 0)
                                                             .OrderBy(x => x.name).ToList();

                            foreach (var fourthLevelItem in fourthLst)
                            {
                                var fourthLevelRP = new Cerebrum.ViewModels.Measurements.ReportPhrase_VM()
                                {
                                    Id = fourthLevelItem.Id,
                                    Name = fourthLevelItem.name,
                                    CustomText = string.Empty,
                                    Value = fourthLevelItem.value,
                                    field = fourthLevelItem.field,
                                    type = fourthLevelItem.type,
                                    Visible = fourthLevelItem.Visible
                                };
                                thirdLevelRP.Phrases.Add(fourthLevelRP);
                            }
                            secLevelRP.Phrases.Add(thirdLevelRP);
                        }
                        newRP.Phrases.Add(secLevelRP);
                    }

                }
                lst.Add(newRP);
            }

            var phrasesWithCustomText = context.ReportPhraseByDoctor.Where(x => x.DrID == userID).ToList();
            foreach (var item in lst)
            {
                foreach (var level1 in item.Phrases)
                {
                    level1.CustomText = GetCustomPhrase(level1.Id, phrasesWithCustomText);
                    foreach (var level2 in level1.Phrases)
                    {
                        level2.CustomText = GetCustomPhrase(level2.Id, phrasesWithCustomText);
                        foreach (var level3 in level2.Phrases)
                        {
                            level3.CustomText = GetCustomPhrase(level3.Id, phrasesWithCustomText);
                        }
                    }
                }
            }

            return lst;
        }
        */
        public List<TestGroupDetailVM> GetGroupDetails(int testID)
        {
            int groupID = 0;

            var group = context.TestGroup.Where(tg => tg.TestId == testID).FirstOrDefault();

            if (group != null)
            {
                groupID = group.GroupId;
            }

            var lst = context
                        .TestGroupDetail
                        .Where(tgd => tgd.TestGroupId == groupID)
                        .Select(x =>
                        new TestGroupDetailVM()
                        {
                            Id = x.Id,
                            Visible = x.Visible,
                            TestGroupId = x.TestGroupId,
                            ReportPhraseId = x.ReportPhraseId ?? 0,
                            MeasurementCategoryId = x.MeasurementCategoryId ?? 0,

                        }).ToList();

            return lst;
        }

        [Obsolete("Moved to WS microservices")]
        public WorkSheetVM RemoveIllegalChars(WorkSheetVM vm)
        {
            vm.ReportPhrases.ForEach(r =>
            {
                r.Phrases.ForEach(rr =>
                {
                    rr.Name = string.IsNullOrEmpty(rr.Name) ? string.Empty : rr.Name.Trim();
                    rr.Value = string.IsNullOrEmpty(rr.Value) ? string.Empty : rr.Value.Trim();
                    rr.CustomText = string.IsNullOrEmpty(rr.CustomText) ? string.Empty : rr.CustomText.Trim();
                });

            });

            return vm;
        }

        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetReportPhrases()
        {
            //select id, name  from vpop where status=0 and spec=0 and parent=-1  and (opt is null ) and (dr is null or dr=7) and (grp is null or grp like '%,0,%')  order by ordernumber
            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();
            var phrases = context.ReportPhrase.ToList();
            //phrases = phrases.Where(x => !x.DrID.HasValue);
            var topLevelPhrases = phrases.Where(r => r.parent == -1 && r.TestID == 1 && r.status == 0).OrderBy(m => m.ordernumber).ToList();

            foreach (var item in topLevelPhrases)
            {
                Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase newRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.name
                };

                var secLst = phrases.Where(x =>
                                            x.parent == item.Id &&
                                            x.root == item.Id &&
                                            x.status == 0)
                                            .OrderBy(x => x.ordernumber).ToList();

                if (secLst.Count > 0)
                {
                    foreach (var subItem in secLst)
                    {
                        var secLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                        {
                            Id = subItem.Id,
                            Name = subItem.name,
                            CustomText = string.Empty
                        };

                        var thirdLst = phrases.Where(x =>
                                                        x.parent == subItem.Id &&
                                                        x.status == 0)
                                                        .OrderBy(x => x.ordernumber).ToList();
                        foreach (var thirdLevelItem in thirdLst)
                        {
                            var thirdLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                            {
                                Id = thirdLevelItem.Id,
                                Name = thirdLevelItem.name,
                                CustomText = string.Empty

                            };

                            var fourthLst = phrases.Where(x =>
                                                             x.parent == thirdLevelItem.Id &&
                                                             x.status == 0)
                                                             .OrderBy(x => x.ordernumber).ToList();

                            foreach (var fourthLevelItem in fourthLst)
                            {
                                var fourthLevelRP = new Cerebrum30.Areas.Measurements.Models.ViewModels.ReportPhrase_VM()
                                {
                                    Id = fourthLevelItem.Id,
                                    Name = fourthLevelItem.name,
                                    CustomText = string.Empty
                                };
                                thirdLevelRP.Phrases.Add(fourthLevelRP);
                            }

                            secLevelRP.Phrases.Add(thirdLevelRP);
                        }

                        newRP.Phrases.Add(secLevelRP);
                    }

                    lst.Add(newRP);
                }
            }

            return lst;
        }

        [Obsolete("Moved to WS microservices")]
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetCustomReportPhrases(List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst, int userID, int practiceID, int testID)
        {
            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> resLst = null; // new List<TopLevelReportPhrase>();

            //search by userID 
            var lstCustom = context.ReportPhrase_Custom.Where(p => p.UserID == userID && p.TestID == testID).OrderBy(m => m.Rank).ToList();

            if (lstCustom != null && lstCustom.Count > 0)
            {
                List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> newLst = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();
                foreach (var item in lst)
                {
                    var custom = lstCustom.Where(t => t.ReportPhraseId == item.Id).FirstOrDefault();
                    // when not found in custom list - use it as is
                    if (custom == null)
                    {
                        newLst.Add(item);
                    }
                    else
                    {
                        if (custom.Visible)
                        {
                            Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase newEntry = new Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase()
                            {
                                Id = custom.ReportPhraseId,
                                Name = custom.Text,
                                ordernumber = custom.Rank
                            };
                            newEntry.Phrases = item.Phrases;
                            newEntry.field = item.field;
                            newEntry.type = item.type;
                            newEntry.Value = item.Value;
                            newLst.Add(newEntry);
                        }
                    }
                }
                resLst = newLst.OrderBy(x => x.ordernumber).ToList(); //.Sort((x,y) => (x.ordernumber < y.ordernumber));
            }
            else
            {
                resLst = lst;
            }

            return resLst;
        }

        [Obsolete("Moved to WS microservices")]
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> LoadReportPhraseSavedValues(

                                       List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst,

                                      int appointmentID,
                                      int testID,
                                      int AppointmentTestLogId
                                      )
        {
            var savedValues = context.ReportPhraseSavedText.Where(s =>

                                               s.AppointmentID == appointmentID &&
                                               s.TestID == testID &&
                                               s.AppointmentTestLogID == AppointmentTestLogId
                       ).ToList();
            if (savedValues != null && savedValues.Count > 0)
            {
                lst.ForEach(s =>
                {
                    var entry = savedValues.Where(v => v.TopLevelReportPhraseID == s.Id).FirstOrDefault();
                    s.Value = entry != null ? entry.Value : string.Empty;
                });
            }

            return lst;
        }
        private string GetCustomPhrase(int reportPhraseID, List<ReportPhraseByDoctor> lst)
        {
            string retVal = string.Empty;
            var entry = lst.Where(x => x.ReportPhraseID == reportPhraseID).FirstOrDefault();
            if (entry != null)
            {
                retVal = entry.Text;
            }
            return retVal;
        }

        public List<ReportPhrase> GetAllReportPhrases()
        {
            List<ReportPhrase> lst = new List<ReportPhrase>();

            var phrases = context.ReportPhrase.ToList();

            phrases.ToList().ForEach(c => lst.Add(c));

            return lst;
        }

        public void SaveReportPhrases(List<ReportPhraseSavedText> model, int userId, string ipAddress)
        {
            context.ReportPhraseSavedText.AddRange(model);

            context.SaveChanges(userId, ipAddress);
        }

        public List<ReportPhraseSavedText> GetReportPhraseSavedValues(int appointmentID, int testID, int appointmentTestLogID)
        {
            var lst = context.ReportPhraseSavedText.Where(r => r.AppointmentID == appointmentID && r.TestID == testID && r.AppointmentTestLogID == appointmentTestLogID).ToList();

            return lst;
        }

        public List<ReportPhraseHistoryData> GetReportPhraseHistory(int reportPhraseID, int appointmentID, int testID)
        {
            List<ReportPhraseHistoryData> retData = new List<ReportPhraseHistoryData>();

            List<int> tmp = new List<int>();

            var logLst = (from s in context.ReportPhraseSavedValue
                          join r in context.ReportPhrase on s.ReportPhraseID equals r.Id
                          join u in context.Users on s.UserID equals u.UserID
                          where
                            s.TopLevelReportPhraseID == reportPhraseID &&
                            s.TestID == testID &&
                            s.AppointmentID == appointmentID
                          orderby s.Id descending
                          select
                          new ReportPhraseHistoryData
                          {
                              ID = s.Id,
                              AppointmentID = s.AppointmentID,
                              TestID = s.TestID,
                              Value = r.name,
                              Name = u.LastName + " " + u.FirstName,
                              LogDate = s.DateEntered

                          }).ToList();


            retData = logLst;

            return retData;

        }

        public List<ReportPhraseNormalVM> GetReportPhraseNormals()
        {
            List<ReportPhraseNormalVM> lst = new List<ReportPhraseNormalVM>();

            var q = (from n in context.ReportPhraseNormal
                     join r in context.ReportPhrase on n.NormalReportPhraseID equals r.Id
                     select new ReportPhraseNormalVM()
                     {
                         ReportPhraseID = n.ReportPhraseID,
                         NormalReportPhraseID = n.NormalReportPhraseID,
                         NormalReportPhraseName = r.name

                     }).ToList();
            lst = q;

            return lst;
        }
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> LoadReportPhraseNormals(List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst, int testID)
        {
            List<ReportPhraseNormalVM> lstNormal = new List<ReportPhraseNormalVM>();

            var phrases = context.ReportPhrase.ToList();
            var normals = context.ReportPhraseNormal.Where(r => r.TestID == testID).ToList();

            //get normal list and name of normal text
            normals.ForEach(n =>
            {
                var entry = phrases.Where(p => p.Id == n.NormalReportPhraseID).FirstOrDefault();
                if (entry != null)
                {
                    lstNormal.Add(new ReportPhraseNormalVM()
                    {
                        ReportPhraseID = n.ReportPhraseID,
                        NormalReportPhraseID = n.NormalReportPhraseID,
                        NormalReportPhraseName = entry.name
                    });

                }
            });

            //replace each top level phrase with corresponding normal phrase
            lst.ForEach(x =>
            {
                var entry = lstNormal.Where(a => a.ReportPhraseID == x.Id).FirstOrDefault();
                if (entry != null)
                {
                    x.NormalPhraseID = entry.NormalReportPhraseID;
                    x.NormalPhraseText = entry.NormalReportPhraseName;
                }

            });
            return lst;
        }

        [Obsolete("Moved to WS microservices")]
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> LoadReportPhraseNormals(List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst)
        {
            List<ReportPhraseNormalVM> lstNormal = new List<ReportPhraseNormalVM>();

            lst.ForEach(s =>
            {
                var phrase = s.Phrases.Where(p => p.Name.ToLower() == "normal".ToLower()).ToList().FirstOrDefault();
                if (phrase != null)
                {
                    lstNormal.Add(new ReportPhraseNormalVM()
                    {
                        ReportPhraseID = s.Id,
                        NormalReportPhraseID = phrase.Id
                        // NormalReportPhraseName = !string.IsNullOrEmpty(phrase.CustomText) ? phrase.CustomText : (!string.IsNullOrEmpty(phrase.Value) ? phrase.Value : phrase.Name)
                    });
                }
            });

            //replace each top level phrase with corresponding normal phrase
            lst.ForEach(x =>
            {
                var entry = lstNormal.Where(a => a.ReportPhraseID == x.Id).FirstOrDefault();
                if (entry != null)
                {
                    x.NormalPhraseID = entry.NormalReportPhraseID;
                    // x.NormalPhraseText = entry.NormalReportPhraseName;
                }

            });
            return lst;
        }

        public string GetMaskedValue(string mask, double decValue)
        {
            string retVal = string.Empty;

            if (string.IsNullOrEmpty(mask))
            {
                retVal = String.Format(MEASURE_FORMAT_DECIMAL, decValue);
            }
            //else
            //if (mask == "0")
            //{
            //    retVal = Math.Truncate(decValue).ToString();
            //}
            else //if (mask == "0.00")
            {
                retVal = decValue.ToString(mask);
            }

            return retVal;
        }

        public string GetMaskedValue(string mask, string strVal)
        {
            double decValue = double.Parse(strVal);

            return GetMaskedValue(mask, decValue);

        }


        public void AddReportPhraseSavedValue(int appointmentID, int testID, int reportPhraseID, int topLevelReportPhraseID, int userID, string ipAddress)
        {
            context.ReportPhraseSavedValue.Add(
                                                    new ReportPhraseSavedValue()
                                                    {
                                                        AppointmentID = appointmentID,
                                                        TestID = testID,
                                                        ReportPhraseID = reportPhraseID,
                                                        TopLevelReportPhraseID = topLevelReportPhraseID,
                                                        UserID = userID,
                                                        DateEntered = DateTime.Now
                                                    }
                                            );
            context.SaveChanges(userID, ipAddress);
        }

        public void RemoveReportPhraseSavedValue(int appointmentID, int testID, int reportPhraseID, int topLevelReportPhraseID, int userId, string ipAddress)
        {
            var entry =

                context.ReportPhraseSavedValue.Where(r =>
                                                            r.AppointmentID == appointmentID &&
                                                            r.TestID == testID &&
                                                            r.ReportPhraseID == reportPhraseID &&
                                                            r.TopLevelReportPhraseID == topLevelReportPhraseID).FirstOrDefault();
            if (entry != null)
            {
                context.ReportPhraseSavedValue.Remove(entry);

                context.SaveChanges(userId, ipAddress);
            }
        }

        [Obsolete("Moved to WS microservices")]
        public int GetImpressionBoxID(List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst)
        {
            int retVal = 0;

            lst.ForEach(r =>
            {
                if (!string.IsNullOrWhiteSpace(r.Name) && r.Name.ToLower().Contains("impression"))
                {
                    retVal = r.Id;
                }
            });

            return retVal;
        }

        [Obsolete("Moved to WS microservices")]
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> LoadReportPhraseeMeasurementsScroll(List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst)
        {
            var dbLst = context.ReportPhraseMeasurmentCategoryScroll.ToList();

            lst.ForEach(r =>

            {
                var match = dbLst.Where(d => d.ReportPhraseID == r.Id).ToList().FirstOrDefault();
                if (match != null)
                {
                    r.AutoScrollCategoryID = match.MeasurementCategoryID;
                }
            });

            return lst;
        }


        public List<ReportPhraseMeasurmentCategoryVM> GetAllReportPhraseMeasurementCategories()
        {
            List<ReportPhraseMeasurmentCategoryVM> retLst = new List<ReportPhraseMeasurmentCategoryVM>();

            retLst = context.ReportPhraseMeasurmentCategory.
                    Select(x => new ReportPhraseMeasurmentCategoryVM()
                    {
                        ID = x.ID,
                        ReportPhraseID = x.ReportPhraseID,
                        MeasurementCategoryID = x.MeasurementCategoryID
                    }).ToList();

            return retLst;

        }

        public List<MeasurementRangeTextVM> GetMeasurementRangeTextList()
        {
            List<MeasurementRangeTextVM> retLst = new List<MeasurementRangeTextVM>();

            retLst = context.MeasurementRangeText.
                    Select(x => new MeasurementRangeTextVM()
                    {
                        ID = x.ID,
                        Text = x.Text,
                        MeasurementID = x.MeasurementId,
                        MeasurementRangeID = x.MeasurementRangeId
                    }).ToList();
            return retLst;

        }
        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> GetTopLevelReportPhrasesOnly(int testID)
        {
            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();

            var allPhrases = context.ReportPhrase.ToList();

            int groupID = (new UserRepository()).GetGroupByTestID(testID);

            allPhrases = (from p in allPhrases

                          where p.parent == -1 && p.status == 0 && p.GroupID == groupID
                          orderby p.ordernumber
                          select p).ToList();

            var topLevelPhrases = allPhrases.Where(p => p.status == 0 && p.parent == -1).OrderBy(m => m.ordernumber).ToList();

            foreach (var item in topLevelPhrases)
            {
                lst.Add(new Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase()
                {
                    Id = item.Id,
                    Name = item.name
                });
            }

            return lst;
        }

        public List<ReportPhraseSetting> GetReportPhraseSettings(int practiceId, int testID, int DocID)
        {
            List<ReportPhraseSetting> lst = new List<ReportPhraseSetting>();
            var lstTopLevel = GetReportPhrasesByGroup(practiceId, testID, DocID);
            //var lstTopLevel = GetTopLevelReportPhrasesOnly(testID);
            int counter = 1;

            foreach (var item in lstTopLevel)
            {
                lst.Add(new ReportPhraseSetting()
                {
                    Id = item.Id,
                    ReportPhraseID = item.Id,
                    Text = item.Name,
                    OriginalText = item.Name,
                    Visible = true,
                    Rank = counter++
                });
            }

            return lst;
        }

        public List<ReportPhraseSetting> GetReportPhraseSettingsCustom(List<ReportPhraseSetting> lst, int testID, int userID)
        {
            List<ReportPhraseSetting> lstNew = new List<ReportPhraseSetting>();

            var lstCustom = context.ReportPhrase_Custom.Where(p => p.TestID == testID && p.UserID == userID).OrderBy(m => m.Rank).ToList();

            if (lstCustom != null && lstCustom.Count > 0)
            {
                foreach (var item in lstCustom)
                {
                    var entry = lst.Where(x => x.ReportPhraseID == item.ReportPhraseId).FirstOrDefault();

                    if (entry != null)
                    {
                        string orgText = entry.OriginalText;

                        lstNew.Add(new ReportPhraseSetting()
                        {
                            Id = item.Id,
                            Text = item.Text,
                            OriginalText = orgText,
                            Visible = item.Visible,
                            Rank = item.Rank,
                            ReportPhraseID = item.ReportPhraseId,
                        });
                    }
                }
            }
            else
            {
                lstNew = lst;
            }

            return lstNew;
        }

        public ReportPhraseSetting_SaveMode GetReportPhraseSettingsMode(int practiceID, int userID, int testID)
        {
            ReportPhraseSetting_SaveMode saveMode = new ReportPhraseSetting_SaveMode();

            //var lstCustom = context. ReportPhrase_Custom.Where(p => p.PatientID == patientID).ToList();

            var lstCustom = context.ReportPhrase_Custom.Where(p => p.UserID > 0 && p.UserID == userID && p.TestID == testID).OrderBy(m => m.Rank).ToList();

            var lstCustomPractice = context.ReportPhrase_Custom.Where(p => p.PracticeID > 0 && p.PracticeID == practiceID && p.TestID == testID).OrderBy(m => m.Rank).ToList();

            //var lstCustomTestPractice = context.ReportPhrase_Custom.Where(p => p.PracticeID == practiceID && p.TestID == testID).OrderBy(m => m.Rank).ToList();
            if (lstCustom != null && lstCustom.Count > 0)
                saveMode = ReportPhraseSetting_SaveMode.User;
            else
            if (lstCustomPractice != null && lstCustomPractice.Count > 0)
                saveMode = ReportPhraseSetting_SaveMode.Practice;
            //else
            //if (lstCustomTestPractice != null && lstCustomTestPractice.Count > 0)
            //    saveMode = ReportPhraseSetting_SaveMode.TestPractice;
            else
            {
                saveMode = ReportPhraseSetting_SaveMode.User;
            }

            return saveMode;
        }
        public void Save_ReportPhrases(VMSaveReportPhrase reportPhrase)
        {
            List<ReportPhraseSavedText> lstToSave = new List<ReportPhraseSavedText>();

            var lstNonEmpty = reportPhrase.ReportPhrases.Where(m => !string.IsNullOrEmpty(m.Value)).ToList();

            foreach (var item in lstNonEmpty)
            {
                lstToSave.Add(new ReportPhraseSavedText()
                {
                    Value = item.Value,
                    AppointmentID = reportPhrase.appointmentID,
                    TestID = reportPhrase.testID,
                    AppointmentTestLogID = reportPhrase.appointmentTestLogId,
                    TopLevelReportPhraseID = item.Id

                });

            }

            if (lstToSave != null && lstToSave.Count > 0)
            {
                context.ReportPhraseSavedText.AddRange(lstToSave);

                context.SaveChanges(reportPhrase.UserId, reportPhrase.IPAddress);
            }

        }
        #endregion

        #region Measurements 
        public WorkSheetVM LoadData(int appointmentID, int testID, int practiceID)
        {
            WorkSheetVM vm = new WorkSheetVM();

            vm.AppointmentID = appointmentID;
            vm.TestID = testID;
            vm.PracticeID = practiceID;

            // vm.UserID = GetMainDoctor(appointmentID);

            var entry = GetLogs(vm.AppointmentID, vm.TestID).OrderByDescending(L => L.Id).FirstOrDefault();

            if (entry != null)
            {
                vm.AppointmentTestLogID = entry.Id;
            }

            if (vm.AppointmentTestLogID > 0)
            {
                //get categories and their measurements
                var lst = GetCategoriesAndMeasurements(vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

                vm.MeasurementCategories = LoadSavedValues(lst, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

                vm.GroupID = GetGroupIdByTest(vm.TestID);
                lst = FilterCategoriesByGroup(vm.GroupID, lst);

                //lst = FilterCategoriesByTest(vm.TestID, lst);

                vm.MeasurementCategories = FilterMeasurementsByPractice(vm.PracticeID, lst, MeasurementShowMode.All);

                lst = GetNonEmptyValues(lst);

                vm.DoctorID = (new Cerebrum30.Areas.VP.DataAccess.VPRepository()).GetMainDoctor(vm.AppointmentID);
                //vm.GroupID = (new UserRepository()).GetGroupIdByTest(vm.TestID);
                //vm.ReportPhrases = GetReportPhrases(vm.GroupID, vm.UserID);

                vm.ReportPhrases = this.GetReportPhrasesByGroup(practiceID, vm.TestID, vm.DoctorID);

                vm.ReportPhrases = GetCustomReportPhrases(vm.ReportPhrases, vm.DoctorID, vm.PracticeID, vm.TestID);

                vm.ReportPhrases = LoadReportPhraseSavedValues(vm.ReportPhrases, vm.AppointmentID, vm.TestID, vm.AppointmentTestLogID);

            }

            return vm;
        }

        public WorkSheetVM LoadRangeStr(WorkSheetVM vm)
        {
            var bsaVal = GetBSAMeasValue(vm);
            var gender = GetPatientGenderByAppointment(vm.AppointmentID);
            string indexUnits = string.Empty;
            vm.MeasurementCategories.ForEach(category =>
            {
                category.Measurements.ForEach(m =>
                {
                    var Ranges = GetRanges(m.measurementCode, category.categoryCode);
                    var Range = Ranges.Where(x => x.gender == gender.ToString() && x.RangeName == MeasurementRanges.Normal.ToString()).ToList();
                    if (Range != null && Range.Count > 0)
                    {
                        m.RangeStr = "(" + Range[0].Range1 + "-" + Range[0].Range2 + ")";

                        indexUnits = Range[0].units;
                    }

                    //loading index range
                    if (!string.IsNullOrEmpty(m.MeasurementSavedValues[0].Value) && bsaVal > 0)
                    {
                        double val = 0;
                        double.TryParse(m.MeasurementSavedValues[0].Value, out val);
                        if (val > 0)
                        {
                            m.IndexRangeStr = GetMeasurementBSARange(val / bsaVal, m.measurementCode, category.categoryCode, gender.ToString());
                            if (!string.IsNullOrEmpty(m.IndexRangeStr))
                            {
                                m.IndexUnits = m.units + "/m2";
                                m.IndexValue = TruncateToTwoDigits((decimal)(val / bsaVal)).ToString();
                            }

                        }
                    }
                    //loading index range
                });
            });

            return vm;
        }
        public WS_Report_VM LoadReportData(int appointmentID, int testID, int practiceID)
        {
            WS_Report_VM vmReturn = new WS_Report_VM();

            WorkSheetVM vm = LoadData(appointmentID, testID, practiceID);

            vm = LoadRangeStr(vm);

            vm.ReportPhrases.ForEach(r =>

           vmReturn.ReportData.Phrases.Add(

                                       new ValueLabel()
                                       {
                                           label = r.Name,
                                           value = r.Value,
                                           type = r.type ?? 0,
                                           field = r.field ?? 0,
                                       })
                );
            vm.MeasurementCategories.ForEach(c =>
            {
                List<ValueLabel> mesurements = new List<ValueLabel>();

                foreach (var measure in c.Measurements)
                {
                    if (measure.name == "Deceleration Slope")
                    {
                        string s = string.Empty;
                    }

                    mesurements.Add(new ValueLabel()
                    {
                        IsIndex = true,
                        label = measure.name,
                        value = measure.MeasurementSavedValues[0].Value,
                        Units = measure.units,
                        Range = measure.RangeStr,
                        //IndexRangeStr = measure.IndexRangeStr,
                        //IndexValue = measure.IndexValue,
                        Bolded = measure.MeasurementSavedValues[0].MeasurementRange != MeasurementRanges.Normal &&
                                measure.MeasurementSavedValues[0].MeasurementRange != MeasurementRanges.NoRangeAvailable

                    });

                    //adding index value
                    if (!string.IsNullOrEmpty(measure.IndexValue))
                    {
                        mesurements.Add(new ValueLabel()
                        {
                            IsIndex = true,
                            label = measure.name + " index",
                            //value = measure.MeasurementSavedValues[0].Value,
                            value = measure.IndexValue,
                            Units = measure.IndexUnits,
                            //Range = measure.RangeStr,
                            IndexRangeStr = measure.IndexRangeStr,
                            IndexValue = measure.IndexValue,
                            Bolded =
                            measure.MeasurementSavedValues[0].MeasurementBSARange != MeasurementRanges.NoRangeAvailable &&
                            measure.MeasurementSavedValues[0].MeasurementBSARange != MeasurementRanges.Normal
                        });
                    }
                }

                //foreach (var measure in c.Measurements)
                //{
                //    mesurements.Add(new ValueLabel()
                //    {
                //        label = measure.name,
                //        value = measure.MeasurementSavedValues[0].Value,
                //        Units = measure.units,
                //        Range = measure.RangeStr,
                //        IndexRangeStr = measure.IndexRangeStr,
                //        IndexValue = measure.IndexValue,
                //        Bolded =
                //        measure.MeasurementSavedValues[0].MeasurementRange != MeasurementRanges.NoRangeAvailable &&
                //        measure.MeasurementSavedValues[0].MeasurementRange != MeasurementRanges.Normal
                //    });
                //}
                vmReturn.ReportData.categories.Add(
                 new MesurementByCategory()
                 {
                     mesurements = mesurements,
                     name = c.name
                 });

            });

            var userRepository = new UserRepository();
            vmReturn.ReportData.officeId = (userRepository).GetOfficeIDByAppointment(appointmentID);
            int? patientId = (userRepository).GetPatientByAppointment(appointmentID);
            if (patientId.HasValue)
            {
                vmReturn.ReportData.patientId = patientId.Value;
                //loading medications 

                var visitStartDt = DateTime.MinValue;
                var visitEndDt = (userRepository).GetAppointmentDate(appointmentID);
                var lstMedication = (new Areas.VP.DataAccess.VPRepository()).GetMedications(vmReturn.ReportData.patientId, visitStartDt, visitEndDt.Value);

                var prior = lstMedication.ToList();

                prior.ForEach(p =>
                {
                    if (p.DiscontinuedMedications.Count > 0)
                    {
                        if (p.DateStarted < visitEndDt.Value)
                        {
                            if (!p.DateDiscontinued.HasValue)
                                vmReturn.ReportData.med_vm.Prior.Add(p);
                            else
                            if (p.DateDiscontinued.Value >= visitEndDt.Value)
                            {
                                vmReturn.ReportData.med_vm.Prior.Add(p);
                            }
                        }
                        else
                        {
                            //var med = p.DiscontinuedMedications.First();
                            foreach (var med in p.DiscontinuedMedications)
                            {
                                if (med.DateDiscontinued.HasValue && med.DateDiscontinued.Value >= visitEndDt.Value)
                                {
                                    vmReturn.ReportData.med_vm.Prior.Add(med);
                                    break;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (p.DateDiscontinued.HasValue)
                        {
                            if (p.DateDiscontinued.Value >= visitEndDt.Value)
                                vmReturn.ReportData.med_vm.Prior.Add(p);
                        }
                        else
                        if (p.DateStarted < visitEndDt.Value)
                        {
                            vmReturn.ReportData.med_vm.Prior.Add(p);
                        }
                    }
                });

                vmReturn.ReportData.med_vm.Added = lstMedication.Where(x =>
                                                    x.DateStarted.Date.AbsoluteStart() == visitEndDt.Value &&
                                                    (!x.DateDiscontinued.HasValue || x.DateDiscontinued >= visitEndDt.Value)
                                                    && x.DiscontinuedMedications.Count == 0)

                                                    .ToList();

                vmReturn.ReportData.med_vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date.AbsoluteStart() == visitEndDt).ToList();
                lstMedication.ForEach(m =>
                {
                    if (m.DiscontinuedMedications.Count > 0 && m.DateStarted.AbsoluteStart() == visitEndDt)
                    {
                        //m.DiscontinuedMedications.Where(x => x.DateStarted.AbsoluteStart() == visitEndDt).ToList().FirstOrDefault();
                        vmReturn.ReportData.med_vm.DoseChanged.Add(m);
                    }
                });
                //vmReturn.ReportData.med_vm.Prior = lstMedication.Where(x => x.IsDiscontinued == false && x.DateStarted.Date < DateTime.Now.Date).ToList();
                //vmReturn.ReportData.med_vm.Added = lstMedication.Where(x => x.IsDiscontinued == false && x.DateStarted.Date == DateTime.Now.Date).ToList();
                //vmReturn.ReportData.med_vm.Discontinued = lstMedication.Where(x => x.IsDiscontinued == true && x.DateDiscontinued.Value.Date == DateTime.Now.Date).ToList();
            }

            return vmReturn;

        }

        public List<MeasurementCategoryVM> GetNonEmptyValues(List<MeasurementCategoryVM> lst)
        {
            List<MeasurementCategoryVM> retLst = new List<MeasurementCategoryVM>();

            foreach (var cat in lst)
            {
                List<MeasurementVM> lstMeas = new List<MeasurementVM>();

                foreach (var measure in cat.Measurements)
                {
                    if (!string.IsNullOrEmpty(measure.MeasurementSavedValues[0].Value) || (measure.isCompulsory ?? false))
                    {
                        lstMeas.Add(measure);
                    }
                }
                if (lstMeas.Count > 0)
                {
                    retLst.Add(cat);
                }
            }

            return retLst;
        }

        public List<MeasurementSavedValueVM> Merge(List<MeasurementSavedValueVM> newLst, int newLogID, int TestID, int AppointmentID, int AppointmentTestLogID)
        {
            List<MeasurementSavedValueVM> retLst = new List<MeasurementSavedValueVM>();
            List<MeasurementSavedValueVM> prevLst = new List<MeasurementSavedValueVM>();

            int prevLogID = 0;

            var prevLogLst = context.AppointmentTestLog
                              .Where(l => l.AppointmentID == AppointmentID && l.TestID == TestID && l.Id < newLogID)
                              .OrderByDescending(l => l.Id).ToList();


            //filter out the discarded values
            //retLst.AddRange(newLst.Where(x => x.Discard != true).ToList());
            retLst.AddRange(newLst.ToList());
            retLst.ForEach(i => i.AppointmentTestLogID = newLogID);

            if (prevLogLst != null && prevLogLst.Count > 0)
            {
                prevLogID = prevLogLst.Max(l => l.Id); //find max log ID smaller than newLog
                if (newLst.Count > 0)
                {
                    var isMerge = prevLogID != AppointmentTestLogID;

                    if (isMerge)
                    {
                        //get saved values
                        prevLst = GetSavedValues(AppointmentID, TestID, prevLogID);
                        //remove same measurements from old list
                        foreach (var item in retLst)
                        {
                            var oldEntry = prevLst.Where(m => m.MeasurementId == item.MeasurementId && m.Value == item.Value).ToList();
                            foreach (var it in oldEntry)
                            {
                                prevLst.Remove(it);
                            }
                        }
                    }
                }
                else
                {
                    bool has1LogBefore = prevLogLst.Count() == 1;
                    prevLst = GetSavedValues(AppointmentID, TestID, prevLogID, has1LogBefore);
                }
            }

            //assign new LogID to list 
            foreach (var item in prevLst)
            {
                item.AppointmentTestLogID = newLogID;
                retLst.Add(item);
            }

            // Prepare list to delete and
            var discardList = newLst.Where(x => x.Discard).Select(m => m.MeasurementId).Distinct().ToList();

            if (discardList != null)
            {
                foreach (int mid in discardList)
                {
                    retLst.RemoveAll(x => x.MeasurementId == mid);
                }
            }
            return retLst;

        }

        private List<MeasurementSavedValueVM> GetSavedValueList(List<MeasurementCategoryVM> model, bool hasMeasurementInFirstLog = false)
        {
            List<MeasurementSavedValueVM> lst = new List<MeasurementSavedValueVM>();

            foreach (var cat in model)
            {
                foreach (var item in cat.Measurements)
                {
                    if (!string.IsNullOrEmpty(item.MeasurementSavedValues[0].Value)) //only save if value is not empty
                    {
                        double result = -1;
                        if (!double.TryParse(item.MeasurementSavedValues[0].Value, out result))//entered value not a double
                        {
                            item.MeasurementSavedValues[0].ErrorMessage = "Invalid Value";

                        }
                        else
                        {
                            item.MeasurementSavedValues[0].Value = GetMaskedValue(item.mask, item.MeasurementSavedValues[0].Value);
                        }

                        if (hasMeasurementInFirstLog && item.MeasurementSavedValues.Count > 1)
                        {
                            lst.AddRange(item.MeasurementSavedValues);
                        }
                        else
                        {
                            lst.Add(item.MeasurementSavedValues[0]);
                        }
                    }
                }
            }

            return lst;

        }

        public List<MeasurementCategory> GetMeasurementCategory(int groupId, int appointmentID, int testID, int appointmentTestLogID)
        {
            var q = (
            from c in context.MeasurementCategory
            join m in context.Measurement on c.Id equals m.MeasurementCategoryID
            join g in context.CategoryByGroup on c.Id equals g.CatgeoryID
            where g.Visible && g.GroupID == groupId
            orderby c.order
            select c
            ).Distinct().ToList();

            return q;
        }

        public List<MeasurementCategoryVM> GetCategoriesAndMeasurements(int groupId, int appointmentID, int testID, int appointmentTestLogID)
        {
            var q = GetMeasurementCategory(groupId, appointmentID, testID, appointmentTestLogID);
            return GetCategoriesAndMeasurements(q, groupId, appointmentID, testID, appointmentTestLogID);
        }

        [Obsolete("Moved to WS microservices")]
        // new, need also groupId
        public List<MeasurementCategoryVM> GetCategoriesAndMeasurements(List<MeasurementCategory> q, int groupId, int appointmentID, int testID, int appointmentTestLogID)
        {
            // combining GetCategoriesAndMeasurements and FilterCategoriesByGroup
            List<MeasurementCategoryVM> lst = new List<MeasurementCategoryVM>();
            //List<MeasurementCategory> tmpLst = new List<MeasurementCategory>();
            //var preselectGroupEntry = context.CategoryByGroup.Where(g => g.Visible && g.GroupID == groupId && catgoryIDs.Contains(g.CatgeoryID)).ToList();
            //var q = (
            //            from c in context.MeasurementCategory
            //            join m in context.Measurement on c.Id equals m.MeasurementCategoryID
            //            join g in context.CategoryByGroup on c.Id equals g.CatgeoryID
            //            where g.Visible && g.GroupID == groupId
            //            orderby c.order
            //            select c
            //            ).Distinct();

            //q.ToList().ForEach(c => tmpLst.Add(c));
            var catIDs = q.Select(x => x.Id).Distinct();
            var allMeasurements = context.Measurement.Where(m => catIDs.Contains(m.MeasurementCategoryID.Value)).ToList();

            //for each category, get only "iscompulsory" fields unless mode is "showall"
            foreach (MeasurementCategory cat in q)
            {
                var measurements = allMeasurements.Where(m => m.MeasurementCategoryID == cat.Id).OrderBy(m => m.order).ToList();
                if (measurements.Count > 0)
                {
                    //cat.measurements.Clear();

                    var MeasurementCategoryVM = new MeasurementCategoryVM()
                    {
                        Id = cat.Id,
                        //Test = cat.Test,
                        par = cat.par,
                        name = cat.name,
                        order = cat.order,
                        categoryCode = cat.categoryCode,
                        status = cat.status,
                        dateAdded = cat.dateAdded,
                    };
                    foreach (var item in measurements)
                    {
                        MeasurementCategoryVM.Measurements.Add(new MeasurementVM()
                        {
                            Id = item.Id,
                            name = item.name,
                            order = item.order,
                            units = item.units,
                            categoryCode = item.categoryCode,
                            measurementCode = item.measurementCode,
                            status = item.status,
                            mask = item.mask,
                            dateAdded = item.dateAdded,
                            isCompulsory = item.isCompulsory,
                            visibleOnWorkSheet = item.visibleOnWorkSheet,
                            calculateBSA = item.calculateBSA,
                            MeasurementCategoryID = MeasurementCategoryVM.Id,
                            MeasurementSavedValues = new List<MeasurementSavedValueVM>
                            {
                                new MeasurementSavedValueVM { MeasurementId = item.Id, TestID = testID, AppointmentID = appointmentID}
                            }
                        });
                    }

                    //cat.measurements.AddRange(measurements);

                    // moved codes below inside the previous loop (sometimes this loop has 2000+ items, so can save a bit of runtime)
                    //foreach (MeasurementVM measure in MeasurementCategoryVM.Measurements)
                    //{
                    //    measure.MeasurementSavedValues.Clear();
                    //    measure.MeasurementSavedValues.Add(new MeasurementSavedValueVM()
                    //    {
                    //        MeasurementId = measure.Id,
                    //        TestID = testID,
                    //        AppointmentID = appointmentID

                    //    });
                    //}
                    lst.Add(MeasurementCategoryVM);
                }
            }

            if (lst != null && lst.Count > 0)

                lst = lst.OrderBy(x => x.order).ToList();

            return lst;
        }

        // old, better use another one - GetCategoriesAndMeasurements(int groupId, int appointmentID, int testID, int appointmentTestLogID)
        public List<MeasurementCategoryVM> GetCategoriesAndMeasurements(int appointmentID, int testID, int appointmentTestLogID)
        {
            //var gender = (new UserRepository()).GetPatientGenderByAppointment(appointmentID);

            List<MeasurementCategoryVM> lst = new List<MeasurementCategoryVM>();
            List<MeasurementCategory> tmpLst = new List<MeasurementCategory>();

            var q = (
                        from c in context.MeasurementCategory
                        join m in context.Measurement on c.Id equals m.MeasurementCategoryID
                        orderby c.order
                        select c
                        ).Distinct();

            q.ToList().ForEach(c => tmpLst.Add(c));

            var allMeasurements = context.Measurement.ToList();

            //for each category, get only "iscompulsory" fields unless mode is "showall"
            foreach (MeasurementCategory cat in tmpLst)
            {
                var measurements = allMeasurements.Where(m => m.MeasurementCategoryID == cat.Id).OrderBy(m => m.order).ToList();
                if (measurements.Count > 0)
                {
                    cat.measurements.Clear();

                    var MeasurementCategoryVM = new MeasurementCategoryVM()
                    {
                        Id = cat.Id,
                        //Test = cat.Test,
                        par = cat.par,
                        name = cat.name,
                        order = cat.order,
                        categoryCode = cat.categoryCode,
                        status = cat.status,
                        dateAdded = cat.dateAdded,
                    };
                    foreach (var item in measurements)
                    {
                        MeasurementCategoryVM.Measurements.Add(new MeasurementVM()
                        {
                            Id = item.Id,
                            name = item.name,
                            order = item.order,
                            units = item.units,
                            categoryCode = item.categoryCode,
                            measurementCode = item.measurementCode,
                            status = item.status,
                            mask = item.mask,
                            dateAdded = item.dateAdded,
                            isCompulsory = item.isCompulsory,
                            visibleOnWorkSheet = item.visibleOnWorkSheet,
                            calculateBSA = item.calculateBSA,
                            MeasurementCategoryID = MeasurementCategoryVM.Id,
                        });
                    }

                    cat.measurements.AddRange(measurements);

                    foreach (MeasurementVM measure in MeasurementCategoryVM.Measurements)
                    {
                        measure.MeasurementSavedValues.Clear();
                        measure.MeasurementSavedValues.Add(new MeasurementSavedValueVM()
                        {
                            MeasurementId = measure.Id,
                            TestID = testID,
                            AppointmentID = appointmentID

                        });
                    }
                    lst.Add(MeasurementCategoryVM);
                }
            }

            if (lst != null && lst.Count > 0)

                lst = lst.OrderBy(x => x.order).ToList();

            return lst;
        }

        public List<MeasurementCategoryVM> FilterCategoriesByGroup_Old(int groupId, List<MeasurementCategoryVM> lst)
        {
            List<MeasurementCategoryVM> lstFiltered = new List<MeasurementCategoryVM>();

            lst.ForEach(c =>
            {
                var groupEntry = context.CategoryByGroup.Where(g => g.GroupID == groupId && g.CatgeoryID == c.Id).FirstOrDefault();
                if (groupEntry != null)
                {
                    if (groupEntry.Visible)
                    {
                        lstFiltered.Add(c);
                    }
                }

            });

            return lstFiltered;
        }

        public List<MeasurementCategoryVM> FilterCategoriesByGroup(int groupId, List<MeasurementCategoryVM> lst)
        {
            List<MeasurementCategoryVM> lstFiltered = new List<MeasurementCategoryVM>();
            var catgoryIDs = lst.Select(x => x.Id).Distinct();
            var preselectGroupEntry = context.CategoryByGroup.Where(g => g.Visible && g.GroupID == groupId && catgoryIDs.Contains(g.CatgeoryID)).ToList();

            lst.ForEach(c =>
            {
                var groupEntry = preselectGroupEntry.Where(g => g.CatgeoryID == c.Id).FirstOrDefault();
                if (groupEntry != null)
                {
                    lstFiltered.Add(c);
                }
            });

            return lstFiltered;
        }

        public List<MeasurementCategoryVM> FilterCategoriesByTest(int testId, List<MeasurementCategoryVM> lst)
        {
            List<MeasurementCategoryVM> lstFiltered = new List<MeasurementCategoryVM>();
            var testGroupDetail = context.TestGroupDetail.ToList();
            lst.ForEach(c =>
            {
                var groupEntry = testGroupDetail.Where(g => g.TestID == testId && g.MeasurementCategoryId == c.Id).FirstOrDefault();
                if (groupEntry != null)
                {
                    if (groupEntry.Visible)
                    {
                        lstFiltered.Add(c);
                    }
                }
            });

            return lstFiltered;
        }

        public List<MeasurementCategoryVM> FilterByShowMode(List<MeasurementCategoryVM> lst, MeasurementShowMode mode)
        {
            //TODO, incomplete or incorrect logic 
            switch (mode)
            {
                case MeasurementShowMode.Filled:
                    lst.ForEach(c =>
                    {
                        c.Measurements.RemoveAll(m => m.isCompulsory == false && string.IsNullOrEmpty(m.MeasurementSavedValues[0].Value));

                    });
                    break;

                default:
                    break;
            }

            lst.RemoveAll(c => c.Measurements.Count == 0);

            return lst;
        }

        //filters measuremnts by PracticeID
        public List<MeasurementCategoryVM> FilterMeasurementsByPractice(int practiceID, List<MeasurementCategoryVM> lst, MeasurementShowMode mode)
        {
            var measureIDs = lst.SelectMany(x => x.Measurements).Select(x => x.Id).Distinct();
            var measByPractices = context.MeasurementByPractice.Where(x => x.PracticeID == practiceID && measureIDs.Contains(x.MeasurementID)).ToList();

            lst.ForEach(c =>
            {
                foreach (var measure in c.Measurements)
                {
                    var measByPractice = measByPractices.Where(g => g.MeasurementID == measure.Id).FirstOrDefault();

                    if (measByPractice != null)
                    {
                        measure.VisibleByPractice = measByPractice.Visible;
                    }
                }
            });

            //removing measurements with VisibleByPractice set to false
            foreach (var c in lst)
            {
                var newMeasLst = c.Measurements.Where(m => m.VisibleByPractice == true).ToList();

                c.Measurements.Clear();
                c.Measurements.AddRange(newMeasLst);
            }

            //removing categroies with zero measurements 
            lst.RemoveAll(c => c.Measurements.Count == 0);

            return lst;
        }


        public List<MeasurementCategoryVM> FilterMeasurementsByPractice_Old(int practiceID, List<MeasurementCategoryVM> lst, MeasurementShowMode mode)
        {
            var measByPractices = context.MeasurementByPractice.ToList();

            lst.ForEach(c =>
            {
                MeasurementCategoryVM newCat = new MeasurementCategoryVM();
                foreach (var measure in c.Measurements)
                {
                    var measByPractice = measByPractices.Where(g => g.PracticeID == practiceID && g.MeasurementID == measure.Id)
                                        .FirstOrDefault();

                    if (measByPractice != null)
                    {
                        measure.VisibleByPractice = measByPractice.Visible;
                    }
                }
            });

            //removing measurements with VisibleByPractice set to false
            foreach (var c in lst)
            {
                var newMeasLst = c.Measurements.Where(m =>
                m.VisibleByPractice == true
                ).ToList();

                c.Measurements.Clear();
                c.Measurements.AddRange(newMeasLst);
            }

            //removing categroies with zero measurements 
            lst.RemoveAll(c => c.Measurements.Count == 0);

            return lst;
        }

        public List<MeasurementCategoryVM> FilterCategoriesByGroup(int groupId, List<MeasurementCategoryVM> lst, List<TestGroupDetailVM> lstGroup)
        {
            List<MeasurementCategoryVM> lstFiltered = new List<MeasurementCategoryVM>();

            lst.ForEach(c =>
            {
                var groupEntry = lstGroup.Where(g => g.TestGroupId == groupId && g.MeasurementCategoryId == c.Id).FirstOrDefault();
                if (groupEntry != null)
                {
                    if (groupEntry.Visible)
                    {
                        lstFiltered.Add(c);
                    }
                }

            });

            return lstFiltered;
        }

        public List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> FilterPhrasesByGroup(int testID, List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lst, List<TestGroupDetailVM> lstGroup)
        {
            int groupId = (new UserRepository()).GetGroupIdByTest(testID);

            List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase> lstFiltered = new List<Cerebrum30.Areas.Measurements.Models.ViewModels.TopLevelReportPhrase>();

            foreach (var item in lst)
            {
                if (IsVisibleByGroup(groupId, item.Id, lstGroup))
                {
                    lstFiltered.Add(item);
                }
            }

            return lstFiltered;
        }
        public bool IsVisibleByGroup(int groupID, int reportPhraseID, List<TestGroupDetailVM> lstGroup)
        {
            bool retVal = false;

            if (lstGroup.Count > 0)
            {
                var groupEntry = lstGroup.Where(g => g.TestGroupId == groupID && g.ReportPhraseId == reportPhraseID).FirstOrDefault();

                if (groupEntry != null)
                {
                    retVal = groupEntry.Visible;
                }
            }

            return retVal;
        }

        /// <summary>
        /// Get Value against each measurement based on appointmentID and testID
        /// </summary>
        /// <param name="lst"></param>
        /// <param name="appointmentID"></param>
        /// <param name="testID"></param>
        /// <param name="appointmentTestLogID"></param>
        /// <returns></returns>
        private string GetRangeCssClass(MeasurementRanges range)
        {
            string rangeClass = " Normal";

            if (range == MeasurementRanges.Normal)
            {
                rangeClass = " Normal";
            }
            else
                   if (range == MeasurementRanges.MildlyAbnormal)
            {
                rangeClass = " MildlyAbnormal";
            }
            else
                   if (range == MeasurementRanges.ModeratelyAbnormal)
            {
                rangeClass = " ModeratelyAbnormal";
            }
            else
                   if (range == MeasurementRanges.SeverelyAbnormal)
            {
                rangeClass = " SeverelyAbnormal";
            }
            else
                   if (range == MeasurementRanges.NoNormalRange)
            {
                rangeClass = " NoNormalRange";
            }
            else
            if (range == MeasurementRanges.OutOfRange)
            {
                rangeClass = " OutOfRange";
            }
            else
            if (range == MeasurementRanges.NoRangeAvailable)
            {
                rangeClass = " NoRangeAvailable";
            }
            return rangeClass;
        }

        private string GetBSARangeCssClass(MeasurementRanges? range)
        {
            string rangeClass = " Normal";

            if (range == MeasurementRanges.Normal)
            {
                rangeClass = " Normal";
            }
            else
                   if (range == MeasurementRanges.MildlyAbnormal)
            {
                rangeClass = " MildlyAbnormal";
            }
            else
                   if (range == MeasurementRanges.ModeratelyAbnormal)
            {
                rangeClass = " ModeratelyAbnormal";
            }
            else
                   if (range == MeasurementRanges.SeverelyAbnormal)
            {
                rangeClass = " SeverelyAbnormal";
            }
            else
                   if (range == MeasurementRanges.NoNormalRange)
            {
                rangeClass = " NoNormalRange";
            }
            else
            if (range == MeasurementRanges.OutOfRange)
            {
                rangeClass = " OutOfRange";
            }
            else
            if (range == MeasurementRanges.NoRangeAvailable)
            {
                rangeClass = " NoRangeAvailable";
            }
            return rangeClass;
        }

        public string GetRangeStr(string mesCode, string catCode, string gender)
        {
            string retStr = GetProperRangeStr(MeasurementRanges.NoRangeAvailable);

            if (string.IsNullOrEmpty(mesCode) || string.IsNullOrEmpty(catCode))
            {
                retStr = "Missing measurementCode and categoryCode";
            }
            else
            {
                var Ranges = GetRanges(mesCode, catCode);

                if (Ranges.Count > 0)
                {
                    var range = Ranges.Where(x => x.gender == gender.ToString() && x.RangeName == MeasurementRanges.Normal.ToString()).ToList().FirstOrDefault();
                    if (range != null)
                    {
                        retStr = "(" + range.Range1 + "-" + range.Range2 + ")";
                    }
                }
                else
                {
                    retStr = GetProperRangeStr(MeasurementRanges.OutOfRange);
                }
            }
            return retStr;
        }
        public string GetBSARangeStr(string mesCode, string catCode, string gender)
        {
            string retStr = GetProperRangeStr(MeasurementRanges.NoRangeAvailable);

            if (string.IsNullOrEmpty(mesCode) || string.IsNullOrEmpty(catCode))
            {
                retStr = "Missing measurementCode and categoryCode";
            }
            else
            {
                var Ranges = GetBSARanges(mesCode, catCode);

                if (Ranges.Count > 0)
                {
                    var range = Ranges.Where(x => x.gender == gender.ToString() && x.RangeName == MeasurementRanges.Normal.ToString()).ToList().FirstOrDefault();
                    if (range != null)
                    {
                        retStr = "(" + range.Range1 + "-" + range.Range2 + ")";
                    }

                }
                else
                {
                    retStr = GetProperRangeStr(MeasurementRanges.OutOfRange);
                }
            }
            return retStr;
        }

        private string GetProperRangeStr(MeasurementRanges range)
        {
            string retStr = string.Empty;

            if (range == MeasurementRanges.NoRangeAvailable)
            {
                retStr = "No Range Available";
            }
            else
                if (range == MeasurementRanges.OutOfRange)
            {
                retStr = "Out Of Range";
            }

            return retStr;
        }
        static MemCacheService ccache = new MemCacheService();
        public List<MeasurementRange> GetMeasurementRange()
        {
            List<MeasurementRange> result = ccache.GetList<MeasurementRange>(CacheKeys.CEREBRUM_MEASUREMENT_RANGE);
            if (result == null)
            {
                result = context.MeasurementRange.AsNoTracking().ToList();
                ccache.SetList<MeasurementRange>(CacheKeys.CEREBRUM_MEASUREMENT_RANGE, result, 360);
            }

            return result;
        }

        public List<MeasurementBSARange> GetMeasurementBSARange()
        {
            List<MeasurementBSARange> result = ccache.GetList<MeasurementBSARange>(CacheKeys.CEREBRUM_MEASUREMENT_BSA_RANGE);
            if (result == null)
            {
                result = context.MeasurementBSARange.AsNoTracking().ToList();
                ccache.SetList<MeasurementBSARange>(CacheKeys.CEREBRUM_MEASUREMENT_BSA_RANGE, result, 360);
            }

            return result;
        }
        public List<MeasurementCategoryVM> LoadSavedValues(List<MeasurementCategoryVM> lst, int appointmentID, int testID, int appointmentTestLogID, int patientID = 0)
        {
            var gender = (new UserRepository()).GetGender(appointmentID);

            var lstSavedValueSet = GetSavedValueSet(appointmentID, testID, appointmentTestLogID);

            var lstRange = GetMeasurementRange();

            var lstBSARange = GetMeasurementBSARange();

            int ProstaticValveId = GetProstaticValveID(patientID, appointmentID);

            double bsaVal = 0;

            var bsaObj = lstSavedValueSet.FirstOrDefault(bsa => bsa.Measurement != null && bsa.Measurement.name.ToLower().Equals("bsa"));
            if (bsaObj != null)
            {
                double.TryParse(bsaObj.Value, out bsaVal);
            }

            foreach (var category in lst)
            {
                foreach (MeasurementVM measure in category.Measurements)
                {
                    var savedValues = lstSavedValueSet.Where(s => s.MeasurementId == measure.Id).ToList();
                    if (savedValues != null && savedValues.Count > 0)
                    {
                        double val = 0;
                        double.TryParse(savedValues[0].Value, out val);

                        MeasurementRanges range = GetMeasurementRange(lstRange, val, measure.measurementCode, category.categoryCode, ProstaticValveId, gender);
                        MeasurementRanges? bsaRange = null;
                        // bsaRange = MeasurementRanges.NoRangeAvailable;
                        if (bsaVal > 0)
                            bsaRange = GetMeasurementBSARange(lstBSARange, val / bsaVal, measure.measurementCode, category.categoryCode, ProstaticValveId, gender);
                        measure.MeasurementSavedValues.Clear();

                        foreach (var savedValue in savedValues)
                        {
                            measure.MeasurementSavedValues.Add(new MeasurementSavedValueVM()
                            {
                                MeasurementId = savedValue.MeasurementId,
                                TestID = savedValue.TestID,
                                AppointmentID = savedValue.AppointmentID,
                                Value = savedValue.Value,
                                MeasurementOperatorId = savedValue.MeasurementOperatorId,
                                MeasurementRange = range,
                                MeasurementBSARange = bsaRange,
                                RangeClass = this.GetRangeCssClass(range),
                                BSARangeClass = this.GetBSARangeCssClass(bsaRange),

                            });
                        }
                    }
                }
            }

            return lst;
        }

        public WorkSheetVM GetBSAValue(WorkSheetVM vm)
        {
            vm.MeasurementCategories.ForEach(c =>
            {
                c.Measurements.ForEach(m =>
                {
                    if (m.name.ToLower() == "bsa")
                    {
                        decimal dummy = 0;
                        decimal.TryParse(m.MeasurementSavedValues[0].Value, out dummy);
                        if (dummy != 0)
                        {
                            vm.BSA = dummy;
                        }
                    }

                });
            });

            return vm;
        }
        public double GetBSAMeasValue(WorkSheetVM vm)
        {
            double retVal = 0;
            vm.MeasurementCategories.ForEach(c =>
            {
                c.Measurements.ForEach(m =>
                {
                    if (!string.IsNullOrEmpty(m.MeasurementSavedValues[0].Value) && m.name.ToLower() == "bsa")
                    {
                        double dummy = 0;
                        double.TryParse(m.MeasurementSavedValues[0].Value, out dummy);
                        if (dummy != 0)
                        {
                            retVal = dummy;
                        }
                    }
                });
            });

            return retVal;
        }

        public double GetBSAMeasValue(List<MeasurementSavedValue> vm)
        {
            double retVal = 0;

            vm.ForEach(m =>
            {
                string x = m.Measurement.name;
                if (!string.IsNullOrEmpty(m.Value) && m.Measurement.name.ToLower() == "bsa")
                {
                    double dummy = 0;
                    double.TryParse(m.Value, out dummy);
                    if (dummy != 0)
                    {
                        retVal = dummy;
                    }
                }
            });

            return retVal;
        }

        public List<MeasurementCategoryVM> LoadRanges(int appID, List<MeasurementCategoryVM> lst)
        {
            var gender = (new UserRepository()).GetGender(appID);

            var lstRange = context.MeasurementRange.ToList();

            var lstBSARange = context.MeasurementBSARange.ToList();

            foreach (var category in lst)
            {
                foreach (MeasurementVM measure in category.Measurements)
                {
                    var savedValues = measure.MeasurementSavedValues;

                    if (savedValues != null && savedValues.Count > 0)
                    {
                        //go through first saved value

                        var savedValue = savedValues[0];

                        double val = 0;

                        MeasurementRanges range = new MeasurementRanges();
                        MeasurementRanges? bsaRange = null;

                        range = MeasurementRanges.Normal;
                        //bsaRange = MeasurementRanges.Normal;

                        if (!string.IsNullOrEmpty(savedValue.Value))
                        {
                            double.TryParse(savedValue.Value, out val);

                            range = this.GetMeasurementRange(lstRange, val, measure.measurementCode, category.categoryCode, 0, gender);

                            bsaRange = this.GetMeasurementBSARange(lstBSARange, val, measure.measurementCode, category.categoryCode, 0, gender);
                        }

                        savedValue.MeasurementRange = range;
                        savedValue.MeasurementBSARange = bsaRange;

                    }
                }

            }

            return lst;

        }

        public List<MeasurementCategoryVM> GetFilledOnly(List<MeasurementCategoryVM> lst)
        {
            List<MeasurementCategoryVM> lstRet = new List<MeasurementCategoryVM>();

            foreach (var cat in lst)
            {
                List<MeasurementVM> lstMeasures = new List<MeasurementVM>();

                foreach (var measure in cat.Measurements)
                {
                    if (!string.IsNullOrEmpty(measure.MeasurementSavedValues[0].Value) || (measure.isCompulsory ?? false))
                    {
                        lstMeasures.Add(measure);
                    }
                }

                if (lstMeasures.Count > 0)
                {
                    cat.Measurements.Clear();

                    cat.Measurements.AddRange(lstMeasures);

                    lstRet.Add(cat);

                }
            }

            return lstRet;
        }
        /// <summary>
        /// Loads all Measurement Operators
        /// </summary>
        /// <returns></returns>
        public List<MeasurementOperator> GetAllMeasurementOperators()
        {
            List<MeasurementOperator> lst = new List<MeasurementOperator>();

            lst.AddRange(context.MeasurementOperator.AsNoTracking().ToList());

            return lst;
        }

        /// <summary>
        /// Get logs based on 'appointmentID' and 'testID'
        /// </summary>
        /// <param name="appointmentID"></param>
        /// <param name="testID"></param>
        /// <param name="AppointmentTestID"></param>
        /// <returns></returns>
        public List<AppointmentTestLog> GetLogs(int appointmentID, int testID)
        {
            return
                    context.AppointmentTestLog
                    .Where(l => l.AppointmentID == appointmentID && l.TestID == testID)
                    .ToList();
        }
        public List<AppointmentTestLog_WS> GetLogsWithName(int appointmentID, int testID)
        {
            return
                   (from x in context.AppointmentTestLog
                    join u in context.Users on x.UserID equals u.UserID
                    into ao
                    from p in ao.DefaultIfEmpty()
                    where
                    x.AppointmentID == appointmentID &&
                    x.TestID == testID
                    select new AppointmentTestLog_WS()
                    {
                        Id = x.Id,
                        Date = x.Date,
                        Status = x.Status,
                        AppointmentID = x.AppointmentID,
                        IP = x.IP,
                        UserID = x.UserID,
                        UserName = p.UserName
                    }).AsNoTracking().ToList();
        }

        /// <summary>
        /// Saves a list of 'MeasurementSavedValue'
        /// </summary>
        /// <param name="model"></param>
        public void SaveMeasurements(List<MeasurementSavedValue> model, int userId, string ipAddress)
        {
            foreach (var item in model)
            {
                if (item.MeasurementOperatorId == 0)
                    item.MeasurementOperatorId = 1;   //if Nothing selected, select 'None' as default operator type
            }

            context.MeasurementSavedValue.AddRange(model);


            context.SaveChanges(userId, ipAddress);
        }

        public void SaveMeasurements(List<MeasurementSavedValueVM> model, int userid, string IPAddress)
        {
            List<MeasurementSavedValue> lst = new List<MeasurementSavedValue>();

            foreach (MeasurementSavedValueVM item in model)
            {
                if (item.MeasurementOperatorId == 0)
                    item.MeasurementOperatorId = 1;   //if Nothing selected, select 'None' as default operator type

                lst.Add(new MeasurementSavedValue()
                {
                    Id = item.Id,
                    Value = item.Value,
                    isCalculated = item.isCalculated,
                    AppointmentID = item.AppointmentID,
                    TestID = item.TestID,
                    AppointmentTestLogID = item.AppointmentTestLogID,
                    MeasurementId = item.MeasurementId,
                    MeasurementOperatorId = item.MeasurementOperatorId,
                });
            }

            context.MeasurementSavedValue.AddRange(lst);

            context.SaveChanges(userid, IPAddress);
        }

        public void ClearPreviousLogs(int appointmentID, int testID, int userId, string ipaddress)
        {
            var previousLogs = context.AppointmentTestLog
               .Where(a => a.AppointmentID == appointmentID &&
                           a.TestID == testID &&
                           a.Status == 0
                      ).ToList();
            foreach (var currentLog in previousLogs)
            {
                currentLog.Status = 1;
            }

            context.SaveChanges(userId, ipaddress);
        }

        /// <summary>
        /// Creates a new Log entry 
        /// </summary>
        /// <param name="appointmentID"></param>
        /// <param name="testID"></param>
        /// <param name="appointmentTestID"></param>
        /// <returns></returns>
        public int CreateLog(int appointmentID, int testID, int UserID, string ipAddress = null)
        {
            int returnID = -1;

            ClearPreviousLogs(appointmentID, testID, UserID, ipAddress);

            AppointmentTestLog log =
                new AppointmentTestLog()
                {
                    Date = DateTime.Now,
                    Status = 0,
                    AppointmentID = appointmentID,
                    TestID = testID,
                    UserID = UserID,
                    IP = ipAddress
                    //AppointmentTestID = appointmentTestID
                };

            context.AppointmentTestLog.Add(log);

            //context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            context.SaveChanges(UserID, ipAddress);

            returnID = log.Id;

            return returnID;
        }

        public List<VMMeasurementHist> GetMeasurementHistory(int measurementID, int appointmentID, int testID)
        {
            List<VMMeasurementHist> measHistList = new List<VMMeasurementHist>();

            List<SqlParameter> parameters = new List<SqlParameter>
            {
                new SqlParameter("appointmentId",appointmentID),
                new SqlParameter("measurementId",measurementID),
                new SqlParameter("testId",testID)

            };

            var dbData = context.GetData<Cerebrum.BLL.Measurements.SPEntities.SP_MeasurementHistory>("dbo.GetMeasurementHistory", parameters).ToList();

            foreach (var dbItem in dbData)
            {
                VMMeasurementHist measHist = new VMMeasurementHist();
                measHist.AppointmentId = dbItem.AppointmentId;
                measHist.AppointmentDate = dbItem.AppointmentDate;
                measHist.AppTestId = dbItem.AppTestId;
                measHist.TestDate = dbItem.TestDate;
                measHist.TestId = dbItem.TestId;
                measHist.MeasurementName = dbItem.MeasurementName;
                measHist.MeasurementValue = dbItem.MeasurementValue;

                measHistList.Add(measHist);
            }

            return measHistList;

        }
        public List<VMPreviousTestItem> GetPreviousVPAppointment(int appointmentID, int patientID)
        {
            List<VMPreviousTestItem> retLst = new List<VMPreviousTestItem>();

            var apptDt = (new UserRepository()).GetAppointmentDate(appointmentID);
            var vpTestID = GetVPTestID();

            var appTestLst = (from at in context.AppointmentTests
                              join t in context.Tests
                              on at.TestId equals t.Id
                              where
                              //at.Appointment.appointmentTime <= apptTime.Value && 
                              at.Appointment.appointmentTime < apptDt &&
                              at.Appointment.PatientRecordId == patientID &&
                              t.Id == vpTestID &&
                              (
                                at.AppointmentTestStatusId != (int)AppointmentStatus.CancellationList &&
                                at.AppointmentTestStatusId != (int)AppointmentStatus.WaitList &&
                                at.AppointmentTestStatusId != (int)AppointmentStatus.Booked &&
                                at.AppointmentTestStatusId != (int)AppointmentStatus.NotArrived &&
                                at.AppointmentTestStatusId != (int)AppointmentStatus.Missed &&
                                at.AppointmentTestStatusId != (int)AppointmentStatus.Cancelled &&
                                at.AppointmentTestStatusId != (int)AppointmentStatus.Triage
                              )
                              orderby at.Appointment.appointmentTime descending
                              select new VMPreviousTestItem()
                              {
                                  ID = at.Id,
                                  AppointmentTestID = at.Id,
                                  AppointmentID = at.AppointmentId,
                                  TestDate = at.Appointment.appointmentTime,
                                  TestID = at.TestId,
                                  TestName = t.testShortName
                              }
                     ).ToList();

            retLst = appTestLst;

            return retLst;

        }

        public List<VMPreviousTestItem> GetPatientsPreviousTests(int practiceId, int patientID, int appointmentId)
        {
            var appointmentDatetime = context.Appointments.Find(appointmentId).appointmentTime;
            //[SP_GetPatientPreviousAppointments]
            List<VMPreviousTestItem> retLst = new List<VMPreviousTestItem>();
            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("practiceId",practiceId),
                new SqlParameter("patientRecordId",patientID),
                new SqlParameter("appointmentDate",appointmentDatetime),
            };

            var appTestLst = context.GetData<VMPreviousTestItem>("[dbo].[SP_GetPatientPreviousAppointments]", parms).ToList();

            var vprepo = (new Cerebrum30.Areas.VP.DataAccess.VPRepository());
            foreach (var item in appTestLst)
            {
                var doctorID = vprepo.GetMainDoctor(item.AppointmentID);
                if (item.TestName.ToLower() == Utility.Helper.VP_TEST_NAME.ToLower())
                {
                    item.Impressions.AddRange(GetVPImpressions(item.AppointmentID, patientID, doctorID));
                }
                else
                {
                    item.Impressions.AddRange(GetImpressions(item.AppointmentID, item.TestID));
                }
            }

            retLst = appTestLst;

            return retLst;

        }
        public List<VMPreviousTestItem> GetPatientsPreviousTests(int practiceId, int patientID)
        {
            //[SP_GetPatientPreviousAppointments]
            List<VMPreviousTestItem> retLst = new List<VMPreviousTestItem>();
            List<SqlParameter> parms = new List<SqlParameter>
            {
                new SqlParameter("practiceId",practiceId),
                new SqlParameter("patientRecordId",patientID ),
            };

            var appTestLst = context.GetData<VMPreviousTestItem>("[dbo].[SP_GetPatientPreviousAppointments]", parms).ToList();
            #region Old Query
            //DateTime now = DateTime.Now.AbsoluteEnd();
            //var appTestLst1 = (from at in context.AppointmentTests
            //                  join t in context.Tests
            //                  on at.TestId equals t.Id
            //                  where
            //                  //at.Appointment.appointmentTime <= apptTime.Value && 
            //                  at.Appointment.appointmentStatus != AppointmentStatus.Cancelled
            //                  && at.Appointment.appointmentTime <= now &&
            //                  at.Appointment.PatientRecordId == patientID &&
            //                  at.Appointment.AppointmentType.Id != (int)AppointmentTypes.NoVisit
            //                  orderby at.Appointment.appointmentTime descending
            //                  select new PreviousTestItemVM()
            //                  {
            //                      ID = at.Id,
            //                      AppointmentTestID = at.Id,
            //                      AppointmentID = at.AppointmentId,
            //                      TestDate = at.Appointment.appointmentTime,
            //                      TestID = at.TestId,
            //                      TestName = t.testShortName
            //                  }
            //         ).ToList();
            #endregion
            var vprepo = (new Cerebrum30.Areas.VP.DataAccess.VPRepository());
            foreach (var item in appTestLst)
            {
                var doctorID = vprepo.GetMainDoctor(item.AppointmentID);
                if (item.TestName.ToLower() == Utility.Helper.VP_TEST_NAME.ToLower())
                {
                    item.Impressions.AddRange(GetVPImpressions(item.AppointmentID, patientID, doctorID));
                }
                else
                {
                    item.Impressions.AddRange(GetImpressions(item.AppointmentID, item.TestID));
                }
            }

            retLst = appTestLst;

            return retLst;

        }
        public List<VMPreviousTestItem> GetPreviousTests(int appointmentID, int testID)
        {
            List<VMPreviousTestItem> retLst = new List<VMPreviousTestItem>();

            DateTime? apptTime = null;
            int? patientID = null;

            var appt = context.Appointments.Find(appointmentID);

            if (appt != null)
            {
                apptTime = appt.appointmentTime;
                patientID = appt.PatientRecordId;
            }

            DateTime now = DateTime.Now.AbsoluteEnd();
            var appTestLst = (from at in context.AppointmentTests
                              join t in context.Tests
                              on at.TestId equals t.Id
                              where
                              at.Appointment.appointmentTime <= now
                              && at.Appointment.PatientRecordId == patientID.Value
                              && at.Appointment.AppointmentType.Id != (int)AppointmentTypes.NoVisit
                              && at.IsActive == true
                              && at.Appointment.IsActive == true
                              orderby at.Appointment.appointmentTime descending
                              select new VMPreviousTestItem()
                              {
                                  ID = at.Id,
                                  AppointmentTestID = at.Id,
                                  AppointmentID = at.AppointmentId,
                                  AppointmentStatus = at.Appointment.appointmentStatus,
                                  TestDate = at.Appointment.appointmentTime,
                                  TestID = at.TestId,
                                  TestName = t.testShortName
                              }
                     ).ToList();

            foreach (var item in appTestLst)
            {
                //var doctorID = (new Cerebrum30.Areas.VP.DataAccess.VPRepository()).GetMainDoctor(item.AppointmentID);
                int doctorId = 0;
                string doctorName = "";
                var appointmentDoctor = (new Cerebrum30.Areas.VP.DataAccess.VPRepository()).GetAppointmentDoctor(item.AppointmentID);

                if (appointmentDoctor != null)
                {
                    doctorId = appointmentDoctor.Id;
                    doctorName = appointmentDoctor.lastName + ", " + appointmentDoctor.firstName;
                    item.DoctorName = doctorName;
                }

                if (item.TestName.ToLower() == Utility.Helper.VP_TEST_NAME.ToLower())
                {
                    item.Impressions.AddRange(GetVPImpressions(item.AppointmentID, patientID.Value, doctorId));
                }
                else
                {
                    item.Impressions.AddRange(GetImpressions(item.AppointmentID, item.TestID));
                }
            }

            retLst = appTestLst;

            return retLst;

        }
        public List<VMReportPhraseSavedText> GetImpressions(int appointmentID, int testID)
        {
            List<VMReportPhraseSavedText> reporPhraseLst = new List<VMReportPhraseSavedText>();

            int maxAppointmentTestLogID = 0;

            var lst = context.ReportPhraseSavedText
                 .Where(r =>
                            r.AppointmentID == appointmentID &&
                            r.TestID == testID).ToList();
            if (lst.Count > 0)
            {
                maxAppointmentTestLogID = lst.Max(s => s.AppointmentTestLogID);
            }

            if (maxAppointmentTestLogID > 0)
            {
                reporPhraseLst = (from i in context.ReportPhraseSavedText
                                  join r in context.ReportPhrase
                                   on i.TopLevelReportPhraseID equals r.Id
                                  where i.AppointmentID == appointmentID &&
                                        i.TestID == testID &&
                                        i.AppointmentTestLogID == maxAppointmentTestLogID
                                  select new VMReportPhraseSavedText()
                                  {
                                      Id = i.Id,
                                      TopLevelReportPhraseID = i.TopLevelReportPhraseID,
                                      Value = i.Value,
                                      TopLevelReportPhraseText = r.name
                                  }).ToList();
                var impressionEntry = reporPhraseLst.Where(p => p.TopLevelReportPhraseText.ToLower().Contains("impression")).ToList().FirstOrDefault();
                if (impressionEntry != null)
                {
                    reporPhraseLst.Remove(impressionEntry);
                    reporPhraseLst.Insert(0, impressionEntry);
                }

            }
            return reporPhraseLst;
        }
        public List<VMReportPhraseSavedText> GetVPImpressions(int appointmentID, int patientID, int doctorID = 0)
        {
            List<VMReportPhraseSavedText> reporPhraseLst = new List<VMReportPhraseSavedText>();
            var phrases = (new Areas.VP.DataAccess.VPRepository()).LoadPhrasesData(appointmentID, patientID, doctorID);
            phrases.ForEach(p =>
            {
                if (!string.IsNullOrEmpty(p.Value))
                {
                    reporPhraseLst.Add(new VMReportPhraseSavedText()
                    {
                        Id = p.Id,
                        TopLevelReportPhraseID = p.Id,
                        Value = p.Value,
                        TopLevelReportPhraseText = p.Name
                    });
                }
            });

            //adding impression entry to front of list 
            var impressionEntry = reporPhraseLst.Where(p => p.TopLevelReportPhraseText.ToLower().Contains("impression")).ToList().FirstOrDefault();
            if (impressionEntry != null)
            {
                reporPhraseLst.Remove(impressionEntry);
                reporPhraseLst.Insert(0, impressionEntry);
            }
            return reporPhraseLst;
        }

        public bool SendReportEmail(Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM vm, int userid, string ipaddress)
        {
            bool emailSent = false;
            string emailFrom = System.Configuration.ConfigurationManager.AppSettings["SMTP_From"].ToString();
            string emailTo = vm.EmailTo;
            string emailBody = "Please find enclosed the consult Report on your patient.";
            string emailSubject = "Warning: Private and Confidential";
            emailSent = Helper.SendMail(emailFrom, emailTo, emailBody, emailSubject, vm.PhysicalPath);
            // Convert local SendReport_VM to the expected Cerebrum.ViewModels.VP.SendReport_VM
            var convertedVm = ConvertToVPSendReport(vm);
            _sendReport.Add_WSSendReport(convertedVm, userid, ipaddress);

            return emailSent;
        }

        public void AddSendReport(Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM vm, int userid, string ipaddress)
        {
            // Convert local SendReport_VM to the expected Cerebrum.ViewModels.VP.SendReport_VM
            var convertedVm = ConvertToVPSendReport(vm);
            _sendReport.Add_WSSendReport(convertedVm, userid, ipaddress);
        }
        public void Add_VPSendReport(Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM vm, int userid, string ipaddress)
        {
            // Convert local SendReport_VM to the expected Cerebrum.ViewModels.VP.SendReport_VM
            var convertedVm = ConvertToVPSendReport(vm);
            _sendReport.Add_VPSendReport(convertedVm, userid, ipaddress);
        }

        /// <summary>
        /// Convert local Measurements SendReport_VM to VP SendReport_VM
        /// </summary>
        private Cerebrum.ViewModels.VP.SendReport_VM ConvertToVPSendReport(Cerebrum30.Areas.Measurements.Models.ViewModels.SendReport_VM localVm)
        {
            return new Cerebrum.ViewModels.VP.SendReport_VM
            {
                Id = localVm.Id,
                EmailTo = localVm.EmailTo,
                FaxTo = localVm.FaxTo,
                Location = localVm.Location,
                DateEntered = localVm.DateEntered,
                Sent = localVm.Sent,
                Amended = localVm.Amended,
                IsVP = localVm.IsVP,
                AppointmentId = localVm.AppointmentId,
                TestId = localVm.TestId,
                PatientId = localVm.PatientId,
                SendType = (AwareMD.Cerebrum.Shared.Enums.SendType)(int)localVm.SendType, // Convert enum
                PhysicalPath = localVm.PhysicalPath,
                URL = localVm.URL,
                DocName = localVm.DocName,
                ErrorMessage = localVm.ErrorMessage
            };
        }
        public VMTest Test(int id)
        {
            return _sendReport.GetTest(id);
        }

        public void AddSendReport_RawData(Cerebrum.ViewModels.VP.SendReport_VM vm, int userid, string ipaddress)
        {
            _sendReport.Add_WSSendReport_RawData(vm, userid, ipaddress);
        }

        public List<string> GetRawFiles(int appointmentID, int testID)
        {
            List<string> retFiles = new List<string>();

            var files = context.WS_SendReport.Where(w =>
                                                        w.AppointmentId == appointmentID &&
                                                        w.TestId == testID &&
                                                        w.Active == true &&
                                                        w.SendTypeId == (int)AwareMD.Cerebrum.Shared.Enums.SendType.Raw).AsNoTracking().ToList();

            files.ForEach(f =>
            {
                if (!retFiles.Contains(f.Location))
                {
                    retFiles.Add(f.Location);
                }
            });

            return retFiles;
        }
        public int GetVPTestID()
        {
            return context.Tests.Where(t => t.testShortName == "VP").Select(s => s.Id).FirstOrDefault();
        }

        public List<ReportHistory> GetReportHistory(int appointmentID, int testID, int patientID, int vpTestID = 0)
        {
            List<ReportHistory> lst = new List<ReportHistory>();

            vpTestID = vpTestID == 0 ? this.GetVPTestID() : vpTestID;

            if (testID == vpTestID)
            {
                lst = (from s in context.VP_SendReport
                       join t in context.SendType on
                         s.SendTypeId equals t.Id
                       where s.AppointmentId == appointmentID && s.PatientRecordId == patientID
                       orderby s.Id descending
                       select new ReportHistory
                       {
                           ID = s.Id,
                           SendTypeID = t.Id,
                           Location = s.Location,
                           DateEntered = s.DateEntered,
                           Sent = s.Sent,
                           Amended = s.Amended,
                           AppointmentId = s.AppointmentId,
                           TestId = testID,
                           SendType = t.Name,
                           URL = s.URL,
                           Email = s.Email,
                           Fax = s.Fax,
                           DocName = s.DocName,
                           ErrorMessage = s.ErrorMessage

                       }).ToList();
            }
            else
            {
                lst = (from s in context.WS_SendReport
                       join t in context.SendType on
                         s.SendTypeId equals t.Id
                       where s.AppointmentId == appointmentID && s.TestId == testID
                       orderby s.Id descending
                       select new ReportHistory
                       {
                           ID = s.Id,
                           SendTypeID = t.Id,
                           Location = s.Location,
                           DateEntered = s.DateEntered,
                           Sent = s.Sent,
                           Amended = s.Amended,
                           AppointmentId = s.AppointmentId,
                           TestId = s.TestId,
                           SendType = t.Name,
                           URL = s.URL,
                           Email = s.Email,
                           Fax = s.Fax,
                           DocName = s.DocName,
                           ErrorMessage = s.ErrorMessage
                       }).ToList();

            }
            return lst;
        }

        public List<int> GetAppointments()
        {
            List<int> lst = new List<int>();

            lst.AddRange(context.Appointments.OrderBy(x => x.Id).Select(x => x.Id).ToList());

            return lst;
        }
        public List<TestGroupDetailVM> GetTestNames()
        {
            List<TestGroupDetailVM> lst = new List<TestGroupDetailVM>();

            lst.AddRange(context.Tests.OrderBy(x => x.Id).Select(x => new TestGroupDetailVM() { Id = x.Id, Name = x.Id + " " + x.testShortName }).ToList());

            return lst;
        }

        public List<PracticeVM> GetPractices()
        {
            List<PracticeVM> lst = new List<PracticeVM>();

            lst.AddRange(context.Practices.OrderBy(x => x.Id).Select(x => new PracticeVM() { ID = x.Id, Name = x.PracticeName }).ToList());

            return lst;
        }

        public List<int> GetTests()
        {
            List<int> lst = new List<int>();

            lst.AddRange(context.Tests.OrderBy(x => x.Id).Select(x => x.Id).ToList());

            return lst;
        }

        public bool GetActionOnAbnormal(int appointmentID)
        {
            bool retVal = false;

            var entry = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (entry != null)
            {
                retVal = entry.actionOnAbnormal;
            }

            return retVal;
        }
        public bool GetIsTestAbnormal(int appointmentID, int testId) // redmine #12567, cloned from SVN 10621
        {
            bool retVal = false;

            var appt = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (appt != null)
            {
                if (appt.appointmentTests != null && appt.appointmentTests.Count > 0)
                {
                    var test = appt.appointmentTests.Where(x => x.TestId == testId).FirstOrDefault();
                    if (test != null)
                    {
                        retVal = test.IsAbnormal;
                    }
                }
            }

            return retVal;
        }
        public void SetActionOnAbnormal(int appointmentID, int testId, bool value, int userId, string ip)
        {
            var appt = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (appt != null)
            {
                // redmine #12567, Abnormal for AppointmentTest level, cloned from svn 10621
                if (appt.appointmentTests != null && appt.appointmentTests.Count > 0)
                {
                    var test = appt.appointmentTests.Where(x => x.TestId == testId).FirstOrDefault();
                    if (test != null)
                    {
                        test.IsAbnormal = value;
                        context.SaveChanges(userId, ip);
                    }
                }
            }
        }

        public void SetForReview(int appointmentID, int appointmentTestID, int userId, string ipAddress)
        {
            var appt = context.AppointmentTests.Where(a => a.Id == appointmentTestID).FirstOrDefault();
            if (appt != null)
            {
                appt.SetForReview = true;
                context.SaveChanges(userId, ipAddress);
            }
        }

        public bool GetForReview(int appointmentID, int appointmentTestID)
        {
            bool retVal = false;
            var appt = context.AppointmentTests.Where(a => a.Id == appointmentTestID).FirstOrDefault();
            if (appt != null)
            {
                retVal = appt.SetForReview;
            }
            return retVal;
        }
        public bool HasBullsEye(int testId)
        {
            return (from tg in context.TestGroup
                    join g in context.Group on tg.GroupId equals g.Id
                    where tg.TestId == testId && g.BullsEyeSvgNumber != BullsEyeSvgNumber.NoGroup
                    select g
                    ).Any();
        }
        public string Reassign(int appointmentTestID, int docID, int userId, string ipAddress)
        {
            string retStr = string.Empty;
            var appt = context.AppointmentTests.Where(a => a.Id == appointmentTestID).FirstOrDefault();

            if (appt != null)
            {
                if (appt.AppointmentTestStatusId == (int)AppointmentTestStatuses.ReportCompleted)
                {
                    throw new Exception("Test cannot be reassigned due to status.");
                }
                else
                {
                    appt.ReassignDocID = docID;
                    appt.ReassignDate = DateTime.Now;
                    context.SaveChanges(userId, ipAddress);

                    var docSaved =

                        (from p in context.PracticeDoctors
                         join e in context.ExternalDoctors on p.ExternalDoctorId equals e.Id
                         where
                         p.Id == docID
                         select e).FirstOrDefault();
                    if (docSaved != null)
                    {
                        retStr = docSaved.lastName + " " + docSaved.firstName;
                    }
                }
            }

            return retStr;
        }
        public bool FileGenerated(int appointmentID, int testID)
        {
            bool exists = false;

            var entry = context.WS_SendReport
                        .Where(r => r.AppointmentId == appointmentID && r.TestId == testID)
                        .OrderByDescending(r => r.Id).FirstOrDefault();

            if (entry != null)
            {
                exists = !string.IsNullOrEmpty(entry.Location);
            }

            return exists;
        }

        #endregion

        public ClientConfiguration GetClientConfigutaion()
        {
            ClientConfiguration retVal = new ClientConfiguration();
            var entry = context.Clients.Where(c => c.Name == "YCC").FirstOrDefault();
            if (entry != null)
            {
                retVal.Id = entry.Id;
                retVal.ClientKey = entry.ClientKey;
                retVal.Secret = entry.Secret;
                retVal.Name = entry.Name;
                retVal.Active = entry.Active;
                retVal.ClientKey = entry.ClientKey;
            }
            return retVal;
        }
        public MeasurementVM GetMeasurement(int mid)
        {
            MeasurementVM retVal = new MeasurementVM();

            var mes = context.Measurement.Where(m => m.Id == mid).FirstOrDefault();

            if (mes != null)
            {
                retVal.Id = mes.Id;
                retVal.name = mes.name;
                retVal.units = mes.units;
            }

            return retVal;
        }

        public List<MeasurementRangeVM> GetRanges(string mesCode, string catCode)
        {
            List<MeasurementRangeVM> lst = new List<MeasurementRangeVM>();

            lst = (from r in context.MeasurementRange
                   join t in context.MeasurementRangeType on r.MeasurementRangeTypeId equals t.Id
                   where r.measurementCode == mesCode && r.categoryCode == catCode
                   select new MeasurementRangeVM()
                   {
                       Range1 = r.Range1,
                       Range2 = r.Range2,
                       gender = r.gender,
                       measurementCode = r.measurementCode,
                       categoryCode = r.categoryCode,
                       RangeName = t.name,
                       units = r.units

                   }).ToList();

            return lst;

        }

        public List<MeasurementRangeVM> GetBSARanges(string mesCode, string catCode)
        {
            List<MeasurementRangeVM> lst = new List<MeasurementRangeVM>();

            lst = (from r in context.MeasurementBSARange
                   join t in context.MeasurementRangeType on r.MeasurementRangeTypeId equals t.Id
                   where r.measurementCode == mesCode && r.categoryCode == catCode
                   select new MeasurementRangeVM()
                   {
                       Range1 = r.IndexedRange1,
                       Range2 = r.IndexedRange2,
                       gender = r.gender,
                       measurementCode = r.measurementCode,
                       categoryCode = r.categoryCode,
                       RangeName = t.name,
                       units = r.units

                   }).ToList();

            return lst;

        }

        public List<MeasurementRangeTypeVM> GetMeasurementRangeTypes()
        {
            List<MeasurementRangeTypeVM> retLst = new List<MeasurementRangeTypeVM>();

            context.MeasurementRangeType.ToList().ForEach(r =>
            {
                retLst.Add(new MeasurementRangeTypeVM()
                {
                    Id = r.Id,
                    color = r.color,
                    name = r.name
                });
            });

            return retLst;
        }

        public List<MeasurementRangeTypeVM> GetMeasurementRangeTypes(List<MeasurementRangeType> inputs)
        {
            List<MeasurementRangeTypeVM> retLst = new List<MeasurementRangeTypeVM>();

            inputs.ToList().ForEach(r =>
            {
                retLst.Add(new MeasurementRangeTypeVM()
                {
                    Id = r.Id,
                    color = r.color,
                    name = r.name
                });
            });

            return retLst;
        }

        public string GetDoctorNotes(int appointmentID, int testID)
        {
            string docComments = string.Empty;
            var at = context.AppointmentTests.Where(a => a.AppointmentId == appointmentID && a.TestId == testID).FirstOrDefault();
            if (at != null)
            {
                docComments = at.PhysicianComments;
            }
            return docComments;
        }

        public string GetTechNotes(int appointmentID, int testID)
        {
            string techComments = string.Empty;
            var at = context.AppointmentTests.Where(a => a.AppointmentId == appointmentID && a.TestId == testID && a.IsActive == true).FirstOrDefault();
            if (at != null)
            {
                techComments = at.TechnicianComments;
            }
            return techComments;
        }

        public void UpdateNotes(VMSaveMeasurmentNote note)
        {
            var at = context.AppointmentTests.FirstOrDefault(a => a.AppointmentId == note.AppointmentId && a.TestId == note.TestId && a.IsActive == true);
            if (at != null)
            {
                at.PhysicianComments = note.DoctorComment;
                at.TechnicianComments = note.TechnicianNote;

                context.SaveChanges(note.UserId, note.IPAddress);
            }
        }

        public string GetXMLFileName(int appointmentID, int testID, int patientID, int doctorID = 0)
        {
            //office_test_report~appt_time~Bill_Num~OHIP~fax
            string fileName = "Newmarket555_Echocardiogram_Report~1_12_2016~169193~**********~**********.pdf";

            var ohipBillingNumber = "000000";
            if (doctorID == 0)
            {
                doctorID = (new Areas.VP.DataAccess.VPRepository()).GetMainDoctor(appointmentID);
            }
            var doctor = context.ExternalDoctors.Where(e => e.Id == doctorID).FirstOrDefault();

            var dem = context.Demographics.Where(d => d.PatientRecordId == patientID).FirstOrDefault();

            var appointment = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();

            var office = context.Offices.Where(o => o.Id == appointment.OfficeId).FirstOrDefault();

            var healthCards = context.HealthCards.Where(h => h.DemographicId == dem.Id)
                              .OrderByDescending(x => x.Id).FirstOrDefault();

            var test = context.Tests.Where(t => t.Id == testID).FirstOrDefault();

            var testName = test.testFullName;

            var appTime = appointment.appointmentTime;

            var officeName = office.name;
            var officeFax = office.fax;
            var referralDocId = appointment.referralDoctorId;
            var referralDocAddressId = appointment.ReferralDoctorAddressId;
            var referralDocPhoneId = appointment.ReferralDoctorPhoneNumberId;
            var fax = "";

            if (doctor != null)
            {
                ohipBillingNumber = doctor.OHIPPhysicianId;
            }

            //check if this doctor that we are faxing to is the referral doctor and see if there is a
            //fax number selected on the daysheet for this doctor
            if (referralDocId > 0 && referralDocId == doctor.Id && referralDocPhoneId > 0)
            {
                var dbFaxPhone = context.ExternalDoctorPhoneNumbers.Find(referralDocPhoneId);
                if (dbFaxPhone != null)
                {
                    fax = dbFaxPhone.faxNumber;
                }

            }
            else
            {
                fax = (from e in context.ExternalDoctorPhoneNumbers
                       where
                            e.ExternalDoctorId == doctorID &&
                            !string.IsNullOrEmpty(e.faxNumber)
                       orderby e.Id descending
                       select
                       e.faxNumber).ToList().FirstOrDefault();
            }

            if (!String.IsNullOrWhiteSpace(fax))
            {
                fax = fax.Replace("-", string.Empty)
                         .Replace("(", string.Empty)
                         .Replace(")", string.Empty)
                         .Replace(" ", string.Empty);
            }

            var ohipNumber = "0000";
            if (healthCards != null)
                ohipNumber = healthCards.number;
            fileName = string.Format("{0}_{1}_Report{2}~{3}~{4}~{5}~{6}.pdf",

                                        officeName,
                                        testName,
                                        DateTime.Now.ToString("yyyyMMddHHmmss"),
                                        appTime.ToShortDateString().Replace("/", "_"),
                                         ohipBillingNumber,
                                        ohipNumber,
                                        fax
                                    //officeFax.Replace("-", string.Empty)
                                    //         .Replace("(", string.Empty)
                                    //         .Replace(")", string.Empty)
                                    //         .Replace(" ", string.Empty)
                                    );

            return fileName;

        }

        public string GetXMLFileNameBookingConfirmation(int appointmentId, VMDoctorReport reportDoctor)
        {
            var appointment = context.Appointments.Find(appointmentId);
            var fileName = "";
            if (appointment != null)
            {
                //var patientBLL = new Cerebrum.BLL.Patient.PatientBLL(context);
                var patientId = appointment.PatientRecordId;
                var officeId = appointment.OfficeId;
                var appTime = appointment.appointmentTime;
                var ohipBillingNumber = reportDoctor.OHIPId;
                var faxNumber = reportDoctor.FaxNumber;
                var patient = _patientBll.GetPatientInfo(patientId);
                var patientOhip = patient.OHIP;
                var office = context.Offices.Find(officeId);
                var officeName = office.name;
                var officeFax = office.fax;

                if (!String.IsNullOrWhiteSpace(faxNumber))
                {
                    faxNumber = faxNumber.Replace("-", string.Empty)
                         .Replace("(", string.Empty)
                         .Replace(")", string.Empty)
                         .Replace(" ", string.Empty);

                    fileName = string.Format("{0}_{1}_Report~{2}~{3}~{4}~{5}.pdf",
                                            officeName,
                                            "Booking",
                                            appTime.ToShortDateString().Replace("/", "_"),
                                             ohipBillingNumber,
                                            patientOhip,
                                            faxNumber);
                }
            }
            return fileName;

        }

        public string GetTestHRMShort(int testID)
        {
            string result = string.Empty;
            var entry = context.Tests.Where(t => t.Id == testID).FirstOrDefault();
            if (entry != null)
            {
                result = entry.HrmTypeShort;
            }
            return result;
        }
        public string GetTestHRMLong(int testID)
        {
            string result = string.Empty;
            var entry = context.Tests.Where(t => t.Id == testID).FirstOrDefault();
            if (entry != null)
            {
                result = entry.HrmTypeLong;
            }
            return result;
        }
        public string GetTestHRMModality(int testID)
        {
            string result = string.Empty;
            var entry = context.Tests.Where(t => t.Id == testID).FirstOrDefault();
            if (entry != null)
            {
                result = entry.HrmModality;
            }
            return result;
        }
        public string GetDocCPSO(int extDocID)
        {
            string result = string.Empty;
            var entry =
                (from e in context.ExternalDoctors
                     //join p in context.PracticeDoctors on e.Id equals p.ExternalDoctorId
                 where
                 e.Id == extDocID
                 select e).FirstOrDefault();

            if (entry != null)
            {
                result = entry.CPSO;
            }
            return result;
        }

        public int GetExternalDoctorId(int practiceDoctorId)
        {
            int externalDoctorId = 0;

            var externalDoc = context.PracticeDoctors.Where(a => a.Id == practiceDoctorId).FirstOrDefault();
            if (externalDoc != null)
            {
                externalDoctorId = externalDoc.ExternalDoctorId;
            }

            return externalDoctorId;
        }

        public string GetOfficeHRMID(int officeID)
        {
            string result = string.Empty;
            var entry = context.Offices.Where(o => o.Id == officeID).FirstOrDefault();
            if (entry != null)
            {
                result = entry.HRM_id;
            }
            return result;
        }
        public Cerebrum30.Areas.Measurements.Models.ViewModels.HL7_Data_VM LoadHL7_VM(int appointmentID, int testID, int physicianID, int patientID)
        {
            Cerebrum30.Areas.Measurements.Models.ViewModels.HL7_Data_VM vm = new Cerebrum30.Areas.Measurements.Models.ViewModels.HL7_Data_VM();

            var p = (from d in context.Demographics
                     where d.PatientRecordId == patientID
                     select new
                     {
                         d.Id,
                         d.PatientRecordId,
                         d.lastName,
                         d.firstName,
                         d.dateOfBirth,
                         gender = d.gender.ToString(),
                         phone = d.phoneNumbers.FirstOrDefault()
                     }).FirstOrDefault();
            vm.HL7_Data_Patient.DemographicID = p.Id;
            vm.HL7_Data_Patient.PatientID = p.PatientRecordId;
            vm.HL7_Data_Patient.lastName = p.lastName;
            vm.HL7_Data_Patient.firstName = p.firstName;
            vm.HL7_Data_Patient.DOB = p.dateOfBirth.Value;
            vm.HL7_Data_Patient.Sex = p.gender;

            var a = context.DemographicsAddress.Where(c => c.DemographicId == vm.HL7_Data_Patient.DemographicID && c.IsActive)
                          .OrderByDescending(x => x.Id).FirstOrDefault();
            if (a != null)
            {
                vm.HL7_Data_Patient.Address = a.addressLine1;
                vm.HL7_Data_Patient.City = a.city;
                vm.HL7_Data_Patient.PostalCode = a.postalCode;
            }
            //var phone = context.DemographicsContactPhoneNumbers.Where(ph => ph.DemographicsContactId == vm.HL7_Data_Patient.DemographicID)
            //    .OrderByDescending(x => x.Id).FirstOrDefault();

            var phone = p.phone != null ? p.phone.phoneNumber : string.Empty;

            if (phone != null)
            {
                vm.HL7_Data_Patient.Phone = phone;
            }

            var test = context.Tests.FirstOrDefault(t => t.Id == testID);

            if (test != null)
            {
                vm.HL7_Data_Test.examTypeShort = test.testShortName;
                vm.HL7_Data_Test.examTypeLong = test.testFullName;  //"Peripheral arterial doppler/lower extremities";
                //vm.HL7_Data_Test.modality = test.modality.ToString(); //"VASCULAR STUDIES";
                var modalities = test.Modalities.Any() ? string.Join(",", test.Modalities.Select(s => s.Modality.modalityName).ToArray()) : "";
                vm.HL7_Data_Test.modality = modalities;
                //vm.HL7_Data_Test.addReport = 1 ;
                // vm.HL7_Data_Test.addReportSufix = "";
                //hl7.DocType = Exams[id].DocType;        //"MR";
                //vm.HL7_Data_Test.EXAM_TYPE = test.;   //"DI";
                //vm.HL7_Data_Test.sendingFacility = "";  //"DI";
            }
            var healthCard = context.HealthCards.Where(h => h.DemographicId == vm.HL7_Data_Patient.DemographicID)
                .OrderByDescending(d => d.Id).FirstOrDefault();

            if (healthCard != null)
            {
                vm.HL7_Data_Patient.HealthCardNumber = healthCard.number;
                vm.HL7_Data_Patient.HealthCardCode = healthCard.version;
            }

            var appt = context.Appointments.Where(at => at.Id == appointmentID).FirstOrDefault();
            var referralDoctorId = appt.referralDoctorId;

            //appt.referralDoctorId

            var refDoc = (from d in context.DemographicsDefaultReferralDoctors
                          join e in context.ExternalDoctors
                          on d.ExternalDoctorId equals e.Id
                          where d.DemographicId == vm.HL7_Data_Patient.DemographicID
                          orderby d.Id descending
                          select e).FirstOrDefault();

            var extDoc = (from d in context.DemographicsMainResponsiblePhysicians
                          join e in context.ExternalDoctors
                          on d.ExternalDoctorId equals e.Id
                          where d.DemographicId == vm.HL7_Data_Patient.DemographicID
                          orderby d.Id descending
                          select e).FirstOrDefault();

            if (refDoc != null)
            {
                vm.HL7_Data_Physician.OrderingDoctorID = refDoc.Id.ToString();
                vm.HL7_Data_Physician.OrderingDoctorLastName = refDoc.lastName;
                vm.HL7_Data_Physician.OrderingDoctorFirstName = refDoc.firstName;
            }

            if (extDoc != null)
            {
                vm.HL7_Data_Physician.DoctorID = extDoc.Id.ToString();
                vm.HL7_Data_Physician.DoctorLastName = extDoc.lastName;
                vm.HL7_Data_Physician.DoctorFirstName = extDoc.firstName;
            }
            return vm;
        }

        public List<Doctor_Report> GetAssociatedContactList(int patientID)
        {
            return (from doc in context.DemographicsAssociatedDoctors
                    join d in context.Demographics on doc.DemographicId equals d.Id
                    join e in context.ExternalDoctors on doc.ExternalDoctorId equals e.Id
                    where
                       d.PatientRecordId == patientID
                    select new Doctor_Report
                    {
                        ID = e.Id,
                        Email = e.email,
                        Fax = e.fax,
                        HRM = e.HRM,
                        Mail = e.mail,
                        Name = e.lastName + " " + e.firstName,
                        LastName = e.lastName,
                        FirstName = e.firstName,
                        EmailAddress = e.emailAddress,
                        DocType = DocType.CC

                    }).ToList();
        }
        public List<PracticeDoctor_VM> GetPracticeDocs(int practiceID)
        {
            return (from p in context.PracticeDoctors
                    join e in context.ExternalDoctors on
                    p.ExternalDoctorId equals e.Id
                    where p.PracticeId == practiceID
                    select new PracticeDoctor_VM() { ID = p.Id, Name = e.lastName + " " + e.firstName })
             .ToList();

        }

        public void UpdateTestStatus(int appointmenttestID, int statusID, int userId, string ipAddress)
        {
            AppointmentTest appTest = context.AppointmentTests.Find(appointmenttestID);
            if (appTest != null)
            {
                appTest.AppointmentTestStatusId = statusID;
                context.SaveChanges(userId, ipAddress);
            }
        }
        public int GetTestStatus(int appointmenttestID)
        {
            int result = 0;
            AppointmentTest appTest = context.AppointmentTests.Find(appointmenttestID);
            if (appTest != null)
            {
                result = appTest.AppointmentTestStatusId;
            }
            return result;
        }

        public Test GetTest(int testID)
        {
            return context.Tests.Where(t => t.Id == testID).AsNoTracking().FirstOrDefault();
        }


        public string GetTestName(int testID)
        {
            string retStr = string.Empty;

            retStr = context.Tests.Where(t => t.Id == testID).FirstOrDefault().testFullName;

            return retStr;
        }
        public BullEyeResponse GetBullEyeInfo(int appointmentId, int testId)
        {
            BullEyeResponse response = new BullEyeResponse();
            response.appointmentId = appointmentId;
            response.testId = testId;
            response.svgNumber = (int)BullsEyeSvgNumber.NoGroup;

            BullsEyeDto dbBullsEye = GetBullsEyeInfo(appointmentId, testId);

            if (dbBullsEye == null)
                response.errorMessage = $"Cannot find the test (id: {testId})";
            else
            {
                response.patientName = (string.IsNullOrEmpty(dbBullsEye.PatientLastName) ? string.Empty : dbBullsEye.PatientLastName.ToUpper() + ", ") + (dbBullsEye.PatientFirstName ?? string.Empty);
                response.svgNumber = (int)dbBullsEye.BullsEyeSvgNumber;

                if (dbBullsEye.BullsEyeSvgNumber == BullsEyeSvgNumber.NoGroup)
                    response.errorMessage = $"Bull's Eye feature is not available for test (id: {testId}, name: {dbBullsEye.TestShortName.Trim()})";
                else if (!string.IsNullOrEmpty(dbBullsEye.BullsEyeSegment))
                    response.segments = JsonConvert.DeserializeObject<List<segment>>(dbBullsEye.BullsEyeSegment);
            }

            return response;
        }

        public void SaveBullsEyeInfo(BullEyeRequest request, int userId, string ipAddress)
        {
            XmlDocument xml = new XmlDocument();
            xml.LoadXml(request.svgData);
            // add "Svg.dll" as reference  (using Svg.2.2.1    https://www.nuget.org/packages/Svg/)
            var svgDocument = Svg.SvgDocument.Open(xml);
            var bitmap = svgDocument.Draw();

            string bullsEyeFolder = System.Configuration.ConfigurationManager.AppSettings["BullsEyeFolder"].ToString();
            bullsEyeFolder = Path.Combine(Path.Combine(bullsEyeFolder, request.appointmentId.ToString()), request.testId.ToString());
            if (!Directory.Exists(bullsEyeFolder))
                Directory.CreateDirectory(bullsEyeFolder);
            string svgImageFileName = Path.Combine(bullsEyeFolder, request.appointmentId.ToString() + "-" + request.testId.ToString() + ".png");
            if (File.Exists(svgImageFileName))
                File.Delete(svgImageFileName);
            //bitmap.Save(svgImageFileName, System.Drawing.Imaging.ImageFormat.Png);
            using (MemoryStream memory = new MemoryStream())
            {
                using (FileStream fs = new FileStream(svgImageFileName, FileMode.Create, FileAccess.ReadWrite))
                {
                    bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                    byte[] bytes = memory.ToArray();
                    fs.Write(bytes, 0, bytes.Length);
                }
            }

            var bullEye = context.BullEye.Where(a => a.appointmentId == request.appointmentId && a.testId == request.testId).FirstOrDefault();
            if (bullEye == null)
            {
                bullEye = new BullEye();
                bullEye.appointmentId = request.appointmentId;
                bullEye.testId = request.testId;
                context.BullEye.Add(bullEye);
            }
            bullEye.segments = JsonConvert.SerializeObject(request.segments);
            bullEye.svgImageFileName = svgImageFileName;

            context.SaveChanges(userId, ipAddress);

        }


        public bool IsClassicAppointment(int appointmentTestID)
        {
            bool result = false;
            var entry = context.AppointmentTests.FirstOrDefault(x => x.Id == appointmentTestID);
            if (entry != null)
            {
                result = entry.IsImported;
            }
            return result;
        }

        public LegacyDocURL GetLegacyURLs(int appointmentTestID)
        {
            LegacyDocURL result = new LegacyDocURL();
            result.AppointmentTestID = appointmentTestID;

            var entry = context.AppointmentTestLegacyDocs.Where(x => x.AppointmentTestId == appointmentTestID).FirstOrDefault();
            if (entry != null)
            {
                result.ReportURL = entry.reportUrl;
                result.RawDocumentURL = entry.rawDocumentUrl;
                result.LetterURL = entry.letterUrl;
            }
            return result;
        }
        public List<LegacyDocURL> GetLegacyURLList(int patientID)
        {
            List<LegacyDocURL> result = new List<LegacyDocURL>();

            var lst = (from a in context.Appointments
                       join at in context.AppointmentTests on a.Id equals at.AppointmentId
                       join doc in context.AppointmentTestLegacyDocs on at.Id equals doc.AppointmentTestId
                       where
                        a.PatientRecordId == patientID
                       select doc
                        ).ToList();
            lst.ForEach(x =>
            {
                result.Add(new LegacyDocURL()
                {
                    ReportURL = x.reportUrl,
                    RawDocumentURL = x.rawDocumentUrl,
                    LetterURL = x.letterUrl

                });
            });

            return result;
        }

        public string GetImageURL(int OfficeId)
        {
            string result = string.Empty;

            var entry = context
                        .OfficeUrls
                        .FirstOrDefault(o => o.officeId == OfficeId && o.urlTypeId == 3);

            if (entry != null)
            {
                result = entry.url;
            }

            return result;
        }
        public string GetDownloadURL(int OfficeId)
        {
            string result = string.Empty;

            var entry = context
                        .OfficeUrls
                        .FirstOrDefault(o => o.officeId == OfficeId && o.urlTypeId == 5);

            if (entry != null)
            {
                result = entry.url;
            }

            return result;
        }
        public string GetUploadURL(int OfficeId)
        {
            string result = string.Empty;

            var entry = context
                        .OfficeUrls
                        .FirstOrDefault(o => o.officeId == OfficeId && o.urlTypeId == 6);

            if (entry != null)
            {
                result = entry.url;
            }

            return result;
        }

        public string GetTargetURL(int OfficeId)
        {
            string result = string.Empty;

            var entry = context
                        .OfficeUrls
                        .FirstOrDefault(o => o.officeId == OfficeId && o.urlTypeId == 7);

            if (entry != null)
            {
                result = entry.url;
            }

            return result;
        }
        public int GetPracticeIDByUserName(string useName)
        {
            int result = 0;

            var entry = context.Users.FirstOrDefault(x => x.UserName == useName);

            if (entry != null)
            {
                result = entry.PracticeID;
            }

            return result;
        }
        public bool TestAppointmentByPractice(int value, int practiceID)
        {
            var dbAppointment = (from o in context.Offices
                                 join a in context.Appointments on o.Id equals a.OfficeId
                                 join p in context.PatientRecords on a.PatientRecordId equals p.Id
                                 where o.PracticeId == p.PracticeId && o.PracticeId == practiceID && a.Id == value
                                 select a).Any();

            return dbAppointment;
        }

        public bool TestDoctorByPractice(int value, int practiceID)
        {
            bool result = false;

            var entry = context.PracticeDoctors.Where(p => p.Id == value && p.PracticeId == practiceID).FirstOrDefault();

            result = entry != null;

            return result;
        }

        public bool TestPatientByPractice(int value, int practiceID)
        {
            bool result = false;

            var entry = context.PatientRecords.Where(p => p.Id == value && p.PracticeId == practiceID).FirstOrDefault();

            result = entry != null;

            return result;
        }
        public Gender GetPatientGenderByAppointment(int appointmentID)
        {
            Gender retVal = Gender.U;
            var appt = context.Appointments.Where(a => a.Id == appointmentID).FirstOrDefault();
            if (appt != null)
            {
                var patient = context.Demographics.Where(d => d.PatientRecordId == appt.PatientRecordId).FirstOrDefault();
                if (patient != null)
                {
                    retVal = patient.gender;
                }
            }
            return retVal;
        }
        public int GetGroupIdByTest(int testID)
        {
            int groupID = 0;

            var group = context.TestGroup.Where(tg => tg.TestId == testID).FirstOrDefault();

            if (group != null)
            {
                groupID = group.GroupId;
            }

            return groupID;
        }


        public WorkSheetData GetWorkSheetData(int appointmentId, int testId)
        {
            List<IEnumerable> resultSets = context.GetDataMultipleResultSets(new List<Func<System.Data.Entity.Infrastructure.IObjectContextAdapter, DbDataReader, IEnumerable>>
            {
                (adapter, reader) => adapter.ObjectContext.Translate<Appointment>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<TestGroup>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<Group>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<Test>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<PracticeDoctor>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<ExternalDoctor>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<AppointmentProvider>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<bool>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<MeasurementCategory>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<MeasurementOperator>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<MeasurementRangeType>(reader).ToList(),
                (adapter, reader) => adapter.ObjectContext.Translate<ReportPhrase>(reader).ToList()
            }
            , "Get_Measurement_Index_DataBundle_V2"
            , new List<SqlParameter>
            {
                new SqlParameter("appointmentId",appointmentId),
                new SqlParameter("testId",testId)
            });

            WorkSheetData result = new WorkSheetData
            {
                Appointment = CerebrumContext.GetFromResultSetOrDefault<Appointment>(resultSets[0]),
                TestGroup = CerebrumContext.GetFromResultSetOrDefault<TestGroup>(resultSets[1]),
                Group = CerebrumContext.GetFromResultSetOrDefault<Group>(resultSets[2]),
                Test = CerebrumContext.GetFromResultSetOrDefault<Test>(resultSets[3]),
                PracticeDoctor = CerebrumContext.GetFromResultSetOrDefault<PracticeDoctor>(resultSets[4]),
                ExternalDoctor = CerebrumContext.GetFromResultSetOrDefault<ExternalDoctor>(resultSets[5]),
                AppointmentProvider = CerebrumContext.GetFromResultSetOrDefault<AppointmentProvider>(resultSets[6]),
                HasRawData = CerebrumContext.GetFromResultSetOrDefault<bool>(resultSets[7]),
                MeasurementCategoryList = (resultSets[8] as List<MeasurementCategory>),
                MeasurementOperatorList = (resultSets[9] as List<MeasurementOperator>),
                MeasurementRangeTypeList = (resultSets[10] as List<MeasurementRangeType>),
                ReportPhraseList = (resultSets[11] as List<ReportPhrase>)
            };

            return result;
        }


        public int UpdatePhraseVisible(int practiceId, int phraseId, int userId, string ipaddress)
        {
            try
            {
                var phraseByP = context.ReportPhraseByPractice.Where(a => a.ReportPhraseID == phraseId && a.PracticeID == practiceId).FirstOrDefault();
                if (phraseByP != null)
                {
                    phraseByP.Visible = !phraseByP.Visible;
                    return context.SaveChanges(userId, ipaddress);
                }
                else
                {
                    context.ReportPhraseByPractice.Add(
                        new ReportPhraseByPractice
                        {
                            PracticeID = practiceId,
                            ReportPhraseID = phraseId,
                            order = 0,
                            Visible = false
                        }
                        );
                    return context.SaveChanges(userId, ipaddress);
                }

                return 0;
            }
            catch (Exception e)
            {
                _log.Error("Error PhraseVisible: Message - " + e.Message);
                if (e.InnerException != null) _log.Error("Error PhraseVisible: Message - " + e.InnerException.Message);
                return 0;
            }
        }
        private BullsEyeDto GetBullsEyeInfo(int appointmentId, int testId)
        {
            BullEye dbBullEye = context.BullEye.FirstOrDefault(s => s.appointmentId == appointmentId && s.testId == testId);

            BullsEyeDto dbBullsEyeInfo = (from a in context.Appointments
                                          join b in context.AppointmentTests on a.Id equals b.AppointmentId
                                          join c in context.Tests on b.TestId equals c.Id
                                          join tg in context.TestGroup on c.Id equals tg.TestId
                                          join grp in context.Group on tg.GroupId equals grp.Id
                                          join d in context.Demographics on a.PatientRecordId equals d.PatientRecordId
                                          where a.Id == appointmentId && b.TestId == testId
                                          select new BullsEyeDto { AppointmentId = a.Id, TestId = b.TestId, TestShortName = c.testShortName, PatientFirstName = d.firstName, PatientLastName = d.lastName, BullsEyeSvgNumber = grp.BullsEyeSvgNumber }).FirstOrDefault();

            dbBullsEyeInfo.BullsEyeSegment = dbBullEye?.segments;

            return dbBullsEyeInfo;
        }
    }
}
