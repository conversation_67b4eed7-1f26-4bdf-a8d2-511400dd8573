﻿using Cerebrum.VirtualVisit;
using Cerebrum.VirtualVisit.Data;
using Cerebrum.VirtualVisit.Seedwork;
using Cerebrum30.Areas.Schedule.DataAccess;
using Cerebrum30.Areas.VirtualVisit.Controllers;
using Cerebrum30.Areas.VirtualVisit.Models;
using Cerebrum30.DAL.DataAccess.Repositories;
using Cerebrum30.Filters;
using log4net;
using System;
using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

using Cerebrum30.Utility;
namespace Cerebrum30.Controllers
{
    /// <summary>
    /// Virtual visit Api for patient side.
    /// </summary>
    [AllowAnonymous]
    [LogUserInfo]
    [ApiController]
    [Route("api/[controller]")]
    public class VirtualVisitController : ControllerBase
    {
        readonly ILog _log = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private IVirtualVisitBLL _virtualVisitBll;
        private readonly IUOWAppointments _uofwAppointments;
        public VirtualVisitController(IUOWVirtualVisit<VirtualVisitRoomRepository, VirtualVisitInvitationRepository, VirtualVisitLogRepository> vvUow, IUOWAppointments uofwAppt)
        {
            _virtualVisitBll = new Cerebrum.VirtualVisit.VirtualVisitBLL<VirtualVisitRoomRepository, VirtualVisitInvitationRepository, VirtualVisitLogRepository>(vvUow);
            _virtualVisitBll.OnChangeSaved(() => { return _virtualVisitBll.SaveChanges(); });
            _uofwAppointments = uofwAppt;
        }


        [Route("api/VirtualVisit/VideoConference/GetPreJoinRoomSettings")]
        [HttpPost]
        public IActionResult GetPreJoinRoomSettings(GetPreJoinRoomSettingsModel inputs)
        {
            Guid roomId;
            InviteType type;
            if (ModelState.IsValid)
            {
                string id = ReadRoomId(inputs.id, out type);
                if (Guid.TryParse(id, out roomId))
                {
                    IRoomSettings result = _virtualVisitBll.GetById(roomId, type);
                    if (result != null)
                    {
                        if (!_virtualVisitBll.IsPracticeEnabled(result.PracticeId)) return NotFound("We cannot find your appointment, please contact administrators.");

                        if (result.ValidationCode.Equals(inputs.pin))
                        {
                            IAppointment apt = _virtualVisitBll.GetAppointment(result.AppointmentId);
                            PreJoinRoomStatus roomStatus = _virtualVisitBll.CheckRoomStatus(result, apt);

                            switch (roomStatus)
                            {
                                case PreJoinRoomStatus.Cancelled:
                                    return StatusCode(408, "The appointment have been cancelled.");
                                case PreJoinRoomStatus.AlreadyEnded:
                                    return StatusCode(410, "Your appointment already ended at " + result.End);
                                case PreJoinRoomStatus.NotStarted:
                                    return StatusCode(403, "Please come back later, appointment will be starting at " + result.Start);
                                default:
                                    if (string.IsNullOrEmpty(result.PatientName)) result.PatientName = apt.PatientFullName;
                                    result.DoctorName = apt.AppProvider;
                                    result.ValidationCode = string.Empty; // for security reason, do not return the code via api
                                    return Ok(result);
                            }
                        }
                        else
                        {
                            //return Content(HttpStatusCode.Unauthorized, "PIN is incorrect, please try again."); // cannot use 401 because our site is enforcing a 302 redirection
                            return StatusCode(417, "PIN is incorrect, please try again.");
                        }
                    }
                    else
                    {
                        _log.Error("VirtualVisitController.GetPreJoinRoomSettings - Room not found:" + inputs.id);
                        return NotFound("Room not found");
                    }
                }
                else
                {
                    _log.Error("VirtualVisitController.GetPreJoinRoomSettings - Wrong parameters, roomId is bad format:" + inputs.id);
                    return BadRequest("Wrong parameters");
                }
            }
            else
            {
                _log.Error("VirtualVisitController.GetPreJoinRoomSettings - missing ID or PIN");
                return BadRequest("Missing parameters");
            }
        }


        [Route("api/VirtualVisit/VideoConference/RoomConnected")]
        [HttpPost]
        public IActionResult RoomConnected(RoomConnectedModel inputs)
        {
            Guid roomId;
            InviteType type;
            string id = ReadRoomId(inputs.RoomId, out type);
            if (ModelState.IsValid && Guid.TryParse(id, out roomId))
            {
                var context = HttpContextProvider.Current;
                string ipAddress = context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

                _virtualVisitBll.RoomConnected(roomId, type, inputs.RoomSid, ipAddress);
            }

            return Ok();
        }

        //[EnableCors(origins: "https://pvv.mycerebrum.com", headers: "*", methods: "OPTIONS, POST")]
        [Route("api/VirtualVisit/VideoConference/Token")]
        [HttpPost]
        public IActionResult Token(VideoConferenceController.PostToken inputs)
        {
            Guid roomId;
            InviteType type;
            string id = ReadRoomId(inputs.Identity, out type);
            if (ModelState.IsValid && Guid.TryParse(id, out roomId))
            {
                if (string.IsNullOrWhiteSpace(inputs.UserType) || (inputs.UserType != "doctor" && string.IsNullOrWhiteSpace(inputs.ValidationCode)))
                {
                    throw new ArgumentNullException("Missing userType / validation-code");
                }
                else
                {
                    IRoomSettings room = _virtualVisitBll.GetById(roomId, type);

                    if (room == null)
                    {
                        return NotFound("room-not-exist");
                    }
                    //else if (inputs.ValidationCode != room.ValidationCode || inputs.Identity != room.Id.ToString())
                    //{
                    //    //return Content(HttpStatusCode.Unauthorized, "invalid-vadliation-code"); // cannot use 401 because our site is enforcing a 302 redirection
                    //    return Content(HttpStatusCode.ExpectationFailed, "invalid-vadliation-code");
                    //}
                    else
                    {
                        if (!_virtualVisitBll.IsPracticeEnabled(room.PracticeId)) return NotFound("Virtual visit is not available, please contact administrators.");
                        string result = _virtualVisitBll.GenerateTwilioToken(room.Id.ToString(), inputs.UserName, room.StartDateTime.AddMinutes(room.DurationInMinute));
                        return Ok(result);
                    }
                }
            }
            else
            {
                return BadRequest("Invalid parameters");
            }
        }

        private string ReadRoomId(string id, out InviteType userType)
        {
            userType = InviteType.Patient;
            string result = id;
            if (result.StartsWith("g_"))
            {
                userType = InviteType.Guest;
                result = id.Replace("g_", string.Empty);
            }

            return result;
        }


        /// <summary>
        /// See ticket 12605 - https://redmine.chrc.net:444/issues/12605
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        [Route("api/VirtualVisit/VideoConference/PatientArrivedLobby")]
        [HttpPost]
        public IActionResult PatientArrivedLobby(PatientArrivedLobbyModel inputs)
        {
            Guid roomId;
            InviteType type;
            string id = ReadRoomId(inputs.id, out type);
            if (ModelState.IsValid && Guid.TryParse(id, out roomId))
            {
                IRoomSettings room = _virtualVisitBll.GetById(roomId, type);
                if (room != null)
                {
                    if (!_virtualVisitBll.IsPracticeEnabled(room.PracticeId)) return NotFound("Virtual visit is not available, please contact administrators.");

                    if (room.ValidationCode.Equals(inputs.pin))
                    {
                        _uofwAppointments.appointmentsBLL.UpdateArrivalTime(room.AppointmentId, DateTime.Now.ToShortTimeString(), "Virtual", Config.Cerebrum.PlaceHolderUsername, _virtualVisitBll.GetPlaceHolderUserId(), (new Models.GetCurrentIPAddress()).ToString());

                        return Ok(new { Message = type.ToString() + " arrived" });
                    }
                    else
                    {
                        //return Content(HttpStatusCode.Unauthorized, "Invalid validation code"); // cannot use 401 because our site is enforcing a 302 redirection
                        return StatusCode(417, "Invalid validation code");
                    }
                }
                else
                {
                    //@TODO add codes for guests - the Id will VirtualVisitInviteGuests instead of Room
                    //@TODO add codes for guests
                    return NotFound("Room not found");
                }
            }
            else
            {
                return BadRequest("Wrong parameters");
            }
        }
    }
}
