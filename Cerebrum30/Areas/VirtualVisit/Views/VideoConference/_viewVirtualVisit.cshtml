@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.VirtualVisit.Seedwork.IRoomSettings
@{

<script>
    $(function () {
        $(".autocomp").autocomplete({
            source: participants,
            minLength: 2,
@* }); *@
@* }); *@
</script>
@Html.ModalHeader(header)
<div class="modal-body">
    <div class="form-horizontal">
        <div>Created: @Model.CreatedDate.ToString("g")</div>
        <div class="margin-bottom3" style="margin-left:3px;">
            <input type="checkbox" onclick="return false;" @strChecked  disabled="disabled" />
            &nbsp;Screen Sharing
        </div>
        <div style="border: 1px solid #a2bfcb; padding-left: 3px; padding-bottom: 2px; margin-bottom: 6px; border-radius: 5px 5px !important;">
            <div style="font-size:10px;">Patient Info</div>
            @if (!consentEmail)
            {
                <div class="margin-bottom3"><span class="custom-bold alert-danger">no email consent on file</span></div>
            }
            @if (!string.IsNullOrEmpty(@Model.InvitedPatientEmail))
            {
                <div class="margin-bottom3">Email: @Model.InvitedPatientEmail</div>

            @if (!string.IsNullOrEmpty(@Model.InvitedPatientPhone))
            {
                <div class="margin-bottom3">Phone: @Model.InvitedPatientPhone</div>

            <div class="margin-bottom3">PIN: @Model.ValidationCode</div>
            <div class="margin-bottom3">Link: <a href="@Model.PatientInviteLink" target="_blank">@Model.PatientInviteLink</a></div>
        </div>

        @foreach (var item in @Model.Participants)
        {
@* y++; *@

            if (!string.IsNullOrEmpty(item.Email))
            {
@* list.Add("email"); *@

            }
            if (!string.IsNullOrEmpty(item.Phone))
            {
@* list.Add("sms"); *@

            <div style="border: 1px solid #a2bfcb; padding-left: 3px; padding-bottom: 2px; margin-bottom: 6px; border-radius: 5px 5px !important;">
                <div style="font-size:10px; display:inline">Participant @y Info</div>&nbsp;&nbsp;&nbsp;
                @if (@Model.StartDateTime > DateTime.Now.AddMinutes(-durationInMinutes) && !isCancelled)
                {
                    <div style="font-size: 10px; display: inline;">
                        <a href="#demo-@y" class="pull-right" title="Remove Participant" data-toggle="collapse" style="font-size: 10px; padding-right:5px;padding-top:5px;"><span class="disabled glyphicon glyphicon-trash text-danger"></span></a>
                        <div id="demo-@y" class="collapse" style="margin-bottom: 6px;">
                            <div>Are you sure you want to remove this participant?</div>
                            <div><label style="vertical-align:middle"><input type="checkbox" id="chk-send-cancellation-@y" checked /></label>&nbsp;<span class="checkbox-text">Send cancellation @checkBoxtext to the removed participant</span></div>
                            <div>
                                <button type="button" data-participant-email="@email" data-participant-phone="@phone" data-participant-id="@item.Id" data-id="@y" data-appointment-id="@Model.AppointmentId" class="btn btn-warning btn-xs btn-delete-participant">Remove</button>

                            </div>
                        </div>
                    </div>

                @if (!string.IsNullOrEmpty(item.Email))
                {
                    <div class="margin-bottom3">
                        Email: @item.Email
                    </div>
                }
                @if (!string.IsNullOrEmpty(item.Phone))
                {
                    <div class="margin-bottom3">
                        Phone: @item.Phone
                    </div>
                }
                <div class="margin-bottom3">
                    PIN: @item.ValidationCode
                </div>
                <div class="margin-bottom3">

                </div>
                <input type="hidden" class="participant-email" value="@item.Email" />
                <input type="hidden" class="participant-phone" value="@item.Phone" />
            </div>

        @if (@Model.Participants.Count == y && y < 4 && @Model.StartDateTime > DateTime.Now.AddMinutes(-durationInMinutes) && !isCancelled)
        {

            <a href="#demo-participant" class="btn btn-link" data-toggle="collapse" style="font-size: 10px;">Invite Participant</a>
            <div id="demo-participant" class="collapse" style="margin-bottom: 6px;">
                <div style="border: 1px solid #a2bfcb; padding-left: 3px; padding-right:5px; padding-bottom: 2px; margin-bottom: 6px; border-radius: 5px 5px !important;">
                    <div>Are you sure you want to invite this participant?</div>
                    <div style="font-size:10px; display:inline">Participant @participantNumber Info</div>
                    <div class="margin-bottom3">
                        @Html.CheckBox("IsEmail", true)
                        @Html.TextBox("Email", "", new { placeholder = "search participant", @class = "autocomp", autocomplete = "off", style = "width:16em" })
                    </div>
                    <div class="margin-bottom3">
                        @Html.CheckBox("IsPhone", false)
                        @Html.TextBox("Phone", "", new { placeholder = "participant phone number", autocomplete = "off", style = "width:8em" })
                        <button type="button" class="btn btn-primary2 btn-xs pull-right btn-add-participant" data-appointment-id="@Model.AppointmentId" data-room-id="@Model.Id">Invite Participant</button>
                    </div>
                </div>
            </div>
            <span id="span-error" class="text-danger"></span>

    </div>
</div>
<div class="modal-footer">
    @if (@Model.StartDateTime > DateTime.Now.AddMinutes(-durationInMinutes) && !isCancelled)
    {
        <button id="btn-resend-invitation-to-all" class="btn btn-primary2 btn-sm btn-resend-invitation-to-all" data-appointment-id="@Model.AppointmentId" >Re-Send invitation to all</button>
    }
    <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
</div>

