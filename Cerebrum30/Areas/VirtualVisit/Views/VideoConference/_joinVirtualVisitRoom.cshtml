@* ﻿@using Cerebrum30.Areas.VirtualVisit.Models; *@
@model Cerebrum.VirtualVisit.Seedwork.IVirtualVisitPractice
@* *@
Check also JavaScript daysheet.js for the form submission handling of 'frm-virtual-visit-create'@* *@

@if (@Model.IsVirtualVisitEnable)
{
<div class="form-group form-group-sm">
<button type="button" class="btn btn-default btn-sm" id="btn-virtualvisit-vp">
<span class="menuText">Virtual Visit</span>
</button>
</div>
<script>
$(function () {
$('#btn-virtualvisit-vp').on('click', function (e) {
@* e.preventDefault(); *@

@* window.open(url,'_blank'); *@
@* }); *@
@* }); *@
</script>

