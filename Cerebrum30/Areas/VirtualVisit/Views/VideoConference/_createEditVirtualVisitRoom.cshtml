@using Cerebrum.ViewModels.Patient
﻿@* *@
    Check also JavaScript daysheet.js for the form submission handling of 'frm-virtual-visit-create'

@* Layout = null;

    if (isError)
    {

    }

@Html.ModalHeader(header)
<div class="modal-body">
    <form class="frm-virtual-visit-create" method="post" role="form" action="VirtualVisit\VideoConference\CreatePreJoinRoom">
        <div class="form-horizontal">
            <span id="span-error" class="text-danger" style="font-size:11px;">@error</span>
            @Html.Hidden("appointmentId", appointmentId)
            @Html.Hidden("start", start)
            @Html.Hidden("roomName", roomName)

            <div class="margin-bottom3" style="margin-left:3px;">@Html.CheckBox("hasScreenSharing", true, attributes) Screen Sharing</div>
            <div style="border: 1px solid #a2bfcb; padding-left: 3px; padding-bottom: 2px; margin-bottom: 6px; border-radius: 5px 5px !important;">
                <div style="font-size:10px;">Patient Info</div>
                @if (!consentEmail)
                {
                    <div class="margin-bottom3"><span class="custom-bold alert-danger">no email consent on file</span></div>
                }
                <div class="margin-bottom3">@Html.CheckBox("isEmail", true, attributes) @Html.TextBox("email", patientEmail, new { placeholder = "patient email address", style = "width:16em" })</div>
                <div class="margin-bottom3">@Html.CheckBox("isPhone", !string.IsNullOrWhiteSpace(patientPhone), attributes) @Html.TextBox("phone", patientPhone, new { placeholder = "patient cell phone number", style = "width:8em" })</div>
            </div>
            @if (!isError)
            {

                {

                    <div id="div-add-more-participants-info-@y-@appointmentId" class="hidden" style="border: 1px solid #a2bfcb; padding-left: 3px; padding-bottom: 2px; margin-bottom: 6px; border-radius: 5px 5px !important;">
                        <div style="font-size:10px;">Participant @y Info <div id="div-delete-participant-@y-@appointmentId" style="display: inline" class="hidden"><a href="javascript:void(null)" data-app-id="@appointmentId" class="pull-right btn-delete-user-guest" title="Remove Participant" style="padding-right:3px;padding-top:3px;"><span class="disabled glyphicon glyphicon-trash text-danger"></span></a></div></div>
                        <div id="div-add-more-participant-email-@y-@appointmentId" class="margin-bottom3">
                            @Html.CheckBox("Participants[" + i + "].IsEmail", true)
                            @Html.TextBox("Participants[" + i + "].Email", "", new { placeholder = "search participant", @class = "autocomp", disabled = "disabled", style = "width:16em" })
                        </div>
                        <div id="div-add-more-participant-phone-@y-@appointmentId" class="margin-bottom3">
                            @Html.CheckBox("Participants[" + i + "].IsPhone", false)
                            @Html.TextBox("Participants[" + i + "].Phone", "", new { placeholder = "participant phone number", autocomplete = "off", disabled = "disabled", style = "width:8em" })
                        </div>
                        <input type="checkbox" class="hidden" id="Prticipants[@i].IsEnabled" name="Participants[@i].IsEnabled" value="true" />*@ <div id="div-delete-participant-@y-@appointmentId" style="padding-left:15px;" class="margin-bottom3 hidden">
                        <button data-app-id="@appointmentId" class="btn btn-link btn-delete-user-guest">Delete Participant</button>
                    </div>
                    </div>

                <div class="form-group form-group-sm">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary2 btn-xs" id="btn-submit-@appointmentId">Send invitation</button>
                        <button type="button" class="btn btn-primary2 btn-xs btn-add-more-user-guest" id="btn-add-more-user-guest-@appointmentId" data-show-user-guest="1" data-app-id="@appointmentId">Invite Participant</button>
                    </div>
                </div>

        </div>
    </form>
</div>
@Html.ModalFooter(isInfoModal: true)

