﻿@model Cerebrum.ViewModels.VirtualVisit.VVSearchReportVM
@{

}
<script src="~/Areas/VirtualVisit/Scripts/virtualvisit.js"></script>

<h2>Virtual Visit Report</h2>
<div class="panel panel-default">
    <div class="panel-heading">Search Report</div>
    <div class="panel-body">
        @{

        }

        @using (Html.BeginForm("Index", "VVReports", new { area = "VirtualVisit" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-search-vv-report", @name = "reportSearchForm", @target = "_blank" }))
        {
            @Html.AntiForgeryToken()

            <div class="form-inline">
                <hr />
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                <div class="form-group">
                    @Html.LabelFor(model => model.FromDate, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.FromDate, new { htmlAttributes = new { @class = "form-control date-picker datepicker", @Value = fromDt } })
                        @Html.ValidationMessageFor(model => model.FromDate, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.ToDate, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.ToDate, new { htmlAttributes = new { @class = "form-control date-picker datepicker", @Value = toDt } })
                        @Html.ValidationMessageFor(model => model.ToDate, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-group">
                        <div class="control-label col-md-3">.</div>
                        <div class="col-md-offset-2 col-md-10">
                            <input type="submit" value="Search" class="btn btn-default btn-search-vvreport" />
                        </div>
                    </div>
                   
                </div>
            </div>

    </div>
</div>
@section Scripts {
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
}