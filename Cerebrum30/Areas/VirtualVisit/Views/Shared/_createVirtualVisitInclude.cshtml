﻿@* *@
    Any pages that have the component to send virtual visit, will need to include this view.@* *@
@* List<VMUser> participants = (List<VMUser>)ViewBag.PracticeUsers; *@

@using Cerebrum.ViewModels.User
<script>

        @if (participants != null)
        {
            foreach (var item in participants)
            {

                {
                    @:{ label: '@item.LastName, @item.LastName (@item.Email)', value: '@item.Email' }
                }
                else
                {
                    @:{ label: '@item.LastName, @item.LastName (@item.Email)', value: '@item.Email' },

@* i++; *@

@* ]; *@

    $(function () {
        $(document).on("click", ".btn-add-more-user-guest", function () {

@* $(this).attr('data-show-user-guest', numberOfExtraUsers + 1); *@
@* $("#div-add-more-participants-info-" + numberOfExtraUsers + "-" + appId).removeClass('hidden'); *@

@* $("#div-delete-participant-" + numberOfExtraUsers + "-" + appId).removeClass('hidden'); *@

@* $("#div-delete-participant-" + i + "-" + appId).addClass('hidden'); *@

@* $(this).hide(); *@

            $(".autocomp").autocomplete({
                source: participants,
                minLength: 2,
@* }); *@
@* }); *@
        $(document).on("click", ".btn-delete-user-guest", function (e) {
@* e.preventDefault(); *@

@* $('#btn-add-more-user-guest-' + appId).show(); *@

@* $('#btn-add-more-user-guest-' + appId).attr('data-show-user-guest', numberOfExtraUsers - 1); *@

@* $("#div-add-more-participants-info-" + i + "-" + appId).addClass('hidden'); *@

@* $("#div-delete-participant-" + deleteId + "-" + appId).removeClass('hidden'); *@
@* }); *@
@* }); *@
</script>
<style type="text/css">
    .popover {
@* width: 235px; *@

    .margin-bottom3 {
@* margin-bottom: 3px; *@

    .margin-bottom8 {
@* margin-bottom: 8px; *@

    .autocomp {
@* position: relative; *@
@* z-index: 9999; *@

    .ui-autocomplete {
@* z-index: 10000 !important; *@

</style>