﻿using Cerebrum3.Infrastructure;
using Cerebrum.Data;
using System;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum30.Utility;
namespace Cerebrum30.Areas.Daysheet.DataAccess
{
    public interface IAppointmentStatusLogRepository : IGenericRepository<AppointmentStatusLog>
    {
    }
    public class AppointmentStatusLogRepository:GenericRepository<CerebrumContext, AppointmentStatusLog>, IAppointmentStatusLogRepository
    {
    }
    public static class AppointmentStatusChangeNotifier
    {
        public static void AppointmentStatusChanged(this Appointment change, AppointmentStatusLogRepository repo)
        {
            var allstatus= repo.FindBy(f => f.AppointmentId == change.Id).ToList();
            if(!allstatus.Any(w=>w.appointmentStatus==change.appointmentStatus))
            {
                var userName = HttpContextProvider.Current.User.Identity.Name;
                repo.Add(new AppointmentStatusLog { AppointmentId = change.Id,userName=userName, changedDateTime = DateTime.Now, appointmentStatus = change.appointmentStatus });
                //repo.Save();
            }            
        }
    }
}