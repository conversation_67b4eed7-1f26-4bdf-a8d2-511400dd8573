﻿@model Cerebrum.ViewModels.ExternalDocument.IndexInitializationReponse
@using Cerebrum30.Helpers
@using Cerebrum.ViewModels.Patient
@{

}

<!--
Very important that _AwareServicesUiLoader is placed in the html header (which this section does).
If not the css cascade layer order will get messed up when certian components inject inline styles
  -->
@section AwareServicesMicroUiLoaderSlot {
    @Html.PartialAsync("_AwareServicesUiLoader")
} {*@ Cerebrum.ViewModels.Patient.VMPatientMenu patientmenu = new Cerebrum.ViewModels.Patient.VMPatientMenu();

    }
@section customcss {
    <style type="text/css">
        #external-document-container {
@* padding-top: 15px; *@
        }

            #external-document-container .form-group {
@* margin-bottom: 6px; *@

            #external-document-container .transparent {
@* opacity: 0; *@

            #external-document-container .info {
@* background-color: #d9edf7; *@

            #external-document-container .clearable {
@* background: #fff url(Content/Images/close-button.gif) no-repeat right -10px center; *@
                padding: 3px 18px 3px 4px; /*  Use the same right padding (18) in jQ!  */
@* border-radius: 3px; *@
@* transition: background 0.4s; *@
            }

                #external-document-container .clearable.x {
@* background-position: right 5px center; *@

                /@* (jQ) Show icon *@/ #external-document-container .clearable.onX {
@* cursor: pointer; *@
                }
                /*  (jQ) hover cursor style  */
                #external-document-container .clearable::-ms-clear {
@* display: none; *@
@* width: 0; *@
@* height: 0; *@
                }
            /@* Remove IE default X *@/ #external-document-container .dropdown-menu, #external-document-container .sub-menu {
@* left: 100%; *@
@* position: absolute; *@
@* top: 0; *@
@* display: none; *@
@* margin-top: -1px; *@

            #external-document-container .dropdown:hover ul, #external-document-container .dropdown:hover ul li:hover ul, #external-document-container .dropdown:hover ul li:hover ul li:hover ul {
@* display: block; *@

                #external-document-container .dropdown:hover ul li ul, #external-document-container .dropdown:hover ul li:hover ul li ul {
@* display: none; *@

        tr.strikeout td:before {
@* content: " "; *@
@* position: absolute; *@
@* display: inline-block; *@
@* padding: 5px 10px; *@
@* margin: 0px 15px; *@
@* left: 0; *@
@* border-bottom: 1px solid #111; *@
@* width: 97%; *@

        .btn-popover-container {
@* display: inline-block; *@

        .btn-popover-content {
@* padding-top: 0; *@
@* padding-bottom: 0; *@
@* margin-top: 0; *@
@* margin-bottom: 0; *@
        }

        .btn-popover-container .btn-popover-title, .btn-popover-container .btn-popover-content {
@* display: none; *@

        .popover-pointer {
@* cursor: pointer; *@

        #external-document-container {
@* margin-bottom: 57px; *@

        #contactManagerMessage, #contactManagerMessageOld {
@* min-height: 60px !important; *@

        .padding-top-4 {
@* padding-top: 4px; *@

        .margin-right-20 {
@* margin-right: 20px; *@

        .pointer {
@* cursor: pointer; *@

        .rotate-0 {
@* transform-origin: center center; *@

        .rotate-90 {
@* transform: rotate(90deg); *@
@* -o-transform: rotate(90deg); *@
@* -ms-transform: rotate(90deg); *@
@* -webkit-transform: rotate(90deg); *@
@* -moz-transform: rotate(90deg); *@
@* transform-origin: center center; *@

        .rotate-180 {
@* transform: rotate(180deg); *@
@* -o-transform: rotate(180deg); *@
@* -ms-transform: rotate(180deg); *@
@* -webkit-transform: rotate(180deg); *@
@* -moz-transform: rotate(180deg); *@
@* transform-origin: center center; *@

        .rotate-270 {
@* transform: rotate(270deg); *@
@* -o-transform: rotate(270deg); *@
@* -ms-transform: rotate(270deg); *@
@* -webkit-transform: rotate(270deg); *@
@* -moz-transform: rotate(270deg); *@
@* transform-origin: center center; *@

    </style>

    <link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />
    @* <link rel="stylesheet" href="Content/jquery-ui-1.12.1.min.css" /> *@
    <link rel="stylesheet" type="text/css" href="/Content/tailwinds.css"></link>

@section scripts {
    <script type="text/javascript" src="~/Areas/ExternalDocument/Scripts/Index.js"></script>
    <script type="text/javascript" src="~/Areas/Schedule/Scripts/sharedFunctions.js"></script>
    <script type="text/javascript" src="~/Areas/Schedule/Scripts/appointments.js"></script>
    <script src="~/Areas/ExternalDocument/Scripts/ExternalDocument.js"></script>
    <script type="text/javascript" src="~/Scripts/pdfobject.js"></script>    @*http://miorepository.altervista.org/pdfobject.js*@
    <script type="text/javascript">
@* var listCriticalResources; *@
@* var documentCriticalResources; *@

        @if (string.IsNullOrEmpty(@Model.errorMessage))
        {
            @:initOnReady = true;
            @:totalItems = @Model.documentList.totalRow;
            @:listCriticalResources = @Html.Raw(Json.Serialize(@Model.documentList.criticalResources));
            @:detailOfficeId = @Model.documentList.documentDetail.detailOfficeId;

            {
                @:detailOfficeId = @Model.documentList.documentDetail.faxHrm.officeId;
            }
            @:documentCriticalResources = @Html.Raw(Json.Serialize(@Model.documentList.documentDetail.criticalResources));
            @:requisitionTypes = @Html.Raw(Json.Serialize(@Model.requisitions));

                foreach (var d in @Model.categories) {
                    @:categories.push(["@d.Item1", "@d.Item2", "@d.Item3"]);
                }

            {
                @:preSelectedRecipients = @Html.Raw(Json.Serialize(@Model.documentList.documentDetail.contactManager.taskMessage.recipients));

                {
                    foreach (var userType in @Model.documentList.documentDetail.contactManager.userTypes)
                    {
                        @:userTypes.push({ value: "@userType.value", text: "@userType.text" });
                    }
                }

                {
                    foreach (var user in @Model.documentList.documentDetail.contactManager.users)
                    {
                        @:users.push({userType: "@user.userType.ToString()", officeId: "@user.officeId", userName: "@user.name", userId: "@user.id"});
                    }
                }

                {
                    if (!string.IsNullOrEmpty(@Model.documentList.documentDetail.contactManager.taskMessage.taskMessageId))
                    {
                        @:taskId = @Model.documentList.documentDetail.contactManager.taskMessage.taskId;
                        @:taskMessageId = @Model.documentList.documentDetail.contactManager.taskMessage.taskMessageId;
                    }

            foreach (var accessData in @Model.clinicServerAccessDatas)
            {
                @:clinicServerAccessDatas.push({officeId: "@accessData.officeId.ToString()", clinicServerUrl: "@accessData.clinicServerUrl", clinicServerToken: "@Html.Raw(accessData.clinicServerToken.Replace("\"", "\\\""))"});

    </script>

@* preload appointment create view  * <script type="text/javascript">*@
    $(function () {*@ loadAppScheduleModal(@Model.practiceId,@Model.officeId);
@* }); *@
</script>
<div id="external-document-container">
    <!-- class="col-sm-12" -->
    @if (string.IsNullOrEmpty(@Model.errorMessage))
    {

        if (@Model.includedMarkSeen)
        {

        }
        using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()

            <div class="row">
                <div class="col-sm-6">
                    <div id="patientInfo" name="patientInfo" style="margin-top: -7px; margin-bottom:15px" class="__MM">
                        @if (@Model.documentList != null && @Model.documentList.documentDetail != null)
                        {
                            <div class="btn-popover-container ">
                                <span class="popover-btn popover-pointer cb-text16 text-primary margin-right-20" style="font-size: 22px;"><span class="bold"> Patient: </span>@Model.documentList.documentDetail.patientName</span>
                                <div class="btn-popover-title">
                                    <span class="default-text-color">e-Chart</span>
                                </div>
                                <div class="btn-popover-content margin-right-20">
                                    @* TODO: Convert Html.RenderAction to ViewComponent - Convert Html.RenderAction to ViewComponent - @ * </div>
                            </div>
                            <span><b>HIN:</b> @Model.documentList.documentDetail.patientHealthCardNumber &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                            <span><b>DOB:</b>  @Model.documentList.documentDetail.patientDOB - mm/dd/yyyy</span>
                            <span class="margin-right-20" style="font-size: 13px !important">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; @("Last App. " + @Model.documentList.documentDetail.lastAppointment)</span>
                            <span class="" style="font-size: 13px !important"> @(" Next App. " + @Model.documentList.documentDetail.nextAppointment) </span>

                    </div>

                    <div class="row __new">
                        <div class="form-group col-sm-6 __34345456  form3">
                            <div class="form-group-sm row">

                                @Html.LabelFor(model => model.classes, htmlAttributes: new { @class = "col-sm-2 col-form-label" })
                                <div class="col-sm-8">
                                    @Html.DropDownList("classId", new SelectList(@Model.classes, "value", "text"), "All", htmlAttributes: new { @class = "form-control reportClassChanged" })
                                </div>
                            </div>

                            <div class="form-group-sm row">
                                @Html.LabelFor(model => model.categories, htmlAttributes: new { @class = "col-sm-2 col-form-label" })
                                <div class="col-sm-8">
                                    @Html.DropDownList("categoryId", new SelectList(@Model.categories, "Item3", "Item2"), "All", htmlAttributes: new { @class = "form-control searchChanged" })
                                </div>
                            </div>

                                    @Html.LabelFor(model => model.subClasses, htmlAttributes: new { @class = "col-sm-2 col-form-label" })
                                    <div class="col-sm-8">
                                        @Html.DropDownList("subClassId", new SelectList(@Model.subClasses, "Item3", "Item2"), "All", htmlAttributes: new { @class = "form-control searchChanged" })
                                    </div>
                                </div>
                        </div>

                        <div class="form-group col-sm-6 __343446 checkboxgroup">
                            <div class="row">
                                <div class="col-sm-3">
                                    <input id="hl7" name="hl7" type="checkbox" class="searchChanged" checked value="true" />
                                    <label class="control-label" style="padding-left: 8px;">HL7</label>
                                </div>
                                <div class="col-sm-3">
                                    <input id="fax" name="fax" type="checkbox" class="searchChanged" checked value="true" />
                                    <label class="control-label" style="padding-left: 8px;">Fax</label>
                                </div>

                                <div class="col-sm-3">
                                    <input id="hrm" name="hrm" type="checkbox" class="searchChanged" checked value="true" />
                                    <label class="control-label" style="padding-left: 8px;">HRM</label>
                                </div>
                                <div class="col-sm-3">
                                    <input id="scan" name="scan" type="checkbox" class="searchChanged" checked value="true" />
                                    <label class="control-label" style="padding-left: 8px;">Scan</label>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12" style="border: 2px solid #f2f2f2">
                                    <div class="row">
                                        <div class="form-group col-sm-12">
                                            <input id="markSeen" name="markSeen" type="checkbox" @includedMarkSeen class="searchChanged" value="true" />
                                            <label class="control-label" style="padding-left: 8px;">
                                                Include Marked Seen
                                                @if (!(string.IsNullOrWhiteSpace(@Model.practiceDoctorId) || @Model.practiceDoctorId == "0"))
                                                {
                                                    <span>(Maximum range is 3 days)</span>
                                                }
                                            </label>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group col-sm-6">
                                            <div class="form-group-sm row">
                                                <label for="filterMarkSeenStart" class="col-sm-2 col-form-label">From:</label>
                                                <div class="col-sm-8">
                                                    <input type="text" id="filterMarkSeenStart" name="filterMarkSeenStart" class="form-control filterTextChanged" readonly />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group col-sm-6">
                                            <div class="form-group-sm row">
                                                <label for="filterMarkSeenEnd" class="col-sm-2 col-form-label">To:</label>
                                                <div class="col-sm-8">
                                                    <input type="text" id="filterMarkSeenEnd" name="filterMarkSeenEnd" class="form-control filterTextChanged" readonly />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @* <hr /> *@
                    <div class="row spacer-top-15">&nbsp </div>

                    <div class="row" style="padding-left: 15px; padding-right: 15px;">
                        <table class="table" id="externalDocumentList" name="externalDocumentList">
                            <thead>
                                <tr class="form-group-sm">

                                    <th><input id="markSeenAllCheckBox" name="markSeenAllCheckBox" type="checkbox" title="Select / Unselect All" /></th>
                                    <th style="width: 160px;">
                                        <a id="sortByDate" name="sortByDate"><span id="textDocumentDate" name="textDocumentDate">Test Date</span> <span id="imageDocumentDate" name="imageDocumentDate" class="glyphicon glyphicon-arrow-down transparent"></span></a>
                                        <a id="linkDocumentDate" name="linkDocumentDate"><span id="titleDocumentDate" name="titleDocumentDate" class="glyphicon glyphicon-transfer pull-right" title="Display Received Date"></span></a>
                                    </th>
                                    <th><a id="sortByAbnormal" name="sortByAbnormal">Abnormal<span id="imageAbnormal" name="imageAbnormal" class="glyphicon glyphicon-arrow-down transparent"></span></a></th>
                                    @if (@Model.documentList.listBy == 0)
                                    {
                                        <th><a id="sortByPatient" name="sortByPatient">Patient <span id="imagePatient" name="imagePatient" class="glyphicon glyphicon-arrow-down transparent"></span></a></th>
                                        <th><a id="sortByDescription" name="sortByDescription">Description <span id="imageDescription" name="imageDescription" class="glyphicon glyphicon-arrow-down transparent"></span></a></th>
                                    }
                                    else
                                    {
                                        <th><a id="sortByDescription" name="sortByDescription">Description <span id="imageDescription" name="imageDescription" class="glyphicon glyphicon-arrow-down transparent"></span></a></th>
                                        <th><a id="sortByComment" name="sortByComment">Comment <span id="imageComment" name="imageComment" class="glyphicon glyphicon-arrow-down transparent"></span></a></th>

                                    <th></th>
                                </tr>
                                <tr class="form-group-sm _009345">
                                    <td></td>
                                    <td>
                                        <input type="text" id="filterDate" name="filterDate" class="form-control clearable filterTextChanged" />
                                    </td>
                                    <td></td>
                                    <td>
                                        <input type="text" id="filterPatient" name="filterPatient" class="form-control clearable filterTextChanged" />
                                    </td>
                                    <td>
                                        <input type="text" id="filterDescription" name="filterDescription" class="form-control clearable filterTextChanged" />
                                    </td>
                                    <td></td>
                                </tr>
                            </thead>
                            <tbody>
                                @if (@Model.documentList.documents.Count == 0)
                                {
                                    <tr><td colspan='6' class='text-center text-danger __3345' style="background-color: white;"><br /><br /><br /><br /><br /><br /><br />No document found<br /><br /><br /><br /><br /><br /></td></tr>
                                }
                                else
                                {

                                    {

                                        {

                                        }

                                        {

                                        if (!@Model.documentList.documents[i].isActive)
                                        {

                                        <tr class="clickable-row @trClass">
                                            <td>
                                                <input type="hidden" id='@("externalDocumentId" + @i)' name='@("externalDocumentId" + @i)' value="@Model.documentList.documents[i].id">
                                                <input type="hidden" id='@("externalDocumentType" + @i)' name='@("externalDocumentType" + @i)' value="@Model.documentList.documents[i].documentType">
                                                <input type="hidden" id='@("externalDocumentIsActive" + @i)' name='@("externalDocumentIsActive" + @i)' value="@(@Model.documentList.documents[i].isActive ? "1" : "0")">
                                                <input type="hidden" id='@("externalDocumentHrmFacilityId" + @i)' name='@("externalDocumentHrmFacilityId" + @i)' value="@Model.documentList.documents[i].hrmFacilityId">
                                                <input type="hidden" id='@("externalDocumentHrmReportNumber" + @i)' name='@("externalDocumentHrmReportNumber" + @i)' value="@Model.documentList.documents[i].hrmReportNumber">
                                                <span id='@("externalDocumentMarkSeenSpan" + @i)' name='@("externalDocumentMarkSeenSpan" + @i)'>
                                                    @if (@Model.documentList.documents[i].markSeen != null)
                                                    {
                                                        if ((bool)@Model.documentList.documents[i].markSeen)
                                                        {
                                                            <span class="glyphicon glyphicon-eye-open" title="mark seen"></span>
                                                        }
                                                        else
                                                        {

                                                            {
                                                                <input id='@("externalDocumentMarkSeenCheckBox" + @i)' name='@("externalDocumentMarkSeenCheckBox" + @i)' type="checkbox" value='@(i + ":" + @Model.documentList.documents[i].id + ":" + @Model.documentList.documents[i].documentType)' class="clickable-row-but" />
                                                            }
                                                        }

                                                </span>
                                            </td>
                                            <td>
                                                @Html.DisplayFor(modelItem => @Model.documentList.documents[i].testDateTime)
                                            </td>
                                            <td>
                                                @if (@Model.documentList.documents[i].abnormal)
                                                {
                                                    <span class="glyphicon glyphicon-exclamation-sign" style="color: red;" title="abnormal"></span>
                                                }
                                            </td>
                                            @if (@Model.documentList.listBy == 0)
                                            {
                                                <td>
                                                    <div class="btn-popover-container">
                                                        <span class="popover-btn popover-pointer cb-text16 text-primary">@Model.documentList.documents[i].patientName</span>
                                                        <div class="btn-popover-title">
                                                            <span class="default-text-color">Patient Menu</span>
                                                        </div>
                                                        <div class="btn-popover-content">

@* *@
                                                            @{ TODO: Convert Html.RenderAction to ViewComponent - *@ TODO: Convert Html.RenderAction to ViewComponent - Html.RenderAction("GetPatientMenu", "Patients", new { area = "", Id = int.Parse(@Model.documentList.documents[i].patientRecordId) *@ }); } {Html.RenderPartial("_PatientMenu", patientmenu); }
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>@Html.DisplayFor(modelItem => @Model.documentList.documents[i].description)</td>

                                            else
                                            {
                                                <td>@Html.DisplayFor(modelItem => @Model.documentList.documents[i].description)</td>
                                                <td id='@("externalDocumentComment" + @i)' name='@("externalDocumentComment" + @i)' style="white-space: pre-line">@Model.documentList.documents[i].comment.ToString().Replace("<br />", Environment.NewLine)</td>

                                            <td><a href="" onclick="return false;"><span class="glyphicon glyphicon-list-alt pull-right" title="Show Document"></span></a></td>
                                        </tr>

                            </tbody>
                        </table>
                    </div>

                    <div class="row">
                        <div class="col-sm-2 text-left spacer-top-7">
                            <input type="button" id="buttonMarkSeens" name="buttonMarkSeens" value="Mark All Selected Seen" class="btn btn-default btn-sm btn-primary" />
                        </div>

                        <div class="col-sm-10 text-right spacer-top-7">
                            <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
                        </div>
                    </div>
                    <hr />
                    <div class="row  __923756y">
                        <div class="col-sm-6 form-horizontal form-group-sm">

                            @* string readOnly = string.Empty; *  string disabled = string.Empty; *@

                                {

                                    {

                                    }

                            <div class="form-group">
                                <label class="col-sm-3 control-label ">Due Date:</label>
                                <div class="col-sm-9"><input type="text" class="form-control" id="contactManagerDueDate" name="contactManagerDueDate" value="@dueDate" @readOnly @disabled /></div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label ">Urgency:</label>
                                <div class="col-sm-9">
                                    @{
                                        if (string.IsNullOrEmpty(disabled))
                                        {
                                            @Html.DropDownList("contactManagerUrgency", new SelectList(urgencies, "value", "text", urgency), htmlAttributes: new { @class = "form-control " })
                                        }
                                        else
                                        {
                                            @Html.DropDownList("contactManagerUrgency", new SelectList(urgencies, "value", "text", urgency), htmlAttributes: new { @class = "form-control ", @disabled = disabled })

                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label ">Subject:</label>
                                <div class="col-sm-9"><input type="text" id="contactManagerSubject" name="contactManagerSubject" class="form-control" value="@subject" @readOnly /></div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label ">Message:</label>
                                <div class="col-sm-9"><textarea id="contactManagerMessage" name="contactManagerMessage" required rows="9" class="form-control" style="height: auto;"></textarea></div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label "></label>
                                <div class="col-sm-9"><textarea id="contactManagerMessageOld" name="contactManagerMessageOld" readonly rows="3" class="form-control" style="height: auto;">@message</textarea></div>
                            </div>

                                        <div class="col-sm-2 padding-top-4">Due Date: </div>
                                        <div class="col-sm-10"><input type="text" class="form-control" id="contactManagerDueDate" name="contactManagerDueDate" value="@dueDate" @readOnly @disabled /></div>
                                    </div>

                                    <div class="row">
                                        <div class="col-sm-2 padding-top-4">Urgency: </div>
                                        <div class="col-sm-10">
                                            @{
                                            if (string.IsNullOrEmpty(disabled))
                                            {
                                                @Html.DropDownList("contactManagerUrgency", new SelectList(urgencies, "value", "text", urgency), htmlAttributes: new { @class = "form-control " })
                                            }
                                            else
                                            {
                                                @Html.DropDownList("contactManagerUrgency", new SelectList(urgencies, "value", "text", urgency), htmlAttributes: new { @class = "form-control ", @disabled = disabled })

                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-2 padding-top-4">Subject: </div>
                                    <div class="col-sm-10"><input type="text" id="contactManagerSubject" name="contactManagerSubject" class="form-control" value="@subject" @readOnly /></div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-2 padding-top-4">Message: </div>
                                    <div class="col-sm-10"><textarea id="contactManagerMessage" name="contactManagerMessage" required rows="9" class="form-control" style="height: auto;"></textarea></div>
                                </div>

                                <div class="row">
                                    <div class="col-sm-2">&nbsp; </div>
                                    <div class="col-sm-10"><textarea id="contactManagerMessageOld" name="contactManagerMessageOld" readonly rows="3" class="form-control" style="height: auto;">@message</textarea></div>
                                </div>
                        </div>

                        <div class="col-sm-1 form-group-sm"></div>

                        <div class="col-sm-5 form-group-sm">
                            <div class="row">
                                <div class="col-sm-3">Send email: </div>
                                <div class="col-sm-9"><input type="checkbox" id="contactManagerEmail" name="contactManagerEmail" /> (Do not use for PHI or sensitive information)</div>
                            </div>

                            <div class="row">
                                <div class="col-sm-3">Office: </div>
                                <div class="col-sm-9">
                                    @if (offices.Count() == 1)
                                    {
                                        @Html.DropDownList("contactManagerOffice", new SelectList(offices, "value", "text"), htmlAttributes: new { @class = "form-control" })
                                    }
                                    else
                                    {
                                        @Html.DropDownList("contactManagerOffice", new SelectList(offices, "value", "text"), "All offices", htmlAttributes: new { @class = "form-control" })

                                </div>
                            </div>

                            <div class="row spacer-top-10">
                                <div class="col-sm-3">Recipients: </div>
                                <div class="col-sm-9">
                                    <div id="contactManagerRecipient" name="contactManagerRecipient" style="height: 128px; padding-left: 0px; overflow-y: auto;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12 form-group-sm">
                            <input type="button" id="buttonSendMessage" name="buttonSendMessage" value="Alert" class="btn btn-default btn-sm btn-primary " />
                            <input type="button" id="buttonAlertReport" name="buttonAlertReport" value="Alert + Mark Seen" class="btn btn-default btn-sm btn-primary " />
                        </div>
                    </div>
                </div>

                <div class="col-sm-6">
                    @{

                        {
                            if (string.IsNullOrEmpty(@Model.documentList.documentDetail.errorMessage))
                            {

                                {

                                }

                                {

                                else
                                {

                            else
                            {

                    <div class="row" style="margin-left: 16px;">
                        <input type="button" id="buttonMarkSeen" name="buttonMarkSeen" value="Mark Seen" class="btn btn-default btn-sm btn-primary" />
                        <input type="button" id="buttonPdfRotateSave" name="buttonPdfRotateSave" value="Rotate & Save" class="btn btn-default btn-sm btn-primary" />
                        <a id="buttonHRMPrint" tabindex="-1" class="btn btn-default btn-sm btn-primary" style="@(@Model.documentList.documentDetail.documentType == "hrm" && (@Model.documentList.documentDetail.url.ToLower().Contains(".txt") || @Model.documentList.documentDetail.url.ToLower().Contains(".text")) ? "" : "display: none;")" onclick="return hrmPrintClicked();">HRM Print</a>

                        <div class="text-left dropdown" style="display: inline-block;">
                            <input type="button" id="buttonAddRequisition" name="buttonAddRequisition" value="Add Requisition" class="btn btn-default btn-sm btn-primary" />
                            <div id="menuNewRequisitionDiv" name="menuNewRequisitionDiv" style="z-index: 2">
                                <ul class="dropdown-menu text-left" id="menuNewRequisition" name="menuNewRequisition" style="left: 24px; top: 32px;"></ul>
                                <iframe class="iframe-hidden" src="about:blank"></iframe>
                            </div>
                        </div>

                        <span id="faxPreviewDetail" name="faxPreviewDetail" style="padding-top: 7px; padding-left: 32px; @(externalDocumentFaxSrc)">
                            <input id="reportView" name="reportView" type="radio" checked value="preview" />Preview
                            <input id="reportView" name="reportView" type="radio" value="detail" style="margin-left: 24px;" />Update Document
                        </span>
                        <span id="hrmXmlData" name="hrmXmlData">
                            <input id="reportView" name="reportView" type="radio" value="xmlData" style="margin-left: 24px;" />Report XML Data
                        </span>

                        @{

                            if (!string.IsNullOrEmpty(url))
                            {
                                <input type="button" value="Go Back" class="btn btn-default btn-sm" style="float: right; margin-right: 32px;" onclick="location.href='@url';" />
                            }

                    </div>

                    <div class="row">
                        <div class="col-sm-12 text-left">
                            <div class="col-sm-12 text-left">
                                <div id="placeHolder-version" data-hrm-versions="@(@Model.documentList.documentDetail.faxHrm == null ? "" : Json.Serialize(@Model.documentList.documentDetail.faxHrm.hrmVersions))">

                            </div>
                        </div>
                    </div>
                    <br />
                    <div class="row8" style="min-height: 500px;">
                        @*672px;*@
                        <div id="externalDocumentFax" name="externalDocumentFax" class="col-sm-12" data-detail-url="@Model.documentList.documentDetail.url" data-detail-type="@Model.documentList.documentDetail.documentType" style="height: 100%; @externalDocumentFaxSrc">
                            @*height: 100%;*@
                            <input id="hrmConfidentialityStatement" name="hrmConfidentialityStatement" type="hidden" value="@Model.hrmConfidentialityStatement" />
                            <div name="faxPreview" id="faxPreview" style="height: 100%; overflow-y: auto;">
                                @{

                                    {

                                    }

                                <iframe name="filePreviewPdf" id="filePreviewPdf" data-office-id="" data-url="" data-pdf-rotation-enable="@enablePdfRotation" src="" style="width: 100%; height: 800px; z-index: 1; display: none;"></iframe>
                                <div name="filePreviewTif" id="filePreviewTif" style="width: 100%; height: 800px; z-index: 1; display: none;"></div>
                                <pre name="filePreviewTxt" id="filePreviewTxt" class="wrap" style="display: none; width: 100%; height: 400px;"></pre>*@ <div style="width: 100%; height: 400px;">*@<img name="filePreviewImg" id="filePreviewImg" src="" alt="" style="display: none; color:cadetblue; border: cadetblue 1px solid;">@*</div>*@
                            </div>

                            <div name="faxDetail" id="faxDetail" class="form-horizontal form-group-sm" style="display: none; ">
                                @*border: 5px solid red; text-align: left*@@ string documentPatientName = string.Empty; *@

                                    {

                                        if (@Model.documentList.documentDetail.faxHrm.categoryId > 0)
                                        {

                                        }

                                    {

                                <div class="form-group text-right">
                                    <div class="col-sm-12">
                                        <input type="button" id="buttonUpdateDetail_d" name="buttonUpdateDetail_d" value="Update" class="btn btn-default btn-sm btn-primary" />
                                        <input type="button" id="buttonDeleteDetail_d" name="buttonDeleteDetail_d" value="Delete" class="btn btn-default btn-sm btn-primary" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Patient:
                                    </label>
                                    <div class="col-sm-9">
                                        <input id="demographicPatientRecordDoctorId" name="demographicPatientRecordDoctorId" type="hidden" value="@Model.documentList.documentDetail.patientRecordId" />
                                        <input id="documentPatientName" name="documentPatientName" type="hidden" value="@documentPatientName" />
                                        <input id="externalDocumentDetailId" name="externalDocumentDetailId" type="hidden" value="@externalDocumentDetailId" />
                                        <input class="form-control clearable" id="documentPatientNameInput" name="documentPatientNameInput" type="text" value="@documentPatientName" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Description
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentDescription" name="documentDescription" type="text" value="@documentDescription" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Test Date
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentTestDate" name="documentTestDate" type="text" value="@documentTestDate" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-3" for="doctors">
                                        Doctor
                                    </label>
                                    <div class="col-sm-9">
                                        @if (@Model.documentList.documentDetail.faxHrm != null)
                                        {
                                            @Html.DropDownList("documentDoctorId", new MultiSelectList(@Model.documentList.documentDetail.faxHrm.doctors, "value", "text", documentDoctorIds), "Please Select", htmlAttributes: new { @class = "form-control", multiple = "true", disabled = "true", @style = "height: 60px !important;" })
                                        }
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-3" for="officeName">Office</label>
                                    <div class="col-sm-9">
                                        <input class="form-control" id="documentOffice" name="documentOffice" readonly="readonly" type="text" value="@documentOffice" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-3" for="classes">Class</label>
                                    <div class="col-sm-7">
                                        @Html.DropDownList("documentClassId", new SelectList(@Model.classes, "value", "text", documentClassId), "Please Select", htmlAttributes: new { @class = "form-control " })
                                    </div>
                                    <div class="col-sm-2">
                                        <input type="button" id="buttonAddNewReportClass" name="buttonAddNewReportClass" value="Add New Class" class="btn btn-default btn-sm btn-primary " />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-3" for="categories">Category</label>
                                    <div class="col-sm-7">
                                        @Html.DropDownList("documentCategoryId", new SelectList(@Model.categories, "Item3", "Item2", documentCategoryId), "Please Select", htmlAttributes: new { @class = "form-control " })
                                    </div>
                                    <div class="col-sm-2">
                                        <input type="button" id="buttonAddNewReportCategory" name="buttonAddNewReportCategory" value="Add New Category" class="btn btn-default btn-sm btn-primary " @documentCategoryIdDisable />
                                    </div>
                                </div>
                                <div class="form-group" id="divMappingHrmReportToCategory" name="divMappingHrmReportToCategory" style="@divMappingHrmReportToCategoryDisplay">
                                    <label class="control-label col-sm-3">Mapping Report to Category</label>
                                    <div class="col-sm-9">
                                        <input id="mappingHrmReportToCategoryAll" name="mappingHrmReportToCategoryAll" type="radio" value="0" checked> This Version Only
                                        <span style="padding-right: 32px;"></span>
                                        <input id="mappingHrmReportToCategoryAll" name="mappingHrmReportToCategoryAll" type="radio" value="1"> All Reports
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Abnormal
                                    </label>
                                    <div class="col-sm-9">
                                        <input id="documentAbnormal" name="documentAbnormal" type="checkbox" value="true" @documentAbnormal />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Source Author
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentSourceAuthorPhysician" name="documentSourceAuthorPhysician" type="text" value="@documentSourceAuthorPhysician" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Source Facility
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control" id="documentSourceFacility" name="documentSourceFacility" type="text" value="@documentSourceFacility" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Source Facility ID
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control" id="documentSendingFacilityId" name="documentSendingFacilityId" type="text" value="@documentSendingFacilityId" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Sent Date
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control" id="documentSentDate" name="documentSentDate" type="text" value="@documentSentDate" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Creation Date
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentCreationDate" name="documentCreationDate" type="text" value="@documentCreationDate" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Date Received
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentReceivedDate" name="documentReceivedDate" type="text" value="@documentReceivedDate" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Observation Date
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentObservationDate" name="documentObservationDate" type="text" value="@documentObservationDate" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Residual Info
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentResidualInfo" name="documentResidualInfo" type="text" value="@documentResidualInfo" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Report Number
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control" id="documentReportNumber" name="documentReportNumber" type="text" value="@documentReportNumber" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Report Status
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentReportStatus" name="documentReportStatus" type="text" value="@documentReportStatus" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Media
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentMedia" name="documentMedia" type="text" value="@documentMedia" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Report Format
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentReportFormat" name="documentReportFormat" type="text" value="@documentReportFormat" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        HRM Class
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentHrmClass" name="documentHrmClass" type="text" value="@documentHrmClass" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        HRM Sub-Class
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentHrmSubclass" name="documentHrmSubclass" type="text" value="@documentHrmSubclass" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Accompanying Sub-Class
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentAccompanyingSubClass" name="documentAccompanyingSubClass" type="text" value="@documentAccompanyingSubClass" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Accompanying Mnemonic
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control clearable" id="documentAccompanyingMnemonic" name="documentAccompanyingMnemonic" type="text" value="@documentAccompanyingMnemonic" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Accompanying Description
                                    </label>
                                    <div class="col-sm-9">
                                        <input class="form-control" id="documentAccompanyingDescription" name="documentAccompanyingDescription" type="text" value="@documentAccompanyingDescription" readonly />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Mark Seen
                                    </label>
                                    <div class="col-sm-9">
                                        <pre id="documentMarkSeen" style="background-color: rgb(212, 207, 202); display: @(string.IsNullOrEmpty(documentMarkSeen) ? "none;" : "")">@documentMarkSeen</pre>
                                    </div>
                                </div>
                                <div class="form-group text-right">

                                        </div>
                                    <div class="col-sm-12">
                                        <input type="button" id="buttonUpdateDetail" name="buttonUpdateDetail" value="Update" class="btn btn-default btn-sm btn-primary" />
                                        <input type="button" id="buttonDeleteDetail" name="buttonDeleteDetail" value="Delete" class="btn btn-default btn-sm btn-primary" />
                                    </div>
                                </div>
                            </div>
                            <div name="externalDocumentXmlDataView" id="externalDocumentXmlDataView" class="form-group col-sm-12" style="height: 660px; padding-left: 0px; overflow-y: auto; display: none;">
                                <table class="table" id="externalDocumentXmlData" name="externalDocumentXmlData" style="font-size: 14px; width: 100%" data-report-xml-data="@(@Model.documentList.documentDetail.faxHrm == null || @Model.documentList.documentDetail.faxHrm.reportXmlData == null ? "" : Json.Serialize(@Model.documentList.documentDetail.faxHrm.reportXmlData))">
                                    <thead>
                                        <tr style="font-size: 16px; font-weight: bold;">
                                            <td>Tag</td>
                                            <td>Value</td>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        <div id="externalDocumentHL7" name="externalDocumentHL7" data-hl7-report-id="@Model.documentList.documentDetail.id" class="col-sm-12" style="height: 660px; overflow-y: auto;  @externalDocumenthl7">
                            @Html.Raw(System.Net.WebUtility.HtmlDecode(@Model.documentList.documentDetail.hl7))
                        </div>
                        <div id="externalDocumentFhir" class="tailwinds" style="@externalDocumentFhir">
                            <div class="mt-4 ml-4 mr-4">
                                <document-view-panel document-id="@Model.documentList.documentDetail.remoteId"></document-view-panel>
                            </div>
                        </div>
                        <div id="externalDocumentError" name="externalDocumentError" class="text-center text-danger" style="@externalDocumentError">
                            <br /><br /><br /><br /><br /><br /><br /><br />
                            <h1 id="externalDocumentErrorMessage" name="externalDocumentErrorMessage">
                                @Model.documentList.documentDetail.errorMessage
                            </h1>
                            <br /><br /><br /><br /><br />
                        </div>
                    </div>
                    <br />

                    <div class="row8">

                        <div class="form-group">
                            <label class="col-sm-12 control-label">
                                Comment
                            </label>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                @{

                                    {

                                    }

                                <textarea id="externalDocumentComment" name="externalDocumentComment" required rows="3" class="form-control" style="width: 100%; max-width: 100%; height: auto;">@comment</textarea>
                            </div>
                        </div>
                        <div class="form-group text-right ">"
                            <div class="col-sm-12" style="margin-top: 7px;"><input type="button" id="buttonUpdateComment" name="buttonUpdateComment" value="Update Comment + Mark Seen" class="btn btn-default btn-sm btn-primary" /></div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-12 control-label" style="margin-top: 12px;">Other Doctor's Comment</label>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                @{*  comment = string.Empty; *  if (@Model.documentList != null && @Model.documentList.documentDetail != null)
                                    {

                                    }
                                }
                                <textarea id="externalDocumentOtherComment" name="externalDocumentOtherComment" required rows="3" class="form-control" style="width: 100%; max-width: 100%; height: auto;" readonly>@comment</textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div id="requisitionFormDialog" name="requisitionFormDialog" style="display:none;"></div>
            <div id="patientMenuTemplateHeader" name="patientMenuTemplateHeader" style="display:none;">
                <div class="btn-popover-container">
                    <span class="popover-btn popover-pointer cb-text16 text-primary" style="font-size: 22px;"><span class="bold"> Patient: </span> PatientMenuTemplatePatientName</span>
                    <div class="btn-popover-title">
                        <span class="default-text-color">e-Chart</span>
                    </div>
                    <div class="btn-popover-content">
                        @* TODO: Convert Html.RenderAction to ViewComponent - Convert Html.RenderAction to ViewComponent - @ *@
                    </div>
                </div>
            </div>
            <div id="patientMenuTemplateTable" name="patientMenuTemplateTable" style="display:none;">
                <div class="btn-popover-container">
                    <span class="popover-btn popover-pointer cb-text16 text-primary">PatientMenuTemplatePatientName</span>
                    <div class="btn-popover-title">
                        <span class="default-text-color">e-Chart</span>
                    </div>
                    <div class="btn-popover-content">@* *@
                        @* TODO: Convert Html.RenderAction to ViewComponent - Convert Html.RenderAction to ViewComponent - @ *@
                    </div>
                </div>
            </div>
            <div class="modal fade in col-sm-12" id="modal-template-id" z-index="0" style="margin: auto;">
                <div class="modal-dialog col-sm-12" id="modal-template-content-id">
                    <div class="modal-content col-sm-12">
                        <div class="modal-header no-borders col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                            <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                                <h4 class="modal-title" id="modal-template-title-id"></h4>
                            </div>
                            <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        </div>
                        <div class="modal-body col-sm-12" id="modal-template-body-id" style="overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
            <div id="pdfRotateandSave" name="pdfRotateandSave" style="display:none; margin-top: 8px; ">
                <div name="filePreviewPdfDiv" id="filePreviewPdfDiv" style="width: 600px; height: 600px; z-index: 1;"></div>
                <br />
                <div class="text-right">
                    <input type="button" id="buttonPdfRotate" name="buttonPdfRotate" value="Rotate" class="btn btn-default btn-sm btn-primary" onclick="rotatePdf();" />
                    <input type="button" id="buttonPdfSave" name="buttonPdfSave" value="Save" class="btn btn-default btn-sm btn-primary" onclick="savePdf();" />
                    <input type="button" id="buttonPdfCancel" name="buttonPdfCancel" value="Cancel" class="btn btn-default btn-sm btn-primary" data-dismiss="modal" style="margin-left: 60px;" />
                </div>
            </div>

    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>

    <div id="imageLoading_ExternalDocument" name="imageLoading_ExternalDocument" style="position: fixed; left: 30%; top: 40%; display: none;">
        <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
    </div>
</div>
<script>
    $(function () {
        $('#buttonUpdateDetail_d').on('click', function () {
@* $('#buttonUpdateDetail').trigger('click'); *@
@* }); *@

        $('#buttonDeleteDetail_d').on('click', function () {
@* $('#buttonDeleteDetail').trigger('click'); *@
@* }); *@
@* }); *@
</script>

