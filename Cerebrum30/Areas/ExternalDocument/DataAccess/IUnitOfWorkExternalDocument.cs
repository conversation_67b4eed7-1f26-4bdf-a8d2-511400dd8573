﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using Cerebrum30.DAL.Infrastructure;
using Cerebrum.Data;
using Cerebrum30.Utility;
namespace Cerebrum30.Areas.ExternalDocument.DataAccess
{
    /// <summary>
    /// Interface of Unit Of Work for External Document
    /// </summary>
    public interface IUnitOfWorkExternalDocument : IDisposable
    {
        IGenericRepository<Office> OfficeRepo { get; }
        IGenericRepository<OfficeUrl> OfficeUrlRepo { get; }
        IGenericRepository<OfficeUrlType> OfficeUrlTypeRepo { get; }
        IGenericRepository<OfficeFaxFolder> OfficeFaxFolderRepo { get; }
        IGenericRepository<PracticeDoctor> PracticeDoctorRepo { get; }
        IGenericRepository<ExternalDoctor> ExternalDoctorRepo { get; }
        IGenericRepository<Demographic> DemographicRepo { get; }
        IGenericRepository<DemographicsHealthCard> DemographicsHealthCardRepo { get; }
        IGenericRepository<DemographicsMainResponsiblePhysician> DemographicsMainResponsiblePhysicianRepo { get; }
        IGenericRepository<PatientRecord> PatientRecordRepo { get; }
        IGenericRepository<LooseReportCategory> LooseReportCategoryeRepo { get; }
        IGenericRepository<ReportClass> ReportClassRepo { get; }
        IGenericRepository<ReportSubClass> ReportSubClassRepo { get; }
        IGenericRepository<ReportReceived> ReportReceivedRepo { get; }
        IGenericRepository<DoctorsReportReviewed> DoctorsReportReviewedRepo { get; }
        IGenericRepository<Appointment> AppointmentRepo { get; }
        IGenericRepository<HL7Report> HL7ReportRepo { get; }
        IGenericRepository<HL7ReportDoctor> HL7ReportDoctorRepo { get; }
        IGenericRepository<HL7Patient> HL7PatientRepo { get; }
        IGenericRepository<HL7MarkedSeen> HL7MarkedSeenRepo { get; }
        IGenericRepository<HL7ReportVersion> HL7ReportVersionRepo { get; }
    }

    /// <summary>
    /// Generic Repository for External Document
    /// </summary>
    public class ExternalDocumentGenericRepository<T> : IGenericRepository<T> where T : class
    {
        private readonly Microsoft.EntityFrameworkCore.DbSet<T> _dbSet;

        public ExternalDocumentGenericRepository(Microsoft.EntityFrameworkCore.DbSet<T> dbSet)
        {
            _dbSet = dbSet;
        }

        #region IGenericRepository<T> implementation

        public virtual IQueryable<T> AsQueryable()
        {
            return _dbSet.AsQueryable();
        }

        public IEnumerable<T> GetAll()
        {
            return _dbSet;
        }

        public IEnumerable<T> Find(Expression<Func<T, bool>> predicate)
        {
            return _dbSet.Where(predicate);
        }


        public T Single(Expression<Func<T, bool>> predicate)
        {
            throw new NotImplementedException();
        }

        public T SingleOrDefault(Expression<Func<T, bool>> predicate)
        {
            throw new NotImplementedException();
        }

        public T First(Expression<Func<T, bool>> predicate)
        {
            throw new NotImplementedException();
        }

        public T GetById(int id)
        {
            throw new NotImplementedException();
        }

        public void Add(T entity)
        {
            throw new NotImplementedException();
        }

        public void Delete(T entity)
        {
            throw new NotImplementedException();
        }

        public void Attach(T entity)
        {
            throw new NotImplementedException();
        }

        #endregion
    }

    /// <summary>
    /// Unit Of Work for External Document
    /// </summary>
    public class UnitOfWorkExternalDocument : CerebrumContext, IUnitOfWorkExternalDocument
    {
        private readonly ExternalDocumentGenericRepository<Office> _officeRepo;
        private readonly ExternalDocumentGenericRepository<OfficeUrl> _officeUrlRepo;
        private readonly ExternalDocumentGenericRepository<OfficeUrlType> _officeUrlTypeRepo;
        private readonly ExternalDocumentGenericRepository<OfficeFaxFolder> _officeFaxFolderRepo;
        private readonly ExternalDocumentGenericRepository<PracticeDoctor> _practiceDoctorRepo;
        private readonly ExternalDocumentGenericRepository<ExternalDoctor> _externalDoctorRepo;
        private readonly ExternalDocumentGenericRepository<Demographic> _demographicRepo;
        private readonly ExternalDocumentGenericRepository<DemographicsHealthCard> _demographicsHealthCardRepo;
        private readonly ExternalDocumentGenericRepository<DemographicsMainResponsiblePhysician> _demographicsMainResponsiblePhysicianRepo;
        private readonly ExternalDocumentGenericRepository<PatientRecord> _patientRecordRepo;
        private readonly ExternalDocumentGenericRepository<LooseReportCategory> _looseReportCategoryRepo;
        private readonly ExternalDocumentGenericRepository<ReportClass> _reportClassRepo;
        private readonly ExternalDocumentGenericRepository<ReportSubClass> _reportSubClassRepo;
        private readonly ExternalDocumentGenericRepository<ReportReceived> _reportReceivedRepo;
        private readonly ExternalDocumentGenericRepository<DoctorsReportReviewed> _doctorsReportReviewedRepo;
        private readonly ExternalDocumentGenericRepository<Appointment> _appointmentRepo;
        private readonly ExternalDocumentGenericRepository<HL7Report> _hl7ReportRepo;
        private readonly ExternalDocumentGenericRepository<HL7ReportDoctor> _hl7ReportDoctorRepo;
        private readonly ExternalDocumentGenericRepository<HL7Patient> _hl7PatientRepo;
        private readonly ExternalDocumentGenericRepository<HL7MarkedSeen> _hl7MarkedSeenRepo;
        private readonly ExternalDocumentGenericRepository<HL7ReportVersion> _hl7ReportVersionRepo;

        /// <summary>
        /// Constructor of Unit Of Work for External Document
        /// </summary>
        public UnitOfWorkExternalDocument()
        {
            _officeRepo = new ExternalDocumentGenericRepository<Office>(Offices);
            _officeUrlRepo = new ExternalDocumentGenericRepository<OfficeUrl>(OfficeUrls);
            _officeUrlTypeRepo = new ExternalDocumentGenericRepository<OfficeUrlType>(OfficeUrlTypes);
            _officeFaxFolderRepo = new ExternalDocumentGenericRepository<OfficeFaxFolder>(OfficeFaxFolders);
            _practiceDoctorRepo = new ExternalDocumentGenericRepository<PracticeDoctor>(PracticeDoctors);
            _externalDoctorRepo = new ExternalDocumentGenericRepository<ExternalDoctor>(ExternalDoctors);
            _demographicRepo = new ExternalDocumentGenericRepository<Demographic>(Demographics);
            _demographicsHealthCardRepo = new ExternalDocumentGenericRepository<DemographicsHealthCard>(HealthCards);
            _demographicsMainResponsiblePhysicianRepo = new ExternalDocumentGenericRepository<DemographicsMainResponsiblePhysician>(DemographicsMainResponsiblePhysicians);
            _patientRecordRepo = new ExternalDocumentGenericRepository<PatientRecord>(PatientRecords);
            _looseReportCategoryRepo = new ExternalDocumentGenericRepository<LooseReportCategory>(LooseReportCategories);
            _reportClassRepo = new ExternalDocumentGenericRepository<ReportClass>(ReportClasses);
            _reportSubClassRepo = new ExternalDocumentGenericRepository<ReportSubClass>(ReportSubClasses);
            _reportReceivedRepo = new ExternalDocumentGenericRepository<ReportReceived>(ReportsReceived);
            _doctorsReportReviewedRepo = new ExternalDocumentGenericRepository<DoctorsReportReviewed>(DoctorsReportsReviewed);
            _appointmentRepo = new ExternalDocumentGenericRepository<Appointment>(Appointments);
            _hl7ReportRepo = new ExternalDocumentGenericRepository<HL7Report>(HL7Reports);
            _hl7ReportDoctorRepo = new ExternalDocumentGenericRepository<HL7ReportDoctor>(HL7ReportDoctors);
            _hl7PatientRepo = new ExternalDocumentGenericRepository<HL7Patient>(HL7Patients);
            _hl7MarkedSeenRepo = new ExternalDocumentGenericRepository<HL7MarkedSeen>(HL7MarkedSeens);
            _hl7ReportVersionRepo = new ExternalDocumentGenericRepository<HL7ReportVersion>(HL7ReportVersions);
        }

        public IGenericRepository<Office> OfficeRepo
        {
            get
            {
                return _officeRepo;
            }
        }

        public IGenericRepository<OfficeUrl> OfficeUrlRepo
        {
            get
            {
                return _officeUrlRepo;
            }
        }

        public IGenericRepository<OfficeUrlType>  OfficeUrlTypeRepo
        {
            get
            {
                return _officeUrlTypeRepo;
            }
        }

        public IGenericRepository<OfficeFaxFolder> OfficeFaxFolderRepo
        {
            get
            {
                return _officeFaxFolderRepo;
            }
        }
        
        public IGenericRepository<PracticeDoctor> PracticeDoctorRepo
        {
            get
            {
                return _practiceDoctorRepo;
            }
        }
        
        public IGenericRepository<ExternalDoctor> ExternalDoctorRepo
        {
            get
            {
                return _externalDoctorRepo;
            }
        }       

        public IGenericRepository<Demographic> DemographicRepo
        {
            get
            {
                return _demographicRepo;
            }
        }        

        public IGenericRepository<DemographicsHealthCard> DemographicsHealthCardRepo
        {
            get
            {
                return _demographicsHealthCardRepo;
            }
        }

        public IGenericRepository<DemographicsMainResponsiblePhysician> DemographicsMainResponsiblePhysicianRepo
        {
            get
            {
                return _demographicsMainResponsiblePhysicianRepo;
            }
        }

        public IGenericRepository<PatientRecord> PatientRecordRepo
        {
            get
            {
                return _patientRecordRepo;
            }
        }

        public IGenericRepository<LooseReportCategory> LooseReportCategoryeRepo
        {
            get
            {
                return _looseReportCategoryRepo;
            }
        }

        public IGenericRepository<ReportClass> ReportClassRepo
        {
            get
            {
                return _reportClassRepo;
            }
        }

        public IGenericRepository<ReportSubClass> ReportSubClassRepo
        {
            get
            {
                return _reportSubClassRepo;
            }
        }

        public IGenericRepository<ReportReceived> ReportReceivedRepo
        {
            get
            {
                return _reportReceivedRepo;
            }
        }

        public IGenericRepository<DoctorsReportReviewed> DoctorsReportReviewedRepo
        {
            get
            {
                return _doctorsReportReviewedRepo;
            }
        }

        public IGenericRepository<Appointment> AppointmentRepo
        {
            get
            {
                return _appointmentRepo;
            }
        }

        public IGenericRepository<HL7Report> HL7ReportRepo
        {
            get
            {
                return _hl7ReportRepo;
            }
        }

        public IGenericRepository<HL7ReportDoctor> HL7ReportDoctorRepo
        {
            get
            {
                return _hl7ReportDoctorRepo;
            }
        }

        public IGenericRepository<HL7Patient> HL7PatientRepo
        {
            get
            {
                return _hl7PatientRepo;
            }
        }

        public IGenericRepository<HL7MarkedSeen> HL7MarkedSeenRepo
        {
            get
            {
                return _hl7MarkedSeenRepo;
            }
        }

        public IGenericRepository<HL7ReportVersion> HL7ReportVersionRepo
        {
            get
            {
                return _hl7ReportVersionRepo;
            }
        }

        /// <summary>
        /// save change
        /// </summary>
        public void Commit(int userId, string ipaddress)
        {
            //this.SaveChanges(HttpContextProvider.Current.User.Identity.Name);
            this.SaveChanges(userId, ipaddress);
        }
    }
}



