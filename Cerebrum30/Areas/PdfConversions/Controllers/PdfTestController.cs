using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL;
using Cerebrum.BLL.RadDicom;
using Cerebrum.BLL.Schedule;
using Cerebrum.Data;
using Cerebrum.ViewModels.Doctor;
using Cerebrum.ViewModels.Medications;
using Cerebrum30.Areas.Measurements.Controllers;
using Cerebrum30.Areas.Measurements.Models.ViewModels;
using Cerebrum30.Areas.Medications.DataAccess;
using Cerebrum30.Areas.PdfConversions.DataAccess;
using Cerebrum30.Areas.PdfConversions.Models.ViewModels;
using Cerebrum30.Areas.VP.DataAccess;
using Cerebrum30.Controllers;
using Cerebrum30.DAL.DataAccess.Repositories;
using Cerebrum30.Security;
using Cerebrum30.Utility;
using Microsoft.AspNetCore.Hosting;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;

namespace Cerebrum30.Areas.PdfConversions
{

    public class PdfTestController : BaseController
    {
        private Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository _measurementRepo;
        private IBookingConfirmationBLL _bookingConfirm;
        private IRadDicomStudyBLL _radStudyBll;
        private IHttpClientFactory _httpClientFactory;
        private IWebHostEnvironment _webHostEnvironment;
        private ITokenHelper UtilityTokenHelper => new TokenHelper(_httpClientFactory, HttpContext);
        public PdfTestController(CerebrumContext c3db, CerebrumRADContext radDb, IHttpClientFactory httpClientFactory, IWebHostEnvironment webHostEnvironment)
        {
            _measurementRepo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            _radStudyBll = new RadDicomStudyBLL(c3db, radDb);
            _httpClientFactory = httpClientFactory;
            _webHostEnvironment = webHostEnvironment;
        }
        public PdfTestController(IBookingConfirmationBLL bookConf, IRadDicomStudyBLL radStudyBll, IHttpClientFactory httpClientFactory, IWebHostEnvironment webHostEnvironment)
        {
            _measurementRepo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            _bookingConfirm = bookConf;
            _radStudyBll = radStudyBll;
            _httpClientFactory = httpClientFactory;
            _webHostEnvironment = webHostEnvironment;
        }

        //overloading method to correct a typo
        public async Task<byte[]> RetreiveRawData(string path, int appointmentId)
        {
            return await RetrieveRawData(path, appointmentId);
        }

        public async Task<byte[]> RetrieveRawData(string path, int appointmentId)
        {
            return await RetrieveRawData(path, appointmentId, CerebrumUser.UserId, CerebrumUser.PracticeId);
        }

        public async Task<byte[]> RetrieveRawData(string path, int appointmentId, int CallerUserID, int CallerPracticeID)
        {
            var measurementRepo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            var officeID = (new UserRepository()).GetOfficeIDByAppointment(appointmentId);
            string baseUrl = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetDownloadURL(officeID) + Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH + path.Replace("\\", "|");

            string token = await UtilityTokenHelper.GenerateTokenAsync(CallerUserID, CallerPracticeID,
                (new Areas.Measurements.DataAccess.MeasurementRepository()).GetDownloadURL((new UserRepository()).GetOfficeIDByAppointment(appointmentId)));

            string extension = Path.GetExtension(path);
            extension = extension.Replace(".", string.Empty);
            var filename = Path.GetFileNameWithoutExtension(path.Replace("|", "//"));
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            try
            {
                _log.Info($"RetrieveRawData Started - path: {path}, baseUrl: {baseUrl}");
                HttpClient httpClient = _httpClientFactory.CreateClient("download");
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", token);

                var response = await httpClient.GetAsync(baseUrl, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    _log.Info($"RetrieveRawData Succeed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                }
                else
                {
                    _log.Info($"RetrieveRawData Failed - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                }
                var contents = await response.Content.ReadAsByteArrayAsync();

                return contents;
            }
            catch (Exception ex)
            {
                _log.Error($"RetrieveRawData Error - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                if (ex.InnerException != null)
                {
                    _log.Error($"RetrieveRawData Error (Inner) - path: {path}, baseUrl: {baseUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                }
                throw ex;
            }
        }

        public byte[] GetRawOrPDF(int appointmentId, int testId)
        {
            byte[] rawBytes = null;
            Task<byte[]> task = _radStudyBll.RetriveFile(_httpClientFactory, CerebrumUser.PracticeId, appointmentId, testId, CerebrumUser.UserId, RadFile_Extension.pdf);
            task.Wait();
            rawBytes = task.Result;

            if (rawBytes == null || rawBytes.Length == 0)
            {
                var rawFiles = _measurementRepo.GetRawFiles(appointmentId, testId);
                if (rawFiles != null && rawFiles.Count > 0)
                {
                    try
                    {
                        task = RetrieveRawData(rawFiles[0], appointmentId);
                        task.Wait();
                        rawBytes = task.Result;
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex);
                    }
                }
            }

            return rawBytes;
        }

        public byte[] GetRawOrPDF1(int appointmentId, int testId, out int location)
        {
            return GetRawOrPDF1(appointmentId, testId, CerebrumUser.UserId, CerebrumUser.PracticeId, out location);
        }

        public byte[] GetRawOrPDF1(int appointmentId, int testId, int UserId, int PracticeId, out int location)
        {
            location = 0;
            byte[] rawBytes = null;
            Task<byte[]> task = _radStudyBll.RetriveFile(_httpClientFactory, PracticeId, appointmentId, testId, UserId, RadFile_Extension.pdf);
            task.Wait();
            rawBytes = task.Result;
            if (rawBytes != null && rawBytes.Length > 0)
            {
                location = 2;
            }
            else if (rawBytes == null || rawBytes.Length == 0)
            {
                var rawFiles = _measurementRepo.GetRawFiles(appointmentId, testId);
                if (rawFiles != null && rawFiles.Count > 0)
                {
                    try
                    {
                        task = RetrieveRawData(rawFiles[0], appointmentId, UserId, PracticeId);
                        task.Wait();
                        rawBytes = task.Result;
                        location = 1;
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex);
                    }
                }
            }

            return rawBytes;
        }

        public ActionResult Report_WS_Pdf(int appointmentId, int testId, int appointmentTestId = 0)// sql
        {
            HttpContext context = HttpContextProvider.Current;

            int patientID = 0;
            int officeId = 0;
            int practiceId = CerebrumUser.PracticeId;
            var vpRepository = new VPRepository();
            var userRepository = new UserRepository();
            //get patientid 
            officeId = userRepository.GetOfficeIDByAppointment(appointmentId);
            patientID = userRepository.GetPatientByAppointment(appointmentId);

            DoctorNames lstDocNames = new DoctorNames();
            var docLst = vpRepository.GetContactList(appointmentId, lstDocNames, testId);

            lstDocNames.rawDataPageNumber = 1;

            int mainDocID = 1;
            int refDocID = 1;
            int ccDocID = 1;
            int famID = 1;
            int repDocID = 1;

            var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
            var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
            var famDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();
            var repDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

            //TODO, change this to associatedDoctor
            var ccDoc = docLst.Where(e => e.DocType == DocType.Family).FirstOrDefault();//P. CHI

            if (mainDoc != null)
            {
                mainDocID = mainDoc.ID;
            }
            if (refDoc != null)
            {
                refDocID = refDoc.ID;
            }
            if (famDoc != null)
            {
                famID = famDoc.ID;
            }
            if (repDoc != null)
            {
                repDocID = repDoc.ID;
            }

            //Get Bulls eye if any 
            byte[] imageBytes = null;
            ServerLocationProvider locator = new ServerLocationProvider(appointmentId, testId);
            string location = locator.GetLocation(AwareMD.Cerebrum.Shared.Enums.DataType.BullsEye, Module.WS);
            var svgImageFileName = appointmentId.ToString() + "-" + testId.ToString() + ".png";
            string bullsEyefullPath = _webHostEnvironment.WebRootPath + location + svgImageFileName;
            if (System.IO.File.Exists(bullsEyefullPath))
            {
                imageBytes = Helper.ImageToByteArray(System.Drawing.Image.FromFile(bullsEyefullPath));
            }

            int fileLocation = 0;
            bool attachedRawData = userRepository.GetAttachedRawDataFlagByTestID(practiceId, testId);
            byte[] rawBytes = GetRawOrPDF1(appointmentId, testId, out fileLocation);
            byte[] rawBytesPdf = null;
            if (attachedRawData)
                rawBytesPdf = rawBytes;

            //TODO, hardcoded
            int numPages = 1;
            int technId = 1;

            int apptTestID = userRepository.GetAppointmentTestIDAppointment(appointmentId, testId);
            technId = userRepository.GetTechnicianIDByAppointment(apptTestID);

            byte[] contents = null;
            int groupID = userRepository.GetGroupByTestID(testId);

            switch (groupID)
            {
                case 1://E1,E2,E3,E4, E45
                    {
                        #region GetContentFor_EchocardiogramReportPdf  ---------SQL--------------+
                        contents = GetContentFor_EchocardiogramReportPdf(
                         HttpContextProvider.Current,
                         testId,
                         appointmentId,
                         patientID,
                         officeId,
                         imageBytes,
                         numPages,
                         mainDocID,
                         technId,
                         lstDocNames,
                         rawBytesPdf
                         , practiceId);//+ 
                        #endregion
                    }
                    break;
                case 2://SE,SE30 
                    {
                        #region GetContentFor_StressEchoStudyPdf ---------SQL--------------med--- ddd +
                        contents = GetContentFor_StressEchoStudyPdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                                                imageBytes, numPages, repDocID, refDocID, famID, ccDocID
                                                , lstDocNames, rawBytesPdf, practiceId);//+ 
                        #endregion
                    }
                    break;
                case 4://ECG
                    {
                        #region ECG   ---------SQL--------------
                        if (CheckIfPdfFile(rawBytes))
                        {
                            contents = GetContentFor_ECGPdf(context, testId, appointmentId, patientID, officeId,
                                rawBytes, numPages, refDocID, technId
                                , lstDocNames, practiceId, rawBytesPdf, fileLocation);//+
                        }
                        else
                        //if (contents == null)
                        {
                            RepositoryForTemplates rep = new RepositoryForTemplates();
                            string errMsg = "There is not report data for this patient!";
                            if (rawBytes == null)
                            {
                                errMsg = "There is not provided ECG file for this patient!";
                            }

                            byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                            Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                            return File(contents_, "application/pdf");
                        }
                        #endregion
                    }
                    break;
                case 6://Holter
                    {
                        #region GetContentFor_HolterPdf    ---------SQL--------------med---ddd +

                        contents = GetContentFor_HolterPdf(HttpContextProvider.Current, testId, appointmentId,
                                                     patientID, officeId, rawBytesPdf, numPages, mainDocID, technId
                                                     , lstDocNames, practiceId);//+ 
                        #endregion
                    }
                    break;
                case 12:
                    {
                        #region GetContentFor_BP_Pdf    ---------SQL--------------med--- ddd +
                        contents = GetContentFor_BP_Pdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                         rawBytesPdf, numPages, repDocID, refDocID, famID, ccDocID
                         , lstDocNames, practiceId);//+ 
                        #endregion
                    }
                    break;
                case 49:
                    {
                        #region GetContentFor_PedriaticEchoReportPdf   ---------SQL--------------med---

                        contents = GetContentFor_PedriaticEchoReportPdf(HttpContextProvider.Current, testId, appointmentId,
                                 patientID, officeId, rawBytesPdf, numPages, refDocID, repDocID
                                 , lstDocNames, practiceId);//+ 
                        #endregion
                    }
                    break;
                default:
                    {
                        if (testId == 3)
                        {
                            #region GetContentFor_StressEchoStudyPdf   ---------SQL-------------- med----ddd +
                            contents = GetContentFor_StressEchoStudyPdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                                                imageBytes, numPages, repDocID, refDocID, famID, ccDocID
                                                , lstDocNames, rawBytesPdf, practiceId);//+ 
                            #endregion
                        }
                        else
                        {
                            #region GetContentFor_Generic_Pdf    ---------SQL--------------med--- ddd

                            contents = GetContentFor_Generic_Pdf(context, testId, appointmentId, patientID, officeId, rawBytesPdf, numPages, refDocID, technId
                                                            , lstDocNames, practiceId);//+ 
                            #endregion
                        }
                    }
                    break;
            }

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        public byte[] Get_Tests_Repors_Pdf(int appointmentId, int testId, IWebHostEnvironment hostEnvironment, int UserId, int PracticeId)
        {
            HttpContext context = HttpContextProvider.Current;

            int patientID = 0;
            int officeId = 0;
            //int practiceID = 0;

            //get patientid 
            officeId = (new UserRepository()).GetOfficeIDByAppointment(appointmentId);
            patientID = (new UserRepository()).GetPatientByAppointment(appointmentId);

            DoctorNames lstDocNames = new DoctorNames();
            var docLst = (new VPRepository()).GetContactList(appointmentId, lstDocNames, testId);
            lstDocNames.rawDataPageNumber = 1;

            int mainDocID = 1;
            int refDocID = 1;
            int ccDocID = 1;
            int famID = 1;
            int repDocID = 1;

            var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
            var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
            var famDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();
            var repDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

            //TODO, change this to associatedDoctor
            var ccDoc = docLst.Where(e => e.DocType == DocType.Family).FirstOrDefault();

            if (mainDoc != null)
            {
                mainDocID = mainDoc.ID;
                //repDocID = mainDocID;
            }
            if (refDoc != null)
            {
                refDocID = refDoc.ID;
            }
            if (ccDoc != null)
            {
                ccDocID = ccDoc.ID;
            }
            if (famDoc != null)
            {
                famID = famDoc.ID;
            }
            if (repDoc != null)
            {
                repDocID = repDoc.ID;
            }
            byte[] imageBytes = null;
            //Get Bulls eye if any 
            ServerLocationProvider locator = new ServerLocationProvider(appointmentId, testId);
            string location = locator.GetLocation(AwareMD.Cerebrum.Shared.Enums.DataType.BullsEye, Module.WS);
            var svgImageFileName = appointmentId.ToString() + "-" + testId.ToString() + ".png";
            string bullsEyefullPath = _webHostEnvironment.WebRootPath + location + svgImageFileName;
            if (System.IO.File.Exists(bullsEyefullPath))
            {
                imageBytes = Helper.ImageToByteArray(System.Drawing.Image.FromFile(bullsEyefullPath));
            }

            //byte[] rawBytes = GetRawOrPDF(appointmentId, testId);
            //byte[] rawBytes = GetRawOrPDF_MNew(appointmentId, testId, UserId, PracticeId);
            int fileLocation = 0;
            bool attachedRawData = new UserRepository().GetAttachedRawDataFlagByTestID(PracticeId, testId);
            byte[] rawBytes = GetRawOrPDF1(appointmentId, testId, out fileLocation);
            byte[] rawBytesPdf = null;
            if (attachedRawData)
                rawBytesPdf = rawBytes;

            //TODO, hardcoded , int UserId, int PracticeId
            int numPages = 10;
            int technId = 1;

            int apptTestID = (new UserRepository()).GetAppointmentTestIDAppointment(appointmentId, testId);
            technId = (new UserRepository()).GetTechnicianIDByAppointment(apptTestID);

            byte[] contents = null;
            int groupID = (new UserRepository()).GetGroupByTestID(testId);

            switch (groupID)
            {
                case 1: //E1,E2,E3,E4, E45
                        //if (testId == 1 || testId == 6 || testId == 10 || testId == 20) //E1,E2,E3,E4, E45
                    {
                        contents = GetContentFor_EchocardiogramReportPdf(
                                 HttpContextProvider.Current,
                                 testId,
                                 appointmentId,
                                 patientID,
                                 officeId,
                                 imageBytes,
                                 numPages,
                                 mainDocID,
                                 technId,
                                 lstDocNames, rawBytes, PracticeId);//+

                    }
                    break;
                case 2: //SE,SE30                 
                    {
                        contents = GetContentFor_StressEchoStudyPdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                                  rawBytes, numPages, repDocID, refDocID, famID, ccDocID
                                  , lstDocNames, rawBytes, PracticeId);//+
                    }
                    break;
                case 4: //ECG
                    {
                        if (CheckIfPdfFile(rawBytes))
                        {
                            contents = GetContentFor_ECGPdf(context, testId, appointmentId, patientID, officeId,
                                rawBytes, numPages, refDocID, technId
                                , lstDocNames, PracticeId, rawBytesPdf, fileLocation);//+
                        }
                        else
                        {
                            RepositoryForTemplates rep = new RepositoryForTemplates();
                            //string errMsg = "There was problem to download ECG image !";
                            string errMsg = "No ECG tracing is available !";
                            contents = rep.GetErrorPdfBytes(errMsg);
                        }
                    }
                    break;
                case 6: //Holter
                    {
                        contents = GetContentFor_HolterPdf(HttpContextProvider.Current, testId, appointmentId,
                                                  patientID, officeId, rawBytes, numPages, mainDocID, technId
                                                  , lstDocNames, PracticeId);//+
                    }
                    break;
                case 12:
                    {
                        contents = GetContentFor_BP_Pdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                                 rawBytes, numPages, repDocID, refDocID, famID, ccDocID
                                 , lstDocNames, PracticeId);//+

                    }
                    break;
                case 49:
                    {
                        contents = GetContentFor_PedriaticEchoReportPdf(HttpContextProvider.Current, testId, appointmentId,
                                         patientID, officeId, rawBytes, numPages, refDocID, repDocID
                                         , lstDocNames, PracticeId);//+

                    }
                    break;
                default:
                    contents = GetContentFor_Generic_Pdf(context, testId, appointmentId, patientID, officeId, rawBytes, numPages, refDocID, technId
                        , lstDocNames, PracticeId);//+
                    break;
            }

            return contents;
        }

        public byte[] Check_ECG_Tests_Repor_Pdf(int appointmentId, int testId, int UserId, int PracticeId)
        {
            HttpContext context = HttpContextProvider.Current;

            int patientID = 0;
            int officeId = 0;

            //get patientid 
            var appInfo = _radStudyBll.GetPatientAndExamStartInfo(appointmentId, testId);
            officeId = appInfo.OfficeId;
            patientID = appInfo.PatientRecordId;

            DoctorNames lstDocNames = new DoctorNames();
            int refDocID = 1;
            byte[] imageBytes = null;
            int numPages = 10;
            int technId = 1;

            byte[] contents = null;
            int groupID = (new UserRepository()).GetGroupByTestID(testId);

            if (groupID == 4) //ECG
            {
                //imageBytes = GetRawOrPDF_MNew(appointmentId, testId, UserId, PracticeId);
                int fileLocation = 0;
                imageBytes = GetRawOrPDF1(appointmentId, testId, UserId, PracticeId, out fileLocation);
                bool attachedRawData = new UserRepository().GetAttachedRawDataFlagByTestID(PracticeId, testId);
                byte[] rawBytesPdf = null;
                if (attachedRawData)
                    rawBytesPdf = imageBytes;
                if (CheckIfPdfFile(imageBytes))
                {
                    contents = GetContentFor_ECGPdf(context, testId, appointmentId, patientID, officeId,
                        imageBytes, numPages, refDocID, technId
                        , lstDocNames, PracticeId, rawBytesPdf, fileLocation);//+
                }
            }

            return contents;
        }

        public async Task<FileResult> Letter_VP_Pdf(int appointmentId, int appointmentTestId = 0, bool isAmended = false)// sql
        {
            byte[] result = null;
            var testID = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetVPTestID();
            var appInfo = _radStudyBll.GetPatientAndExamStartInfo(appointmentId, testID);

            var lst = _measurementRepo.GetReportHistory(appointmentId, testID, appInfo.PatientRecordId);

            var entry = lst.Where(x =>
                        x.AppointmentId == appointmentId &&
                        x.TestId == testID &&
                        x.SendTypeID == (int)AwareMD.Cerebrum.Shared.Enums.SendType.ClinicServer
                        ).OrderByDescending(y => y.ID).FirstOrDefault();

            if (entry != null)
            {
                var svmap = _webHostEnvironment.WebRootPath + "/Areas/VP/uploads/" + entry.URL;
                MeasurementController cntrl = new MeasurementController(new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository(), new UserRepository(), new VPRepository(), _radStudyBll, _httpClientFactory);
                // TODO: Fix ControllerContext for ASP.NET Core - constructor signature changed
                // In ASP.NET Core, ControllerContext is typically set by the framework or through DI
                // cntrl.ControllerContext = new ControllerContext(this.Request.RequestContext, cntrl);
                cntrl.ControllerContext = this.ControllerContext; // Use current controller's context as temporary fix
                string token = await UtilityTokenHelper.GenerateTokenAsync(CerebrumUser.UserId, CerebrumUser.PracticeId, appInfo.OfficeURL);
                Task<byte[]> task = cntrl.RetrieveFileBytes(entry.URL, appointmentId, token);
                task.Wait();
                result = task.Result;

                if (result == null)
                {
                    RepositoryForTemplates rep = new RepositoryForTemplates();
                    string errMsg = "There is not report data for this patient!";
                    byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                    Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                    return File(result, "application/pdf");
                }
                else
                {
                    Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                    return File(result, "application/pdf");
                }
            }
            else
            {
                if (!isAmended)
                {
                    if ((new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository()).GetTestStatus(appointmentTestId) >= (int)AppointmentTestStatuses.ReportCompleted)
                    {
                        isAmended = true;
                    }
                }

                HttpContext context = HttpContextProvider.Current;
                int vpTestID = (new Areas.VP.DataAccess.VPRepository()).GetVPTestID();

                int practiceID = 0;
                bool isChartNote = false;

                //get patientid 
                practiceID = (new UserRepository()).GetPracticeAppointment(appointmentId);

                var finalEntry = (new VPRepository()).GetLogs(appointmentId, appInfo.PatientRecordId).OrderByDescending(L => L.Id).ToList().FirstOrDefault();

                if (finalEntry != null && finalEntry.Finalized != null && finalEntry.Finalized == 1)
                {
                    isChartNote = true;
                }

                DoctorNames lstDocNames = new DoctorNames();
                lstDocNames.Amended = isAmended;
                lstDocNames.VPLetterType = isChartNote ? VPLetterType.ChartNote : VPLetterType.SendLetter;

                var docLst = (new VPRepository()).GetContactList(appointmentId, lstDocNames, vpTestID);

                int mainDocID = 1;
                int refDocID = 1;
                int ccDocID = 1;

                var mainDoc = docLst.Where(e => e.DocType == DocType.Reporting).ToList().FirstOrDefault();
                var refDoc = docLst.Where(e => e.DocType == DocType.Referral).ToList().FirstOrDefault();
                //TODO
                var ccDoc = docLst.Where(e => e.DocType == DocType.Family).ToList().FirstOrDefault();

                if (mainDoc != null)
                {
                    mainDocID = mainDoc.ID;
                }
                if (refDoc != null)
                {
                    refDocID = refDoc.ID;
                }
                if (ccDoc != null)
                {
                    ccDocID = ccDoc.ID;
                }

                byte[] contents
                    = GetContentForLetterPdf_VP(
                    HttpContext,
                   appointmentId,
                   appInfo.PatientRecordId,
                    appInfo.OfficeId,
                    mainDocID, //main
                    refDocID, //refdoc
                    ccDocID, //ccdoc
                    practiceID,
                    lstDocNames);//+

                if (contents == null)
                {
                    RepositoryForTemplates rep = new RepositoryForTemplates();
                    string errMsg = "There is not report data for this patient!";
                    byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                    Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                    return File(contents_, "application/pdf");
                }
                else
                {
                    Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                    return File(contents, "application/pdf");
                }
            }
        }

        public FileResult Letter_VP_Pdf_Preview(int appointmentId, int appointmentTestId = 0, bool isAmended = false)// sql
        {
            if (!isAmended)
            {
                if ((new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository()).GetTestStatus(appointmentTestId) >= (int)AppointmentTestStatuses.ReportCompleted)
                {
                    isAmended = true;
                }
            }

            HttpContext context = HttpContextProvider.Current;
            int vpTestID = (new Areas.VP.DataAccess.VPRepository()).GetVPTestID();
            appointmentTestId = appointmentTestId == 0 ? (new UserRepository()).GetAppointmentTestID(appointmentId, vpTestID) : appointmentTestId;

            int patientID = 0;
            int officeId = 0;
            int practiceID = 0;

            //get patientid 
            officeId = (new UserRepository()).GetOfficeIDByAppointment(appointmentId);
            patientID = (new UserRepository()).GetPatientByAppointment(appointmentId);
            practiceID = (new UserRepository()).GetPracticeAppointment(appointmentId);

            DoctorNames lstDocNames = new DoctorNames();
            lstDocNames.VPLetterType = VPLetterType.SendLetter;
            lstDocNames.Amended = isAmended;
            var trainee = (new Areas.VP.DataAccess.VPRepository()).GetTraineeUser(appointmentTestId);
            //lstDocNames.traineeID = (new Areas.VP.DataAccess.VPRepository()).GetTrainee(appointmentTestId);
            //lstDocNames.traineeName = (new Areas.VP.DataAccess.VPRepository()).GetTraineeName(appointmentTestId);
            lstDocNames.traineeID = trainee.userId;
            lstDocNames.traineeName = trainee.ExternalDoctorId != null
                ? ((trainee.LastName ?? "") + ", " + (trainee.FirstName ?? "")) : "";
            lstDocNames.traineeExternalDoctorID = trainee.ExternalDoctorId;

            var docLst = (new VPRepository()).GetContactList(appointmentId, lstDocNames, vpTestID);

            int mainDocID = 111111;
            int refDocID = 111111;
            int ccDocID = 111111;

            byte[] contents
                = GetContentForLetterPdf_VP(
                HttpContextProvider.Current,
               appointmentId,
               patientID,
                officeId,
                mainDocID, //main
                refDocID, //refdoc
                ccDocID, //ccdoc
                practiceID,
                lstDocNames);//+

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");

                //TempData["error"] = "Problem to create VP Letter Report. Contact to Administrator!";
                //return RedirectToAction("Error");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        //??? couldn't find where is it used
        public ActionResult ReportFor_VP_Pdf(int appointmentId, int patientId, int officeId,
                                    int mainDocId, int refDocId, int ccDocId, int practiceID) //done 
        {
            HttpContext context = HttpContextProvider.Current;

            byte[] contents = GetContentForLetterPdf_VP(context, appointmentId, patientId, officeId,
                                                        mainDocId, refDocId, ccDocId, practiceID,
                                                        null);//+
            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        public byte[] GetContentForLetterPdf_VP(Microsoft.AspNetCore.Http.HttpContext httpcontext, int appointmentId, int patientId, int officeId,
                                            int mainDocId, int refDocId, int ccDocId, int practiceID, DoctorNames doctorNames, int signatureDoctorExternalId = 0)//done  sql
        {
            VPRepository repo = new VPRepository();

            var daysheetBLL = new DaysheetBLL();
            string ipAddress = IPAddress(Request);
            //this will give you Reason for visit, and orders from flow sheet
            var appointmentInfo = daysheetBLL.GetDaysheetAppointmentItem(appointmentId, CerebrumUser.UserId, ipAddress);

            var UserID = CerebrumUser.UserId;
            var vm = repo.LoadReportData(appointmentId, UserID);

            string reasonForVisit = appointmentInfo == null ? "" : (appointmentInfo.Purpose ?? "");
            if (vm == null) return null;
            List<ValueLabel> orders = new List<ValueLabel>();
            string comments = "";
            if (appointmentInfo != null && appointmentInfo.Order != null
                && appointmentInfo.Order.Orders != null && appointmentInfo.Order.Orders.Count > 0)
            {
                #region orders
                foreach (var item in appointmentInfo.Order.Orders)
                {
                    string str = "";
                    if (item.TestName == null)
                    {
                        str = (item.Name ?? "") + " " + (item.RequisitionTime);
                    }
                    else
                    {
                        str = (item.TestName ?? "") + " " + (item.RequisitionTime);
                    }
                    orders.Add(new ValueLabel { label = item.TestName, value = str });
                }

                comments = appointmentInfo.Order.Comment ?? "";
                #endregion
            }
            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportData data_ = vm.ReportData;
            ReportTypeData data = new ReportTypeData();

            #region patient
            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB_M;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
                data.PtnName1 = p_data.PtnName1;
            }


            #endregion

            #region office
            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            #region doctors

            int practiceId = CerebrumUser.PracticeId;
            data.teamMemberList = vm.ReportData.Logs.Where(tt => tt.Name != null).Select(t => new ValueLabel { label = t.Name, value = t.Name }).ToList();

            //doctorNames.reportingDocId is external doctor Id
            //signatureDoctorExternalId>0? signatureDoctorExternalId:
            int repDocId = signatureDoctorExternalId > 0 ? signatureDoctorExternalId : (doctorNames != null ? doctorNames.reportingDocId : 0);
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);
            //if referral doctor missing family doctor is insted of 
            int subId = doctorNames.refferingDocId;
            if (doctorNames.refferingDocId == 0 && doctorNames.familyDocId != 0)
            {
                subId = doctorNames.familyDocId;
            }
            data.refDoctor = rep.GetRefferalDoctorData(subId);
            //changing under header if main and refferal doctors are the same
            data.isMainAndReffDocsSame = false;
            if (doctorNames.reportingDocId == doctorNames.refferingDocId
                && doctorNames.refferingDocId != 0)
            {
                data.isMainAndReffDocsSame = true;
                if (doctorNames.familyDocId != 0)
                {
                    data.familyDoctor = rep.GetRefferalDoctorData(doctorNames.familyDocId);
                    //remove family doc from the bottom
                    //data.PtnFamilyDoc = "";
                }
            }


            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            //repDocId and traineeDocId are external doctor ids
            byte[] signImg = rep.GetDoctorsSignature(repDocId, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }

            //traineeId is trainees external doctor id
            int traineeId = doctorNames.traineeExternalDoctorID ?? 0;

            if (traineeId != 0)
            {
                byte[] signImgTrainee = rep.GetDoctorsSignature(traineeId, practiceId);
                if (rep.IsValidImage(signImgTrainee))
                {
                    data.signatureTrainee = signImgTrainee;
                }
            }
            #endregion

            data.dateOfExam = doctorNames.VisitDate;
            data.isAmended = doctorNames.Amended;
            data.OpeningStatement = vm.ReportData.OpeningStatement ?? "";
            data.ReasonForVisit = reasonForVisit;
            data.trainee = doctorNames.traineeName ?? "";
            //"Koopman, Wilma" 
            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            data.logoLocation = officeData.logoLocation;

            #endregion

            #region Configuration
            //data.isTable = officeData.isTable;
            data.isTable = false;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion

            data.cpp = data_.CPP;
            data.ReportResult = data_.Phrases;
            data.isImpressionEmpty = true;
            ValueLabel echoImpressinon = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("impression")).FirstOrDefault();
            if (echoImpressinon != null && echoImpressinon.value != null && echoImpressinon.value != "")
            {
                data.isImpressionEmpty = false;
            }
            data.isPhysicalExamEmpty = true;
            ValueLabel physicalExam = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("physical exam")).FirstOrDefault();
            if (physicalExam != null && physicalExam.value != null && physicalExam.value != "")
            {
                data.isPhysicalExamEmpty = false;
            }
            #region old
            //data.isImpressionEmpty = true;
            //ValueLabel echoImpressinon = data_.Phrases.Where(t => t.label.Trim() == "Impression").FirstOrDefault();
            //if (echoImpressinon != null && echoImpressinon.value != null && echoImpressinon.value != "")
            //{
            //    data.isImpressionEmpty = false;
            //}
            //data.isPhysicalExamEmpty = true;
            //ValueLabel physicalExam = data_.Phrases.Where(t => t.label.Trim() == "Physical Exam").FirstOrDefault();
            //if (physicalExam != null && physicalExam.value != null && physicalExam.value != "")
            //{
            //    data.isPhysicalExamEmpty = false;
            //} 
            #endregion

            //data.categories = data_.categories;   "Most Recent Investigations"

            #region test data
            //        vm.ReportData.med_vm.DoseChanged = new List<VMPatientMedication> {
            //            new VMPatientMedication { MedicationName = "aaa", Dose = "1320 mg" },
            //            new VMPatientMedication { MedicationName = "aaa1", Dose = "132 mg" },
            //            new VMPatientMedication { MedicationName = "aaa2", Dose = "1 mg" }
            //};
            //        vm.ReportData.med_vm.Discontinued = new List<VMPatientMedication> {
            //            new VMPatientMedication { MedicationName = "bbb", Dose = "1320 mg" },
            //            new VMPatientMedication { MedicationName = "bbb1", Dose = "132 mg" },
            //            new VMPatientMedication { MedicationName = "bbb2", Dose = "1 mg" } }; 
            #endregion

            #region Medications
            List<VMPatientMedication> listForValueLabel = new List<VMPatientMedication>();
            if (vm.ReportData.med_vm.Added != null && vm.ReportData.med_vm.Added.Count > 0)
            {
                listForValueLabel = vm.ReportData.med_vm.Added;
            }

            if (vm.ReportData.med_vm.DoseChanged != null && vm.ReportData.med_vm.DoseChanged.Count > 0)
            {
                listForValueLabel.AddRange(vm.ReportData.med_vm.DoseChanged);
            }

            //combined added and dosechanged
            data.DoseChanged = GetMedicationValueLabel(listForValueLabel,
                            "Medications added or dose changed at this visit ");
            //data.DoseChanged = GetMedicationValueLabel(vm.ReportData.med_vm.DoseChanged,
            //    "Medications added or dose changed at this visit ");
            data.Discontinued = GetMedicationValueLabel(vm.ReportData.med_vm.Discontinued,
                "Discontinued Medications ");
            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior,
                "Prior Medications ");
            data.Allergies = GetAlergiesValueLabel(vm.ReportData.med_vm.Allergies,
                "Allergies");
            #endregion
            #endregion

            #endregion

            byte[] contents = null;
            if (doctorNames != null && doctorNames.VPLetterType == VPLetterType.ChartNote)
            {//Assessment,Plan,Impression
                #region cdfconfiguration
                List<ValueLabel> diagnosisList = new List<ValueLabel>();
                if (data_.DiagnoseCode.Diagnosis != null && data_.DiagnoseCode.Diagnosis != "")
                {
                    diagnosisList.Add(new ValueLabel { label = data_.DiagnoseCode.Code, value = data_.DiagnoseCode.Diagnosis });
                }
                if (data_.DiagnoseCode2.Diagnosis != null && data_.DiagnoseCode2.Diagnosis != "")
                {
                    diagnosisList.Add(new ValueLabel { label = data_.DiagnoseCode2.Code, value = data_.DiagnoseCode2.Diagnosis });
                }
                if (data_.DiagnoseCode3.Diagnosis != null && data_.DiagnoseCode3.Diagnosis != "")
                {
                    diagnosisList.Add(new ValueLabel { label = data_.DiagnoseCode3.Code, value = data_.DiagnoseCode3.Diagnosis });
                }
                data.diagnosisList = diagnosisList;

                List<ValueLabel> cdfTextList = new List<ValueLabel>();
                List<ValueLabel> cdfNumericList = new List<ValueLabel>();
                if (data_.categories != null && data_.categories.Count > 0)
                {
                    MesurementByCategory CDF = data_.categories.Where(t => t.name == "CDF").FirstOrDefault();
                    MesurementByCategory tempCDF = CDF;
                    if (CDF != null && CDF != null && CDF.mesurements.Count > 0)
                    {
                        #region overdue
                        List<ValueLabel> overdueList = CDF.mesurements.Where(tt => tt.OverDue && tt.value != null && tt.value != "").
                    Select(t => new ValueLabel { label = t.label, value = t.value }).ToList();
                        //    overdueList = new List<ValueLabel>
                        //{ new ValueLabel {label="ov1",value="a" }, new ValueLabel { label = "ov2", value = "a2" } };
                        if (overdueList != null && overdueList.Count > 0)
                        {
                            string ovd = "";
                            foreach (var item in overdueList)
                            {
                                ovd += ", " + item.label;
                            }

                            if (ovd.Substring(0, 1) == ",")
                            {
                                ovd = ovd.Substring(1);
                            }
                            ValueLabel overdue = new ValueLabel { label = "Overdue", value = ovd };

                            data.overdue = overdue;
                        }
                        #endregion

                        #region out of target
                        List<ValueLabel> outTargetList = CDF.mesurements.Where(tt => tt.OverDue && tt.value != null && tt.value != "").
                    Select(t => new ValueLabel { label = t.label, value = t.value }).ToList();
                        //    outTargetList = new List<ValueLabel>
                        //{ new ValueLabel {label="tg1",value="a" }, new ValueLabel { label = "tg2", value = "a2" } };
                        if (outTargetList != null && outTargetList.Count > 0)
                        {
                            string ovT = "";
                            foreach (var item in outTargetList)
                            {
                                ovT += ", " + item.label;
                            }

                            if (ovT.Substring(0, 1) == ",")
                            {
                                ovT = ovT.Substring(1);
                            }
                            ValueLabel outTarget = new ValueLabel { label = "Out of Target", value = ovT };

                            data.outTarget = outTarget;
                        }
                        #endregion


                        foreach (var item in CDF.mesurements)
                        {
                            if (item != null && item.IsText == true && item.value != null && item.value != "")
                            {
                                item.label = item.label.Replace("\n", "");
                                cdfTextList.Add(item);
                            }
                            else
                            {
                                cdfNumericList.Add(item);
                            }
                        }
                        //List<string> labelList = cdfTextList.Select(t => t.label).ToList();
                        //CDF.mesurements.RemoveAll(t => labelList.Contains(t.label));
                        data_.categories.Remove(CDF);
                    }
                }

                data.cdfTextList = cdfTextList;
                data.cdfNumericList = cdfNumericList;
                data.orders = orders;
                data.comments = comments;
                #endregion

                #region configure phraises
                List<ValueLabel> upToPhysicalExamList = new List<Models.ViewModels.ValueLabel>();
                ValueLabel assesment = data_.Phrases.Where(t => t.label.Trim() == "Assessment").FirstOrDefault();
                if (assesment != null)
                { upToPhysicalExamList.Add(assesment); }
                data_.Phrases.Remove(assesment);
                ValueLabel plan = data_.Phrases.Where(t => t.label.Trim() == "Plan").FirstOrDefault();
                if (plan != null)
                { upToPhysicalExamList.Add(plan); }
                if (echoImpressinon != null)
                { upToPhysicalExamList.Add(echoImpressinon); }
                data_.Phrases.Remove(plan);
                data_.Phrases.Remove(physicalExam);
                data_.Phrases.Remove(echoImpressinon);
                //List<ValueLabel> upToPhysicalExamList = new List<Models.ViewModels.ValueLabel> { assesment, plan , echoImpressinon };
                data.upToPhysicalExamList = upToPhysicalExamList;
                data.physicalExam = physicalExam;

                data.mostRecentInvestigations = data_.Phrases.Where(t => t.label.Trim() == "Most Recent Investigations").FirstOrDefault();
                if (data.mostRecentInvestigations != null)
                {
                    data_.Phrases.Remove(data.mostRecentInvestigations);
                }

                #endregion

                data.categories = data_.categories;
                contents = rep.FetchPdfBytesForChartNote_VP(data, printDirectoryPath);
            }
            else
            {
                data.categories = data_.categories;
                contents = rep.FetchPdfBytesForLetter_VP(data, printDirectoryPath);
            }

            if (contents != null)
            {
                List<byte[]> byteArr = new List<byte[]>() { contents };
                contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0);
            }

            var bookingConfirmationMessage = _bookingConfirm.GetMessage(CerebrumUser.PracticeId);
            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult Echocardiogram_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
                            int imageBytesId, int numPages, int refDoc, int technId) //done+
        {
            var context = HttpContext;
            byte[] imageBytes = null;

            imageBytes = GetImageBytes(imageBytesId);

            byte[] rawBytes = null;
            var rawFiles = (new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository()).GetRawFiles(appointmentId, testId);
            if (rawFiles != null && rawFiles.Count > 0)
            {
                try
                {
                    Task<byte[]> task = RetrieveRawData(rawFiles[0], appointmentId);
                    task.Wait();
                    rawBytes = task.Result;
                }
                catch (Exception ex)
                {
                    _log.Error(ex);
                }
            }

            byte[] contents = GetContentFor_EchocardiogramReportPdf(context, testId, appointmentId,
                                        patientId, officeId, imageBytes, numPages, refDoc, technId,
                                        null, rawBytes, CerebrumUser.PracticeId);//+

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");

                //TempData["error"] = "Problem to create Echocardiongram Report. Contact to Administrator!";
                //return RedirectToAction("Error");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        public byte[] GetContentFor_EchocardiogramReportPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId,
            int patientId, int officeId, byte[] imageBytes, int numPages, int repDoc, int technId, DoctorNames doctorNames, byte[] rawBytes, int practiceId)//done++  sql
        {//1, 6,10, 20 --esg'svar rawFiles

            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();

            WS_Report_VM repData = repo.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data

            ReportTypeData data = new ReportTypeData();
            data.bulleseyeImg = imageBytes;
            if (CheckIfPdfFile(rawBytes))
            {
                data.rawData = rawBytes;
            }

            PatientData_VM p_data = rep.GetPatinetDataById(patientId);
            //int practiceId = p_data.practiceId;

            #region office_patient
            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";

            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            //doctorNames.reportingDocId is external doctor Id
            int repDocId = doctorNames != null ? doctorNames.reportingDocId : 0;
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }


            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            data.dateOfExam = (new UserRepository()).GetAppointmentDate(appointmentId).Value.ToShortDateString();
            #region Report Name
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion

            #region config
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }

            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion

            data.ReportResult = data_.Phrases;

            data.categories = data_.categories;

            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");

            #region test configuration
            //1, 7,11, 22
            //if (testId == 1 || testId == 7 || testId == 11 || testId == 16 || testId == 22)
            //{
            //    data.impressionLocation = ImpressionLocation.Top;
            //    data.isTable = true;
            //}
            #endregion

            #endregion

            #endregion

            byte[] contents = null;
            //testId == 1 || testId == 6 || testId == 10 || testId == 9 || testId == 20 || testId == 32
            contents = rep.FetchPdfBytesForEchocardiogramTestReport(data, printDirectoryPath, testId);
            if (contents != null)
            {
                List<byte[]> byteArr = new List<byte[]>() { contents };

                //for testing only
                //data.rawData = FetchImigBytesForReportContext(6, HttpContextProvider.Current);
                PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                if (data.rawData != null)
                {
                    contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, data.rawData, "",
                        (doctorNames == null ? 0 : doctorNames.rawDataPageNumber), data.OfficeName, pageHeader);
                }
                else
                {
                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0, data.OfficeName, pageHeader);
                }
            }

            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult Holter_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
                    int imageBytesId, int numPages, int refDoc, int technId) //done+
        {

            var context = HttpContext;

            byte[] contents = GetContentFor_HolterPdf(context, testId, appointmentId,
                                        patientId, officeId, null, numPages, refDoc, technId
                                        , null, CerebrumUser.PracticeId);//+

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        private byte[] GetContentFor_HolterPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId, int officeId,
                                                            byte[] imageBytes, int numPages, int refDoc, int technId, DoctorNames doctorNames, int practiceId)//done++   sql
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();

            if (CheckIfPdfFile(imageBytes))
            {
                data.rawData = imageBytes;
            }

            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient data
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            //doctorNames.reportingDocId is external doctor Id
            int repDocId = doctorNames != null ? doctorNames.reportingDocId : 0;
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            data.dateOfExam = doctorNames.VisitDate;
            #region Report Name
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion

            //int practiceId = CerebrumUser.PracticeId;
            //int practiceId = p_data.practiceId;
            byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config data
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion
            #region phrases
            //ValueLabel echoImpressinon = data_.Phrases.Where(t => t.label.ToLower().Trim() == "impression" ||
            //                        t.label.ToLower().Trim().Contains("impression")).FirstOrDefault();
            //if (echoImpressinon == null)
            //{
            //    echoImpressinon = data_.Phrases.Where(t => t.label.ToLower().Trim() == "interpretation" ||
            //                                    t.label.ToLower().Trim().Contains("interpretation")).FirstOrDefault();
            //}

            //if (echoImpressinon != null)
            //{
            //    foreach (var item in data_.Phrases)
            //    {
            //        if (item == echoImpressinon)
            //        {
            //            item.type = 1;
            //        }
            //    }

            //    //data_.Phrases.Remove(echoImpressinon);
            //    //data.EchoImpressinon = echoImpressinon.value;
            //}


            //data.EchoImpressinon = "";
            //data.ReasonForTest = "";
            //ValueLabel echoImpressinon = data_.Phrases.Where(t => t.label.ToLower().Trim() == "impression").FirstOrDefault();
            //if (echoImpressinon != null)
            //{
            //    data_.Phrases.Remove(echoImpressinon);
            //    data.EchoImpressinon = echoImpressinon.value;
            //}

            #endregion

            data.ReportResult = data_.Phrases;
            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");

            #region test configuration
            #endregion

            #endregion

            #endregion

            byte[] contents = null;
            //if (testId == 5 || testId == 7 || testId == 8 || testId == 13 || testId == 12)
            contents = rep.FetchPdfBytesForHolterReport(data, printDirectoryPath, testId);
            if (contents != null)
            {
                PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                List<byte[]> byteArr = new List<byte[]>() { contents };
                if (data.rawData != null)
                {
                    contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, data.rawData, "",
                        (doctorNames == null ? 0 : doctorNames.rawDataPageNumber), data.OfficeName, pageHeader);
                }
                else
                {
                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0, data.OfficeName, pageHeader);
                }
            }

            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult ECG_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
                    int imageBytesId, int numPages, int refDoc, int technId) //done+
        {
            HttpContext context = HttpContextProvider.Current;
            byte[] imageBytes = null;
            //imageBytes = FetchImigBytesForReport(imageBytesId);

            //imageBytes = FetchImigBytesForReport(4);
            bool ifItIsPdf = CheckIfPdfFile(imageBytes);
            byte[] contents = null;
            if (ifItIsPdf)
            {
                contents = GetContentFor_ECGPdf(context, testId, appointmentId, patientId, officeId,
                    imageBytes, numPages, refDoc, technId
                    , null, CerebrumUser.PracticeId, null);//+
            }

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                if (imageBytes == null)
                {
                    errMsg = "There is not provided ECG file for this patient!";
                }
                if (!ifItIsPdf)
                {
                    errMsg = "ECG file provided is in wrong format!";
                }
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        private byte[] GetContentFor_ECGPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId, int officeId,
                        byte[] imageBytes, int numPages, int refDoc, int technId, DoctorNames doctorNames, int practiceId, byte[] imageBytesPdf, int fileLocation = 0)//done+   sql
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();
            string appTestDate = rep.GetAppointmentTestDate(appointmentId);

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();
            data.appTestDate = appTestDate;
            data.fileLocation = fileLocation;

            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient data
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnFName = p_data.fName;
                data.PtnLName = p_data.lName;
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");

            if (doctorNames != null)
            {
                data.dateOfExam = doctorNames.VisitDate;
                #region Report Name
                //if (testId == 4)
                //{
                //    data.ReportName = "ECG REPORT";
                //}
                data.ReportName = rep.GetTestNameForReport(testId);
                #endregion

                //int practiceId = p_data.practiceId;
                byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId);
                if (rep.IsValidImage(signImg))
                {
                    data.signature = signImg;
                }
            }

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config data
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion
            #region phrases
            ValueLabel ECG_Results = data_.Phrases.Where(t => t.label.ToLower().Trim() == "ecg results").FirstOrDefault();
            if (ECG_Results != null)
            {
                data.ECG_Results = ECG_Results.value;
            }

            #endregion

            #region mesurement
            //ESG_Report_Mesurements
            data.ESG_Report_Mesurements.PR_Interval_ = "";
            data.ESG_Report_Mesurements.HR_ = "";
            data.ESG_Report_Mesurements.QRS_Duration_ = "";
            data.ESG_Report_Mesurements.QT_Interval_ = "";
            data.ESG_Report_Mesurements.QTc_Interval_ = "";
            data.ESG_Report_Mesurements.QRS_Axsis_ = "";
            data.ESG_Report_Mesurements.P_Axsis_ = "";
            data.ESG_Report_Mesurements.T_Axsis_ = "";
            data.ESG_Report_Mesurements.RR_Interval_ = "";
            MesurementByCategory PR_IntervalCategory = data_.categories.Where(t => t.name.Trim() == "ECG Measurements").Select(t => t).FirstOrDefault();
            if (PR_IntervalCategory != null && PR_IntervalCategory.mesurements != null && PR_IntervalCategory.mesurements.Count > 0)
            {
                data.ESG_Report_Mesurements.PR_Interval_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "PR Interval").
                            Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.HR_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "HR").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.QRS_Duration_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "QRS Duration").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.QT_Interval_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "QT Interval").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.QTc_Interval_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "QTc Interval").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.QRS_Axsis_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "QRS Axis").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.RR_Interval_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "RR Interval").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.P_Axsis_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "P Axis").
                    Select(t => t.value).FirstOrDefault()) ?? "";

                data.ESG_Report_Mesurements.T_Axsis_ = (PR_IntervalCategory.mesurements.Where(t1 => t1.label.Trim() == "T Axis").
                    Select(tt => tt.value).FirstOrDefault()) ?? "";
            }

            #endregion

            #endregion

            #endregion

            byte[] contents = null;
            if (testId == 4 && imageBytes != null)
            {
                //if fileLocation = 1 - uploaded; fileLocation = 2 - from DICOM () ; fileLocation = 0 - there was problem to get file
                //attaching original report to Cerebrum report
                //

                // Add header to final PDF file disregard is it RAW data or DICOM
                //#if DEBUG
                //                contents = rep.FetchPdfBytesForECGReport(data, printDirectoryPath, testId, imageBytes);
                //                if (contents != null)
                //                {
                //                    List<byte[]> byteArr1 = new List<byte[]>() { contents };
                //                    PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                //                    contents = PdfMerger.MergeFilesForServiceC3(byteArr1, "", 0, data.OfficeName, pageHeader);
                //                }
                //#else
                switch (data.fileLocation)
                {
                    case 2: // for DICOM cover existing header with C3 header
                        {
                            contents = rep.FetchPdfBytesForECGReport(data, printDirectoryPath, testId, imageBytes);
                            if (contents != null)
                            {
                                //   List<byte[]> byteArr1 = new List<byte[]>() { contents };
                                //   PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                                //   contents = PdfMerger.MergeFilesForServiceC3(byteArr1, "", 0, data.OfficeName, pageHeader);
#if DEBUG
                                System.IO.File.WriteAllBytes(@"C:\emr\Code\C3Working\!\report.pdf", contents);
#endif
                            }
                        }
                        break;
                    case 1: //for uploaded report adding new page with C3 report
                        //////List<byte[]> byteArr = new List<byte[]>();
                        //get generic report content with ecg
                        contents = GetContentFor_Generic_Pdf(httpcontext, testId, appointmentId, patientId, officeId, imageBytesPdf, numPages, refDoc, technId
                                                    , doctorNames, practiceId);//+ 

                        break;
                }
                //#endif
            }
            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult StressTestStudy_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
            int imageBytesId, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc) //done+
        {
            var context = HttpContext;


            byte[] contents = GetContentFor_StressTestStudyPdf(context, testId, appointmentId, patientId, officeId,
                        null, numPages, repDoc, refDoc, famDoc, ccDoc, CerebrumUser.PracticeId);

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        private byte[] GetContentFor_StressTestStudyPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId,
                        int officeId, byte[] imageBytes, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc, int practiceId)// done++
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();

            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();

            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient data
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.PtnFamilyDoc = p_data.PtnFamilyDoc;
                data.PtnRefDoc = p_data.PtnRefDoc;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            //data.dateOfExam = doctorNames.VisitDate;
            #region Report Name
            //if (testId == 3)
            //{
            //    data.ReportName = "Exercise Stress Study";
            //}
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion

            //int practiceId = CerebrumUser.PracticeId;
            //int practiceId = p_data.practiceId;
            byte[] signImg = rep.GetDoctorsSignature(refDoc, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }
            data.ReportingDoc = rep.GetExtDoctorName(repDoc);
            data.ReferringDoc = rep.GetExtDoctorName(refDoc);
            data.ccDoctor = rep.GetExtDoctorName(ccDoc);

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config data
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion
            #region phraises
            //ValueLabel echoImpressinon = data_.Phrases.Where(t => t.label.Trim() == "Impression Clinical").FirstOrDefault();
            //if (echoImpressinon != null)
            //{
            //    data.EchoImpressinon = echoImpressinon.value;
            //}

            //ValueLabel reasonForTest = GetReasonForTest(data_, testId);
            //if (reasonForTest != null)
            //{
            //    data.ReasonForTest = reasonForTest.value;
            //}

            //ValueLabel reasonForTermination = GetReasonForTermination(data_, testId);
            //if (reasonForTermination != null)
            //{
            //    data.ReasonForTermination = reasonForTermination.value;
            //}
            //ValueLabel symptomsDuringTest = GetSymptomsDuringTest(data_, testId);
            //if (symptomsDuringTest != null)
            //{
            //    data_.Phrases.Remove(symptomsDuringTest);
            //    data.SymptomsDuringTest = symptomsDuringTest.value;
            //}
            //ValueLabel protocol = GetProtocol(data_, testId);
            //if (protocol != null)
            //{
            //    data_.Phrases.Remove(protocol);
            //    data.Protocol = protocol.value;
            //}

            ValueLabel eCG_ExData = GetECG_ExData(data_, testId);
            if (eCG_ExData != null)
            {
                data.ECG_ExData = eCG_ExData.value;
            }

            ValueLabel eCG_RestData = GetECG_RestData(data_, testId);
            if (eCG_RestData != null)
            {
                data.ECG_RestData = eCG_RestData.value;
            }
            #endregion

            #region mesurement
            MesurementByCategory General2Category = data_.categories.Where(t => t.name.Trim() == "General2").
                Select(t => t).FirstOrDefault();
            if (General2Category != null && General2Category.mesurements != null)
            {
                data.Workload = RetrieveMeasurement(General2Category, "Workload");
                data.MaxAgePredictedHR = RetrieveMeasurement(General2Category, "MPHR");
                data.MaxAgePredictedHRAttained = RetrieveMeasurement(General2Category, "Percentage of MPHR achieved");
                data.ExersizeDuration = RetrieveMeasurement(General2Category, "Duration");
            }

            MesurementByCategory Resting_Data_Category = data_.categories.Where(t => t.name.Trim() == "Resting Data3").
                Select(t => t).FirstOrDefault();
            if (Resting_Data_Category != null && Resting_Data_Category.mesurements != null)
            {
                data.HeartRate_RestData = RetrieveMeasurement(Resting_Data_Category, "Resting HR");

                data.BloodPresure_RestData = RetrieveMeasurement(Resting_Data_Category, "Resting Blood Pressure");
            }

            MesurementByCategory Peak_Exercise_Category = data_.categories.Where(t => t.name.Trim() == "Peak Exercise3").
                                    Select(t => t).FirstOrDefault();
            if (Peak_Exercise_Category != null && Peak_Exercise_Category.mesurements != null)
            {
                data.HeartRate_ExData = RetrieveMeasurement(Peak_Exercise_Category, "Max HR");

                data.BloodPresure_ExData = RetrieveMeasurement(Peak_Exercise_Category, "Max BP");
            }

            #endregion

            #region medications
            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");



            var patMedRep = new PatientMedicationRepository();
            List<string> patientMedications = patMedRep.GetPatientMedications(patientId, DateTime.MinValue, DateTime.MaxValue);
            //patientMedications = new List<string>() { "Pereindopril Erburui 4 mg o.d", "Pereindopril Erburui 4 mg o.d", "Pereindopril Erburui 4 mg o.d" };
            string meds = "";
            int n_med = 0;
            if (patientMedications != null)
            {
                foreach (var item in patientMedications)
                {
                    n_med += 1;
                    meds += " " + item + ", ";
                }
            }
            data.medications = (meds.Trim().LastIndexOf(",") > 0 && meds.Trim().LastIndexOf(",") == (meds.Trim().Count() - 1)) ?
                meds.Remove(meds.LastIndexOf(","), 1) : meds;
            #endregion

            #region test configuration
            //2, 15
            if (testId == 3)
            {
                data.impressionBorder = false;//temporary
                data.impressionLocation = ImpressionLocation.Bottom;//temporary
            }
            #endregion

            #endregion

            #endregion

            byte[] contents = null;
            if (testId == 3)
            {
                contents = rep.FetchPdfBytesForStressTestStudyReport(data, printDirectoryPath, testId);
                //for ECG tests imageBytes are bytes of pdf file
                List<byte[]> byteArr = null;
                if (contents != null)
                {
                    if (imageBytes != null && rep.IsValidImage(imageBytes))
                    {
                        byteArr = new List<byte[]>() { contents, imageBytes };
                    }
                    else
                    {
                        byteArr = new List<byte[]>() { contents };
                    }

                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0);
                }
            }


            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult StressEchoStudy_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
                                        int imageBytesId, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc) //done+
        {
            HttpContext context = HttpContextProvider.Current;
            byte[] imageBytes = null;
            imageBytes = FetchImigBytesForReport(1);

            byte[] contents = GetContentFor_StressEchoStudyPdf(context, testId, appointmentId, patientId, officeId,
                        imageBytes, numPages, repDoc, refDoc, famDoc, ccDoc
                        , null, null, CerebrumUser.PracticeId);//+

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        public byte[] GetContentFor_StressEchoStudyPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId, int officeId,
                                        byte[] imageBytes, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc,
                                        DoctorNames doctorNames, byte[] rawBytes, int practiceId)//done++   sql med
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();

            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();
            data.testTypeId = testId;

            //TEMPORARY 
            //byte[] byteArray = GetImageBytes(12);
            //rawBytes = byteArray;
            //doctorNames.rawDataPageNumber = 1;
            ///////////

            data.bulleseyeImg = imageBytes;
            if (CheckIfPdfFile(rawBytes))
            {
                data.rawData = rawBytes;
            }


            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            //doctorNames.reportingDocId is external doctor Id
            int repDocId = doctorNames != null ? doctorNames.reportingDocId : 0;
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            //data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            data.dateOfExam = doctorNames.VisitDate;
            #region Report Name
            //if (testId == 2 || testId == 15)
            //{
            //    data.ReportName = "Exercise Stress Echo Study";
            //}
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion

            //int practiceId = CerebrumUser.PracticeId;
            int practiceId_ = p_data.practiceId;
            byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId_);
            if (signImg != null && rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }

            //data.ccDoctor = rep.GetExtDoctorName(ccDoc);

            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion
            #region Phrasis
            data.EchoImpressinon = "";

            ValueLabel eCG_ExData = GetECG_ExData(data_, testId);
            if (eCG_ExData != null)
            {
                data.ECG_ExData = eCG_ExData.value;
                data_.Phrases.Remove(eCG_ExData);
            }// Study quality   "diastolic function" "chamber sizes"

            ValueLabel eCG_RestData = GetECG_RestData(data_, testId);
            if (eCG_RestData != null)
            {
                data.ECG_RestData = eCG_RestData.value;
                data_.Phrases.Remove(eCG_RestData);
            }

            ValueLabel echo_RestData = GetEcho_RestData(data_, testId);
            if (echo_RestData != null)
            {
                data.Echo_RestData = echo_RestData.value;
                data_.Phrases.Remove(echo_RestData);
            }

            ValueLabel echo_ExData = GetEcho_ExData(data_, testId);
            if (echo_ExData != null)
            {
                data.Echo_ExData = echo_ExData.value;
                data_.Phrases.Remove(echo_ExData);
            }

            data.Phrases = data_.Phrases;
            #endregion

            #region mesurement
            MesurementByCategory general = data_.categories.Where(t => t.name.Trim() == "General").FirstOrDefault();
            if (general != null)
            {
                data_.categories.Remove(general);
                data.generalCategory = general;
            }



            MesurementByCategory General2Category = data_.categories.Where(t => t.name.Trim() == "General2").
                Select(t => t).FirstOrDefault();
            if (General2Category != null && General2Category.mesurements != null)
            {
                data.Workload = RetrieveMeasurement(General2Category, "Workload");
                data.MaxAgePredictedHR = RetrieveMeasurement(General2Category, "MPHR");
                data.MaxAgePredictedHRAttained = RetrieveMeasurement(General2Category, "Percentage of MPHR achieved");
                data.ExersizeDuration = RetrieveMeasurement(General2Category, "Duration");
            }
            if (General2Category != null)
            {
                data_.categories.Remove(General2Category);
            }

            MesurementByCategory Resting_Data_Category = data_.categories.Where(t => t.name.Trim() == "Resting Data3").
                Select(t => t).FirstOrDefault();

            if (Resting_Data_Category != null && Resting_Data_Category.mesurements != null)
            {
                data.HeartRate_RestData = RetrieveMeasurement2(Resting_Data_Category, "resting hr", "hr resting");
                data.BloodPresure_RestData = RetrieveMeasurement2(Resting_Data_Category, "resting blood pressure", "bp resting");
            }

            MesurementByCategory Peak_Exercise_Category = data_.categories.Where(t => t.name.Trim() == "Peak Exercise3").
                                    Select(t => t).FirstOrDefault();
            if (Peak_Exercise_Category != null && Peak_Exercise_Category.mesurements != null)
            {
                data.HeartRate_ExData = RetrieveMeasurement(Peak_Exercise_Category, "Max HR");
                data.BloodPresure_ExData = RetrieveMeasurement(Peak_Exercise_Category, "Max BP");
            }

            if (Resting_Data_Category != null)
            {
                data_.categories.Remove(Resting_Data_Category);
            }

            if (Peak_Exercise_Category != null)
            {
                data_.categories.Remove(Peak_Exercise_Category);
            }
            #endregion

            //data.ReportResult = data_.Phrases;

            data.categories = data_.categories;

            #region test configuration
            //2, 15
            if (testId == 2 || testId == 25)
            {
                //data.impressionLocation = ImpressionLocation.Top;
                //data.isTable = true;
                //data.impressionBorder = false;//temporary
            }
            #endregion

            #endregion

            #endregion

            byte[] contents = null;
            //if (testId == 2 || testId == 25)
            contents = rep.FetchPdfBytesForStressEchoStudyReport(data, printDirectoryPath, testId);
            if (contents != null)
            {
                PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                List<byte[]> byteArr = new List<byte[]>() { contents };
                if (data.rawData != null)
                {
                    contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, data.rawData, "",
                        (doctorNames == null ? 0 : doctorNames.rawDataPageNumber), data.OfficeName, pageHeader);
                }
                else
                {
                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0, data.OfficeName, pageHeader);
                }
            }

            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult BP_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
                                int imageBytesId, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc) //done+
        {
            HttpContext context = HttpContextProvider.Current;


            byte[] contents = GetContentFor_BP_Pdf(context, testId, appointmentId, patientId, officeId,
                        null, numPages, repDoc, refDoc, famDoc, ccDoc
                        , null, CerebrumUser.PracticeId);//+

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        private byte[] GetContentFor_BP_Pdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId, int officeId,
                        byte[] imageBytes, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc, DoctorNames doctorNames, int practiceId)//done++   sql
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();

            //TEMPORARY 
            //byte[] byteArray = GetImageBytes(12);
            //imageBytes = byteArray;
            //doctorNames.rawDataPageNumber = 1;
            ///////////

            if (CheckIfPdfFile(imageBytes))
            {
                data.rawData = imageBytes;
            }

            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient data
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            //doctorNames.reportingDocId is external doctor Id
            int repDocId = doctorNames != null ? doctorNames.reportingDocId : 0;
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            //data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            data.dateOfExam = doctorNames.VisitDate;
            doctorNames.rawDataPageNumber = 10000;
            #region Report Name
            //if (testId == 11)
            //{
            //    data.ReportName = "24 hour Ambulatory BP Monitoring REPORT";
            //}
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion

            //int practiceId = CerebrumUser.PracticeId;
            //int practiceId = p_data.practiceId;
            byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config data
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            data.ReportResult = data_.Phrases;
            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");
            #region test configuration
            //data.isTable = false;
            #endregion

            #endregion
            byte[] contents = null;
            //if (testId == 11)
            contents = rep.FetchPdfBytesFor_BP_Report(data, printDirectoryPath, testId);
            if (contents != null)
            {
                PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                List<byte[]> byteArr = new List<byte[]>() { contents };
                if (data.rawData != null)
                {
                    contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, data.rawData, "",
                        (doctorNames == null ? 0 : doctorNames.rawDataPageNumber), data.OfficeName, pageHeader);
                }
                else
                {
                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0, data.OfficeName, pageHeader);
                }
            }

            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult ELR_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
                        int imageBytesId, int numPages, int repDoc, int refDoc, int famDoc, int ccDoc) //done+
        {
            HttpContext context = HttpContextProvider.Current;


            byte[] contents = GetContentFor_ELR_Pdf(context, testId, appointmentId, patientId, officeId,
                        null, numPages, repDoc, refDoc, famDoc, ccDoc, CerebrumUser.PracticeId);

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        private byte[] GetContentFor_ELR_Pdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId, int officeId, byte[] imageBytes,
                                    int numPages, int repDoc, int refDoc, int famDoc, int ccDoc, int practiceId)//done++
        {
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();

            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient data
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.PtnFamilyDoc = p_data.PtnFamilyDoc;
                data.PtnRefDoc = p_data.PtnRefDoc;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            //data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            //data.dateOfExam = doctorNames.VisitDate;
            data.dateOfExam = (new UserRepository()).GetAppointmentDate(appointmentId).Value.ToString("MMMM dd, yyyy");
            #region Report Name
            //if (testId == 24)
            //{
            //    data.ReportName = "EVENT LOOP RECORDER REPORT";
            //}
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion
            //int practiceId = p_data.practiceId;//  CerebrumUser.PracticeId;
            byte[] signImg = rep.GetDoctorsSignature(refDoc, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }
            data.ReportingDoc = rep.GetExtDoctorName(repDoc);
            data.ReferringDoc = rep.GetExtDoctorName(refDoc);
            data.ccDoctor = rep.GetExtDoctorName(ccDoc);

            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config data
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            data.ReportResult = data_.Phrases;
            #region test configuration
            //24
            if (testId == 24)
            {
                data.isTable = false;
            }
            #endregion

            #endregion

            byte[] contents = null;
            if (testId == 24)
            {
                contents = rep.FetchPdfBytesFor_ELR_Report(data, printDirectoryPath, testId);
                List<byte[]> byteArr = null;
                if (contents != null)
                {
                    if (imageBytes != null)
                    {
                        //byteArr = new List<byte[]>() { contents, imageBytes };
                        contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, imageBytes, "", numPages);
                    }
                    else
                    {
                        byteArr = new List<byte[]>() { contents };
                        contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0);
                    }

                    //contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0);
                }
            }

            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public ActionResult PedriaticEcho_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
            int imageBytesId, int numPages, int refDoc, int technId) //done+
        {
            HttpContext context = HttpContextProvider.Current;
            byte[] imageBytes = null;
            //imageBytes = FetchImigBytesForReport(4);

            byte[] contents = GetContentFor_PedriaticEchoReportPdf(context, testId, appointmentId, patientId, officeId,
                        imageBytes, numPages, refDoc, technId
                        , null, CerebrumUser.PracticeId);//+

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        public byte[] GetContentFor_PedriaticEchoReportPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId,
                        int patientId, int officeId, byte[] imageBytes, int numPages, int refDoc, int technId, DoctorNames doctorNames, int practiceId)//done++ sql
        {//14 --pedriatic report
            //int practiceId = CerebrumUser.PracticeId;

            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            WS_Report_VM repData = repo.LoadReportData(appointmentId, testId, practiceId);
            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();
            //TEMPORARY 
            //byte[] byteArray = GetImageBytes(12);
            //imageBytes = byteArray;
            //doctorNames.rawDataPageNumber = 1;
            ///////////

            if (CheckIfPdfFile(imageBytes))
            {
                data.rawData = imageBytes;
            }


            PatientData_VM p_data = rep.GetPatinetDataById(patientId);
            //int practiceId = p_data.practiceId;

            byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }

            Tuple<string, string, string, string, string> repDocData = rep.GetReportingDoctorData(refDoc, appointmentId);
            data.RefAddress = repDocData.Item3;
            data.RefPhone = repDocData.Item4;
            data.RefFax = repDocData.Item5;

            //doctorNames.reportingDocId is external doctor Id
            int repDocId = doctorNames != null ? doctorNames.reportingDocId : 0;
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);
            data.refDoctor = rep.GetMainDoctorData(refDoc);

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();

            #region office_patient
            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            //PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB_M;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                if (string.IsNullOrWhiteSpace(data.PtnFamilyDoc))
                    data.PtnFamilyDoc = p_data.PtnFamilyDoc;
                if (string.IsNullOrWhiteSpace(data.PtnRefDoc))
                    data.PtnRefDoc = p_data.PtnRefDoc;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
                data.PtnPhone = p_data.PtnPhone;
            }

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            //data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            data.dateOfExam = doctorNames.VisitDate;

            #region config
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }

            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion
            MesurementByCategory general = data_.categories.Where(t => t.name.Trim() == "General").FirstOrDefault();
            if (general != null)
            {
                data_.categories.Remove(general);
                data.generalCategory = general;
            }

            data.ReportResult = data_.Phrases;

            data.categories = data_.categories;

            #region test configuration
            //14
            if (testId == 14)
            {
                //data.ReportName = "Pediatic Echo REPORT";
                data.isTable = true;
            }
            data.ReportName = rep.GetTestNameForReport(testId);
            #endregion

            #endregion
            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");

            if (!string.IsNullOrWhiteSpace(p_data.Age))
            {
                int patientAge = 0;
                if (int.TryParse(p_data.Age, out patientAge))
                {
                    if (patientAge >= 0 && patientAge <= 16)
                    {
                        int appointmentTestLogId = (new Cerebrum.BLL.RadAdapter.MesmrementPhrases()).GetAppointmentTestLogId(appointmentId, testId);
                        data.wsZscore = (new Cerebrum.BLL.Measurements.MeasurementBLL()).GetZScore(appointmentTestLogId);

                        foreach (var category in data.categories)
                        {
                            foreach (var mesurement in category.mesurements)
                            {
                                mesurement.Range = null;
                                mesurement.IndexRangeStr = null;
                            }
                        }
                    }
                }
            }

            #endregion

            //--- temporary ZScore initialization ----
            #region Temporary Initialization
            data.zscoreData = new DS();
            if (data.zscoreData != null)
            {
                data.zscoreData.fpart = new List<Fzscore>(){
                    new Fzscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Daubeney_Mean =1.3,Daubeney_Score=1.8,Daubeney_Min=1.05,Daubeney_Max=2.03},
                    new Fzscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Daubeney_Mean =1.3,Daubeney_Score=1.8,Daubeney_Min=1.05,Daubeney_Max=2.03},
                    new Fzscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Daubeney_Mean =1.3,Daubeney_Score=1.8,Daubeney_Min=1.05,Daubeney_Max=2.03}
                };
                data.zscoreData.spart = new List<Szscore>(){
                    new Szscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Kampmann_Mean=1.95},
                    new Szscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Kampmann_Mean=1.95},
                    new Szscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Kampmann_Mean=1.95},
                    new Szscore(){Type="PRA Prox",WUnits ="1.2cm",HSC_Mean=1.3,HSC_Score=1.4,HSC_Min=1.5,HSC_Max =1.6,Kampmann_Mean=1.95}
                };
                data.zscoreData.tapseZs = new List<TapseZscore>(){
                    new TapseZscore(){Name="name1",Value1 ="Value1",Value2="Value2",Mean="Mean",Zsc="Zsc",NormalMin ="NormalMin",NormalMax ="NormalMax"},
                    new TapseZscore(){Name="name2",Value1 ="Value12",Value2="Value22",Mean="Mean2",Zsc="Zsc2",NormalMin ="NormalMin2",NormalMax ="NormalMax2"},
                    new TapseZscore(){Name="name3",Value1 ="Value13",Value2="Value23",Mean="Mean3",Zsc="Zsc3",NormalMin ="NormalMin3",NormalMax ="NormalMax3"}
                };
                data.zscoreData.coronaryZs = new List<CoronaryZscore>(){
                    new CoronaryZscore(){Name="name1",Value1 ="Value1",Value2="Value2",Mean1="Mean",Zsc1="Zsc",NormalMin1 ="NormalMin",NormalMax1 ="NormalMax",
                    Mean2="Mean",Zsc2="Zsc",NormalMin2 ="NormalMin",NormalMax2 ="NormalMax",
                    Mean3="Mean",Zsc3="Zsc",NormalMin3 ="NormalMin",NormalMax3 ="NormalMax"},
                    new CoronaryZscore(){Name="name1",Value1 ="Value1",Value2="Value2",Mean1="Mean",Zsc1="Zsc",NormalMin1 ="NormalMin",NormalMax1 ="NormalMax",
                    Mean2="Mean",Zsc2="Zsc",NormalMin2 ="NormalMin",NormalMax2 ="NormalMax",
                    Mean3="Mean",Zsc3="Zsc",NormalMin3 ="NormalMin",NormalMax3 ="NormalMax"}
                };
            }
            data.zscoreData = null;

            #endregion
            //--- temporary ZScore initialization ----

            byte[] contents = null;
            contents = rep.FetchPdfBytesFor_EPEDTestReport(data, printDirectoryPath, testId);
            if (contents != null)
            {
                PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                List<byte[]> byteArr = new List<byte[]>() { contents };
                if (data.rawData != null)
                {
                    contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, data.rawData, "",
                        (doctorNames == null ? 0 : doctorNames.rawDataPageNumber), data.OfficeName, pageHeader);
                }
                else
                {
                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0, data.OfficeName, pageHeader);
                }




                //List<byte[]> byteArr = new List<byte[]>() { contents };
                //contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0);
            }


            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }


        public ActionResult Generic_Report_Pdf(int testId, int appointmentId, int patientId, int officeId,
            int imageBytesId, int numPages, int refDoc, int technId) //done
        {

            HttpContext context = HttpContextProvider.Current;

            byte[] contents = GetContentFor_Generic_Pdf(context, testId, appointmentId,
                                        patientId, officeId, null, numPages, refDoc, technId, null, CerebrumUser.PracticeId);

            if (contents == null)
            {
                RepositoryForTemplates rep = new RepositoryForTemplates();
                string errMsg = "There is not report data for this patient!";
                byte[] contents_ = rep.GetErrorPdfBytes(errMsg);
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents_, "application/pdf");
            }
            else
            {
                Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
                return File(contents, "application/pdf");
            }
        }

        private byte[] GetContentFor_Generic_Pdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId, int officeId,
                                                            byte[] imageBytes, int numPages, int refDoc, int technId, DoctorNames doctorNames, int practiceId)//done++ sql
        {
            //imageBytes = null;//?
            //testId = 6;
            //appointmentId = 2;
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo1 = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            WS_Report_VM repData = repo1.LoadReportData(appointmentId, testId, practiceId);

            WS_ReportData data_ = repData.ReportData;

            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportTypeData data = new ReportTypeData();

            //TEMPORARY 
            //byte[] byteArray = GetImageBytes(12);
            //imageBytes = byteArray;
            //doctorNames.rawDataPageNumber = 1;
            ///////////

            if (CheckIfPdfFile(imageBytes))
            {
                data.rawData = imageBytes;
            }

            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            #region office_patient data
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.Age = p_data.Age;
                data.AgeOnReport = p_data.AgeAccurate;
            }

            //doctorNames.reportingDocId is external doctor Id
            int repDocId = doctorNames != null ? doctorNames.reportingDocId : 0;
            data.reportingDoctor = rep.GetMainDoctorData(repDocId);

            data.ReportingDoc = doctorNames != null ? doctorNames.reportingDoc : "";
            data.Technologist = doctorNames != null ? doctorNames.technician : "";
            data.PtnFamilyDoc = doctorNames != null ? doctorNames.familyDoc : "";
            data.PtnRefDoc = doctorNames != null ? doctorNames.refferingDoc : "";
            data.AssociatedDoc = doctorNames != null ? doctorNames.assocciatedDoc : "";
            data.AssocciatedDoctors = doctorNames != null ? doctorNames.AssocciatedDoctors : new List<string>();
            data.ReportingDoc2 = doctorNames != null ? doctorNames.reportingDoc2 : "";
            data.ReportingDoc2Degrees = doctorNames != null ? doctorNames.reportingDoc2Degrees : "";

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }
            #endregion

            data.dateOfExam = doctorNames.VisitDate;
            #region Report Name
            data.ReportName = rep.GetTestNameForReport(testId);
            data.testGroupId = new UserRepository().GetGroupByTestID(testId);
            #endregion

            //int practiceId = CerebrumUser.PracticeId;
            //int practiceId = p_data.practiceId;
            byte[] signImg = rep.GetDoctorsSignature(doctorNames.reportingDocId, practiceId);
            if (rep.IsValidImage(signImg))
            {
                data.signature = signImg;
            }

            if (doctorNames.reportingDocId2 > 0)
            {
                byte[] signImgRepDoc2 = rep.GetDoctorsSignature(doctorNames.reportingDocId2, practiceId);
                if (rep.IsValidImage(signImgRepDoc2))
                {
                    data.signatureRepDoc2 = signImgRepDoc2;
                }
            }

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            #endregion

            #region config data
            data.logoLocation = officeData.logoLocation;
            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;
            #endregion

            #region MyRegion
            #region phrases
            data.EchoImpressinon = "";
            #endregion

            data.ReportResult = data_.Phrases;
            data.categories = data_.categories;
            data.Prior = GetMedicationValueLabel(data_.med_vm.Prior, "Prior Medications ");

            #region test configuration
            #endregion

            #endregion

            #endregion

            byte[] contents = null;

            contents = rep.FetchPdfBytesFor_Generic_Report(data, printDirectoryPath, testId);
            if (contents != null)
            {
                PdfPTable pageHeader = rep.GetPdfPageHeader(data);
                List<byte[]> byteArr = new List<byte[]>() { contents };
                if (data.rawData != null)
                {
                    contents = PdfMerger.MergeFilesForServiceC3_ThenAddAnotherPages(contents, data.rawData, "",
                        (doctorNames == null ? 0 : doctorNames.rawDataPageNumber), data.OfficeName, pageHeader);
                }
                else
                {
                    contents = PdfMerger.MergeFilesForServiceC3(byteArr, "", 0, data.OfficeName, pageHeader);
                }
            }


            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }



        // GET: PdfConversions/PdfTest
        public ActionResult Index()
        {
            //Helper.SendCodeByMail("Email Code", "<EMAIL>", "string code");

            //HttpContext context = HttpContextProvider.Current;
            //SimpleModel model = new PdfConversions.SimpleModel();
            //model.context = context;
            //RepositoryForTemplates rep = new RepositoryForTemplates();
            //ReportTypeData data = new ReportTypeData();
            //data.imgLeftUrl = HttpContext.Server.MapPath("~/Areas/PdfConversions/Images/Grant.rtf");
            //Microsoft.Office.Interop.Word.Application _App = new Microsoft.Office.Interop.Word.Application();
            //Microsoft.Office.Interop.Word.Document _Doc = _App.Documents.Open(data.imgLeftUrl);

            ////object _DocxFileName = "C:/xxx.docx";
            //object _DocxFileName = data.imgLeftUrl.Replace(".rtf", ".doc") ;
            //Object FileFormat = Microsoft.Office.Interop.Word.WdSaveFormat.wdFormatXMLDocument;

            //_Doc.SaveAs(ref _DocxFileName, ref FileFormat);

            //var wordApp = new Microsoft.Office.Interop.Word.Application();
            //var currentDoc = wordApp.Documents.Open(data.imgLeftUrl);
            //object _DocxFileName = data.imgLeftUrl.Replace(".rtf", ".doc");
            //currentDoc.SaveAs(ref _DocxFileName, Microsoft.Office.Interop.Word.WdSaveFormat.wdFormatDocumentDefault);
            //currentDoc.Close();
            //wordApp.Quit();





            //string path = HttpRuntime.AppDomainAppPath + @"PdfFiles\Consultation\MCDI5317.csv";
            //string path1 = HttpRuntime.AppDomainAppPath + @"PdfFiles\Consultation\MCDI5317.xml";
            //string[] source = System.IO.File.ReadAllLines(path);

            //XElement cust = new XElement("Root",
            //    from str in source
            //    let fields = str.Split(',')
            //    select new XElement("Customer",
            //        new XAttribute("CustomerID", fields[0]),
            //        new XElement("CompanyName", fields[1]),
            //        new XElement("ContactName", fields[2]),
            //        new XElement("ContactTitle", fields[3]),
            //        new XElement("Phone", fields[4]),
            //        new XElement("FullAddress", fields[5])

            //        )
            //);

            //new XDocument(cust
            //         //new XElement("root",
            //         //    new XElement("someNode", "someValue")
            //         //)
            //         ).Save(path1);




            return View();
        }

        public ActionResult Error()
        {
            ViewBag.Message = (string)TempData["error"];
            TempData["error"] = "";
            return View();
        }

        // GET: PdfTest
        public ActionResult PdfMerge()
        {
            return View();
        }


        public ActionResult PdfReportFor_VP(int appointmentId)
        {
            VPRepository repo = new VPRepository();
            var UserID = (new UserRepository()).GetUserID(HttpContext.User.Identity.Name);
            VP_Report_VM vm = repo.LoadReportData(appointmentId, UserID);
            TempData["report"] = vm;

            return RedirectToAction("ReportFor_VP");
        }

        public ActionResult PdfReportFor_WS(int appointmentId)
        {
            //MeasurementRepository.LoadData()
            Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository repo = new Cerebrum30.Areas.Measurements.DataAccess.MeasurementRepository();
            //var UserID = (new UserRepository()).GetUserID(HttpContext.User.Identity.Name);
            int practiceId = IndentityInfo.GetPracticeId(User.Identity);
            //VP_Report_VM vm = repo.LoadReportData(appointmentId, UserID);
            WorkSheetVM vs = repo.LoadData(appointmentId, 1, practiceId);
            TempData["report"] = vs;

            return RedirectToAction("ReportFor_VP");
        }
        public ActionResult ReportFor_VP()//test
        {
            HttpContext context = HttpContextProvider.Current;

            byte[] contents = GetContentForLetterPdf_VP(context, 1, 1, 1, 1, 1, 1, 1, null);
            Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
            return File(contents, "application/pdf");
        }

        // [Authorize]
        //this method for testing purposes only
        public ActionResult VP_Report_for_test()
        {
            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Mock Data
            //ReportData data_ = vm.ReportData;
            //ReportTypeData data = new ReportTypeData();

            //data_.patientId = 1;
            //data_.officeId = 3;
            //string patient_office = "";
            //PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            //if (p_data == null)
            //{
            //    patient_office = "Patient information is not provided for report.";
            //}
            //else
            //{
            //    data.PtnName = p_data.PtnName;
            //    data.PtnDOB = p_data.PtnDOB;
            //    data.PtnOHIP = p_data.PtnOHIP;
            //    data.sex = p_data.sex;
            //    data.PtnFamilyDoc = p_data.PtnFamilyDoc;
            //    data.PtnRefDoc = p_data.PtnRefDoc;
            //}

            //ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            //if (officeData == null)
            //{
            //    patient_office += "     Office information is not provided for report.";
            //}
            //else
            //{
            //    data.OfficeName = officeData.OfficeName;
            //    data.OfficeAddress = officeData.OfficeAddress;
            //    data.OfficePhone = officeData.OfficePhone;
            //    data.OfficeFax = officeData.OfficeFax;
            //}

            //if (patient_office != "")
            //{
            //    byte[] contentsError = rep.FetchPdfBytesForError(data, printDirectoryPath, patient_office);
            //    Helper.DeleteDirectory(printDirectoryPath);

            //    Response.Headers["Content-Disposition"] = "inline; filename=test.pdf";
            //    return File(contentsError, "application/pdf");

            //}

            //data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            //data.ReportName = "ECHOCARDIOGRAM REPORT";

            //data.ReportingDoc = "Alfredo Pantano, MD";
            //data.Technologist = "Christia, Yang";
            //data.EchoImpressinon = "Grade 1 LV function.  Normal LV cavity size and systolic function.  No significant valvular disease. All chambers normal size.";
            //data.ReasonForTest = "F/U";

            //if (officeData.logoLocation == LogoLocation.Left)
            //{
            //    if (officeData.logoImageL != null)
            //    {
            //        data.logoImage = officeData.logoImageL;
            //    }
            //    else
            //    {
            //        data.logoImage = GetEmptyImage();
            //    }
            //}
            //else if (officeData.logoLocation == LogoLocation.Middle)
            //{
            //    if (officeData.logoImageM != null)
            //    {
            //        data.logoImage = officeData.logoImageM;
            //    }
            //    else
            //    {
            //        data.logoImage = GetEmptyImage();
            //    }
            //}
            //else
            //{
            //    if (officeData.logoImageR != null)
            //    {
            //        data.logoImage = officeData.logoImageR;
            //    }
            //    else
            //    {
            //        data.logoImage = GetEmptyImage();
            //    }
            //}

            //data.logoLocation = officeData.logoLocation;
            //data.isTable = officeData.isTable;
            //data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            //data.showRefDoc = officeData.showRefDoc;
            //data.isAddressBold = officeData.isAddressBold;
            //data.impressionLocation = officeData.impressionLocation;
            //data.impressionBorder = officeData.impressionBorder;
            //data.isWebsite = officeData.isWebsite;
            //data.siteUrl = officeData.siteUrl;
            //data.fontSize = officeData.fontSize;
            //data.fontHeadSize = officeData.fontHeadSize;
            //data.fontType = officeData.fontType;
            //data.isSex = officeData.isSex;

            //data.ReportResult = data_.Phrases;
            //#region MyRegion
            //data.categories = data_.categories;
            //#endregion

            #endregion
            //int n = 1;//Stress Test
            int n = 4;// ECG
            //int n = 2;// SE , SE30
            //int n = 1;// E1 , E2, E3, E45
            //int n = 6;//H1, H2, H3, H7, H14
            //int n = 12;//BP
            //int n = 5;//ELR
            //int n = 14;//EPED
            byte[] imageBytes = null;
            if (n == 3 || n == 4)
            {
                imageBytes = FetchImigBytesForReport(4);
            }
            else if (n == 2 || n == 1)
            {
                imageBytes = FetchImigBytesForReport(1);
            }
            else if (n == 6)
            {
                imageBytes = FetchImigBytesForReport(6);
            }
            else if (n == 12)
            {
                imageBytes = FetchImigBytesForReport(12);
            }
            else if (n == 5)
            {
                imageBytes = FetchImigBytesForReport(6);
            }
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 1, 1, 1, 2, imageBytes, 5, 1, 4);//   1, 6,10, 20 --cardiogram's
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 2, 1, 1, 1, imageBytes, 3, 1, 1); //  2, 15 --stress echo
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 3, 1, 1, 1, imageBytes, 3, 1, 1); //  3 --stress test
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 4, 1, 1, 1, imageBytes, 3, 1, 1); //  4 --ECG
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 5, 1, 1, 1, imageBytes, 3, 1, 1); //  5,7,8,12,13 --holters
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 11, 1, 1, 1, imageBytes,3, 1, 1); //  12 --BP
            //byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, 24, 1, 1, 1, imageBytes,3, 1, 1); //  24 --ELR
            byte[] contents = GetContentForReportPdfByTestId(HttpContextProvider.Current, n, 1653369, 1, 1, imageBytes, 3, 1, 1, CerebrumUser.PracticeId); //  14 --EPED
            Helper.DeleteDirectory(printDirectoryPath);

            Response.Headers["Content-Disposition"] = "inline; filename=Report.pdf";
            return File(contents, "application/pdf");
        }


        public Tuple<int, string> GetUserIdFolderPath()
        {
            HttpContext context = HttpContextProvider.Current;
            var UserID = (new UserRepository()).GetUserID(context.User.Identity.Name);

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;

            return new Tuple<int, string>(UserID, printDirectoryPath);
        }

        public byte[] GetContentForReportPdf(Microsoft.AspNetCore.Http.HttpContext httpcontext, int appointmentId, int patientId, int officeId, int practiceId)
        {
            string ReportingDoc = "Alfredo Pantano, MD";
            string Technologist = "Christia, Yang";

            VPRepository repo = new VPRepository();

            var UserID = (new UserRepository()).GetUserID(httpcontext.User.Identity.Name);
            VP_Report_VM vm = repo.LoadReportData(appointmentId, UserID);
            RepositoryForTemplates rep = new RepositoryForTemplates();

            string printDirectory = DateTime.Now.Ticks.ToString() + "_EchocardiogramReport";
            string printDirectoryPath = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Pdfs/" + printDirectory;
            Helper.CreateDirectory(printDirectoryPath);

            #region Data
            ReportData data_ = vm.ReportData;
            //WS_ReportData data_ = vm_WS.ReportData;
            ReportTypeData data = new ReportTypeData();

            //data_.patientId = 1;
            //data_.officeId = 7;
            data_.patientId = patientId;
            data_.officeId = officeId;
            string patient_office = "";
            PatientData_VM p_data = rep.GetPatinetDataById(data_.patientId);
            if (p_data == null)
            {
                patient_office = "Patient information is not provided for report.";
            }
            else
            {
                data.PtnName = p_data.PtnName;
                data.PtnDOB = p_data.PtnDOB;
                //data.PtnOHIP = p_data.PtnOHIP;
                data.PtnOHIP = p_data.PtnOHIP_VC;
                data.sex = p_data.sex;
                data.PtnFamilyDoc = p_data.PtnFamilyDoc;
                data.PtnRefDoc = p_data.PtnRefDoc;
            }

            ReportTypeData officeData = rep.GetOfficeDataPdf(data_.officeId);
            if (officeData == null)
            {
                patient_office += "     Office information is not provided for report.";
            }
            else
            {
                data.OfficeName = officeData.OfficeName;
                data.OfficeAddress = officeData.OfficeAddress;
                data.OfficePhone = officeData.OfficePhone;
                data.OfficeFax = officeData.OfficeFax;
                data.OfficeCity = officeData.OfficeCity;
                data.headerType = officeData.headerType;
            }

            if (patient_office != "")
            {
                return null;
            }

            data.dateOfExam = DateTime.Now.ToString("MMMM dd, yyyy");
            data.ReportName = "ECHOCARDIOGRAM REPORT";

            data.ReportingDoc = ReportingDoc;
            data.Technologist = Technologist;

            #region logo
            if (officeData.logoLocation == LogoLocation.Left)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageL))
                {
                    data.logoImage = officeData.logoImageL;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else if (officeData.logoLocation == LogoLocation.Middle)
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageM))
                {
                    data.logoImage = officeData.logoImageM;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            else
            {
                if (FileConversions.CheckIfImageFile(officeData.logoImageR))
                {
                    data.logoImage = officeData.logoImageR;
                }
                else
                {
                    data.logoImage = GetEmptyImage();
                }
            }
            data.logoLocation = officeData.logoLocation;

            #endregion

            data.isTable = officeData.isTable;
            data.isOfficeNameVisible = officeData.isOfficeNameVisible;
            data.showRefDoc = officeData.showRefDoc;
            data.isAddressBold = officeData.isAddressBold;
            data.impressionLocation = officeData.impressionLocation;
            data.impressionBorder = officeData.impressionBorder;
            data.isWebsite = officeData.isWebsite;
            data.siteUrl = officeData.siteUrl;
            data.fontSize = officeData.fontSize;
            data.fontHeadSize = officeData.fontHeadSize;
            data.fontType = officeData.fontType;
            data.isSex = officeData.isSex;

            #region MyRegion
            #region  Mock Data
            //data.cpp = new List<ValueLabel>()
            //{
            //    new ValueLabel() { label="Study Quality",value="The study was technically difficult, the image quality was adequate. "},
            //     new ValueLabel() { label="LV size and function",value="Grade 1 LV function.  Normal LV cavity size and systolic function.  No left ventricular hypertrophy.  No pericardial effusion. "},
            //      new ValueLabel() { label="RV,PA,PV",value="Normal right ventricular size and systolic function. The pulmonic valve is normal in structure and function.  "},
            //       new ValueLabel() { label="LA,RA",value="Normal left atrium. Normal right atrium. No doppler evidence of inter-atrial shunt to suggest ASD."},
            //        new ValueLabel() { label="Aorta and AV",value="Poor visualization of the AV, appears tricuspid.  Trace aortic regurgitation.  No aortic stenosis.  Dilated ascending aorta at 40mm.  "},
            //         new ValueLabel() { label="MV and TV",value="Mildly thickened mitral valve with posterior leaflet annular calcification.  Mild mitral regurgitation.  Mild tricuspid regurgitation.  RVSP 20mmHg(assuming RAP of 3mmHg).  "},

            //};
            #endregion

            data.OpeningStatement = data_.OpeningStatement ?? "";
            data.cpp = data_.CPP;

            data.ReportResult = data_.Phrases;
            data.categories = data_.categories;
            #endregion

            #endregion

            byte[] contents = rep.FetchPdfBytesForReport_VP(data, printDirectoryPath);
            //-------------------------------------------------------------------------




            Helper.DeleteDirectory(printDirectoryPath);

            return contents;
        }

        public byte[] GetContentForReportPdfByTestId(Microsoft.AspNetCore.Http.HttpContext httpcontext, int testId, int appointmentId, int patientId,
            int officeId, byte[] imageBytes, int numPages, int refDoc, int technId, int practiceId)
        {

            if (testId == 1 || testId == 6 || testId == 10 || testId == 20)
            {

                return GetContentFor_EchocardiogramReportPdf(httpcontext, testId, appointmentId, patientId,
                    officeId, imageBytes, numPages, refDoc, technId
                    , null, null, practiceId);//+
            }
            else if (testId == 5 || testId == 7 || testId == 8 || testId == 13 || testId == 12)
            {
                return GetContentFor_HolterPdf(httpcontext, testId, appointmentId, patientId, officeId, imageBytes, numPages, refDoc, technId
                    , null, practiceId);//+
            }
            else if (testId == 14)
            {
                return GetContentFor_PedriaticEchoReportPdf(httpcontext, testId, appointmentId, patientId,
                                                                officeId, imageBytes, numPages, refDoc, technId
                                                                , null, practiceId);//+
            }
            else if (testId == 2 || testId == 15)
            {
                return GetContentFor_StressEchoStudyPdf(httpcontext, testId, appointmentId, patientId, officeId,
                    imageBytes, numPages, 1, refDoc, 1, 1,
                    null, null, CerebrumUser.PracticeId);//+
            }
            else if (testId == 3)
            {
                return GetContentFor_StressTestStudyPdf(httpcontext, testId, appointmentId, patientId, officeId,
                    imageBytes, numPages, 1, refDoc, 1, 1, practiceId);
            }
            else if (testId == 4)
            {
                return GetContentFor_ECGPdf(httpcontext, testId, appointmentId, patientId, officeId, imageBytes, numPages, refDoc, technId
                    , null, practiceId, null, 2);//+
            }
            else if (testId == 11)
            {
                return GetContentFor_BP_Pdf(httpcontext, testId, appointmentId, patientId, officeId,
                    imageBytes, numPages, 1, refDoc, 1, 1
                    , null, practiceId);//+
            }
            else if (testId == 24)
            {
                return GetContentFor_ELR_Pdf(httpcontext, testId, appointmentId, patientId, officeId,
                    imageBytes, numPages, 1, refDoc, 1, 1, practiceId);
            }
            return null;
        }


        public byte[] GetContentForReportPdf(int testId, int appointmentId, int practiceId)//calling on SendReport only
        {
            HttpContext context = HttpContextProvider.Current;

            int patientID = 0;
            int officeId = 0;
            int mainDocID = 1;
            int refDocID = 1;
            int ccDocID = 1;
            int famID = 1;
            int numPages = 10;
            int technId = 1;
            int repDocID = 1;

            int apptTestID = (new UserRepository()).GetAppointmentTestIDAppointment(appointmentId, testId);

            //get patientid 
            officeId = (new UserRepository()).GetOfficeIDByAppointment(appointmentId);
            patientID = (new UserRepository()).GetPatientByAppointment(appointmentId);
            technId = (new UserRepository()).GetTechnicianIDByAppointment(apptTestID);
            //practiceID = (new UserRepository()).GetPracticeAppointment(appointmentId);

            DoctorNames lstDocNames = new DoctorNames();
            lstDocNames.rawDataPageNumber = 1;
            var docLst = (new VPRepository()).GetContactList(appointmentId, lstDocNames, testId);

            var mainDoc = docLst.FirstOrDefault(e => e.DocType == DocType.Reporting);
            var refDoc = docLst.FirstOrDefault(e => e.DocType == DocType.Referral);
            var famDoc = docLst.FirstOrDefault(e => e.DocType == DocType.Family);
            var repDoc = docLst.FirstOrDefault(e => e.DocType == DocType.Reporting);
            //TODO
            var ccDoc = docLst.FirstOrDefault(e => e.DocType == DocType.Family);


            if (mainDoc != null)
            {
                mainDocID = mainDoc.ID;
            }
            if (refDoc != null)
            {
                refDocID = refDoc.ID;
            }
            if (ccDoc != null)
            {
                ccDocID = ccDoc.ID;
            }
            if (famDoc != null)
            {
                famID = famDoc.ID;
            }
            if (repDoc != null)
            {
                repDocID = repDoc.ID;
            }






            byte[] imageBytes = null;
            //if fileLocation = 1 - uploaded; fileLocation = 2 - from DICOM () ; fileLocation = 0 - there was problem to get file
            int fileLocation = 0;
            bool attachedRawData = (new UserRepository()).GetAttachedRawDataFlagByTestID(practiceId, testId);
            byte[] rawBytes = GetRawOrPDF1(appointmentId, testId, out fileLocation);
            byte[] rawBytesPdf = null;
            if (attachedRawData)
                rawBytesPdf = rawBytes;

            #region old
            //byte[] rawBytes = null;
            //var rawFiles = (new MeasurementRepository()).GetRawFiles(appointmentId, testId);
            //if (rawFiles.Count > 0)
            //{
            //    Task<byte[]> task = RetrieveRawData(rawFiles[0], appointmentId);
            //    task.Wait();
            //    rawBytes = task.Result;
            //} 
            #endregion


            int groupID = (new UserRepository()).GetGroupByTestID(testId);
            if (groupID == 1) //E1,E2,E3,E45
            {
                //Get Bulls eye if any 
                //byte[] imageBytes = null;
                ServerLocationProvider locator = new ServerLocationProvider(appointmentId, testId);
                string location = locator.GetLocation(AwareMD.Cerebrum.Shared.Enums.DataType.BullsEye, Module.WS);
                var svgImageFileName = appointmentId.ToString() + "-" + testId.ToString() + ".png";
                string bullsEyefullPath = _webHostEnvironment.WebRootPath + location + svgImageFileName;
                if (System.IO.File.Exists(bullsEyefullPath))
                {
                    imageBytes = Helper.ImageToByteArray(System.Drawing.Image.FromFile(bullsEyefullPath));
                }
                //Get Bulls eye if any 
                return GetContentFor_EchocardiogramReportPdf(context, testId, appointmentId,
                                            patientID, officeId, imageBytes, numPages, refDocID, technId
                                            , lstDocNames, rawBytesPdf, practiceId);//+

            }
            else
            if (groupID == 2) //SE
            {
                return GetContentFor_StressEchoStudyPdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                                                rawBytesPdf, numPages, repDocID, refDocID, famID, ccDocID
                                                , lstDocNames, null, CerebrumUser.PracticeId);//+

                //if (testId == 2)
                //    return GetContentFor_StressEchoStudyPdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                //                rawBytes, numPages, repDocID, refDocID, famID, ccDocID
                //                , lstDocNames, null, CerebrumUser.PracticeId);//+
                //else if (testId == 15)
                //    return GetContentFor_StressTestStudyPdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                //              rawBytes, numPages, repDocID, refDocID, famID, ccDocID, practiceId);
            }
            else
            if (groupID == 4) //ECG
            {
                //var imageBytes = FetchImigBytesForReportContext(4, context);
                ////var imageBytes = FetchImigBytesForReport(4);//it's for testing only ...
                //var rawFiles = (new MeasurementRepository()).GetRawFiles(appointmentId, testId);
                //if (rawFiles.Count > 0)
                //{
                //    Task<byte[]> task = RetrieveRawData(rawFiles[0]);
                //    task.Wait();
                //    imageBytes = task.Result;
                //}

                bool ifItIsPdf = CheckIfPdfFile(rawBytes);
                if (ifItIsPdf)
                {
                    return GetContentFor_ECGPdf(context, testId, appointmentId, patientID, officeId,
                        rawBytes, numPages, refDocID, technId
                        , lstDocNames, practiceId, rawBytesPdf, fileLocation);//+
                }

            }
            if (groupID == 6) //Holter
            {
                if (testId == 24)
                {
                    return GetContentFor_ELR_Pdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                                     rawBytesPdf, lstDocNames.rawDataPageNumber, repDocID, refDocID, famID, ccDocID, practiceId);
                }
                else
                {
                    return GetContentFor_HolterPdf(context, testId, appointmentId,
                                         patientID, officeId, rawBytesPdf, numPages, refDocID, technId
                                         , lstDocNames, practiceId);//+
                }
            }

            else
            if (groupID == 12)
            {
                return GetContentFor_BP_Pdf(HttpContextProvider.Current, testId, appointmentId, patientID, officeId,
                         rawBytesPdf, numPages, repDocID, refDocID, famID, ccDocID
                         , lstDocNames, practiceId);//+

            }
            else
            if (groupID == 49)
            {
                return GetContentFor_PedriaticEchoReportPdf(HttpContextProvider.Current, testId, appointmentId,
                                 patientID, officeId, rawBytesPdf, numPages, refDocID, repDocID
                                 , lstDocNames, practiceId);//+
            }
            else
            {
                if (testId == 3)
                {
                }

                return GetContentFor_Generic_Pdf(context, testId, appointmentId, patientID, officeId, rawBytesPdf, numPages, refDocID, technId
                    , lstDocNames, practiceId);//+
            }
        }

        private ValueLabel GetBloodPresure_ExData(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            if (testId == 2 || testId == 15)
            {
                retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("peak exercise bp")).FirstOrDefault();
            }
            else if (testId == 3)
            {
                retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("peak exercise bp")).FirstOrDefault();
            }

            #region old
            //if (testId == 2 || testId == 15)
            //{
            //    retVal = data_.Phrases.Where(t => t.label.ToLower().Trim() == "peak exercise bp").FirstOrDefault();
            //}
            //else if (testId == 3)
            //{
            //    retVal = data_.Phrases.Where(t => t.label.ToLower().Trim() == "peak exercise bp").FirstOrDefault();
            //} 
            #endregion

            return retVal;
        }

        private ValueLabel GetBloodPresure_RestData(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            if (testId == 2 || testId == 15)
            {
                retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("resting bp")).FirstOrDefault();
            }
            else if (testId == 3)
            {
                retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("resting blood pressure")).FirstOrDefault();
            }

            //if (testId == 2 || testId == 15)
            //{
            //    retVal = data_.Phrases.Where(t => t.label.ToLower().Trim() == "resting bp").FirstOrDefault();
            //}
            //else if (testId == 3)
            //{
            //    retVal = data_.Phrases.Where(t => t.label.ToLower().Trim() == "resting blood pressure").FirstOrDefault();
            //}

            return retVal;
        }
        private ValueLabel GetEcho_ExData(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            //retVal = data_.Phrases.Where(t => ((t.label.Trim() == "Stress echo") || (t.label.ToLower().Trim() == "stress echo"))).FirstOrDefault();
            retVal = data_.Phrases.Where(t => (t.label != null && t.label.Trim().ToLower().Contains("stress echo"))).FirstOrDefault();

            return retVal;
        }

        private ValueLabel GetEcho_RestData(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;

            //ValueLabel retVal1 = data_.Phrases.Where(t => t.label.Trim() == "Resting LV").FirstOrDefault();
            ValueLabel retVal1 = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("resting lv")).FirstOrDefault();
            //ValueLabel retVal2 = data_.Phrases.Where(t => t.label.Trim() == "chamber sizes").FirstOrDefault();
            ValueLabel retVal2 = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("chamber sizes")).FirstOrDefault();
            //ValueLabel retVal3 = data_.Phrases.Where(t => t.label.Trim() == "diastolic function").FirstOrDefault();
            ValueLabel retVal3 = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("diastolic function")).FirstOrDefault();
            //ValueLabel retVal4 = data_.Phrases.Where(t => t.label.ToLower().Trim() == "resting echo").FirstOrDefault();
            ValueLabel retVal4 = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("resting echo")).FirstOrDefault();
            string echo_rest_data = (retVal1 == null ? " " : retVal1.value) + "  "
            + (retVal2 == null ? " " : retVal2.value) + "  "
            + (retVal3 == null ? " " : retVal3.value) + "  "
            + (retVal4 == null ? " " : retVal4.value);
            retVal = new ValueLabel() { label = "echo_rest_data", value = echo_rest_data };

            return retVal;
        }

        private ValueLabel GetECG_RestData(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            //retVal = data_.Phrases.Where(t => t.label.Trim() == "Resting ECG").FirstOrDefault();

            retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("resting ecg")).FirstOrDefault();

            return retVal;
        }//"diastolic function" "chamber sizes"

        private ValueLabel GetECG_ExData(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            //retVal = data_.Phrases.Where(t => t.label == "Stress ECG").FirstOrDefault();
            retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("stress ecg")).FirstOrDefault();

            return retVal;
        }

        private ValueLabel GetProtocol(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            //retVal = data_.Phrases.Where(t => (t.label == "Protocol")).FirstOrDefault();

            retVal = data_.Phrases.Where(t => (!string.IsNullOrEmpty(t.label)
                        && t.label.Trim().ToLower().Contains("protocol"))).FirstOrDefault();

            return retVal;
        }//

        //private ValueLabel GetStressQuality(WS_ReportData data_, int testId)
        //{
        //    ValueLabel retVal = null;
        //    retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("study quality")).FirstOrDefault();

        //    //retVal = data_.Phrases.Where(t => (t.label == "Study quality")).FirstOrDefault();

        //    return retVal;
        //}

        private ValueLabel GetSymptomsDuringTest(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("symptoms during the test")).FirstOrDefault();

            //retVal = data_.Phrases.Where(t => ((t.label == "Symptoms during the test") ||
            //(t.label == "Symptoms during the test"))).FirstOrDefault();

            return retVal;
        }

        private ValueLabel GetReasonForTermination(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("reasons for termination")).FirstOrDefault();

            //    retVal = data_.Phrases.Where(t => ((t.label == "Reasons for termination") ||
            //(t.label == "Reason for Termination"))).FirstOrDefault();

            return retVal;
        }

        private ValueLabel GetReasonForTest(WS_ReportData data_, int testId)
        {
            ValueLabel retVal = null;
            retVal = data_.Phrases.Where(t => t.label != null && t.label.Trim().ToLower().Contains("reason for test")).FirstOrDefault();

            //retVal = data_.Phrases.Where(t => ((t.label == "Reason for Test") ||
            //                                    (t.label.ToLower() == "reason for testing") ||
            //                                    (t.label == "Reason for Testing"))).FirstOrDefault();

            return retVal;
        }

        private ValueLabel GetMedicationValueLabel(List<VMPatientMedication> doseChanged, string label)
        {
            ValueLabel retVal = new ValueLabel();
            retVal.label = label;
            retVal.value = "None";


            if (doseChanged != null && doseChanged.Count > 0)
            {
                string ss = "";
                foreach (var item in doseChanged)
                {
                    if (item.MedicationName != null && item.MedicationName != "")
                    {
                        ss += " " + CultureInfo.CurrentCulture.TextInfo.ToTitleCase(item.MedicationName.ToLower()) + " " + (item.Dose ?? "") + " " + (item.SIG ?? "") + ", ";
                        ss = ss.Trim();
                    }
                }
                if (ss != "")
                {
                    ss = ss.Trim().Substring(0, ss.Length - 1);
                    retVal.value = ss;
                }
            }

            return retVal;
        }

        private ValueLabel GetAlergiesValueLabel(List<VMPatientAllergyInt> allergies, string label)
        {
            ValueLabel retVal = new ValueLabel();
            retVal.label = label;
            retVal.value = "None";

            string ss = "";
            foreach (var item in allergies)
            {
                if (item.MedicationName != null && item.MedicationName != "")
                {
                    ss += " " + CultureInfo.CurrentCulture.TextInfo.ToTitleCase(item.MedicationName.ToLower()) + " " + (item.Severity ?? "") + ",  ";
                    ss = ss.Trim();
                }
            }
            if (ss != "")
            {
                ss = ss.Trim().Substring(0, ss.Length - 1);
                retVal.value = ss;
            }

            return retVal;
        }

        private byte[] GetEmptyImage()
        {
            Bitmap bmp = new Bitmap(16, 16);
            ImageConverter converter = new ImageConverter();

            return (byte[])converter.ConvertTo(bmp, typeof(byte[]));
        }

        private byte[] FetchImigBytesForReport(int n)
        {
            string imgLeftUrl = "";
            if (n == 1)
            {
                imgLeftUrl = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Images/bulleseye.jpg";
            }
            else if (n == 4)
            {
                imgLeftUrl = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Images/ecg_1.pdf";
            }
            else if (n == 6 || n == 8 || n == 9 || n == 14 || n == 13)
            {
                imgLeftUrl = _webHostEnvironment.WebRootPath + "/Areas/PdfConversions/Images/1437031410-Brough,MaryE.pdf";
            }
            else if (n == 12)
            {
                // TODO: In ASP.NET Core, use IWebHostEnvironment instead of Server.MapPath
                imgLeftUrl = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Areas/PdfConversions/Images/24_Hour_Ambulatory_Bp_Monitoring_Report_for_Sigrid_Maushagen_1_20_2017.pdf");
            }
            byte[] content = System.IO.File.ReadAllBytes(imgLeftUrl);

            return content;
        }


        public byte[] FetchImigBytesForReportContext(int n, Microsoft.AspNetCore.Http.HttpContext HttpContext)//Temporarry
        {
            string imgLeftUrl = "";
            if (n == 1)
            {
                imgLeftUrl = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Areas/PdfConversions/Images/bulleseye.jpg");
            }
            else if (n == 4)
            {
                imgLeftUrl = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Areas/PdfConversions/Images/ecg_1.pdf");
            }
            else if (n == 6 || n == 8 || n == 9 || n == 14 || n == 13)
            {
                imgLeftUrl = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Areas/PdfConversions/Images/1437031410-Brough,MaryE.pdf");
            }
            else if (n == 12)
            {
                imgLeftUrl = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Areas/PdfConversions/Images/24_Hour_Ambulatory_Bp_Monitoring_Report_for_Sigrid_Maushagen_1_20_2017.pdf");
            }
            byte[] content = System.IO.File.ReadAllBytes(imgLeftUrl);

            return content;
        }

        private byte[] GetImageBytes(int imageBytesId)
        {
            try
            {
                return FetchImigBytesForReport(imageBytesId);
            }
            catch (Exception)
            {
                return null;
            }
        }

        private bool CheckIfPdfFile(byte[] imageArray)
        {
            if (imageArray == null) return false;
            if (imageArray.Length == 0) return false;

            bool retBool = false;
            PdfReader pdfReader = null;
            try
            {
                pdfReader = new PdfReader(imageArray);
                retBool = true;
                if (pdfReader != null)
                {
                    pdfReader.Close();
                }
            }
            catch (Exception)
            {
                if (pdfReader != null)
                {
                    pdfReader.Close();
                }
            }

            return retBool;
        }
        private string RetrieveMeasurement(MesurementByCategory general2Category, string name)
        {
            try
            {
                var q = (general2Category.mesurements.Where(t1 => t1.label.Trim() == name).FirstOrDefault() != null) ?
                    general2Category.mesurements.Where(t1 => t1.label.Trim() == name).FirstOrDefault() : null;//???
                if (q != null)
                {
                    if (string.IsNullOrWhiteSpace(q.value))
                        return "";
                    return q.value + " " + q.Units;
                }
            }
            catch (Exception x)
            {
                Helper.WriteToLog($"Exception in RetrieveMeasurement() reading {name}");
                Helper.WriteToLog(x.Message);
            }
            return "";
        }

        private string RetrieveMeasurement2(MesurementByCategory category, string v1, string v2)
        {
            try
            {
                var q = (category.mesurements.Where(t1 =>
                t1.label != null && (t1.label.Trim().ToLower().Contains(v1) || t1.label.Trim().ToLower().Contains(v2))).FirstOrDefault());
                if (q != null)
                {
                    if (string.IsNullOrWhiteSpace(q.value))
                        return "";
                    return q.value + " " + q.Units;
                }
            }
            catch (Exception x)
            {
                Helper.WriteToLog($"Exception in RetrieveMeasurement2() reading {v1} or {v2} ");
                Helper.WriteToLog(x.Message);
            }
            return "";
        }

    }
}