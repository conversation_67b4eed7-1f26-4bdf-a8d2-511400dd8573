@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Patient.VMHealthCardDbCompare
@functions{
    public string formatDate(string arg){     
// return String.IsNullOrEmpty(arg) ? arg : (Convert.ToDateTime(arg)).ToString("MM/dd/yyyy"); 
    }
}
@if (Model != null){
<div>
    @Html.Hidden("PatientRecordId", @Model.PatientRecordId)
    @Html.Hidden("HasAnyAppointmentToday", @Model.HasAnyAppointmentToday)
    @if (@Model.PatientsArrived.Count() > 0)
    {

        {
            @Html.HiddenFor(x => @Model.PatientsArrived[i])
        }
    }
    
    <table class="table table-striped">
        <thead><tr><th></th><th>From Card</th><th>Patient File</th><th>Action Required</th></tr></thead>
        <tbody>
        @{

        <tr class="@ohipmsg"><td>Number</td><td>@Model.HealthNumber.Item1</td><td>@Model.HealthNumber.Item2</td>
            <td>
            @if (!@Model.HealthNumber.Item3)
            {
                <span>@*</span>*@
            }
            </td>
        </tr>
        <tr class="@VCmsg"><td>Version Code</td><td>@Model.VersionCode.Item1</td><td>@Model.VersionCode.Item2</td>
            <td>
                @if (!@Model.VersionCode.Item3)
                {
                    <span>@*</span>*@
                }
            </td>
            </tr>
        <tr class="@LNmsg"><td>Last Name</td><td>@Model.LastName.Item1</td><td>@Model.LastName.Item2</td>
            <td>
                @if (!@Model.LastName.Item3)
                {
                    <span>*</span>
                }
            </td>
            </tr>
        <tr class="@FNmsg"><td>First Name</td><td>@Model.FirstName.Item1</td><td>@Model.FirstName.Item2</td>
            <td>
                @if (!@Model.FirstName.Item3)
                {
                    <span>*</span>
                }
            </td>
            </tr>
        <tr class="@BDmsg"><td>DoB</td>
            <td>@formatDate(@Model.BirthDate.Item1.ToString()) Model.BirthDate.Item1*@</td>
            <td>@formatDate(@Model.BirthDate.Item2.ToString()) Model.BirthDate.Item2*@</td>
            <td>
                @if (!@Model.BirthDate.Item3) {
                    <span></span>
                }
            </td>
        </tr>
        <tr class="@Ismsg"><td>Issued Date</td>
            <td>@formatDate(@Model.Issuedate.Item1.ToString()) @*Model.Issuedate.Item1*@ </td>
            <td>@formatDate(@Model.Issuedate.Item2.ToString()) @*Model.Issuedate.Item2*@</td>
            <td>
                @if (!@Model.Issuedate.Item3) {
                    <span></span>
                }
            </td>
        </tr>
        <tr class="@Exmsg"><td>Expiry Date</td>
            <td>@formatDate(@Model.ExpiryDate.Item1.ToString()) @*Model.ExpiryDate.Item1*@ </td>
            <td>@formatDate(@Model.ExpiryDate.Item2.ToString()) @*Model.ExpiryDate.Item2*@</td>
            <td>
                @if (!@Model.ExpiryDate.Item3)
                {
                    <span>*</span>
                }
            </td>
        </tr>
    </tbody>
</table>
</div>  
<div>
    <br />
    @if (!(@Model.HealthNumber.Item3 && @Model.VersionCode.Item3 && @Model.LastName.Item3 && @Model.FirstName.Item3 && @Model.BirthDate.Item3 && @Model.Issuedate.Item3 && @Model.ExpiryDate.Item3))
    {
        <p class="alert-danger">* Please update Patient Info</p>
    }
    
</div> 

else
{
    @Html.Display("Not Found")

