﻿using Cerebrum30.Areas.Daysheet.DataAccess;
using Cerebrum30.Areas.Schedule.DataAccess;
using Cerebrum30.Areas.Schedule.Mappers.ViewModels;
using Cerebrum30.Areas.Schedule.Models.Helper;
using Cerebrum30.Areas.Schedule.Models.ViewModels;
using Cerebrum30.Areas.VP.DataAccess;
using Cerebrum30.DAL.DataAccessGB;
using Cerebrum30.Utility;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum.Data;
using AwareMD.Cerebrum.Shared;
using AwareMD.Cerebrum.Shared.Enums;
using log4net;
using Newtonsoft.Json;
using Cerebrum30.Areas.Schedule.Helpers;

namespace Cerebrum30.Areas.Schedule.Mappers
{
    public class ScheduleMapper : IDisposable
    {
        readonly IUnitOfWorkSchedule unitRep;
        readonly IUnitOfWorkHealthCardService unitRepHC; // health card unit of work
        readonly AppointmentTypeRepository appTypeRep;
        readonly VPRepository vpRepository;
        public Scheduler _sch;
        private CerebrumContext context;
        log4net.ILog _log;
        public ScheduleMapper(IUnitOfWorkSchedule uofwSchedule, IUnitOfWorkHealthCardService uofCardRepo)
        {
            unitRep = uofwSchedule;
            unitRepHC = uofCardRepo;
            _sch = new Scheduler(uofwSchedule);
            appTypeRep = new AppointmentTypeRepository();
            vpRepository = new VPRepository();
            context = new CerebrumContext();
            _log = LogManager.GetLogger(typeof(ScheduleMapper));
        }

        public VMScheduleMain GetScheduleMain(int practiceId)
        {
            var scheduleMain = new VMScheduleMain();
            scheduleMain.PracticeId = practiceId;
            scheduleMain.Offices = GetOffices(practiceId);
            return scheduleMain;
        }

        public VMSchedule GetSchedule(int practiceId, VMScheduleRequest scheduleRequest, VMOffice selectedOffice)
        {
            var schedule = new VMSchedule();
            var scheduleTypes = GetScheduleTypes();
            //var offices = GetOffices(practiceId);
            //var office = offices.FirstOrDefault(o => o.Id == scheduleRequest.OfficeId);
            var scheduleType = scheduleTypes.FirstOrDefault(s => s.Id == scheduleRequest.ScheduleTypeId);

            schedule.Office = selectedOffice;
            schedule.ScheduleType = scheduleType;
            schedule.SelectedDate = scheduleRequest.Date;
            schedule.ScheduleTypes = scheduleTypes;
            schedule.AppointmentTypes = GetAppointmentTypes();
            schedule.TestTypes = GetTestTypes();
            LoadData(schedule);
            return schedule;
        }

        public VMDaySheet GetDaySheet(int practiceId, VMDaySheetRequest daySheetRequest, VMOffice selectedOffice)
        {
            var daySheet = new VMDaySheet();
            IEnumerable<Appointment> dbAppointments;
            var scheduleTypes = GetScheduleTypes();
            var practiceUsers = IndentityInfo.UserManager.Users.Where(p => p.PracticeID == practiceId).ToList();
            var practiceTests = GetPracticeTests(practiceId);
            var practiceDoctors = GetPracticeDoctors(practiceId);
            var appointmentTypeItems = GetAppointmentTypeItems();
            var appointmentTestStatuses = GetAppointmentTestStatuses();
            var appointmentConfirmations = GetAppointmentConfirmations();
            //var officeTechs = GetOfficeTechsLookup(selectedOffice.Id);
            var startOfDay = daySheetRequest.Date.Date;
            var endOfDay = startOfDay.AddDays(1);
            var scheduleType = scheduleTypes.FirstOrDefault(s => s.Id == daySheetRequest.ScheduleTypeId);
            daySheet.Office = selectedOffice;
            daySheet.ScheduleType = scheduleType;
            daySheet.SelectedDate = daySheetRequest.Date;
            daySheet.ScheduleTypes = scheduleTypes;
            daySheet.AppointmentStatuses = GetAppointmentStatuses();
            daySheet.AppointmentConfirmations = appointmentConfirmations;
            daySheet.TestStatuses = appointmentTestStatuses;
            daySheet.PracticeDoctors = practiceDoctors;
            daySheet.PracticeTests = practiceTests;
            daySheet.PaymentMethods = GetPaymentMethods();
            daySheet.AppointmentStatusId = daySheetRequest.AppointmentStatusId;
            daySheet.DoctorId = daySheetRequest.DoctorId;
            daySheet.Expected = daySheetRequest.Expected;
            daySheet.ExcludeTestOnly = daySheetRequest.ExcludeTestOnly;

            //get appointments data
            dbAppointments = unitRep.appointmentRepository
            .Find(a => a.appointmentStatus != AppointmentStatus.WaitList
            && a.OfficeId == selectedOffice.Id
            && a.appointmentTime >= startOfDay && a.appointmentTime < endOfDay).OrderBy(o => o.appointmentTime);

            //apply filters
            if (daySheetRequest.DoctorId > 0)
            {
                dbAppointments = dbAppointments.Where(a => a.PracticeDoctorId == daySheetRequest.DoctorId);
            }

            if (daySheetRequest.AppointmentStatusId >= 0)
            {
                var appointmentStatus = (AppointmentStatus)daySheetRequest.AppointmentStatusId;
                dbAppointments = dbAppointments.Where(a => a.appointmentStatus == appointmentStatus);
            }

            if (daySheetRequest.Expected)
            {

            }


            foreach (var dbAppointment in dbAppointments.ToList())
            {
                var appointment = LoadAppointmentItem(dbAppointment, appointmentTypeItems, appointmentTestStatuses, appointmentConfirmations, practiceDoctors, practiceTests, practiceUsers);
                daySheet.Appointments.Add(appointment);

            }

            if (daySheetRequest.ExcludeTestOnly)
            {
                daySheet.Appointments = daySheet.Appointments.Where(a => a.AppointmentTypeId != 1).ToList();
            }

            return daySheet;
        }

        public VMAppointmentItem GetAppointmentItem(int appointmentId)
        {
            VMAppointmentItem appItem = null;
            var dbAppointment = unitRep.appointmentRepository.GetById(appointmentId);

            if (dbAppointment != null)
            {
                var practiceId = unitRep.officeRepository.GetById(dbAppointment.OfficeId).PracticeId;
                var practiceUsers = IndentityInfo.UserManager.Users.Where(p => p.PracticeID == practiceId).ToList();
                var practiceTests = GetPracticeTests(practiceId);
                var practiceDoctors = GetPracticeDoctors(practiceId);
                var appointmentTypeItems = GetAppointmentTypeItems();
                var appointmentTestStatuses = GetAppointmentTestStatuses();
                var appointmentConfirmations = GetAppointmentConfirmations();
                appItem = LoadAppointmentItem(dbAppointment, appointmentTypeItems, appointmentTestStatuses, appointmentConfirmations, practiceDoctors, practiceTests, practiceUsers);
            }

            return appItem;

        }

        public VMAppointment GetAppointment(int appointmentId)
        {
            VMAppointment appointment = null;
            var dbAppointment = unitRep.appointmentRepository.Find(a => a.Id == appointmentId).FirstOrDefault();
            if (dbAppointment != null)
            {

                //TODO: add appointment provider
                appointment = new VMAppointment();
                appointment.Id = dbAppointment.Id;
                appointment.PatientId = dbAppointment.PatientRecordId;
                var patDemo = dbAppointment.PatientRecord.Demographics.LastOrDefault();
                appointment.PatientFullName = patDemo.FullName;
                appointment.OfficeId = dbAppointment.OfficeId;
                appointment.PracticeId = unitRep.officeRepository.Find(o => o.Id == appointment.OfficeId).First().PracticeId;
                appointment.ReferralDoctorId = dbAppointment.referralDoctorId;
                if (appointment.ReferralDoctorId > 0)
                {
                    appointment.ReferralDoctor = unitRepHC.ExternalDoctorRepro.Find(d => d.Id == appointment.ReferralDoctorId).First().FullName;
                }
                else
                {
                    appointment.ReferralDoctor = "";
                }
                appointment.AppointmentTypeId = dbAppointment.AppointmentTypeId;
                appointment.AppointmentDate = dbAppointment.appointmentTime;
                appointment.AppointmentTime = dbAppointment.appointmentTime.ToShortTimeString();
                appointment.PracticeDoctorId = dbAppointment.PracticeDoctorId;
                appointment.Notes = dbAppointment.appointmentNotes;
                appointment.AppointmentStatus = dbAppointment.appointmentStatus;
                appointment.Purpose = dbAppointment.appointmentPurpose;
                appointment.PaymentMethodId = (int)dbAppointment.appointmentPaymentMethod;
                appointment.PreConditions = GetPreconditions(); // get all the preconditions
                appointment.ActionOnAbnormal = dbAppointment.actionOnAbnormal;
                appointment.MWLSentFlag = dbAppointment.MWLSentFlag;
                appointment.MWLURL = dbAppointment.MWLUrl;
                var practiceTests = GetPracticeTests(appointment.PracticeId);
                foreach (var test in practiceTests)
                {
                    var dbAppTest = dbAppointment.appointmentTests.FirstOrDefault(a => a.TestId == test.TestId && a.IsActive==true);
                    if (dbAppTest != null)
                    {
                        test.AppointmentTestId = dbAppTest.Id;
                        test.TestDate = dbAppTest.startTime;
                        test.TestStartTime = dbAppTest.startTime.ToShortTimeString();
                        test.TestDuration = (int)dbAppTest.testDuration;
                        foreach (var tr in dbAppTest.AppointmentTestResources)
                        {
                            test.TestResources.Add(new VMAppointmentTestResource { UserId = tr.assignedToUserId, AppointmentTestId = test.AppointmentTestId });
                        }

                        test.TestStatusId = dbAppTest.AppointmentTestStatusId;
                        test.Selected = true;
                    }
                }
                //var officeUrl = unitRep.officeUrlRepository.Find(of => of.officeId == dbAppointment.OfficeId && of.officeUrlType.urlType.Equals("MWL")).FirstOrDefault();
                //if (officeUrl != null)
                //{
                //    appointment.MWLURL = MWL(dbAppointment, patDemo, appointment.app, dbAppTests, officeUrl.url);
                //}
                var preConditions = GetPreconditions();
                foreach (var preCon in preConditions)
                {
                    var dbPreCon = dbAppointment.appointmentPreconditons
                        .FirstOrDefault(p => p.Type.ToLower() == preCon.Name.ToLower() && p.Status);

                    if (dbPreCon != null)
                    {
                        preCon.AppointmentPreconId = dbPreCon.Id;
                        preCon.Selected = true;
                    }
                }

                appointment.Tests = practiceTests;
                appointment.PreConditions = preConditions;


            }


            return appointment;
        }

        public VMCommentsMain GetCommentsMain(int patientId)
        {
            var commentsMain = new VMCommentsMain();
            //TODO: fix to use repository
            var dbPatient = GetPatientRecord(patientId);
            commentsMain.PatientId = patientId;
            commentsMain.PatientFullName = dbPatient.Demographics.First().FullName;
            commentsMain.CommentSearch.PatientId = patientId;
            commentsMain.Comments = GetDoctorsComments(patientId,
                commentsMain.CommentSearch.CommentStart,
                commentsMain.CommentSearch.CommentEnd);

            commentsMain.CommentAdd.CommentPatientId = patientId;
            commentsMain.CommentAdd.StartDate = commentsMain.CommentSearch.CommentStart;
            commentsMain.CommentAdd.EndDate = commentsMain.CommentSearch.CommentEnd;
            return commentsMain;
        }

        public VMAppointmentHistory GetAppointmentHistory(int appointmentId)
        {

            var appHistory = new VMAppointmentHistory();
            var appointment = GetAppointment(appointmentId);
            var modifers = unitRep.appointmentModifierRepository.Find(f => f.AppointmentId == appointmentId);
            var first = modifers.FirstOrDefault();
            appHistory.AppointmentId = appointmentId;
            appHistory.AppointmentTime = appointment.AppointmentTime;

            var j = AppointmentJsonDeserialize(first.changes);
            appHistory.CreatedBy = first.userId.ToString(); // TODO: Get actual username from userId
            appHistory.PracticeDoctorId = appointment.PracticeDoctorId;
            appHistory.PracticeDoctor = j["doctor"].ToString();
            appHistory.PatientId = appointment.PatientId;
            appHistory.PatientFullName = j["patientName"].ToString();
            appHistory.AppointmentChanges = GetAppointmentChanges(modifers.ToList());

            return appHistory;
        }
        private Dictionary<string, string> AppointmentJsonDeserialize(string json)
        {
            return JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
        }
        public List<VMAppHistoryItem> GetAppointmentChanges(List<AppointmentModifier> history)
        {
            var appHistoryItems = new List<VMAppHistoryItem>();

            history = history.Where(w => (!w.reasonforChange.Equals("New"))).OrderByDescending(o => o.createDate).ToList();

            foreach (var h in history)
            {
                var appHistoryItem = new VMAppHistoryItem();
                appHistoryItem.Id = h.Id;
                appHistoryItem.ChangeDate = h.createDate;
                appHistoryItem.ChangedBy = h.userId.ToString(); // TODO: Get actual username from userId
                appHistoryItem.Reason = h.reasonforChange;
                Dictionary<string, string> changes = JsonConvert.DeserializeObject<Dictionary<string, string>>(h.changes);
                appHistoryItem.appointmentDate = DateTime.Parse(changes["appointmentTime"].ToString());
                appHistoryItem.Doctor = changes["doctor"].ToString();
                appHistoryItem.patientName = changes["patientName"].ToString();
                appHistoryItem.Office = changes["name"].ToString();

                appHistoryItem.ReferralDoctor = changes["RefMD"].ToString();

                appHistoryItems.Add(appHistoryItem);
            }

            return appHistoryItems;
        }

        public List<VMComment> GetDoctorsComments(int patientId, DateTime startDate, DateTime endDate, bool all = false)
        {
            //TODO: fix to add the doctors name
            var comments = new List<VMComment>();
            var dbComments = new List<DoctorComment>();
            if (all)
            {
                dbComments = context.DoctorComments
                    .Where(p => p.PatientRecordId == patientId)
                    .OrderByDescending(o => o.DateCreated)
                    .ToList();
            }
            else
            {
                var start = startDate.Date;
                var end = endDate.Date.AddDays(1);
                dbComments = context.DoctorComments
                   .Where(p => p.PatientRecordId == patientId &&
                   p.DateCreated >= start && p.DateCreated <= end)
                   .OrderByDescending(o => o.DateCreated)
                   .ToList();
            }


            foreach (var dbComment in dbComments)
            {
                var comment = new VMComment();
                comment.Id = dbComment.Id;
                comment.CommentDate = dbComment.DateCreated;
                comment.Comments = dbComment.Comment;
                comment.Doctor = "Testing Doctor";

                comments.Add(comment);
            }

            return comments;

        }

        public List<VMOffice> GetOffices(int practiceId)
        {
            var offices = new List<VMOffice>();
            var dbOffices = unitRep.officeRepository.Find(f => f.PracticeId == practiceId).ToList();
            foreach (var dbOffice in dbOffices)
            {
                var office = new VMOffice();
                office.Id = dbOffice.Id;
                office.PracticeId = dbOffice.PracticeId;
                office.Name = dbOffice.name;
                offices.Add(office);
            }

            return offices;
        }

        public List<VMScheduleType> GetScheduleTypes()
        {
            var scheduleTypes = new List<VMScheduleType>();
            scheduleTypes.Add(new VMScheduleType() { Id = 1, Name = "2 Weeks", DisplayOrder = 3 });
            scheduleTypes.Add(new VMScheduleType() { Id = 2, Name = "Week", DisplayOrder = 2 });
            scheduleTypes.Add(new VMScheduleType() { Id = 3, Name = "Day", DisplayOrder = 1 });

            return scheduleTypes.OrderBy(o => o.DisplayOrder).ToList(); ;
        }

        public List<VMResourceType> GetResourceTypes()
        {
            var resourceTypes = new List<VMResourceType>();
            var dbResourceTypes = unitRep.testResourceTypeRepository.GetAll();
            foreach (var dbResourceType in dbResourceTypes)
            {
                var resourceType = new VMResourceType();
                resourceType.Id = dbResourceType.Id;
                resourceType.Name = dbResourceType.TestResourceName;

                resourceTypes.Add(resourceType);
            }

            return resourceTypes;
        }

        public List<VMVisitType> GetVisitTypes()
        {
            var visitTypes = new List<VMVisitType>();

            visitTypes.Add(new VMVisitType() { Id = 1, Name = "New Patient", Color = "#DC143C" }); //crimson
            visitTypes.Add(new VMVisitType() { Id = 2, Name = "New Triage Patient", Color = "#228B22" }); //forestgreen
            visitTypes.Add(new VMVisitType() { Id = 3, Name = "2 New Patients", Color = "#FF00FF" }); //fuchsia
            visitTypes.Add(new VMVisitType() { Id = 4, Name = "New + Regular", Color = "#FFD700" }); //Gold
            visitTypes.Add(new VMVisitType() { Id = 5, Name = "Regular", Color = "#1E90FF" }); //dodgerblue
            visitTypes.Add(new VMVisitType() { Id = 6, Name = "2 Regular", Color = "#0000FF" }); //Blue            

            return visitTypes;
        }

        public List<VMAppointmentType> GetAppointmentTypes()
        {
            var appTypes = new List<VMAppointmentType>();

            appTypes.Add(new VMAppointmentType() { Id = 1, Name = "New Patient", Color = "#DC143C" }); //crimson
            appTypes.Add(new VMAppointmentType() { Id = 2, Name = "New Triage Patient", Color = "#228B22" }); //forestgreen
            appTypes.Add(new VMAppointmentType() { Id = 3, Name = "2 New Patients", Color = "#FF00FF" }); //fuchsia
            appTypes.Add(new VMAppointmentType() { Id = 4, Name = "New + Regular", Color = "#FFD700" }); //Gold
            appTypes.Add(new VMAppointmentType() { Id = 5, Name = "Regular", Color = "#1E90FF" }); //dodgerblue
            appTypes.Add(new VMAppointmentType() { Id = 6, Name = "2 Regular", Color = "#0000FF" }); //Blue            

            return appTypes;
        }

        public List<VMAppointmentTypeItem> GetAppointmentTypeItems()
        {
            var appTypeItems = new List<VMAppointmentTypeItem>();
            var dbAppTypes = appTypeRep.Find(f => f.appointmentType == null).ToList();
            foreach (var dbAppType in dbAppTypes)
            {
                foreach (var dbAppTypeItem in dbAppType.children)
                {
                    var appTypeItem = new VMAppointmentTypeItem();
                    appTypeItem.Id = dbAppTypeItem.Id;
                    appTypeItem.Name = dbAppTypeItem.name;
                    appTypeItem.AppointmentTypeId = dbAppType.Id;
                    appTypeItem.AppointmentType = dbAppType.name;

                    appTypeItems.Add(appTypeItem);
                }
            }

            return appTypeItems;
        }

        public List<VMAppointmentConfirmation> GetAppointmentConfirmations()
        {
            var appConfirms = new List<VMAppointmentConfirmation>();
            var dbAppConfirms = Enum.GetValues(typeof(AppConfirmation))
                .Cast<AppConfirmation>().ToList();

            foreach (var dbAppConfirm in dbAppConfirms)
            {
                var appConfirm = new VMAppointmentConfirmation();
                var appConfirmDesc = dbAppConfirm.GetAttributeValue<DescriptionAttribute, string>(x => x.Description);

                appConfirm.Id = (int)dbAppConfirm;
                appConfirm.Text = Enum.GetName(typeof(AppointmentStatus), dbAppConfirm);
                appConfirm.Color = Enum.GetName(typeof(AppointmentStatus), dbAppConfirm);
                appConfirm.Description = appConfirmDesc != null ? appConfirmDesc : appConfirm.Text;
                appConfirms.Add(appConfirm);
            }


            return appConfirms;
        }

        public List<VMAppointmentStatus> GetAppointmentStatuses()
        {
            var appStatuses = new List<VMAppointmentStatus>();
            var dbAppStatuses = Enum.GetValues(typeof(AppointmentStatus))
                .Cast<AppointmentStatus>().ToList();

            foreach (var dbAppStatus in dbAppStatuses)
            {
                var appStatus = new VMAppointmentStatus();
                var appStatusDesc = dbAppStatus.GetAttributeValue<DescriptionAttribute, string>(x => x.Description);

                appStatus.Id = (int)dbAppStatus;
                appStatus.Text = Enum.GetName(typeof(AppointmentStatus), dbAppStatus);
                appStatus.Color = Enum.GetName(typeof(AppointmentStatus), dbAppStatus);
                appStatus.Description = appStatusDesc != null ? appStatusDesc : appStatus.Text;
                appStatuses.Add(appStatus);
            }


            return appStatuses;
        }

        public List<VMAppointmentTestStatus> GetAppointmentTestStatuses()
        {
            var appTestStatuses = new List<VMAppointmentTestStatus>();
            var dbAppTestStatuses = unitRep.appointmentTestStatusRepository.GetAll().ToList();

            foreach (var dbAppTestStatus in dbAppTestStatuses)
            {
                var appTestStatus = new VMAppointmentTestStatus();
                appTestStatus.Id = dbAppTestStatus.Id;
                appTestStatus.Status = dbAppTestStatus.Status;
                appTestStatus.Color = dbAppTestStatus.Color;

                appTestStatuses.Add(appTestStatus);
            }

            return appTestStatuses;
        }

        public List<VMPrecondition> GetPreconditions()
        {
            var preConditions = new List<VMPrecondition>();
            var dbPreConditions = Enum.GetValues(typeof(AppPreconditions))
                .Cast<AppPreconditions>().ToList();

            foreach (var dbPreCondition in dbPreConditions)
            {
                var preCondition = new VMPrecondition();
                preCondition.Id = (int)dbPreCondition;
                preCondition.Name = Enum.GetName(typeof(AppPreconditions), dbPreCondition);

                preConditions.Add(preCondition);
            }


            return preConditions;
        }

        public List<VMPaymentMethod> GetPaymentMethods()
        {
            var paymentMethods = new List<VMPaymentMethod>();
            var dbPaymentMethods = Enum.GetValues(typeof(PaymentMethod))
                .Cast<PaymentMethod>().ToList();

            foreach (var dbPaymentMethod in dbPaymentMethods)
            {
                var paymentMethod = new VMPaymentMethod();
                paymentMethod.Id = (int)dbPaymentMethod;
                paymentMethod.Name = Enum.GetName(typeof(PaymentMethod), dbPaymentMethod).Replace("_", "");

                paymentMethods.Add(paymentMethod);
            }

            return paymentMethods;
        }

        public List<VMTestType> GetTestTypes()
        {
            var testTypes = new List<VMTestType>();

            testTypes.Add(new VMTestType() { Id = 1, Name = "Echo", Color = "#FF69B4" }); // hotpink
            testTypes.Add(new VMTestType() { Id = 2, Name = "EPED", Color = "#FF7F50" }); // coral
            testTypes.Add(new VMTestType() { Id = 3, Name = "Holter", Color = "#DAA520" }); //goldenrod
            testTypes.Add(new VMTestType() { Id = 4, Name = "BP", Color = "#FFE4C4" }); // bisque
            testTypes.Add(new VMTestType() { Id = 5, Name = "SE", Color = "#00FFFF" }); // cyan
            testTypes.Add(new VMTestType() { Id = 6, Name = "GXT", Color = "#8FBC8F" }); // darkseagreen

            return testTypes;
        }

        public List<VMResouce> GetResources(ScheduleDayVM dbDay, int timeSlotIncrement)
        {
            var resources = new List<VMResouce>();
            var docs = new List<VMResouce>();

            // load resources
            foreach (var dbResource in dbDay.scheduleDayResources.OrderBy(o =>
            o.permissionIds.OrderBy(p => p).FirstOrDefault()))
            {
                var resource = new VMResouce();
                resource.Id = dbResource.UserId;
                resource.FullName = dbResource.userName;
                resource.Initials = dbResource.userInitial;
                resource.StartTime = dbResource.startTime;
                resource.EndTime = dbResource.finishTime;
                resource.ResourceType = new VMResourceType { Name = Enum.GetName(typeof(UserTypeEnum), dbResource.TestResourceType), Color = dbResource.colorHex };// resourceTypes.FirstOrDefault(i => i.Id == dbResource.TestResourceType);
                resource.IsDoctor = resource.ResourceType != null && resource.ResourceType.Name.ToLower() == "doctor" ? true : false;

                foreach (var dbResourceSlot in dbResource.ScheduleTimeSlots)
                {
                    if (dbResourceSlot.Status != AwareMD.Cerebrum.Shared.Enums.SlotStatus.breaktime)
                    {
                        var resourceSlot = new VMResourceTimeSlot();
                        resourceSlot.StartHour = dbResourceSlot.startHour;
                        resourceSlot.StartMinute = dbResourceSlot.startMinute;
                        resourceSlot.EndHour = dbResourceSlot.endHour;
                        resourceSlot.EndMinute = dbResourceSlot.endMinute;
                        resourceSlot.Color = dbResourceSlot.colorHex;
                        resourceSlot.Duration = (resourceSlot.EndMinute - resourceSlot.StartMinute) + ((resourceSlot.EndHour - resourceSlot.StartHour) * 60);

                        foreach (var dbAppInfo in dbResourceSlot.AppointmentInfo)
                        {
                            var appInfo = new VMAppointmentInfo();
                            appInfo.AppointmentId = dbAppInfo.appointmentId;
                            appInfo.AppointmentTypeItem = dbAppInfo.AppointmentType;
                            appInfo.FirstName = dbAppInfo.firstname;
                            appInfo.LastName = dbAppInfo.lastName;
                            appInfo.PatientId = dbAppInfo.PatientRecordId;
                            appInfo.OHIP = dbAppInfo.OHIP;
                            appInfo.TestId = dbAppInfo.TestId;
                            appInfo.TestName = dbAppInfo.testName;
                            appInfo.TestDuration = (int)dbAppInfo.testDuration;
                            appInfo.Color = dbAppInfo.color;
                            resourceSlot.Appointments.Add(appInfo);
                        }
                        resourceSlot.SlotStatus = GetSlotStatus(dbResourceSlot.Status);
                        resource.TimeSlots.Add(resourceSlot);
                    }
                }

                if (resource.IsDoctor)
                {
                    docs.Add(resource);
                }
                else
                {
                    resources.Add(resource);
                }
            }

            resources.InsertRange(0, docs);
            return resources;
        }

        public List<VMAppointmentTest> GetPracticeTests(int practiceId)
        {
            var tests = new List<VMAppointmentTest>();
            var dbPracticeTests = unitRep.practicetestRepository
                .Find(p => p.PracticeId == practiceId).ToList();

            foreach (var dbTest in dbPracticeTests)
            {
                var test = new VMAppointmentTest();
                test.TestId = dbTest.TestId;
                test.Name = dbTest.Test.testShortName;
                test.TestFullName = dbTest.Test.testFullName;
                test.TestDuration = dbTest.Test.duration;
                test.RequireDevice = dbTest.Test.RequireDevice;
                tests.Add(test);

            }
            return tests;
        }

        public List<VMPatient> GetPracticePatients(int practiceId)
        {
            var patients = new List<VMPatient>();
            var dbPatients = unitRep.patientRecordRepository.Find(p => p.PracticeId == practiceId);
            foreach (var dbPatient in dbPatients)
            {
                var demographics = dbPatient.Demographics.LastOrDefault();
                var patient = new VMPatient();
                patient.Id = dbPatient.Id;
                patient.FirstName = demographics.firstName;
                patient.LastName = demographics.lastName;
                patient.FullName = demographics.FullName;
                patient.DateOfBirth = demographics.dateOfBirth;
                patient.DefaultPaymentMethodId = (int)demographics.defaultPaymentMethod;
                patient.OHIP = "";

                if (demographics.defaultReferralDoctors.Any())
                {
                    patient.LastReferringDoctorId = demographics.defaultReferralDoctors.Last().Id;
                }

                if (demographics.healthcards.Any())
                {
                    patient.OHIP = demographics.healthcards.LastOrDefault().number;
                }

                patients.Add(patient);
            }

            return patients;
        }

        public List<VMResouce> GetPracticeScheduleUsers(int practiceId)
        {
            var scheduleUsers = new List<VMResouce>();

            var dbScheduleUsers = unitRep.scheduleUserRepository.Find(s => s.PracticeId == practiceId).ToList();
            var dbUsers = IndentityInfo.UserManager.Users.Where(u => u.PracticeID == practiceId).ToList();

            var schUsers = (from sd in dbScheduleUsers
                            join dbu in dbUsers on sd.UserId equals dbu.UserID
                            select new { sd, dbu }).ToList();

            foreach (var item in schUsers)
            {
                var resource = new VMResouce();
                resource.Id = item.sd.UserId;
                resource.FirstName = item.dbu.FirstName;
                resource.LastName = item.dbu.LastName;
                resource.FullName = resource.FirstName + " " + resource.LastName;

                scheduleUsers.Add(resource);
            }

            return scheduleUsers;
        }

        public List<VMTest> GetResourceTests(int userId, int practiceId)
        {
            var tests = new List<VMTest>();

            return tests;
        }

        public Cerebrum.Data.PatientRecord GetPatientRecord(int patientId)
        {
            return unitRep.patientRecordRepository.Find(p => p.Id == patientId).FirstOrDefault();
        }

        public List<VMDoctor> GetPracticeDoctors(int practiceId)
        {

            var doctors = new List<VMDoctor>();
            var dbDocs = (from exd in context.ExternalDoctors
                          join prd in context.PracticeDoctors on exd.Id equals prd.ExternalDoctorId
                          where prd.PracticeId == practiceId && prd.ApplicationUser != null
                          select new { prd, exd }).ToList();
            foreach (var dbDoctor in dbDocs)
            {
                var doctor = new VMDoctor();
                doctor.Id = dbDoctor.exd.Id;
                doctor.PracticeDoctorId = dbDoctor.prd.Id;
                doctor.FirstName = dbDoctor.exd.firstName;
                doctor.LastName = dbDoctor.exd.lastName;
                doctors.Add(doctor);
            }

            return doctors;
        }

        public VMDoctorScheduleMain GetPracticeDoctorSchedule(int practiceDocId)
        {
            var dbPracDoc = unitRepHC.PracticeDoctorRepro.Find(d => d.Id == practiceDocId).FirstOrDefault();
            var user = IndentityInfo.UserManager.Users.FirstOrDefault(u => u.Id == dbPracDoc.ApplicationUserId);
            if (user != null)
            {
                Office off = null;
                var doc = unitRepHC.ExternalDoctorRepro.GetById(dbPracDoc.ExternalDoctorId);

                var docScheduleMain = new VMDoctorScheduleMain { DoctorName = "Dr. " + doc.firstName + " , " + doc.lastName, PracticeDoctorId = dbPracDoc.Id };
                var docSchedule = new List<VMDoctorSchedule>();

                var usersch = _sch.GetScheduleUser(dbPracDoc.PracticeId, user.UserID);
                if (usersch != null)
                {
                    var currentDate = DateTime.Today;
                    var dates = _sch.DateRange(currentDate, usersch.endDate);
                    foreach (var dt in dates)
                    {
                        var dygrp = from ug in usersch.ScheduleWeekDays
                                    where ug.dayOfWeek == dt.DayOfWeek
                                    group ug by ug.dayOfWeek into scdygrp
                                    select new { scdygrp.Key, other = scdygrp };
                        foreach (var d in dygrp)
                        {
                            var ds = new VMDoctorSchedule { PracticeId = dbPracDoc.PracticeId };
                            ScheduleWeekDay swd = null;
                            if (d.other.Any(a => a.date != null))
                            {
                                var dy = d.other.Where(w => w.date == dt);
                                if (dy != null)
                                {
                                    swd = dy.FirstOrDefault();
                                }
                                else
                                {
                                    swd = d.other.FirstOrDefault(a => a.date == null);
                                }
                            }
                            else
                            {
                                swd = d.other.OrderBy(o => o.date).FirstOrDefault();
                            }
                            // to reduce database trip
                            if (off == null || off.Id != swd.officeId)
                                off = unitRep.officeRepository.GetById(swd.officeId);

                            ds.Date = swd.date == null ? dt : (DateTime)swd.date;
                            ds.StartTime = swd.startTime;
                            ds.FinisihTime = swd.finishTime;
                            ds.OfficeId = swd.officeId;
                            ds.Officename = off.name;
                            ds.DoctotId = dbPracDoc.Id;
                            ds.Comments = swd.comment;

                            docSchedule.Add(ds);

                            //currentDate = currentDate.AddDays(1);
                        }
                    }



                    docScheduleMain.DoctorScheduleList = docSchedule;
                }

                return docScheduleMain;
            }

            return null;
        }

        public int GetSelectedPracticeDoctorId(int userId)
        {
            var user = IndentityInfo.UserManager.Users.Where(u => u.UserID == userId).FirstOrDefault();
            var dbPracDoc = unitRepHC.PracticeDoctorRepro.Find(d => d.ApplicationUserId == user.Id).FirstOrDefault();
            return dbPracDoc.Id;
        }

        public List<int> GetResourceTestIds(int userId, int practiceId)
        {
            var ids = new List<int>();
            var practiceTests = GetPracticeTests(practiceId);
            var user = IndentityInfo.UserManager.Users.Where(u => u.UserID == userId).FirstOrDefault();
            var permissions = IndentityInfo.FindPermissions(user).Distinct().ToArray();
            return ids;
        }

        public List<VMDoctor> GetReferralDoctors(string doctorName)
        {
            var doctors = new List<VMDoctor>();
            var dbReferalDoctors = unitRepHC.ExternalDoctorRepro
                .Find(d => d.firstName.ToLower().Contains(doctorName) || d.lastName.ToLower().Contains(doctorName));

            foreach (var dbRefDoctor in dbReferalDoctors)
            {
                var doctor = new VMDoctor();
                doctor.Id = dbRefDoctor.Id;
                doctor.FirstName = dbRefDoctor.firstName;
                doctor.LastName = dbRefDoctor.lastName;
                doctors.Add(doctor);
            }

            return doctors;
        }

        public List<VMDoctor> GetPatientReferralDoctors(int patientId)
        {
            var doctors = new List<VMDoctor>();
            var dbPatient = unitRep.patientRecordRepository.Find(p => p.Id == patientId).FirstOrDefault();
            var demographics = dbPatient.Demographics.LastOrDefault();

            foreach (var dbRefDoctor in demographics.defaultReferralDoctors)
            {
                var dbDocInfo = unitRepHC.ExternalDoctorRepro.Single(d => d.Id == dbRefDoctor.ExternalDoctorId);
                var doctor = new VMDoctor();
                doctor.Id = dbRefDoctor.Id;
                doctor.FirstName = dbDocInfo.firstName;
                doctor.LastName = dbDocInfo.lastName;
                doctors.Add(doctor);
            }

            return doctors;
        }

        public List<VMLookupItem> GetOfficeTechsLookup(int officeId)
        {
            var techs = new List<VMLookupItem>();

            var dbTechs = (from u in context.Users
                           join su in context.ScheduleUsers on u.UserID equals su.UserId
                           join of in context.Offices on su.PracticeId equals of.PracticeId
                           where of.Id == officeId && u.CerebrumUserType != UserTypeEnum.Room &&
                           u.CerebrumUserType != UserTypeEnum.Doctor
                           select u).ToList();

            foreach (var user in dbTechs)
            {
                var tech = new VMLookupItem();
                tech.Value = user.UserID.ToString();
                tech.Text = user.FirstName + " " + user.LastName;
                techs.Add(tech);
            }

            return techs;
        }

        public List<VMAppointmentResult> BookAppointments(List<VMAppointmentOption> selectedOptions)
        {
            return new List<VMAppointmentResult>()
            {
                new VMAppointmentResult() { AppointmentId = 1, Status = AppointmentBookStatus.Booked, StatusMessage = "Booked" },
                new VMAppointmentResult() { AppointmentId = 2, Status = AppointmentBookStatus.Booked, StatusMessage = "Booked" },
                new VMAppointmentResult() { AppointmentId = 3, Status = AppointmentBookStatus.Booked, StatusMessage = "Booked" },
            };
        }

        public string AddTestToAppointment(int practiceId, int appointmentId, int testId, string user)//,bool doublebook)
        {
            var app = unitRep.appointmentRepository.GetById(appointmentId);
            var patientArrived = false;
            var currentDate = System.DateTime.Now;

            var testsAdded = new List<AppointmentTest>(); // newly added tests

            // check to see if the patient is in the office
            if (app.appointmentTime.Date == currentDate.Date && (app.appointmentStatus == AppointmentStatus.Arrived
                || app.appointmentStatus == AppointmentStatus.DoingTests
                || app.appointmentStatus == AppointmentStatus.ReadyForDoctor))
            {
                patientArrived = true;
            }
            var tests = app.appointmentTests;

            var dbtests = unitRep.testRepository.GetById(testId);
            var selectedTests = new List<VMAppointmentTest>();
            selectedTests.Add(new VMAppointmentTest { TestId = dbtests.Id });

            foreach (var t in tests)
            {
                var newTest = new VMAppointmentTest();
                if (t.AppointmentTestResources != null && t.AppointmentTestResources.Count() > 0)
                {
                    foreach (var tr in t.AppointmentTestResources)
                    {
                        newTest.TestResources.Add(new VMAppointmentTestResource { AppointmentTestId = tr.AppointmentTestId, UserId = tr.assignedToUserId });
                    }
                    newTest.AppointmentId = t.AppointmentId;
                    newTest.AppointmentTestId = t.Id;
                    newTest.TestId = t.TestId;
                    newTest.TestDuration = (int)t.testDuration;
                    //newTest.Name = t.testShortName;
                    newTest.ReferralDoctorId = t != null ? t.referralDoctorId : 0;
                    newTest.Selected = true;

                    selectedTests.Add(newTest);
                }
            }
            var slots = _sch.NewEmptySlotsMultipleResourceCheckTimeWithPermission(practiceId, app.OfficeId, app.Id, app.appointmentTime, selectedTests, false, false);
            string testBookedMessage = "Cannot Add test";
            if (slots.SelectedTests != null && slots.SelectedTests.Count() == selectedTests.Count())
            {
                foreach (var test in slots.SelectedTests)
                {
                    var dbAppTest = new AppointmentTest();
                    dbAppTest.AppointmentId = appointmentId;
                    dbAppTest.TestId = test.TestId;
                    dbAppTest.testDuration = (TestDuration)test.TestDuration;
                    dbAppTest.referralDoctorId = app.referralDoctorId;
                    dbAppTest.startTime = test.TestDate;
                    dbAppTest.DateCreated = currentDate;
                    if (patientArrived)
                    {
                        dbAppTest.AppointmentTestStatusId = (int)AppointmentTestStatuses.Arrived; //arrived - patient is already in office
                    }
                    foreach (var tr in test.TestResources)
                    {
                        dbAppTest.AppointmentTestResources.Add(new AppointmentTestResource { assignedToUserId = tr.UserId, permissionId = tr.permissionId, isDoctorRequiredInOffice = tr.isDoctorRequiredInOffice });
                    }
                    //unitRep.appointmentTestRepository.Add(dbAppTest);
                    testsAdded.Add(dbAppTest);
                }
                try
                {
                    //unitRep.appointmentTestRepository.Save();
                    unitRep.appointmentTestRepository.ModifyTests(appointmentId, testsAdded, user);
                    var newTests = string.Join(",", slots.SelectedTests.Select(s => s.Name));
                    var changes = AppointmentChanges(app, newTests, user, "Test Added");
                    unitRep.appointmentModifierRepository.Add(changes);
                    unitRep.appointmentModifierRepository.Save();
                }
                catch { return testBookedMessage; }

            }
            else
                return testBookedMessage;

            return "Test Added";
        }
        private VMAppointmentOption AppointmentTests(VMAppointment ap, DateTime appointmentDateTime, bool bookReservedSlot, bool findNextAvailableDateEmptySlot)
        {
            var appointmentTypeItem = context.AppointmentTypes.FirstOrDefault(a => a.Id == ap.AppointmentTypeId);
            var selectedTests = ap.Tests.Where(t => t.Selected).ToList();

            if (appointmentTypeItem != null && appointmentTypeItem.VPRequired)
            {
                var dbVpTest = unitRep.testRepository.Find(t => t.testShortName.ToLower() == "vp").FirstOrDefault();
                if (dbVpTest != null)
                {
                    var vpTest = new VMAppointmentTest();
                    vpTest.TestResources.Add(new VMAppointmentTestResource { UserId = ap.ResourceId });
                    vpTest.TestId = dbVpTest.Id;
                    vpTest.TestDuration = (int)appointmentTypeItem.duration;
                    vpTest.Name = dbVpTest.testShortName;
                    vpTest.ReferralDoctorId = ap.ReferralDoctorId;
                    vpTest.Selected = true;

                    selectedTests.Add(vpTest);
                }
            }
            var appOption = _sch.NewEmptySlotsMultipleResourceCheckTimeWithPermission(ap.PracticeId, ap.OfficeId, ap.Id, appointmentDateTime, selectedTests, bookReservedSlot, findNextAvailableDateEmptySlot);
            return appOption;
        }

        public VMAppointmentResult CreateAppointment(VMAppointment appointment, int userId)
        {

            var appResult = new VMAppointmentResult();
            var selectedTests = appointment.Tests.Where(t => t.Selected).ToList();
            var selectedPrecons = appointment.PreConditions.Where(p => p.Selected).ToList();
            var appointmentTypeItem = context.AppointmentTypes.FirstOrDefault(a => a.Id == appointment.AppointmentTypeId);
            var dateTime = string.IsNullOrWhiteSpace(appointment.AppointmentTime) ? Convert.ToDateTime(appointment.AppointmentDate.ToShortDateString()) : Convert.ToDateTime(appointment.AppointmentDate.ToShortDateString() + " " + appointment.AppointmentTime);
            var dbAppointmentTests = new List<AppointmentTest>();
            var dbAppointment = new Appointment();
            dbAppointment.PatientRecordId = appointment.PatientId;
            dbAppointment.OfficeId = appointment.OfficeId;
            dbAppointment.referralDoctorId = appointment.ReferralDoctorId;
            dbAppointment.PracticeDoctorId = appointment.PracticeDoctorId;
            dbAppointment.AppointmentTypeId = appointment.AppointmentTypeId;
            dbAppointment.appointmentRegistrar = userId;
            dbAppointment.appointmentNotes = appointment.Notes;
            dbAppointment.appointmentPurpose = appointment.Purpose;
            dbAppointment.appointmentPaymentMethod = (PaymentMethod)appointment.PaymentMethodId;
            dbAppointment.actionOnAbnormal = appointment.ActionOnAbnormal;

            foreach (var precon in selectedPrecons)
            {
                var dbPrecon = new AppointmentPreconditon();
                dbPrecon.Type = precon.Name;
                dbPrecon.Status = true;

                dbAppointment.appointmentPreconditons.Add(dbPrecon);
            }

            // add a vp test if its required base on the appointment type item

            if (appointmentTypeItem != null && appointmentTypeItem.VPRequired)
            {
                var dbVpTest = unitRep.testRepository.Find(t => t.testShortName.ToLower() == "vp").FirstOrDefault();
                if (dbVpTest != null)
                {
                    var vpTest = new VMAppointmentTest();
                    vpTest.TestResources.Add(new VMAppointmentTestResource { UserId = appointment.ResourceId });
                    vpTest.TestId = dbVpTest.Id;
                    vpTest.TestDuration = (int)appointmentTypeItem.duration;
                    vpTest.Name = dbVpTest.testShortName;
                    vpTest.ReferralDoctorId = appointment.ReferralDoctorId;
                    vpTest.Selected = true;

                    selectedTests.Add(vpTest);
                }
            }
            string newTests = "";
            if (selectedTests.Any())
            {
                var tests = selectedTests.Select(s => s.TestId).ToArray();
                //when there is no availability, this will be null
                var appOption = _sch.NewEmptySlotsMultipleResourceCheckTimeWithPermission(appointment.PracticeId, appointment.OfficeId, null, dateTime, selectedTests, false, false);
                // validate all tests booked 
                if (appOption == null)//|| appOption.SelectedTests.Count!=selectedTests.Count)
                {
                    var passedTests = selectedTests.Select(s => s.Name).ToArray();
                    //var returnedTests = appOption.SelectedTests.Select(r => r.Name).ToArray();
                    //var ex = passedTests.Except(returnedTests);
                    appResult.Status = AppointmentBookStatus.NotAvailable;
                    appResult.StatusMessage = string.Join(",", passedTests) + " Timeslot Not Found.";
                    return appResult;
                }
                else if (appOption != null && appOption.SelectedTests.Count != selectedTests.Count)
                {
                    var passedTests = selectedTests.Select(s => s.Name).ToArray();
                    var returnedTests = appOption.SelectedTests.Select(r => r.Name).ToArray();
                    var ex = passedTests.Except(returnedTests);
                    appResult.Status = AppointmentBookStatus.NotAvailable;
                    appResult.StatusMessage = string.Join(",", ex) + " Timeslot Not Found.";
                    return appResult;
                }
                newTests = string.Join(",", appOption.SelectedTests.Select(s => s.Name));
                var dateTestsCreated = System.DateTime.Now;
                foreach (var test in appOption.SelectedTests)
                {
                    var dbAppTest = new AppointmentTest();
                    dbAppTest.TestId = test.TestId;
                    dbAppTest.testDuration = (TestDuration)test.TestDuration;
                    dbAppTest.referralDoctorId = appointment.ReferralDoctorId;
                    dbAppTest.startTime = test.TestDate;
                    foreach (var tr in test.TestResources)
                    {
                        dbAppTest.AppointmentTestResources.Add(new AppointmentTestResource { assignedToUserId = tr.UserId, permissionId = tr.permissionId, isDoctorRequiredInOffice = tr.isDoctorRequiredInOffice });
                    }

                    dbAppTest.DateCreated = dateTestsCreated;
                    dbAppointmentTests.Add(dbAppTest);
                }
            }

            // log provider
            if (appointment.PracticeDoctorId > 0)
            {
                var pracDoc = unitRepHC.PracticeDoctorRepro.Find(p => p.Id == appointment.PracticeDoctorId).FirstOrDefault();
                if (pracDoc != null)
                {
                    var currentDate = System.DateTime.Now;
                    dbAppointment.appointmentProviders.Add(new AppointmentProvider { ExternalDoctorId = pracDoc.ExternalDoctorId, DateCreated = currentDate });
                }
            }

            var appTime = dbAppointmentTests != null &&
                dbAppointmentTests.Any() ?
               dbAppointmentTests.OrderBy(o => o.startTime).First().startTime : dateTime;
            dbAppointment.appointmentTime = appTime;
            dbAppointment.appointmentStatus = AppointmentStatus.Booked;
            dbAppointment.DateCreated = DateTime.Now;

            using (var transaction = context.Database.BeginTransaction())
            {
                try
                {
                    context.Appointments.Add(dbAppointment);
                    context.SaveChanges(userId.ToString());

                    foreach (var test in dbAppointmentTests)
                    {
                        test.AppointmentId = dbAppointment.Id;
                        context.AppointmentTests.Add(test);
                        context.SaveChanges(userId.ToString());

                        test.AccessionNumber = dbAppointment.Id + "_" + test.Id;
                        context.Entry(test).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                        context.SaveChanges(userId.ToString());
                    }
                    try
                    {
                        var changes = AppointmentChanges(dbAppointment, newTests, userId.ToString(), "New");
                        context.AppointmentModifiers.Add(changes);
                        context.SaveChanges(userId.ToString());
                    }
                    catch (Exception nex)
                    {
                        _log.Error("Appointment Modifier Adding exception : " + nex.Message);
                    }
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    _log.Error("Create Appointment" + ex.Message);
                    return new VMAppointmentResult { StatusMessage = "Please contact Admin for more info.", Status = AppointmentBookStatus.NotBooked };
                }
            }
            //unitRep.appointmentRepository.Add(dbAppointment);
            //unitRep.appointmentRepository.Save();

            appointment.Id = dbAppointment.Id;
            appResult.AppointmentId = dbAppointment.Id;
            appResult.Status = AppointmentBookStatus.Booked;
            appResult.StatusMessage = "Booked";

            return appResult;
        }
        private AppointmentModifier AppointmentChanges(Appointment a, string tests, string userName, string reason)
        {
            var office = unitRep.officeRepository.GetById(a.OfficeId);
            var patient = unitRep.demographicRepository.Find(f => f.PatientRecordId == a.PatientRecordId).LastOrDefault();
            var practiceDoctor = unitRep.practiceDoctorRepository.GetById(a.PracticeDoctorId);
            var doctor = practiceDoctor == null ? null : unitRep.externalDoctorRepository.GetById(practiceDoctor.ExternalDoctorId);
            var referal = unitRep.externalDoctorRepository.GetById(a.referralDoctorId);
            var selectedChange = new { appointmentId = a.Id, office.name, patientName = patient.lastName + " , " + patient.firstName, doctor =doctor!=null? doctor.firstName + " " + doctor.lastName:"", RefMD = referal != null ? referal.firstName + " " + referal.lastName : "", a.appointmentTime, tests = tests };

            var changes = JsonConvert.SerializeObject(selectedChange, Formatting.Indented, new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            });

            return new AppointmentModifier { AppointmentId = a.Id, changes = changes, createDate = DateTime.Now, reasonforChange = reason != null ? reason : "" };
        }
        private void AppointmentPreConditions(CerebrumContext context, VMAppointment editAppointment)
        {
            var editPreconditions = editAppointment.PreConditions.Where(p => p.Selected).Select(s => s.Name).ToList();
            if (editPreconditions != null)
            {
                var dbPreconditions = context.AppointmentPreconditions.Where(app => app.AppointmentId == editAppointment.Id);
                if (dbPreconditions != null && dbPreconditions.Count() > 0)
                {
                    var newPreconditions = editPreconditions.Except(dbPreconditions.Select(d => d.Type).ToList());
                    foreach (var pre in newPreconditions)
                    {
                        context.AppointmentPreconditions.Add(new AppointmentPreconditon { Status = true, Type = pre, AppointmentId = editAppointment.Id });
                    }
                    var removePreconditions = dbPreconditions.Select(d => d.Type).ToList().Except(editPreconditions);
                    foreach (var r in removePreconditions)
                    {
                        var torem = dbPreconditions.FirstOrDefault(f => f.Type == r);
                        context.AppointmentPreconditions.Remove(torem);
                    }

                }
                else
                {
                    foreach (var pre in editPreconditions)
                    {
                        context.AppointmentPreconditions.Add(new AppointmentPreconditon { Status = true, Type = pre, AppointmentId = editAppointment.Id });
                    }

                }

            }
        }
        private void AppointmentTests(CerebrumContext context, VMAppointment edAp, DateTime apDT, string user, out DateTime newDate, out string newTestsstring)
        {
            var dbTests = context.AppointmentTests.Where(a => a.AppointmentId == edAp.Id && a.IsActive==true);
            var appointmentTypeItem = context.AppointmentTypes.FirstOrDefault(a => a.Id == edAp.AppointmentTypeId);

            var selectedTests = edAp.Tests.Where(w => w.Selected == true).Count() == 0 ? new List<VMAppointmentTest>() : edAp.Tests.Where(w => w.Selected == true).ToList();
            if (appointmentTypeItem != null && appointmentTypeItem.VPRequired)
            {
                var dbVpTest = unitRep.testRepository.Find(t => t.testShortName.ToLower() == "vp").FirstOrDefault();
                if (dbVpTest != null && (!selectedTests.Any(a => a.TestId == dbVpTest.Id)))
                {
                    var vpTest = new VMAppointmentTest();
                    vpTest.TestResources.Add(new VMAppointmentTestResource { UserId = edAp.ResourceId });
                    vpTest.TestId = dbVpTest.Id;
                    vpTest.TestDuration = (int)appointmentTypeItem.duration;
                    vpTest.Name = dbVpTest.testShortName;
                    vpTest.ReferralDoctorId = edAp.ReferralDoctorId;
                    vpTest.Selected = true;

                    selectedTests.Add(vpTest);
                }
            }

            var appOption = _sch.NewEmptySlotsMultipleResourceCheckTimeWithPermission(edAp.PracticeId, edAp.OfficeId, edAp.Id, apDT, selectedTests, false, false);
            if (appOption != null && appOption.SelectedTests.Count != selectedTests.Count)
            {
                var passedTests = selectedTests.Select(s => s.Name).ToArray();
                var returnedTests = appOption.SelectedTests.Select(r => r.Name).ToArray();
                var ex = passedTests.Except(returnedTests);
                throw new VMAppointmentResultException { Status = Cerebrum.ViewModels.Schedule.AppointmentBookStatus.NotAvailable, StatusMessage = string.Join(",", ex) + " Timeslot Not Found.", AppointmentId = edAp.Id };
            }
            newTestsstring = string.Join(",", appOption.SelectedTests.Select(s => s.Name));
            var newTests = new List<AppointmentTest>();
            foreach (var test in appOption.SelectedTests)
            {
                var dbAppTest = new AppointmentTest();
                dbAppTest.AppointmentId = edAp.Id;
                dbAppTest.TestId = test.TestId;
                dbAppTest.testDuration = (TestDuration)test.TestDuration;
                dbAppTest.referralDoctorId = edAp.ReferralDoctorId;
                dbAppTest.startTime = test.TestDate;
                foreach (var tr in test.TestResources)
                {
                    dbAppTest.AppointmentTestResources.Add(new AppointmentTestResource { assignedToUserId = tr.UserId, permissionId = tr.permissionId, isDoctorRequiredInOffice = tr.isDoctorRequiredInOffice });
                }

                newTests.Add(dbAppTest);
            }

            if (dbTests != null && dbTests.Count() > 0)
            {
                foreach (var n in newTests)
                {
                    var dbt = context.AppointmentTests.FirstOrDefault(fd => fd.AppointmentId == n.AppointmentId && fd.TestId == n.TestId);
                    if (dbt == null)
                    {
                        context.AppointmentTests.Add(n);
                    }
                    else
                    {
                        dbt.testDuration = (TestDuration)n.testDuration;
                        dbt.referralDoctorId = edAp.ReferralDoctorId;
                        dbt.startTime = n.startTime;
                        dbt.IsActive = true;
                        context.Entry(dbt).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                    }
                    context.SaveChanges(user);
                }
                //var newexpTests = newTests.Select(s => new { s.TestId, s.AppointmentId }).Except(dbTests.Select(v => new { v.TestId, v.AppointmentId }).ToList());
                //foreach (var ext in newexpTests)
                //{
                //    var t = newTests.FirstOrDefault(fd => fd.AppointmentId == ext.AppointmentId && fd.TestId == ext.TestId);
                //    var dbt=context.AppointmentTests.FirstOrDefault(fd => fd.AppointmentId == ext.AppointmentId && fd.TestId == ext.TestId);
                //    if (dbt == null)
                //    {
                //        context.AppointmentTests.Add(t);
                //    }else
                //    {
                //        dbt.testDuration = (TestDuration)t.testDuration;
                //        dbt.referralDoctorId = edAp.ReferralDoctorId;
                //        dbt.startTime = t.startTime;
                //        dbt.IsActive = true;
                //        context.Entry(dbt).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                //        context.SaveChanges(user);
                //    }
                //    context.SaveChanges(user);
                //}
                var removeTests = dbTests.Select(s => new { s.TestId, s.AppointmentId }).ToList().Except(newTests.Select(v => new { v.TestId, v.AppointmentId }));

                foreach (var r in removeTests)
                {
                    var torem = dbTests.FirstOrDefault(fd => fd.AppointmentId == r.AppointmentId && fd.TestId == r.TestId);
                    torem.IsActive = false;
                    context.Entry(torem).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                    context.SaveChanges(user);
                }
            }
            else
            {
                foreach (var n in newTests)
                {
                    context.AppointmentTests.Add(n);
                }
                context.SaveChanges(user);

            }
            newDate = newTests.FirstOrDefault().startTime;
        }

        public VMAppointmentResult EditAppointment(VMAppointment appointment, int userId)
        {
            var appResult = new VMAppointmentResult();
            //check the date of the appointment
            //check the tests

            // AppointmentChanges for appointment modifer

            var selectedTests = appointment.Tests.Where(t => t.Selected).ToList();
            var testIds = selectedTests.Select(i => i.TestId).ToArray();
            var dateTime = Convert.ToDateTime(appointment.AppointmentDate.ToShortDateString() + " " + appointment.AppointmentTime);
            var appointmentTypeItem = context.AppointmentTypes.FirstOrDefault(a => a.Id == appointment.AppointmentTypeId);

            var dbAppointment = context.Appointments.Find(appointment.Id);
            dbAppointment.referralDoctorId = appointment.ReferralDoctorId;
            dbAppointment.PracticeDoctorId = appointment.PracticeDoctorId;
            dbAppointment.AppointmentTypeId = appointment.AppointmentTypeId;
            dbAppointment.appointmentRegistrar = userId;
            dbAppointment.appointmentNotes = appointment.Notes;

            bool appointmentDateChange = DateTime.Compare(dateTime.Date, dbAppointment.appointmentTime.Date) > 0 | DateTime.Compare(dateTime.Date, dbAppointment.appointmentTime.Date) < 0;
            if (appointmentDateChange)
            {

                using (var transcation = context.Database.BeginTransaction())
                {
                    try
                    {
                        AppointmentPreConditions(context, appointment);

                        string updatedTests = "";

                        AppointmentTests(context, appointment, dateTime, userId.ToString(), out dateTime, out updatedTests);

                        dbAppointment.appointmentTime = dateTime;
                        context.Entry(dbAppointment).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                        context.SaveChanges(userId.ToString());

                        //TO DO Appointment Reason
                        var changeHistory = AppointmentChanges(dbAppointment, updatedTests, userId.ToString(), dbAppointment.appointmentNotes);
                        context.AppointmentModifiers.Add(changeHistory);
                        context.SaveChanges(userId.ToString());

                        transcation.Commit();
                    }
                    catch (Exception ex)
                    {
                        transcation.Rollback();
                        appResult.Status = AppointmentBookStatus.NotBooked;
                        appResult.StatusMessage = "Please contact you admin for more info.";

                        return appResult;
                    }
                }
            }


            appResult.Status = AppointmentBookStatus.Booked;
            appResult.StatusMessage = "Updated Successfully";

            return appResult;
        }

        //public VMAppointmentBook GetAppointmentBookingOptions(VMAppointmentRequest appointmentRequest)
        //{
        //    var appBooking = new VMAppointmentBook();
        //    var practiceTests = GetPracticeTests(appointmentRequest.PracticeId).ToList();
        //    var selectedTests = practiceTests.Where(p => appointmentRequest.SelectedTests.Any(t => t.TestId == p.TestId));
        //    var appointmentTypes = GetAppointmentTypeItems();

        //    appBooking.AppointmentDate = appointmentRequest.AppointmentDate;
        //    appBooking.AppointmentTime = System.DateTime.Now.ToShortTimeString();
        //    appBooking.OfficeId = appointmentRequest.OfficeId;
        //    appBooking.OfficeName = GetOffices(appointmentRequest.PracticeId).FirstOrDefault().Name;
        //    appBooking.PatientId = appointmentRequest.PatientId;
        //    appBooking.PatientName = GetPatientRecord(appointmentRequest.PatientId).Demographics.LastOrDefault().FullName;
        //    appBooking.ReferralDoctorId = 0;

        //    for (int i = 0; i < 10; i++)
        //    {
        //        var option = new VMAppointmentOption();
        //        option.OptionNumber = (i + 1);
        //        option.PracticeId = appointmentRequest.PracticeId;
        //        option.OfficeId = appointmentRequest.OfficeId;
        //        option.PatientId = appointmentRequest.PatientId;
        //        option.AppointmentTypeId = appointmentRequest.AppointmentTypeId;
        //        option.AppointmentTypeName = appointmentTypes.Where(a => a.Id == appointmentRequest.AppointmentTypeId).FirstOrDefault().Name;
        //        option.ReferralDoctorId = appointmentRequest.ReferralDoctorId;
        //        option.RequestedDate = appointmentRequest.AppointmentDate;
        //        option.StartTime = "9:00 AM";
        //        option.EndTime = "12:00 PM";
        //        option.TotalDuration = 180;


        //        foreach (var dbTest in selectedTests)
        //        {
        //            var test = new VMAppointmentTest();
        //            test.Id = dbTest.Id;
        //            test.Name = dbTest.Name;
        //            test.TestDate = System.DateTime.Now;
        //            test.TestDuration = dbTest.TestDuration;
        //            test.TestStartTime = "9:00 AM";
        //            test.TestEndTime = "10:00 AM";

        //            option.SelectedTests.Add(test);
        //        }

        //        appBooking.AppointmentOptions.Add(option);
        //    }

        //    return appBooking;
        //}

        public VMAppointmentBook GetAppointmentBookingOptions(VMAppointmentRequest appointmentRequest)
        {
            var appBooking = new VMAppointmentBook();
            var practiceTests = GetPracticeTests(appointmentRequest.PracticeId).ToList();
            var appointmentTypes = GetAppointmentTypeItems();
            var testDayGrp = from a in appointmentRequest.SelectedTests
                             group a by a.TestDateTime into ag
                             select new { ag.Key, ag };

            appBooking.AppointmentDate = appointmentRequest.AppointmentDate;
            appBooking.AppointmentTime = appointmentRequest.AppointmentTime;
            appBooking.OfficeId = appointmentRequest.OfficeId;
            appBooking.OfficeName = GetOffices(appointmentRequest.PracticeId).FirstOrDefault().Name;
            appBooking.PatientId = appointmentRequest.PatientId;
            appBooking.PatientName = GetPatientRecord(appointmentRequest.PatientId).Demographics.LastOrDefault().FullName;
            appBooking.ReferralDoctorId = appointmentRequest.ReferralDoctorId;
            //appBooking.PracticeDoctorId = 

            //foreach (var testDay in testDayGrp)
            //{
            //    var aro = appointmentRequests;


            //    appBooking.AppointmentDate = aro.AppointmentDate;
            //    appBooking.AppointmentTime = aro.AppointmentTime;
            //    appBooking.OfficeId = aro.OfficeId;
            //    appBooking.OfficeName = GetOffices(aro.PracticeId).FirstOrDefault().Name;
            //    appBooking.PatientId = aro.PatientId;
            //    appBooking.PatientName = GetPatientRecord(aro.PatientId).Demographics.LastOrDefault().FullName;
            //    appBooking.ReferralDoctorId = 0;
            //    var tests = testDay.ag.Select(s => s.TestId).ToArray();

            //    var emptySlotOp = _sch.NewEmptySlots(aro.PracticeId, aro.OfficeId, aro.AppointmentDate, tests, true);

            //    emptySlotOp.PatientId = aro.PatientId;
            //    emptySlotOp.AppointmentTypeId = aro.AppointmentTypeId;
            //    emptySlotOp.AppointmentTypeName = appointmentTypes.Where(a => a.Id == aro.AppointmentTypeId).FirstOrDefault().Name;
            //    emptySlotOp.ReferralDoctorId = aro.ReferralDoctorId;
            //    emptySlotOp.RequestedDate = aro.AppointmentDate;

            //    appBooking.AppointmentOptions.Add(emptySlotOp);
            //}

            return appBooking;
        }
        public VMAppointmentBook GetAppointmentBookingOptionsNew(VMAppointmentRequest appointmentRequests)
        {
            var appBooking = new VMAppointmentBook();
            var typegrp = from a in appointmentRequests.SelectedTests
                          group a by a.TestDateTime into ag
                          select new { ag.Key, ag };
            foreach (var g in typegrp)
            {
                var aro = appointmentRequests;
                var practiceTests = GetPracticeTests(aro.PracticeId).ToList();
                var appointmentTypes = GetAppointmentTypeItems();

                appBooking.AppointmentDate = aro.AppointmentDate;
                appBooking.AppointmentTime = aro.AppointmentTime;
                appBooking.OfficeId = aro.OfficeId;
                appBooking.OfficeName = GetOffices(aro.PracticeId).FirstOrDefault().Name;
                appBooking.PatientId = aro.PatientId;
                appBooking.PatientName = GetPatientRecord(aro.PatientId).Demographics.LastOrDefault().FullName;
                appBooking.ReferralDoctorId = 0;
                var tests = g.ag.Select(s => s.TestId).ToArray();

                var emptySlotOp = _sch.NewEmptySlots(aro.PracticeId, aro.OfficeId, aro.AppointmentDate, tests, true);

                emptySlotOp.PatientId = aro.PatientId;
                emptySlotOp.AppointmentTypeId = aro.AppointmentTypeId;
                emptySlotOp.AppointmentTypeName = appointmentTypes.Where(a => a.Id == aro.AppointmentTypeId).FirstOrDefault().Name;
                emptySlotOp.ReferralDoctorId = aro.ReferralDoctorId;
                emptySlotOp.RequestedDate = aro.AppointmentDate;

                appBooking.AppointmentOptions.Add(emptySlotOp);
            }

            return appBooking;
        }

        public void AddComment(int patientId, int practiceDoctorId, string comment)
        {
            var dbComment = new DoctorComment();
            dbComment.PatientRecordId = patientId;
            dbComment.PracticeDoctorId = practiceDoctorId;
            dbComment.Comment = comment;

            context.DoctorComments.Add(dbComment);
            context.SaveChanges();
        }

        public void UpdateMWLFlag(int appointmentId)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.MWLSentFlag = !ap.MWLSentFlag;
            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();
        }

        public void UpdateRoom(int appointmentId, string room)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.roomNumber = room;
            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();
        }

        public void UpdateAppConfirmation(int appointmentId, int appConfirmId)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.appointmentConfirmation = (AppConfirmation)appConfirmId;

            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();
        }

        public void UpdateAppointmentStatus(int appointmentId, int appointmentStatusId)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.appointmentStatus = (AppointmentStatus)appointmentStatusId;

            if (ap.appointmentStatus == AppointmentStatus.Arrived)
            {
                ap.LeftTime = null;
            }

            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();

            AppointmentStatusLog appLog = new AppointmentStatusLog();
            appLog.AppointmentId = ap.Id;
            appLog.changedDateTime = System.DateTime.Now;
            appLog.appointmentStatus = (AppointmentStatus)appointmentStatusId;
            appLog.userName = HttpContextProvider.Current.User.Identity.Name;

            unitRep.appointmentStatusLogRepository.Add(appLog);
            unitRep.appointmentStatusLogRepository.Save();
        }

        public void UpdateTestStatus(int appointmentId, int appointmentTestId, int testStatusId, string room = "")
        {
            unitRep.appointmentTestRepository.UpdateTestStatus(appointmentTestId, testStatusId);
            var appTestStatus = (AppointmentTestStatuses)testStatusId;

            if (!String.IsNullOrWhiteSpace(room))
            {
                var app = unitRep.appointmentRepository.GetById(appointmentId);
                app.roomNumber = room;
                unitRep.appointmentRepository.Edit(app);
                unitRep.appointmentRepository.Save();
            }

            if (appTestStatus == AppointmentTestStatuses.Test_Started)
            {
                var app = unitRep.appointmentRepository.GetById(appointmentId);
                if (app.appointmentStatus != AppointmentStatus.DoingTests)
                {
                    app.appointmentStatus = AppointmentStatus.DoingTests;
                    unitRep.appointmentRepository.Edit(app);
                    unitRep.appointmentRepository.Save();
                }
            }
        }

        public void UpdateTestStatusesToArrived(int appointmentId)
        {
            unitRep.appointmentTestRepository.UpdateTestStatusesToArrived(appointmentId);
        }

        public void UpdatePaymentMethod(int appointmentId, int paymentMethodId)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.appointmentPaymentMethod = (PaymentMethod)paymentMethodId;
            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();
        }
        private string MWL(Appointment app, Demographic demo, string provider, List<AppointmentTest> appTests, string officeUrl)
        {
            try
            {
                var mwl = string.IsNullOrWhiteSpace(app.MWLUrl);
                _log.Info("MWL URL Method:" + mwl);

                //var dtcomp = DateTime.Today.Date.CompareTo(app.appointmentTime.Date) == 0;
                if (mwl /*&& dtcomp*/)
                {
                    var gender = demo.gender == Gender.M ? 1 : 0;
                    DateTime birthDate;
                    DateTime.TryParse(demo.dateOfBirth.ToString(), out birthDate);

                    var testids = appTests.Select(s => s.TestId).ToArray();

                    var tests = unitRep.testRepository.Find(f => testids.Contains(f.Id));
                    var testnames = tests.Select(s => s.testShortName).ToArray();
                    var modalities = tests.Select(m => m.HrmModality).Where(m => !string.IsNullOrEmpty(m)).Distinct();
                    string tids = string.Join(",", testids);
                    string tns = string.Join(",", tests.Select(s => s.testShortName).ToArray());
                    string tm = string.Join(",", modalities);

                    app.MWLUrl = (officeUrl != null ? officeUrl : "") + "?aid=" + app.Id + "&pid=" + app.PatientRecordId + "&lname=" + demo.lastName + "&fname=" + demo.firstName + "&bd=" + (demo.dateOfBirth != null ? ((DateTime)demo.dateOfBirth).ToString("yyyyMMdd") : "") + "&sex=" + gender + "&tid=" + string.Join(",", testids) + "&tn=" + tns + "&dn=" + provider + "&tm=" + tm;

                    unitRep.appointmentRepository.Edit(app);
                    unitRep.appointmentRepository.Save();
                }
                //else
                //{
                //    _log.Info("Same Day MWL URL Creation only. Date difference found");
                //}

            }
            catch (Exception ex)
            {
                ex.Log<ScheduleMapper>();
                return null;
            }
            return app.MWLUrl;
        }

        public void UpdateArrivalTime(int appointmentId, string time)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.ArrivedTime = time;
            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();

            UpdateAppointmentStatus(appointmentId, (int)AppointmentStatus.Arrived);
            UpdateTestStatusesToArrived(appointmentId);
        }
        public int OHIPSwipArrived(string ohip, int practiceId, int officeId)
        {
            var hc = unitRep.demographicHealthcardRepository.Find(f => f.number.Equals(ohip)).LastOrDefault();
            if (hc != null)
            {
                var demo = hc.Demographic;

                if (demo.PatientRecord.PracticeId == practiceId)
                {
                    var patientRecordId = demo.PatientRecordId;
                    var td = DateTime.Today.Date;
                    var endDate = td.AddDays(1);
                    var appointments = unitRep.appointmentRepository.Find(a => (a.appointmentTime > td && a.appointmentTime <= endDate) && a.PatientRecordId == patientRecordId && a.OfficeId == officeId);

                    if (appointments != null && appointments.Count() > 0)
                    {
                        var ap = appointments.FirstOrDefault();
                        ap.ArrivedTime = DateTime.Now.ToString("HH:mm");
                        unitRep.appointmentRepository.Edit(ap);
                        unitRep.appointmentRepository.Save();

                        UpdateAppointmentStatus(ap.Id, (int)AppointmentStatus.Arrived);
                        UpdateTestStatusesToArrived(ap.Id);
                        return ap.Id;
                    }
                }
            }
            return 0;
        }
        public void UpdateLeftTime(int appointmentId, string time)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.LeftTime = time;
            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();

            UpdateAppointmentStatus(appointmentId, (int)AppointmentStatus.Left);
        }

        public void UpdatePrecondition(int appointmentId, int preconditionId)
        {
            //TODO: use repository for precondition
            var precon = context.AppointmentPreconditions.Where(f => f.AppointmentId == appointmentId && f.Id == preconditionId).FirstOrDefault();
            precon.Status = !precon.Status;
            context.Entry(precon).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
            context.SaveChanges();
        }

        public void UpdateActionOnAbornmal(int appointmentId)
        {
            var ap = unitRep.appointmentRepository.GetById(appointmentId);
            ap.actionOnAbnormal = !ap.actionOnAbnormal;
            unitRep.appointmentRepository.Edit(ap);
            unitRep.appointmentRepository.Save();
        }

        public bool DeleteAppointmentTest(int appointmentTestId)
        {
            var appTest = unitRep.appointmentTestRepository.SingleOrDefault(t => t.Id == appointmentTestId);
            if (appTest != null)
            {
                appTest.IsActive = false;
                unitRep.appointmentTestRepository.Edit(appTest);
                unitRep.appointmentTestRepository.Save();
                return true;
            }

            return false;
        }

        public bool hasOfficeSchedule(int officeId)
        {
            var schedule = unitRep.scheduleRepository.Find(f => f.OfficeId == officeId).FirstOrDefault();
            return schedule != null ? true : false;
        }

        private Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus GetSlotStatus(AwareMD.Cerebrum.Shared.Enums.SlotStatus dbSlotStatus)
        {
            switch (dbSlotStatus)
            {
                case AwareMD.Cerebrum.Shared.Enums.SlotStatus.available: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.Available;
                case AwareMD.Cerebrum.Shared.Enums.SlotStatus.occupied: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.Occupied;
                case AwareMD.Cerebrum.Shared.Enums.SlotStatus.breaktime: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.Break;
                case AwareMD.Cerebrum.Shared.Enums.SlotStatus.outsideofofficehours: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.OutsideOfficeHours;
                case AwareMD.Cerebrum.Shared.Enums.SlotStatus.overbooked: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.Overbooked;
                case AwareMD.Cerebrum.Shared.Enums.SlotStatus.reserved: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.Available; // Map reserved to available
                default: return Cerebrum30.Areas.Schedule.Mappers.ViewModels.SlotStatus.Break;
            }
        }

        private VMAppointmentItem LoadAppointmentItem(Appointment dbAppointment,
            List<VMAppointmentTypeItem> appointmentTypeItems,
            List<VMAppointmentTestStatus> appointmentTestStatuses,
            List<VMAppointmentConfirmation> appointmentConfirmations,
            List<VMDoctor> practiceDoctors,
            List<VMAppointmentTest> practiceTests,
            List<Cerebrum.Data.ApplicationUser> practiceUsers)
        {
            var appointment = new VMAppointmentItem();
            var patDemo = dbAppointment.PatientRecord.Demographics.LastOrDefault();
            var patientAlerts = vpRepository.GetCPPAlerts(dbAppointment.PatientRecordId);
            var refDoc = unitRepHC.ExternalDoctorRepro.Find(d => d.Id == dbAppointment.referralDoctorId).FirstOrDefault();
            var appointmenTypeItem = appointmentTypeItems.First(t => t.Id == dbAppointment.AppointmentTypeId);
            var officeTechs = GetOfficeTechsLookup(dbAppointment.OfficeId);

            appointment.AppointmentId = dbAppointment.Id;
            appointment.PatientId = dbAppointment.PatientRecordId;
            appointment.PatientFullName = patDemo != null ? patDemo.FullName : "";
            appointment.OfficeId = dbAppointment.OfficeId;
            appointment.PracticeId = unitRep.officeRepository.Find(o => o.Id == appointment.OfficeId).First().PracticeId;
            appointment.AppProviderId = dbAppointment.PracticeDoctorId;
            appointment.AppProvider = practiceDoctors.First(p => p.PracticeDoctorId == dbAppointment.PracticeDoctorId).FullName;
            appointment.ReferralDoctorId = dbAppointment.referralDoctorId;
            appointment.ReferralDoctor = refDoc != null ? refDoc.FullName : "";
            appointment.AppointmentTypeId = appointmenTypeItem.AppointmentTypeId;
            appointment.AppointmentType = appointmenTypeItem.AppointmentType;
            appointment.AppointmentTypeItemId = appointmenTypeItem.Id;
            appointment.AppointmentTypeItem = appointmenTypeItem.Name;
            appointment.AppointmentDate = dbAppointment.appointmentTime;
            appointment.AppointmentTime = dbAppointment.appointmentTime.ToShortTimeString();
            appointment.ArrivedTime = dbAppointment.ArrivedTime;
            appointment.LeftTime = dbAppointment.LeftTime;
            appointment.Notes = dbAppointment.appointmentNotes;
            appointment.Purpose = dbAppointment.appointmentPurpose;
            appointment.ActionOnAbnormal = dbAppointment.actionOnAbnormal;

            var appStatusDesc = dbAppointment.appointmentStatus.GetAttributeValue<DescriptionAttribute, string>(x => x.Description);
            appointment.AppointmentStatus = dbAppointment.appointmentStatus;
            appointment.AppointmentStatusDesc = appStatusDesc != null ? appStatusDesc : dbAppointment.appointmentStatus.ToString();

            var appConfirmDesc = dbAppointment.appointmentConfirmation.GetAttributeValue<DescriptionAttribute, string>(x => x.Description);
            appointment.AppConfirmation = dbAppointment.appointmentConfirmation;
            appointment.AppConfimationDesc = appConfirmDesc != null ? appConfirmDesc : dbAppointment.appointmentConfirmation.ToString();

            appointment.Purpose = dbAppointment.appointmentPurpose;
            appointment.PaymentMethodId = (int)dbAppointment.appointmentPaymentMethod;
            appointment.PaymentMethod = dbAppointment.appointmentPaymentMethod.ToString();
            appointment.Room = dbAppointment.roomNumber;


            //check for prerequisites
            appointment.Prerequisite = new VMPrerequisite() { Id = 1, Text = "Fullfilled", Color = "green" };

            //check for patient alerts
            foreach (var alert in patientAlerts)
            {
                if (!String.IsNullOrWhiteSpace(alert.Description) && alert.Description.ToLower().Contains("overdue"))
                {
                    var patAlert = new VMPatientAlert();
                    patAlert.Id = alert.Id;
                    patAlert.Description = alert.Description;

                    appointment.PatientAlerts.Add(patAlert);
                }
            }


            var dbAppTests = dbAppointment.appointmentTests.Where(t => t.IsActive).OrderBy(o => o.startTime).ToList();
            foreach (var dbAppTest in dbAppTests)
            {
                var test = new VMAppointmentItemTest();
                test.AppointmentId = dbAppTest.AppointmentId;
                test.AppointmentTestId = dbAppTest.Id;
                test.TestId = dbAppTest.TestId;
                test.TestDate = dbAppTest.startTime;
                test.TestStartTime = dbAppTest.startTime.ToShortTimeString();
                test.TestDuration = (int)dbAppTest.testDuration;
                test.Room = dbAppointment.roomNumber;
                var practiceTest = practiceTests.First(t => t.TestId == dbAppTest.TestId);
                test.Name = practiceTest.Name;
                test.TestFullName = practiceTest.TestFullName;
                test.RequireDevice = practiceTest.RequireDevice;
                foreach (var tr in dbAppTest.AppointmentTestResources)
                {
                    if (tr.assignedToUserId != null)
                        test.TestResourceIds.Add((int)tr.assignedToUserId);
                    var fullname = practiceUsers.FirstOrDefault(u => u.UserID == tr.assignedToUserId).FirstName;
                    test.TestResources.Add(new VMAppointmentTestResource { UserFullName = fullname, AppointmentTestId = tr.AppointmentTestId, UserId = tr.assignedToUserId });
                    //test.UserId = dbAppTest.ApplicationUserId;
                }

                test.TestStatusId = dbAppTest.AppointmentTestStatusId;
                var appTestStatus = appointmentTestStatuses.FirstOrDefault(t => t.Id == test.TestStatusId);
                test.TestStatusColor = appTestStatus != null ? appTestStatus.Color : "";
                test.TestStatusDesc = appTestStatus != null ? appTestStatus.Status : "";
                test.TestStatus = (AppointmentTestStatuses)test.TestStatusId;
                test.PhysicianComments = dbAppTest.PhysicianComments;
                test.TechnicianComments = dbAppTest.TechnicianComments;
                test.OfficeTechs = officeTechs;

                //test.TestResources = new List<int>() { test.UserId }; // fix to add more that one resource if there are multiple per test
                appointment.Tests.Add(test);
            }
            #region
            //TODO: fix for when there is no office url
            var officeUrl = unitRep.officeUrlRepository.Find(of => of.officeId == dbAppointment.OfficeId && of.officeUrlType.urlType.Equals("MWL")).FirstOrDefault();

            appointment.MWLUrl = dbAppointment.MWLUrl;

            if (officeUrl != null && appointment.MWLUrl == null)
            {
                appointment.MWLUrl = MWL(dbAppointment, patDemo, appointment.AppProvider, dbAppTests, officeUrl.url);
                _log.Info("MWL URL Created" + appointment.MWLUrl);
            }

            #endregion
            foreach (var dbPreCon in dbAppointment.appointmentPreconditons)
            {
                var preCon = new VMPrecondition();
                preCon.AppointmentPreconId = dbPreCon.Id;
                preCon.Name = dbPreCon.Type;
                preCon.Selected = dbPreCon.Status;

                appointment.PreConditions.Add(preCon);
            }


            if (refDoc != null)
            {
                if (refDoc.OHIPPhysicianId == null || refDoc.OHIPPhysicianId.Trim() == "")
                {
                    appointment.DoctorActionable.Add("Billing# missing");
                }
                if (refDoc.CPSO == null || refDoc.CPSO.Trim() == "")
                {
                    appointment.DoctorActionable.Add("CPSO# missing");
                }
                if (refDoc.phoneNumbers.Count == 0)
                {
                    appointment.DoctorActionable.Add("Phone# and Fax# missing");
                }
                else
                {
                    foreach (var pn in refDoc.phoneNumbers)
                    {
                        if (pn.faxNumber == null || pn.faxNumber.Trim() == "")
                        {
                            if (!appointment.DoctorActionable.Any(a => a.Equals("Fax# missing")))
                                appointment.DoctorActionable.Add("Fax# missing");
                        }
                    }
                }
            }

            //check missing demographic data              
            if (patDemo.dateOfBirth == null)
            {
                appointment.PatientActionable.Add("DOB missing");
            }
            var hc = patDemo.healthcards.LastOrDefault();
            if (patDemo.healthcards == null || patDemo.healthcards.Count() == 0)
            {
                appointment.PatientActionable.Add("OHIP Card# missing");
            }
            if (patDemo.firstName == null || patDemo.firstName.Trim() == "")
            {
                appointment.PatientActionable.Add("First name missing");
            }
            if (patDemo.lastName == null || patDemo.lastName.Trim() == "")
            {
                appointment.PatientActionable.Add("Last name missing");
            }

            return appointment;
        }



        private void LoadData(VMSchedule schedule)
        {
            var days = new List<VMScheduleDay>();
            var timeSlots = new List<VMTimeSlot>();
            //var resourceTypes = GetResourceTypes();

            var dbSchedule = new ScheduleVM();
            var dbTimeSlots = new List<ScheduleDayResourceTimeSlotVM>();


            // load days for the schedule           
            int daysToAdd = 0; //TODO need to check for when there is only one selected date
            switch (schedule.ScheduleType.Id)
            {
                case 3:
                    daysToAdd = 0;
                    break;
                case 2:
                    daysToAdd = 7;
                    break;
                default:
                    daysToAdd = 14;
                    break;
            }

            dbSchedule = _sch.GetOfficeAppSchedule(schedule.Office.PracticeId, schedule.Office.Id,
                schedule.SelectedDate, schedule.SelectedDate.AddDays(daysToAdd));

            dbTimeSlots = dbSchedule.ScheduleDays.SelectMany(s => s.scheduleDayResources.OrderBy(o => o.permissionIds.OrderBy(p => p).Select(m => m).FirstOrDefault()).SelectMany(t => t.ScheduleTimeSlots)).ToList();

            schedule.TimeSlotIncrement = dbSchedule.timeSlotDuration;
            int earliestHour = 7; //7:00 AM
            int lastestHour = 17; //5:00 PM

            //load time slots 
            if (dbTimeSlots.Any())
            {
                earliestHour = dbTimeSlots.OrderBy(o => o.startHour).First().startHour;
                lastestHour = dbTimeSlots.OrderByDescending(o => o.endHour).First().endHour;
                lastestHour = lastestHour < 23 ? lastestHour + 1 : lastestHour;
            }

            for (int i = earliestHour; i < lastestHour; i++)
            {
                for (int m = 0; m < 60; m++)
                {
                    if (m % schedule.TimeSlotIncrement == 0)
                    {
                        var timeSlot = new VMTimeSlot();
                        timeSlot.Hour = i;
                        timeSlot.Minute = m;
                        timeSlots.Add(timeSlot);
                    }

                }
            }


            foreach (var d in dbSchedule.ScheduleDays)
            {
                var day = new VMScheduleDay();
                day.Date = d.date;
                day.Resources = GetResources(d, schedule.TimeSlotIncrement);
                days.Add(day);
            }


            schedule.ScheduleDays = days;
            schedule.TimeSlots = timeSlots.OrderBy(o => o.Hour).ToList(); //TODO: also need to sort by the minutes           

        }

        //public void AddPatients()
        //{

        //    var practice0 = context.Practices.Single(p => p.PracticeName == "Waterloo"); //Instead of "Waterloo" select the practice from the dropbox 
        //    var practice1 = context.Practices.Single(p => p.PracticeName == "New Market");
        //    var practice2 = context.Practices.Single(p => p.PracticeName == "Toronto");

        //    var demographicsAPI = new DemographicsAPI(context);
        //    //
        //    // The data below comes from the old Cerebrum Data Base or from the user interface
        //    //
        //    var patient0 = new PatientData { firstName = "Philip", middleName = "Morgan", lastName = "Clarkson", dateOfBirth = DateTime.Now.AddYears(-75) };
        //    var patient1 = new PatientData { firstName = "John", middleName = "Arthur", lastName = "Bond", dateOfBirth = DateTime.Now.AddYears(-25) };
        //    var patient2 = new PatientData { firstName = "Pula", middleName = "James", lastName = "Gordon", dateOfBirth = DateTime.Now.AddYears(-50) };
        //    var patient3 = new PatientData { firstName = "Alex", middleName = "Goga", lastName = "Habey", dateOfBirth = DateTime.Now.AddYears(-30) };
        //    var patient4 = new PatientData { firstName = "INETTA", lastName = "ELLIS", dateOfBirth = DateTime.Parse("1970-06-26") };

        //    PatientNavgation patientNavigation = demographicsAPI.AddNewPatient(practice0, patient0);
        //    patientNavigation = demographicsAPI.AddNewPatient(practice1, patient1);
        //    patientNavigation = demographicsAPI.AddNewPatient(practice1, patient3);
        //    patientNavigation = demographicsAPI.AddNewPatient(practice1, patient4);
        //}

        public void Dispose()
        {
            unitRep.Dispose();
            unitRepHC.Dispose();
            appTypeRep.Dispose();
            context.Dispose();
            vpRepository.Dispose();
        }
    }
}

