﻿using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.Text;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using System.Configuration;
using System.Net.Http.Headers;

using Cerebrum.BLL.Utility;
using Cerebrum.ViewModels.Econsult.OneIdToken;

using Cerebrum30.Helpers;
using Cerebrum30.Areas.EForms.Data;
using Cerebrum30.API.OneIdToken.Healpers;
using Newtonsoft.Json;
using Cerebrum.BLL.Econsult;


using Cerebrum30.Utility;
namespace Cerebrum30.API.OneIdToken
{
    public class OneIdTokenController : OneIdTokenBaseApiController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public OneIdTokenController(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        [Route("api/OneIdToken2")]
        public async Task<IActionResult> OAuthOneIdCallback()
        {

            // step 4
            // if success to login to OneId - OneId server sends to this controller response (token)
            string error = string.Empty;
            var page = string.Empty;
            var userId = Convert.ToInt32(HttpContext.Session.GetString("UserId") ?? "0");

            try
            {
                // we received token
                var queryString = Request.Query;
                //
                string authorizationCode = string.Empty;
                string stateValue = string.Empty;
                string clientId = string.Empty;
                string iss = string.Empty;
                string responseError = string.Empty;
                string responseErrorDescription = string.Empty;
                // step 5 get values: code, state, client_id, iss. If error - get error and exit
                if (queryString.ContainsKey("code"))
                {
                    authorizationCode = queryString["code"];
                }
                if (queryString.ContainsKey("state"))
                {
                    stateValue = queryString["state"];
                }
                if (queryString.ContainsKey("client_id"))
                {
                    clientId = queryString["client_id"];
                }
                if (queryString.ContainsKey("iss"))
                {
                    iss = queryString["iss"];
                }
                if (queryString.ContainsKey("error"))
                {
                    responseError = queryString["error"];
                    responseErrorDescription = queryString["error_description"];

                    UtilityHelper.WriteEconsultOneIdError("OneId authorize code is empty : " + responseErrorDescription, userId);
                    string description = string.Empty;
                    if (responseErrorDescription.Contains("No selected UAO found in the request or requested UAO does not match with the entitlement"))
                    {
                        description = "UaoNotFound";
                    }
                    var redirectUrl = CerebrumUrl + GetControllerName(userId) + "/OAuthError?error=" + responseError + "&description=" + description;

                    if (userId > 0)
                    {
                        _log.Error("userId: " + userId + ", description: " + responseErrorDescription + ", error: " + responseError);
                    }

                    return Redirect(redirectUrl);
                }

                WriteErrorIfEmpty(authorizationCode, "authorizationCode");
                WriteErrorIfEmpty(stateValue, "stateValue");
                WriteErrorIfEmpty(clientId, "clientId");
                WriteErrorIfEmpty(iss, "iss");

                UtilityHelper.WriteEconsultOneIdLog("authorizationCode: " + authorizationCode + Environment.NewLine + "stateValue: " + stateValue
                                                        + Environment.NewLine + "clientId: " + clientId + Environment.NewLine + "iss: " + iss,
                                                        userId);
                // step 6
                // get identityToken - the same step as step 1
                var scope = System.Configuration.ConfigurationManager.AppSettings["IdentityServerAccessTokenScope"];
                var identityToken = UserServiceHelpers.GetAwareMDIdentityToken(scope);

                var httpClient = _httpClientFactory.CreateClient("eConsult");

                string REQUEST_TIMEOUT = System.Configuration.ConfigurationManager.AppSettings["EconsultRequestTimeout"];
                httpClient.Timeout = TimeSpan.FromSeconds(int.Parse(REQUEST_TIMEOUT));
                // id token null
                if (string.IsNullOrEmpty(identityToken))
                {
                    // write to log error
                    UtilityHelper.WriteEconsultOneIdError("identityToken is empty", userId);
                }
                // step 7
                // the same as step 2, but method is different - AccessToken. In step 2 it is AuthorizeUrl
                // we save it in DB
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", identityToken);


                AccessTokenRequest request = new AccessTokenRequest()
                {
                    AuthorizationCode = authorizationCode,
                    AuthorizationState = stateValue,
                    ClientId = clientId,
                    Iss = iss,
                    RedirectUri = System.Configuration.ConfigurationManager.AppSettings["AssertionConsumerServiceURL"] //"https://c3test.mycerebrum.com/api/OneIdToken2/"
                };
                string requestString = JsonConvert.SerializeObject(request);
                UtilityHelper.WriteEconsultOneIdLog("requestString: " + requestString, userId);

                var requestContent = new StringContent(requestString, Encoding.UTF8, "application/json");
                string authenticationUrl = System.Configuration.ConfigurationManager.AppSettings["eConsultAuthenticationUrl"]; //"https://externalauthentication.mycerebrum.com"
                HttpResponseMessage httpResponse = await httpClient.PostAsync($"{authenticationUrl}/API/Authorization/AccessToken", requestContent);
                string strResponse = httpResponse.Content.ReadAsStringAsync().Result;

                UtilityHelper.WriteEconsultOneIdLog("strResponse: " + strResponse + Environment.NewLine + "controller: api/OneIdToken2, method: OAuthOneIdCallback, StatusCode: " + httpResponse.StatusCode, userId);

                if (httpResponse.StatusCode == HttpStatusCode.OK)
                {
                    // step 8
                    // get token
                    Cerebrum.ViewModels.Econsult.OneIdToken.OAuthSessionResponse oauthSessionResponse = JsonConvert.DeserializeObject<Cerebrum.ViewModels.Econsult.OneIdToken.OAuthSessionResponse>(strResponse);
                    if (string.IsNullOrEmpty(oauthSessionResponse.AccessToken))
                    {
                        // redirect to error page
                        error = "access token is empty";
                        page = GetControllerName(userId) + $"/OAuthError?error={error}";
                        var errorRedirectUrl = CerebrumUrl + page; //CerebrumUrl: "https://c3test.mycerebrum.com/"
                        UtilityHelper.WriteEconsultOneIdError(error, userId);
                        return Redirect(errorRedirectUrl);
                    }
                    UtilityHelper.WriteEconsultOneIdLog("AccessToken: " + oauthSessionResponse.AccessToken, userId);
                    //HttpContextProvider.Current.Session["OAuthOneIdAccessToken"] = oauthSessionResponse.AccessToken;
                    // step 9 
                    // save oauthSessionResponse in session
                    HttpContext.Session.SetString("oauthSessionResponse", Newtonsoft.Json.JsonConvert.SerializeObject(oauthSessionResponse));
                    page = "OneIdLogin/Login";
                    //OneIdLoginAnonymousController
                    // step 10
                    // redirect to c3 site, if user already login we redirect to "OneIdLogin/Login", else to "OneIdLoginAnonymous/Login"
                    if (oauthSessionResponse.UserId.GetValueOrDefault() == 0)
                    {
                        page = "OneIdLoginAnonymous/Login";
                    }

                    // save in DB AuthZId
                    if (userId > 0 && !string.IsNullOrEmpty(oauthSessionResponse.AuthZId))
                    {
                        EconsultBLL _EconsultBLL = new EconsultBLL();
                        _EconsultBLL.UserId = userId;

                        await _EconsultBLL.SaveOneIdToken(_httpClientFactory, oauthSessionResponse);
                    }
                    else if (userId > 0 && string.IsNullOrEmpty(oauthSessionResponse.AuthZId))
                    {
                        UtilityHelper.WriteEconsultError("AuthZId is null for userId: " + userId, userId);
                    }
                    var successRedirectUrl = CerebrumUrl + page;
                    UtilityHelper.WriteEconsultOneIdLog("redirect to: " + successRedirectUrl, userId);
                    return Redirect(successRedirectUrl);
                }
                else
                {
                    error = "There was a problem. Please Contact Administrator!";// show to user
                    var err = WebUtility.UrlEncode(strResponse);

                    _log.Error("userId:" + userId + ", method: OAuthOneIdCallback, OneIdTokenController, error: " + err);
                }
                page = GetControllerName(userId) + "/OAuthError?error=" + error;
                var errorUrl = CerebrumUrl + page;

                return Redirect(errorUrl);
            }
            catch (TaskCanceledException tce)
            {
                if (tce.CancellationToken.IsCancellationRequested)
                {
                    //return RedirectToAction("OAuthError", new { error = "Request Timeout" });
                    error = "Request Timeout";
                    page = GetControllerName(userId) + $"/OAuthError?error={error}";
                    var timeoutUrl = CerebrumUrl + page;

                    _log.Error("userId:" + userId + ", method: OAuthOneIdCallback, OneIdTokenController, error: " + error);

                    return Redirect(timeoutUrl);
                }
            }
            catch (Exception ex)
            {
                UtilityHelper.WriteEconsultError(ex, userId);
            }
            error = "Error getting token";
            page = GetControllerName(userId) + "/OAuthError?error={error}";
            var finalRedirectUrl = CerebrumUrl + page;

            _log.Error("userId:" + userId + ", method: OAuthOneIdCallback, OneIdTokenController, error: " + error);

            return Redirect(finalRedirectUrl);
        }
        private void WriteErrorIfEmpty(string param, string name)
        {
            if (string.IsNullOrEmpty(param))
            {
                UtilityHelper.WriteEconsultOneIdError("Param " + name + " is empty", Convert.ToInt32(HttpContext.Session.GetString("UserId") ?? "0"));
            }
        }
        private string GetControllerName(int userId)
        {
            return userId == 0 ? "OneIdLoginAnonymous" : "OneIdLogin";
        }
    }
}
