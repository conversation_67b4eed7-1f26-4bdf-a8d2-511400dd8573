﻿using System.Linq;
using Microsoft.AspNet.Identity;
using System.Security.Principal;
using System.Web;
using Cerebrum.Data;
using Cerebrum30.DataObjects;

using Cerebrum30.Utility;
namespace Cerebrum.Data
{
    class DemographicsAPI
    {
        private CerebrumContext m_context;
        public DemographicsAPI(CerebrumContext context)
        {
            m_context = context;
        }
        /// <summary>
        /// Add a New Patient
        /// </summary>
        /// <param name="practice"></param>
        /// <param name="patientData"></param>
        /// <returns></returns>
        public PatientNavgation AddNewPatient(Practice practice, PatientData patientData)
        {
            // Enter here patient data collected from the User Interface or old DB : add more fields as needed below
            var patientdemographic = new Demographic
            {
                firstName = patientData.firstName,
                middleName = patientData.middleName,
                lastName = patientData.lastName,
                dateOfBirth = patientData.dateOfBirth
            };
            var patientRecord = new PatientRecord();
            practice.PatientRecords.Add(patientRecord);
            //m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            m_context.SaveChanges();
            patientRecord.Demographics.Add(patientdemographic);
            //m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            m_context.SaveChanges();
            return new PatientNavgation { PatientRecordId = patientRecord.Id, DemographicId = patientdemographic.Id };
        }
        /// <summary>
        /// GetPatientDemographic
        /// </summary>
        /// <param name="practice"></param>
        /// <param name="patientData"></param>
        /// <returns></returns>
        public Demographic GetPatientDemographic(Practice practice, PatientData patientData) {

            var PatientRecordIds = (from pr in m_context.PatientRecords where pr.PracticeId == practice.Id select pr.Id).ToList();
            return (from d in m_context.Demographics where  PatientRecordIds.Contains(d.PatientRecordId) == true &&
                                                            d.lastName.ToUpper() == patientData.lastName.ToUpper() &&
                                                            d.firstName.ToUpper() == patientData.firstName.ToUpper() select d).Single();
            //&&
            //d.dateOfBirth == patientData.dateOfBirth select d).Single();
        }
        /// <summary>
        /// GetPatientDemographic
        /// </summary>
        /// <param name="practice"></param>
        /// <param name="OHIPCard"></param>
        /// <returns></returns>
        public Demographic GetPatientDemographic(Practice practice, string OHIPCard)
        {
            var PatientRecordIds = (from pr in m_context.PatientRecords where pr.PracticeId == practice.Id select pr.Id).ToList();
            var demographicsId = (from c in m_context.HealthCards where c.number == OHIPCard select c.DemographicId).Single();
            return (from d in m_context.Demographics where (PatientRecordIds.Contains(d.PatientRecordId) == true && demographicsId == d.Id)
                    select d).Single();
        }
        /// <summary>
        /// Add healthcard field to Demographic 
        /// </summary>
        /// <param name="demographic"></param>
        /// <param name="healthcard"></param>
        /// <returns></returns>
        public void AddHealthcard(Demographic demographic, DemographicsHealthCard healthcard)
        {
            demographic.healthcards.Add(healthcard);
            //m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            m_context.SaveChanges();
        }
        /// <summary>
        ///Add addresses field to Demographic  
        /// </summary>
        /// <param name="demographic"></param>
        /// <param name="address"></param>
        /// <returns></returns>
        public void AddAddress(Demographic demographic, DemographicsAddress address)
        {
            demographic.addresses.Add(address);
            m_context.SaveChanges();
            //m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
        }
        /// <summary>
        /// Add phoneNumber field to Demographic 
        /// </summary>
        /// <param name="demographic"></param>
        /// <param name="phoneNumber"></param>
        public void AddPhoneNumber(Demographic demographic, DemographicsPhoneNumber phoneNumber)
        {
            demographic.phoneNumbers.Add(phoneNumber);
            //m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            m_context.SaveChanges();
        }
        /// <summary>
        /// Add DemographicsContacts field to Demographic
        /// </summary>
        /// <param name="demographic"></param>
        /// <param name="demographicsContact"></param>
        public void AddDemographicsContact(Demographic demographic, DemographicsNextOfKin demographicsContact)
        {
            demographic.demographicsContacts.Add(demographicsContact);
            //m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            m_context.SaveChanges();
        }
    }
}
