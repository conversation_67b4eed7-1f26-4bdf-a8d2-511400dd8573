﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using Cerebrum.Data;
using Cerebrum30.Utility;
namespace Cerebrum30.API
{
    class AppointmentsAPI
    {
        private CerebrumContext m_context;
        public AppointmentsAPI(CerebrumContext context)
        {
            m_context = context;
        }
        /// <summary>
        /// Add a new appointment 
        /// </summary>
        /// <param name="patientRecord"></param>
        /// <param name="appointmentData"></param>
        /// <returns></returns>
        public int AddAppointment(PatientRecord patientRecord, AppointmentData appointmentData)
        {
            var appointment = new Appointment
            {
                appointmentTime = appointmentData.Time,
                appointmentPurpose = appointmentData.Purpose,
                appointmentNotes = appointmentData.Notes,
                OfficeId = appointmentData.OfficeId,
                referralDoctorId = appointmentData.referralDoctorId,
                appointmentConfirmation = appointmentData.Confirmation,
                AppointmentTypeId = appointmentData.AppointmentTypeId,
                appointmentPaymentMethod = appointmentData.PaymentMethod,
                actionOnAbnormal = appointmentData.actionOnAbnormal,
                bookingConfirmation = appointmentData.bookingConfirmation,
                roomNumber = appointmentData.roomNumber
            };
            appointment.appointmentStatus = appointmentData.appointmentStatus;
            foreach (var item in appointmentData.Providers)
            {
                appointment.appointmentProviders.Add(new AppointmentProvider { ExternalDoctorId = item.ExternalDoctorId });
            }
            foreach (var item in appointmentData.Tests)
            {
                appointment.appointmentTests.Add(new AppointmentTest { TestId = item.PracticeTestId, referralDoctorId = item.referralDoctorId, startTime = item.startTime, testDuration = item.TestDuration });
            }
            foreach (var item in appointmentData.appPreconditions)
            {
                appointment.appointmentPreconditons.Add(new AppointmentPreconditon { Type = item.type, Status = item.status });
            }
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            patientRecord.Appointments.Add(appointment);
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            return appointment.Id;
        }
        public int AddAppointment(int patientRecordId, AppointmentData appointmentData)
        {
            var appointment = new Appointment
            {
                appointmentTime = appointmentData.Time,
                appointmentPurpose = appointmentData.Purpose,
                appointmentNotes = appointmentData.Notes,
                OfficeId = appointmentData.OfficeId,
                referralDoctorId = appointmentData.referralDoctorId,
                appointmentConfirmation = appointmentData.Confirmation,
                AppointmentTypeId = appointmentData.AppointmentTypeId,
                appointmentPaymentMethod = appointmentData.PaymentMethod,
                actionOnAbnormal=appointmentData.actionOnAbnormal,
                bookingConfirmation=appointmentData.bookingConfirmation,
                roomNumber=appointmentData.roomNumber,
                appointmentStatus=appointmentData.appointmentStatus
            };
            appointment.appointmentStatus = appointmentData.appointmentStatus;
            foreach (var item in appointmentData.Providers)
            {
                appointment.appointmentProviders.Add(new AppointmentProvider { ExternalDoctorId = item.ExternalDoctorId });
            }
            foreach (var item in appointmentData.Tests)
            {
                appointment.appointmentTests.Add(new AppointmentTest { TestId = item.PracticeTestId, referralDoctorId = item.referralDoctorId, startTime = item.startTime, testDuration = item.TestDuration, AppointmentTestStatusId = (int)item.TestStatus, AppointmentTestResources = new List<AppointmentTestResource> { new AppointmentTestResource {assignedToUserId= item.UserId } } });
            }
            foreach (var item in appointmentData.appPreconditions)
            {
                appointment.appointmentPreconditons.Add(new AppointmentPreconditon { Type = item.type, Status = item.status });
            }
            var patientrec = m_context.PatientRecords.SingleOrDefault(s => s.Id == patientRecordId);
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            patientrec.Appointments.Add(appointment);
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            return appointment.Id;
        }
        public int EditAppointment(AppointmentData appData)
        {
            var app = m_context.Appointments.Single(a => a.Id == appData.Id);
            app.appointmentTime = appData.Time;
            app.appointmentPurpose = appData.Purpose;
            app.appointmentNotes = appData.Notes;
            app.OfficeId = appData.OfficeId;
            app.referralDoctorId = appData.referralDoctorId;
            app.appointmentConfirmation = appData.Confirmation;
            app.AppointmentTypeId = appData.AppointmentTypeId;
            app.appointmentPaymentMethod = appData.PaymentMethod;
            app.appointmentStatus = appData.appointmentStatus;
            app.actionOnAbnormal = appData.actionOnAbnormal;
            app.bookingConfirmation = appData.bookingConfirmation;
            app.roomNumber = appData.roomNumber;
            foreach (var item in appData.Providers)
            {
                app.appointmentProviders.Add(new AppointmentProvider { ExternalDoctorId = item.ExternalDoctorId });
            }
            
            var ctx = ((System.Data.Entity.Infrastructure.IObjectContextAdapter)m_context).ObjectContext;
            ctx.ExecuteStoreCommand("DELETE FROM [AppointmentTests] WHERE AppointmentId= {0}", app.Id);
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            foreach (var item in appData.Tests)
            {
                app.appointmentTests.Add(new AppointmentTest { TestId = item.PracticeTestId, referralDoctorId = item.referralDoctorId });
            }
            var appconditions = app.appointmentPreconditons.ToList();
            var newappcondition = new List<AppointmentPreconditon>();
            foreach (var item in appData.appPreconditions)
            {
                if (!appconditions.Any(a => a.Type.Equals(item.type)))
                    if (appconditions.Any(a => a.Type.Equals(item.type)))
                    {
                        var exists = appconditions.Where(a => a.Type.Equals(item.type)).FirstOrDefault();
                        if (exists.Status != item.status)
                        {
                            exists.Status = item.status;
                            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
                        }
                    }
                    else
                    {
                        newappcondition.Add(new AppointmentPreconditon { Type = item.type, Status = item.status });
                    }
            }
            var arr = appData.appPreconditions.Select(s => s.type.ToString()).ToArray();
            var newcod = appconditions.Where(w => (!arr.Contains(w.Type)));

            foreach (var item in newcod)
            {
                item.Status = false;
                m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            }
            app.appointmentPreconditons.AddRange(newappcondition);
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            m_context.AppointmentPreconditions.RemoveRange(newcod);
            m_context.SaveChanges((HttpContextProvider.Current.User.Identity.IsAuthenticated) ? HttpContextProvider.Current.User.Identity.Name : "Anonymous");
            return app.Id;
        }
    }
}
