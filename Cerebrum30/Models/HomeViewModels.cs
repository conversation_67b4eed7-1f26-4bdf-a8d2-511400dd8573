﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;

using Cerebrum30.Utility;
namespace Cerebrum30.Models
{
    /// <summary>
    /// Holds criteria for role based activity selector
    /// </summary>
    public class HomeViewModels
    {
        public bool ShowReports { get; set; }
        public bool ShowDashboard { get; set; }
        public bool ShowAdmin { get; set; }
        public bool ShowWebApiHelp { get; set; } = false;
        public HomeViewModels()
        {
            SetViewModel();
        }
        public void SetViewModel()
        {
#if DEBUG
            ShowWebApiHelp = true;
#endif
            if (HttpContextProvider.Current.User.Identity.IsAuthenticated)
            {
                if (HttpContextProvider.Current.User.IsInRole("Doctor"))
                {
                    ShowReports = true;
                }
                if (HttpContextProvider.Current.User.IsInRole("Admin"))
                {
                    ShowDashboard = true;
                    ShowAdmin = true;
                }
            }
        }
    }
}