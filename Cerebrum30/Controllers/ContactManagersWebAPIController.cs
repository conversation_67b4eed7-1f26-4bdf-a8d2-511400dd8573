﻿using AwareMD.Cerebrum.Shared;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.BLL.Documents;
using Cerebrum.BLL.Requisition;
using Cerebrum.ContactManager;
using Cerebrum.Data;
using Cerebrum30.Filters;
using Cerebrum.ViewModels.ContactManagerNew;
using Cerebrum30.Utility;
using Microsoft.AspNet.Identity;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ILog = Cerebrum.BLL.Utility.ILogGeneric;

namespace Cerebrum30.Controllers
{
    /// <summary>
    /// Contact manager API
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ContactManagersWebAPIController : BaseAPIController, ILog
    {
        private const string ORDER_ASC = "asc";
        private const string ORDER_DESC = "desc";
        private const string DATE_FORMAT_DUE = "MM/dd/yyyy";
        private const string DATE_FORMAT_MESSAGE = "MM/dd/yyyy HH:mm:ss";
        private const string FAX = "fax";
        private const string HL7 = "hl7";
        private const string HRM = "hrm";
        private const string EXTERNALDOCUMENTFAX = "fax";
        private const string PERMISSIONVIEWCOMMONMESSAGES = "ViewCommonMessages";
        private const string PERMISSIONADDREQUISITION = "AddRequisition";
        private const string TYPEAPPOINTMENT = "appointment";
        private const string TYPEREQUISITION = "requisition";

        //requisition status
        private const string STATUSORDERED = "ordered";
        private const string STATUSARRANGED = "arranged";

        private const string MS_HttpContext = "MS_HttpContext";
        private const string RemoteEndpointMessage = "System.ServiceModel.Channels.RemoteEndpointMessageProperty";

        private readonly CerebrumContext _context;
        private List<string> criticalResources = new List<string> { PERMISSIONVIEWCOMMONMESSAGES };
        private List<string> intersection = new List<string>();

        private readonly IContactManagerService _contactManagerService;
        private readonly ITokenHelper _utilityTokenHelper;

        /// <summary>Initialization of unit of work for contact manager</summary>
        public ContactManagersWebAPIController(CerebrumContext context, IContactManagerService contactManagerService, ITokenHelper utilityTokenHelper)
        {
            _context = context;
            _contactManagerService = contactManagerService;
            _utilityTokenHelper = utilityTokenHelper;
        }

        /// <summary>load tasks</summary>
        /// <returns></returns>
        [HttpPost("LoadTasks")]
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        // [LogUserInfo]
        public ActionResult<ContactManagerTaskListViewModel> LoadTasks(ContactManagerTaskListRequest taskListRequest)
        {
            ContactManagerTaskListViewModel result = new ContactManagerTaskListViewModel();
            try
            {
                int userId = 0;         // = CerebrumUser.UserId
                int practiceId = IndentityInfo.GetPracticeId(User.Identity);
                string ipAddress = string.Empty;        // = Request.UserHostAddress
                string taskSource = taskListRequest.taskSource.Trim().ToLower();
                if (taskListRequest.readRequest == 2 || taskSource == "commonfolder")
                {
                    intersection = IndentityInfo.GetPermissions(User, criticalResources);
                }
                var bll = new ContactManagerBLL(practiceId, userId, User.Identity.GetUserId(), ipAddress);
                result = bll.LoadTasks(taskListRequest, intersection);
                result.isAdministrator = intersection.Contains(PERMISSIONVIEWCOMMONMESSAGES);
                return result;
            }
            catch (Exception ex)
            {
                WriteErrorToAuditLog(ex);
                result.errorMessage = "There was a problem. Please Contact Administrator!";
                return result;
            }
        }

        /// <summary>mark task as done</summary>
        /// <param name="markDoneRequest">markDone's request data</param>
        /// <returns>return empty if succ; else return error message</returns>
        [HttpPost("MarkDone")]
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        // [LogUserInfo]
        public ActionResult<string> MarkDone(ContactManagerTextValueViewModel markDoneRequest)
        {
            try
            {
                string userId = User.Identity.GetUserId();
                int taskDefinitionId = Convert.ToInt32(markDoneRequest.text);
                string message = MarkTaskDone(userId, taskDefinitionId);

                return message;
            }
            catch (Exception ex)
            {
                WriteErrorToAuditLog(ex);
                return "There was a problem. Please Contact Administrator!";
            }
        }

        /// <summary>mark message as seen</summary>
        /// <param name="markSeenRequest">markSeen's request data</param>
        /// <returns>return empty if succ; else return error message</returns>
        [HttpPost("MarkSeen")]
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        // [LogUserInfo]
        public ActionResult<string> MarkSeen(ContactManagerTextValueViewModel markSeenRequest)
        {
            try
            {
                string userId = User.Identity.GetUserId();
                int taskMessageId = Convert.ToInt32(markSeenRequest.text);
                string message = MarkTaskMessageSeen(userId, 0, taskMessageId);

                return message;
            }
            catch (Exception ex)
            {
                WriteErrorToAuditLog(ex);
                return "There was a problem. Please Contact Administrator!";
            }
        }

        /// <summary>return initialized data from database</summary>
        /// <returns>list of urgency, office, recipient</returns>
        [HttpPost("InitializeNewTask")]
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        // [LogUserInfo]
        public async Task<ActionResult<ContactManagerNewTaskViewModel>> InitializeNewTask(Cerebrum.ViewModels.ContactManagerNew.ContactManagerNewTaskRequest newTaskRequest)
        {
            ContactManagerNewTaskViewModel result = new ContactManagerNewTaskViewModel();
            try
            {
                int practiceId = IndentityInfo.GetPracticeId(User.Identity);
                string userId = User.Identity.GetUserId();

                newTaskRequest.practiceId = practiceId;
                newTaskRequest.applicationUserId = userId;

                result.errorMessage = _contactManagerService.CheckMessagePermission(newTaskRequest.practiceId, newTaskRequest.taskMessageId, newTaskRequest.patientRecordId);
                if (!string.IsNullOrEmpty(result.errorMessage))
                    return result;

                result = await _contactManagerService.InitializeNewTask(newTaskRequest);
                return result;
            }
            catch (Exception ex)
            {
                WriteErrorToAuditLog(ex);
                result.errorMessage = "There was a problem. Please Contact Administrator!";
                return result;
            }
        }

        /// <summary>add new task to database</summary>
        /// <param name="newTask">new task data</param>
        /// <returns>return empty data if there is no error; return error message if there is error</returns>
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        [HttpPost("AddNewTask")]
        public ActionResult<string> AddNewTask(ContactManagerTaskData newTask)
        {
            int userId = GetUserId();
            string ipAddress = GetIpAddress();
            return AddNewTask(newTask, userId, ipAddress);
        }

        /// <summary>add new task to database</summary>
        /// <param name="newTask">new task data</param>
        /// <returns>return empty data if there is no error; return error message if there is error</returns>
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        //temporary route name to unblock staging of blocking error, this code must be refcatorized
        [HttpPost("AddNewTask_NotInUsedByUIOnlyByController")]
        // [LogUserInfo]
        public ActionResult<string> AddNewTask(ContactManagerTaskData newTask, int userId, string ipAddress)
        {
            if (string.IsNullOrEmpty(newTask.recipients) && newTask.markDone == 0)
                return "New message doesn't have recipient! ";

            var practiceId = IndentityInfo.GetPracticeId(User.Identity);
            string userGuId = User.Identity.GetUserId();

            var result = _contactManagerService.AddNewTask(newTask, practiceId, userGuId, userId, ipAddress);
            return result;
        }



        /// <summary>get new task's attachment</summary>
        /// <param name="request">request</param>
        /// <returns></returns>
        [HttpPost("GetNewTaskAttachment")]
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        // [LogUserInfo]
        public async Task<ActionResult<ContactManagerNewTaskAttachmentResponse>> GetNewTaskAttachment(ContactManagerNewTaskAttachmentRequest request)
        {
            ContactManagerNewTaskAttachmentResponse response = new ContactManagerNewTaskAttachmentResponse();
            try
            {
                int practiceId = IndentityInfo.GetPracticeId(User.Identity);
                int userId = GetUserId();
                response = await GetNewTaskAttachmentAsync(request.patientRecordId, request.taskMessageId, practiceId, userId);
            }
            catch (Exception ex)
            {
                WriteErrorToAuditLog(ex);
                response.errorMessage = "There was a problem. Please Contact Administrator! ";
            }

            return response;
        }

        [HttpPost("UpdateTask")]
        // TODO: Re-enable these attributes after converting to ASP.NET Core equivalents
        // [ValidateApiHeaderAntiForgeryToken]
        public ActionResult UpdateTask(ContactManagerTaskData newTask)
        {
            int practiceId = IndentityInfo.GetPracticeId(User.Identity);
            int userId = GetUserId();
            string ipAddress = GetIpAddress();
            bool saved = _contactManagerService.UpdateTask(newTask, practiceId, userId, ipAddress);
            if (saved)
            {
                return Ok("Task Updated.");
            }

            return NotFound("could not update the task");
        }

        /// <summary>get new task's attachment</summary>
        /// <param name="request">request</param>
        /// <returns></returns> 
        private async Task<ContactManagerNewTaskAttachmentResponse> GetNewTaskAttachmentAsync(string patientRecordId, string taskMessageId, int practiceId, int userId)
        {
            ContactManagerNewTaskAttachmentResponse response = new ContactManagerNewTaskAttachmentResponse();
            List<ContactManagerNewTaskAttachment> attachments = new List<ContactManagerNewTaskAttachment>();

            if (!string.IsNullOrEmpty(taskMessageId))
            {
                int nTaskMessageId = int.Parse(taskMessageId);
                var taskReports = (from td in _context.CM_TaskDefinitions
                                   join tm in _context.CM_TaskMessages on td.id equals tm.CM_TaskDefinitionId
                                   join tr in _context.CM_TaskReports on td.id equals tr.CM_TaskDefinitionId
                                   where tm.id == nTaskMessageId
                                   select new
                                   {
                                       tr.reportId,
                                       tr.reportType
                                   }).ToList();
                if (taskReports.Count > 0)
                {
                    //string reportName;
                    var documentsBLL = new DocumentsBLL();
                    //string reportUrl;
                    //System.Net.Http.IHttpClientFactory _httpClientFactory = new ;
                    List<Cerebrum.ViewModels.ExternalDocument.ClinicServerAccessData> clinicServerAccessDatas = new List<Cerebrum.ViewModels.ExternalDocument.ClinicServerAccessData>();

                    foreach (var taskReport in taskReports)
                    {
                        //reportType = HL7;
                        //reportName = string.Empty;
                        //reportUrl = string.Empty;
                        ContactManagerNewTaskAttachment attachment = new ContactManagerNewTaskAttachment();
                        attachment.reportId = taskReport.reportId;
                        attachment.reportType = taskReport.reportType.ToString().ToLower();

                        if (taskReport.reportType == ReportType.Fax || taskReport.reportType == ReportType.HRM)
                        {
                            attachment.reportType = taskReport.reportType == ReportType.Fax ? FAX : HRM;
                            var fax = (from r in _context.ReportsReceived
                                       where r.Id == taskReport.reportId
                                       select new { r.fileName, r.url, r.officeId }).FirstOrDefault();
                            if (fax != null)
                            {
                                //reportName = fax.fileName;
                                //reportUrl = Cerebrum.BLL.Utility.UtilityHelper.GetDocumentUrl(fax.url);
                                attachment.reportName = fax.fileName;
                                attachment.reportUrl = Cerebrum.BLL.Utility.UtilityHelper.GetDocumentUrl(fax.url);
                                attachment.officeId = fax.officeId;

                                var clinicServerAccessData = clinicServerAccessDatas.Where(a => a.officeId == attachment.officeId).FirstOrDefault();
                                if (clinicServerAccessData == null)
                                {
                                    string url = documentsBLL.GetDownloadURL(practiceId, attachment.officeId);
                                    if (!string.IsNullOrWhiteSpace(url))
                                    {
                                        clinicServerAccessData = new Cerebrum.ViewModels.ExternalDocument.ClinicServerAccessData();
                                        clinicServerAccessData.officeId = attachment.officeId;
                                        clinicServerAccessData.clinicServerToken = await _utilityTokenHelper.GenerateTokenAsync(userId, practiceId, url);
                                        clinicServerAccessData.clinicServerUrl = $"{url.TrimEnd('/')}/{Cerebrum.BLL.Utility.UtilityHelper.CLINIC_DOC_PATH.TrimStart('/').TrimEnd('/')}/";
                                        clinicServerAccessDatas.Add(clinicServerAccessData);
                                    }
                                }
                                if (clinicServerAccessData != null)
                                {
                                    attachment.clinicServerUrl = clinicServerAccessData.clinicServerUrl;
                                    attachment.clinicServerToken = clinicServerAccessData.clinicServerToken;
                                }
                            }
                        }
                        else if (taskReport.reportType == ReportType.Fhir)
                        {

                            var doc = _context.Documents.Where(d => d.Id == taskReport.reportId).FirstOrDefault();
                            if (doc != null)
                            {
                                attachment.reportName = doc.Name;
                                attachment.reportUrl = doc.Url;
                                attachment.fhirId = doc.FhirId.ToString();
                            }
                        }
                        //attachments.Add(new ContactManagerNewTaskAttachment { reportId = taskReport.reportId, reportType = reportType, reportName = reportName, reportUrl = reportUrl });
                        attachments.Add(attachment);
                    }
                }
            }

            if (!string.IsNullOrEmpty(patientRecordId))
            {
                string errorMessage = string.Empty;

                RequisitionBLL bll = new RequisitionBLL(_context);
                var requisitionList = bll.GetRequisitionList(int.Parse(patientRecordId), 0, out errorMessage);
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    response.errorMessage = errorMessage;
                    return response;
                }

                int requisitionId;
                int requisitionStatusArranged = GetRequisitionStatusId(STATUSARRANGED);
                int requisitionStatusOrdered = GetRequisitionStatusId(STATUSORDERED);
                string requisitionName;
                foreach (var requisition in requisitionList)
                {
                    if (requisition.requisitionPatientId == 0)
                        continue;

                    foreach (var testOrdered in requisition.testOrdered)
                    {
                        if (testOrdered.source.ToLower() == TYPEREQUISITION && (testOrdered.status == requisitionStatusArranged || testOrdered.status == requisitionStatusOrdered))
                        {
                            switch (testOrdered.type.ToLower())
                            {
                                case "internal":
                                case "external":
                                    requisitionName = testOrdered.type + ":";
                                    foreach (var requisitionItem in testOrdered.requisitionItem)
                                    {
                                        requisitionName += " " + requisitionItem.testName;
                                    }
                                    requisitionId = testOrdered.id;
                                    attachments.Add(new ContactManagerNewTaskAttachment { reportId = requisitionId, reportType = "requisition", reportName = testOrdered.type, reportUrl = string.Empty });
                                    break;
                                default:
                                    attachments.Add(new ContactManagerNewTaskAttachment { reportId = testOrdered.id, reportType = "requisition", reportName = testOrdered.type, reportUrl = string.Empty });
                                    break;
                            }
                        }
                    }
                }
            }

            response.attachments = attachments;
            return response;
        }

        private string MarkTaskDone(string userId, int taskDefinitionId)
        {
            CM_TaskDefinition task = _context.CM_TaskDefinitions
                .Select(t => t).Where(t => t.id == taskDefinitionId).FirstOrDefault();

            if (task == null)
            {
                return "Cannot find the task";
            }

            task.closedBy = userId;
            task.dateClosed = DateTime.Now;
            task.taskStatus = CMTaskStatus.Closed;
            _context.Entry(task).State = EntityState.Modified;
            _context.SaveChanges(GetUserId(), GetIpAddress());

            string message = MarkTaskMessageSeen(userId, taskDefinitionId, 0);

            return message;
        }

        private string MarkTaskMessageSeen(string userId, int taskDefinitionId, int taskMessageId)
        {
            List<int> taskMessageIds = new List<int>();
            if (taskDefinitionId == 0)
            {
                var taskMessage = _context.CM_TaskMessages.Where(a => a.id == taskMessageId).FirstOrDefault();
                if (taskMessage == null)
                {
                    return "Cannot find the message";
                }
                taskMessageIds = _context.CM_TaskMessages.Where(a => a.CM_TaskDefinitionId == taskMessage.CM_TaskDefinitionId && a.id <= taskMessage.id).Select(b => b.id).ToList();
            }
            else
            {
                taskMessageIds = _context.CM_TaskMessages.Where(a => a.CM_TaskDefinitionId == taskDefinitionId).Select(b => b.id).ToList();
            }

            var recipients = _context.CM_TaskMessageRecipients.Where(a => a.userid == userId && a.seen == false && taskMessageIds.Contains(a.CM_TaskMessageId)).ToList();
            recipients.ForEach(a =>
            {
                a.seen = true;
                a.seenAt = DateTime.Now;
            });

            _context.SaveChanges(GetUserId(), GetIpAddress());

            return string.Empty;
        }


        private int GetRequisitionStatusId(string requisitionStatusName)
        {
            var requisitionStatus = (from t in _context.RequisitionStatus
                                     where t.text.ToLower().StartsWith(requisitionStatusName.ToLower().Substring(0, 4))
                                     select t).FirstOrDefault();

            if (requisitionStatus == null)
            {
                return -1;
            }

            return requisitionStatus.id;
        }

        private int GetUserId()
        {
            var userIdGuid = User.Identity.GetUserId();
            var user = _context.Users.FirstOrDefault(u => u.Id == userIdGuid);
            if (user != null)
                return user.UserID;

            return 0;
        }

        private string GetIpAddress()
        {
            if (Request != null && HttpContext.Items.ContainsKey(MS_HttpContext))
            {
                dynamic ctx = HttpContext.Items[MS_HttpContext];
                if (ctx != null)
                {
                    var ip = Helper.GetClientIpByHeaderxForwarded();
                    if (!string.IsNullOrEmpty(ip))
                    {
                        return ip;
                    }
                    // TODO: Implement proper IP address retrieval for ASP.NET Core
                    return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
                }

            }

            if (Request != null && HttpContext.Items.ContainsKey("RemoteEndpointMessage"))
            {
                dynamic remoteEndpoint = HttpContext.Items["RemoteEndpointMessage"];
                if (remoteEndpoint != null)
                    return remoteEndpoint.Address;
            }

            if (HttpContextProvider.Current != null)
            {
                var ip = Helper.GetClientIpByHeaderxForwarded();
                if (!string.IsNullOrEmpty(ip))
                {
                    return ip;
                }
                return RequestUtils.GetIpAddress(HttpContextProvider.Current.Request);
            }

            return "0.0.0.0";
        }
    }
}
