using Cerebrum.BLL.Utility;
using Cerebrum.Data;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;

using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication;
using Cerebrum.BLL.Econsult;
using Cerebrum.ViewModels.Econsult.FederationBroker;
using Cerebrum.ViewModels.Econsult;
using System.Net;
using System.IO;
using System.Text;
using System.Collections;
using System.Threading.Tasks;
using Cerebrum30.Infrastructure;
using Cerebrum.BLL.User;
using System.Net.Http;
using System.Security.Claims;
using Cerebrum30.Security;
using Cerebrum30.API.OneIdToken.Healpers;
using Newtonsoft.Json;
using Cerebrum.IdentityModelBLL.Services;
using System.Net.Http.Headers;
using Cerebrum.IdentityModelBLL.Dto;
using System.Net.Sockets;
using Cerebrum.ViewModels.Econsult.OneIdToken;
using Cerebrum30.Helpers;
using Cerebrum30.Utility;

namespace Cerebrum30.Controllers
{
    public class OneIdLoginAnonymousController : Controller
    {
        private IUserCredentialBLL _userCredential;
        private readonly IHttpClientFactory _httpClientFactory;
        private ApplicationSignInManager _signInManager;
        private bool _isNewUaoImplementationActive = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["NewUaoImplementationActive"]);
        private log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public OneIdLoginAnonymousController(IUserCredentialBLL userCredential, IHttpClientFactory httpClientFactory, ApplicationSignInManager signInManager)
        {
            _userCredential = userCredential;
            _httpClientFactory = httpClientFactory;
            _signInManager = signInManager;
        }

        public ApplicationSignInManager SignInManager
        {
            get
            {
                return _signInManager;
            }
            private set { _signInManager = value; }
        }
        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult> Index(string clientId)//OAuthLogin()
        {
            string[] words = clientId.Split('|');
            string ohClientId = string.Empty;
            string practiceName = string.Empty;
            if (words.Length > 1)
            {
                ohClientId = words[0];
                practiceName = words[1];
            }
            HttpContext.Session.SetString("AnonymousClientId", ohClientId ?? "");
            HttpContext.Session.SetString("PracticeName", practiceName ?? "");
            // step 1
            // get identityToken 
            var scope = System.Configuration.ConfigurationManager.AppSettings["IdentityServerAuthorizeUrlScope"];
            var identityToken = UserServiceHelpers.GetAwareMDIdentityToken(scope);
            // step 2
            // send request to auth server - "https://externalauthentication.mycerebrum.com/API/Authorization/AuthorizeUrl"
            var httpClient = _httpClientFactory.CreateClient("eConsult");

            string REQUEST_TIMEOUT = System.Configuration.ConfigurationManager.AppSettings["EconsultRequestTimeout"];
            httpClient.Timeout = TimeSpan.FromSeconds(int.Parse(REQUEST_TIMEOUT));

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", identityToken);
            var ipAddress = IPAddress(Request);
            AuthorizeUrlRequest request = new AuthorizeUrlRequest()
            {
                IPAddress = ipAddress,
                IsAnonymousUser = true,
                RedirectUri = System.Configuration.ConfigurationManager.AppSettings["AssertionConsumerServiceURL"], //"https://c3test.mycerebrum.com/api/OneIdToken2/",
                OntarioHealthClientId = ohClientId
                //ScopeId = "OrgRead"
            };
            if (_isNewUaoImplementationActive)
            {
                request.ScopeId = "OpenId";
            }
            string requestString = JsonConvert.SerializeObject(request);
            var requestContent = new StringContent(requestString, Encoding.UTF8, "application/json");
            string authenticationUrl = System.Configuration.ConfigurationManager.AppSettings["eConsultAuthenticationUrl"]; //"https://externalauthentication.mycerebrum.com"
            string strResponseUrl = "";
            string strResponse = "";

            HttpResponseMessage httpResponse;
            try
            {
                var url = $"{authenticationUrl}/API/Authorization/AuthorizeUrl";
                httpResponse = await httpClient.PostAsync(url, requestContent);

                strResponseUrl = await httpResponse.Content.ReadAsStringAsync();
                if (httpResponse.StatusCode == HttpStatusCode.OK)
                {
                    // step 3
                    // if respone OK it returns redirect url with all required parameters,
                    // this is OneId Login
                    return Redirect(strResponseUrl);
                }
                UtilityHelper.WriteEconsultOneIdError("Error in OneIdLoginAnonymousController, strResponseUrl: " + strResponseUrl +
                    Environment.NewLine + "StatusCode: " + httpResponse.StatusCode +
                    Environment.NewLine + "ipAddress: " + ipAddress);

                if (httpResponse.StatusCode == HttpStatusCode.Unauthorized)
                {
                    strResponse = "You are not authorized to access this resource";
                    return RedirectToAction("OAuthError", new { error = strResponse });
                }
            }
            catch (TaskCanceledException tce)
            {
                if (tce.CancellationToken.IsCancellationRequested)
                {
                    return RedirectToAction("OAuthError", new { error = "Request Timeout" });
                }
            }
            catch (Exception ex)
            {
                UtilityHelper.WriteEconsultError(ex);
            }
            strResponse = $"Error occurred when accessing authentication url: {strResponseUrl}";
            return RedirectToAction("OAuthError", new { error = strResponse });
        }


        [HttpGet]
        [AllowAnonymous]
        // this user has not logged in to cerebrum, only OneId, try to log in using 'OneIdSubjectNameId'
        public async Task<ActionResult> Login()
        {
            try
            {
                // old OneIdUserSession
                var sessionJson = HttpContext.Session.GetString("oauthSessionResponse");
                if (sessionJson != null)
                {
                    var oneIdUserSession = System.Text.Json.JsonSerializer.Deserialize<Cerebrum.ViewModels.Econsult.OneIdToken.OAuthSessionResponse>(sessionJson);
                    var OneIdSubjectNameId = Convert.ToString(oneIdUserSession.Subject);

                    if (OneIdSubjectNameId != "")
                    {
                        // check database if we can autologin
                        using (var context = new CerebrumContext())
                        {
                            // maybe we need to implement multiple logins for the same doctor (OneId has only one login, but many in cerebrum DB)
                            // as discussed with Igor autologin to only one account that already linked, otherwise login to cerebrum first !!!
                            //var CUsers = context.CUsers.Where(u => u.ApplicationUser.OneIdSubjectNameId != null
                            //            && u.ApplicationUser.OneIdSubjectNameId.ToLower() == OneIdSubjectNameId.ToLower()).FirstOrDefault();
                            var CUsers = context.Users.Where(u => u.OneIdSubjectNameId != null
                                       && u.OneIdSubjectNameId.ToLower() == OneIdSubjectNameId.ToLower()).ToList();
                            // check how many accounts this user has, if one autologin
                            // else -show return list of username to login
                            // what to do if one or more account not linked to OneId Federation Broker???
                            if (CUsers?.Count > 0)
                            {
                                if (CUsers.Count == 1)
                                {
                                    // if one user found - log him/her in
                                    //SignInManager.SignIn(CUsers[0], true, true);
                                    await SignInCerebrumUser(CUsers[0]);
                                    var userId = CUsers[0].UserID;
                                    // save OneId tokens
                                    // if user came after logged in to OneId site, save OneIdSession
                                    await SaveOneIdToken(userId);
                                    if (oneIdUserSession.FirstName != null && oneIdUserSession.LastName != null)
                                    {
                                        if (Helper.ToLowerAndTrim(CUsers[0].FirstName) != Helper.ToLowerAndTrim(oneIdUserSession.FirstName) || Helper.ToLowerAndTrim(CUsers[0].LastName) != Helper.ToLowerAndTrim(oneIdUserSession.LastName))
                                        {
                                            UtilityHelper.WriteEconsultError("Invalid user, LinkToOneId = true, login OneIdLoginAnonymous controller" +
                                                Environment.NewLine + "user FirstName: " + CUsers[0].FirstName + ", oneId FirstName: " + oneIdUserSession.FirstName +
                                                Environment.NewLine + "user LastName: " + CUsers[0].LastName + ", oneId LastName: " + oneIdUserSession.LastName, userId);
                                        }
                                        if (_isNewUaoImplementationActive)
                                        {
                                            // log attempts, user was found, user with scope, UAO
                                            _log.Info("OneIdLoginAnonymousController, method Login, user was found. User with scope and UAO selected. OneIdSubjectNameId: " + OneIdSubjectNameId + ", userId: " + userId);
                                        }
                                    }
                                    else
                                    {
                                        if (_isNewUaoImplementationActive)
                                        {
                                            // log attempts, user was found, user without scope, no uao provided
                                            _log.Info("OneIdLoginAnonymousController, method Login, user was found. User without scope and UAO. OneIdSubjectNameId: " + OneIdSubjectNameId + ", userId: " + userId);
                                        }
                                    }
                                    // if user has one UAO call oauth api to get token for UAO with all scopes, then redirect to daysheet
                                    // else show UI select UAO, then call oauth api to get token for UAO with all scopes, then redirect to daysheet
                                    if (_isNewUaoImplementationActive)
                                    {
                                        var cerebrumUser = new CerebrumUser(User as ClaimsPrincipal);
                                        if (cerebrumUser.UserUaoCount == 0)
                                        {
                                            // log attempts, user was found, user does not have UAO in EMR DB
                                            _log.Info("OneIdLoginAnonymousController, method Login, user was found. UAO is not set. OneIdSubjectNameId: " + OneIdSubjectNameId + ", userId: " + userId);

                                            // test Delia, who has no UAO
                                            return RedirectToAction("Error2", "OneIdLogin", new { error = "UAO is not set, please contact administrator" });
                                        }
                                        else if (cerebrumUser.UserUaoCount == 1)
                                        {
                                            // log attempts, user was found, user has one UAO in EMR DB
                                            _log.Info("OneIdLoginAnonymousController, method Login, user was found. User has one UAO in EMR DB. OneIdSubjectNameId: " + OneIdSubjectNameId + ", userId: " + userId);

                                            // redirect to One ID Login site with UAO, dr. Spencer has one UAO
                                            return RedirectToAction("OAuthLogin", "OneIdLogin", new { area = "" });
                                        }
                                        else
                                        {
                                            // log attempts, user was found, user has multiple UAO in EMR DB
                                            _log.Info("OneIdLoginAnonymousController, method Login, user was found. User has multiple UAO in EMR DB. Redirected to select UAO. OneIdSubjectNameId: " + OneIdSubjectNameId + ", userId: " + userId);

                                            // redirect to select UAO, dr. Renata has two UAO
                                            return RedirectToAction("Index", "UserUAO", new { area = "Admin" });
                                        }
                                    }
                                    else
                                    {
                                        // old implementation
                                        return RedirectToAction("Index", "Daysheet", new { area = "Schedule" });
                                    }
                                }
                                else
                                {
                                    // more then one linked users found
                                    return RedirectToAction("Error", new { error = CUsers.Count.ToString() });
                                }
                            }
                            else
                            {
                                if (_isNewUaoImplementationActive)
                                {
                                    // log attempts, user was not found
                                    _log.Info("OneIdLoginAnonymousController, method Login, user was not found. OneIdSubjectNameId: " + OneIdSubjectNameId);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                UtilityHelper.WriteEconsultError(ex);
            }
            // this flag is used to prompt user to link account onlyonce
            HttpContext.Session.SetString("LoginAsAnonymous", "true");

            return RedirectToAction("Login", "Account");
        }

        [HttpGet]
        [AllowAnonymous]
        public ActionResult LinkMe()
        {
            try
            {
                var listOfUsers = TempData["listOfUsers"] as List<VMLinkMeModel>;
                TempData["listOfUsers"] = listOfUsers;

                ViewBag.ListOfUsers = listOfUsers;
            }
            catch (Exception ex)
            {
                UtilityHelper.WriteEconsultError(ex);
            }

            return View();
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public ActionResult LinkMe(string Users)
        {
            try
            {
                if (HttpContext.Session.GetString("oauthSessionResponse") != null)
                {
                    // resignin user
                    ReSignInUser(Users);
                }
                if (_isNewUaoImplementationActive)
                {
                    var cerebrumUser = new CerebrumUser(User as ClaimsPrincipal);

                    if (cerebrumUser.UserUaoCount == 0)
                    {
                        // test Delia, who has no UAO
                        return RedirectToAction("Error2", "OneIdLogin", new { error = "UAO is not set, please contact administrator" });
                    }
                    else if (cerebrumUser.UserUaoCount == 1)
                    {
                        // redirect to One ID Login site with UAO, dr. Spencer has one UAO
                        return RedirectToAction("OAuthLogin", "OneIdLogin", new { area = "" });
                    }
                    else
                    {
                        // redirect to select UAO, dr. Renata has two UAO
                        return RedirectToAction("Index", "UserUAO", new { area = "Admin" });
                    }
                }
                else
                {
                    return RedirectToAction("Index", "Consult", new { area = "eConsult" });
                }
            }
            catch (Exception ex)
            {
                UtilityHelper.WriteEconsultError(ex);
            }

            return View();
        }

        [HttpGet]
        [AllowAnonymous]
        public ActionResult Error()
        {
            var numberOfUsersFound = HttpUtility.HtmlDecode(Request.Query["error"].ToString());
            var message = numberOfUsersFound + " users linked to One Id login. Please contact admin!";
            ViewBag.Message = message;

            return View();
        }
        [HttpGet]
        [AllowAnonymous]
        public ActionResult Error2()
        {
            var message = HttpUtility.HtmlDecode(Request.Query["error"].ToString());
            ViewBag.Message = message;

            return View("Error");
        }
        [HttpGet]
        [AllowAnonymous]
        public ActionResult OAuthError()
        {
            ViewBag.Message = HttpUtility.HtmlDecode(Request.Query["error"].ToString());
            ViewBag.Description = Request.Query["description"].ToString();

            return View("Error");
        }
        private async void ReSignInUser(string id)
        {
            using (var context = new CerebrumContext())
            {
                var userId = int.Parse(id);
                var CUser = context.Users.Where(a => a.UserID == userId).FirstOrDefault();

                await SignInCerebrumUser(CUser);

                await SaveOneIdToken(userId);
            }
        }

        // AuthenticationManager is replaced by SignInManager in ASP.NET Core
        // Use dependency injection instead
        private async Task SignInCerebrumUser(Cerebrum.Data.ApplicationUser user, bool isPersistent = false)
        {
            if (User.Identity.IsAuthenticated)
            {
                await HttpContext.SignOutAsync();
            }

            // TODO: Implement proper user lockout reset for ASP.NET Core Identity
            // var result = await SignInManager.UserManager.ResetAccessFailedCountAsync(user.Id);
            // var lockoutEndDate = await SignInManager.UserManager.GetLockoutEndDateAsync(user.Id);
            // if (lockoutEndDate != null && lockoutEndDate > DateTime.UtcNow)
            // {
            //     var lockoutEnabled = await SignInManager.UserManager.GetLockoutEnabledAsync(user.Id);
            //     if (!lockoutEnabled)
            //         result = await SignInManager.UserManager.SetLockoutEnabledAsync(user.Id, true);
            //     result = await SignInManager.UserManager.SetLockoutEndDateAsync(user.Id, DateTime.UtcNow);
            //     if (!lockoutEnabled)
            //         result = await SignInManager.UserManager.SetLockoutEnabledAsync(user.Id, false);
            // }
            string ipAddress = IPAddress(Request);
            var claimsList = _userCredential.GetUserClaims(_httpClientFactory, user, ipAddress);
            var identity = new ClaimsIdentity(claimsList, "ApplicationCookie");

            await HttpContext.SignInAsync("ApplicationCookie", new ClaimsPrincipal(identity), new AuthenticationProperties() { IsPersistent = isPersistent });

            var cerebrumUser = new CerebrumUser(new ClaimsPrincipal(identity));
            HttpContext.User = cerebrumUser;
            // for signalR
            HttpContext.Session.SetInt32("PracticeId", cerebrumUser.PracticeId);
            HttpContext.Session.SetInt32("UserId", cerebrumUser.UserId);
        }

        private string IPAddress(Microsoft.AspNetCore.Http.HttpRequest r)
        {
            var ip = GetUserIP(r);
            if (ip.Equals("::1"))
            {
                try
                {
                    ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork).ToString();
                }
                catch { }
            }
            return ip;
        }
        private string GetUserIP(Microsoft.AspNetCore.Http.HttpRequest r)
        {
            return RequestUtils.GetIpAddress(r);
        }
        private async Task SaveOneIdToken(int userId)
        {
            var sessionData = HttpContext.Session.GetString("oauthSessionResponse");
            if (!string.IsNullOrEmpty(sessionData))
            {
                var oneIdUserSession = System.Text.Json.JsonSerializer.Deserialize<Cerebrum.ViewModels.Econsult.OneIdToken.OAuthSessionResponse>(sessionData);
                EconsultBLL _EconsultBLL = new EconsultBLL();
                _EconsultBLL.UserId = userId;

                await _EconsultBLL.SaveOneIdToken(_httpClientFactory, oneIdUserSession);
            }
        }

    }
}