
//#define LDAP

using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Cerebrum30.Models;
using Cerebrum30.Utility;
using Cerebrum.Data;
using System.Security.Claims;
using Cerebrum30.Infrastructure;
using Cerebrum30.Areas.WebBooking.DataAccess;
using AwareMD.Cerebrum.Shared.Enums;
using Newtonsoft.Json;
using Cerebrum.BLL.User;
using Cerebrum.ViewModels.User;
using log4net;
using System.Net;
using System.Net.Sockets;
using System.Net.Http;
using System.Configuration;
using System.Globalization;
using Microsoft.Extensions.Configuration;
// TODO: Replace System.Web.Script.Serialization with Newtonsoft.Json or System.Text.Json
// using System.Web.Script.Serialization;
using System.Text;
using Cerebrum.BLL.Utility;
using Ninject;
using Cerebrum30.Security;
using System.Security.Claims;
using Cerebrum.Data;
using Cerebrum.BLL.Econsult;
using Cerebrum.ViewModels.Econsult.OneIdToken;
using Cerebrum30.Areas.EForms.Controllers.BLL;
using AwareMD.Cerebrum.Shared;
using AwareMD.Eforms.Service;
using Cerebrum30.Extensions;
using Cerebrum.BLL.Practice;

namespace Cerebrum30.Controllers
{

    public class AccountController : Controller
    {
        private log4net.ILog _log;
        private IUserCredentialBLL _userCredential;
        private IUserPasswordService _userPasswordService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IConfiguration _configuration;

        private const string TWOFACTORAUTHENTICATIONCODEEXPIREDTIME = "TwoFactorAuthenticationCodeExpiredTime";
        private const string FORM_BUILDER_POPUP = "FormBuilderPopup";
        private const string FORM_BUILDER_LOGOUT_REQUIRED = "FormBuilderLogoutRequired";
        private const string FORM_BUILDER_OPENED = "FormBuilderOpened";

        private const string DATEFORMAT = "MM/dd/yyyy HH:mm:ss";
        private bool _isNewUaoImplementationActive = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["NewUaoImplementationActive"]);
        private readonly bool _fbNewImplementation = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["FbNewImplementation"]);
        private readonly string _formbuilderLogoutUrl = System.Configuration.ConfigurationManager.AppSettings["FormbuilderSite"] + "Logout.aspx";
        private IPracticeBLL _practiceBLL;

        public AccountController(IUserCredentialBLL userCredential, IHttpClientFactory httpClientFactory, IPracticeBLL practiceBLL, IUserPasswordService userPasswordService, UserManager<ApplicationUser> userManager, SignInManager<ApplicationUser> signInManager, IConfiguration configuration)
        {
            _userCredential = userCredential;
            _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            _httpClientFactory = httpClientFactory;
            _practiceBLL = practiceBLL;
            _userPasswordService = userPasswordService;
            _userManager = userManager;
            _signInManager = signInManager;
            _configuration = configuration;
        }


        // UserManager is now injected via constructor - no longer needed as property
        //
        private void WriteCookie(bool enabled)
        {
            var cookieOptions = new CookieOptions();
            if (!enabled)
            {
                cookieOptions.Expires = DateTime.Now.AddDays(-1);
            }
            else
            {
                cookieOptions.Expires = DateTime.Now.AddMinutes(1);
            }

            Response.Cookies.Append("OneIdPopup", "Required", cookieOptions);
        }

        private void WriteFormBuilderCookie(bool enabled)
        {
            var cookieOptions = new CookieOptions();
            if (!enabled)
            {
                cookieOptions.Expires = DateTime.Now.AddDays(-1);
            }
            else
            {
                cookieOptions.Expires = DateTime.Now.AddMinutes(1);
            }

            Response.Cookies.Append(FORM_BUILDER_POPUP, "Required", cookieOptions);
        }
        // GET: /Account/Login
        [AllowAnonymous]
        public ActionResult Login(string returnUrl)
        {
            ResponseNoCache();
            if (User.Identity.IsAuthenticated)
            {
                return RedirectToAction("RedirectToLocal", "Manage");
            }

#if LDAP
            //  ViewBag.ReturnUrl = returnUrl;
#else
            ViewBag.ReturnUrl = returnUrl;
            var i = User.Identity.IsAuthenticated;
            ViewBag.LinkToOneIdEnabled = IslinkToOneIdEnabled;

            ViewBag.isShowPopup = false;

            string cookieValue = Request.Cookies["OneIdPopup"];
            if (!string.IsNullOrEmpty(cookieValue))
            {
                // In ASP.NET Core, cookies are simple string values, not complex objects
                if (cookieValue == "Required" && HttpContext.Session.GetObject<OAuthSessionResponse>("oauthSessionResponse") == null)
                {
                    ViewBag.isShowPopup = true;
                }
            }

            ViewBag.isFbLogoutRequired = false;

            string fbCookieValue = Request.Cookies[FORM_BUILDER_POPUP];
            if (!string.IsNullOrEmpty(fbCookieValue))
            {
                // In ASP.NET Core, cookies are simple string values, not complex objects
                if (fbCookieValue == "Required")
                {
                    ViewBag.isFbLogoutRequired = true;
                    ViewBag.FbLogoutUrl = _formbuilderLogoutUrl;
                }
            }
            Response.Headers["Cache-Control"] = "no-cache";
            // get list of practices that have Ontario Health ClientId
            ViewBag.Practices = _practiceBLL.GetAllPractice().Where(p => p.OntarioHealthClientId != null && p.OntarioHealthClientId.Length > 0).ToList();

            // Add configuration values for the view
            ViewBag.ApplicationName = _configuration["ApplicationName"] ?? "Cerebrum";
            ViewBag.EConsultActive = _configuration["eConsultActive"] ?? "false";

            LoginViewModel model = new LoginViewModel();
            return View(model);
#endif
        }

        // SignInManager is now injected via constructor - no longer needed as property


        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult> Login(LoginViewModel model, string returnUrl)
        {
            ResponseNoCache();
            // for One Id users session is required
            if (HttpContext.Session.GetObject<OAuthSessionResponse>("oauthSessionResponse") == null)
            {
                HttpContext.Session.Clear();
            }
            if (User.Identity.IsAuthenticated)
                await _signInManager.SignOutAsync();

            ViewBag.Practices = _practiceBLL.GetAllPractice().Where(p => p.OntarioHealthClientId != null && p.OntarioHealthClientId.Length > 0).ToList();
            
            // Add configuration values for the view
            ViewBag.ApplicationName = _configuration["ApplicationName"] ?? "Cerebrum";
            ViewBag.EConsultActive = _configuration["eConsultActive"] ?? "false";

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var ip = GetIPAddress(Request);
            string computername = "";
            try
            {
                computername = Dns.GetHostEntry(ip).HostName.ToString();
            }
            catch { }
            model.RememberMe = false;

            //TODO: update after certification
            var defaultExpiredDays = 120; // password expired
            var maxFailedAttemps = 5; // default if no office
            var user = await _userManager.FindByNameAsync(model.Email.Trim());
            //GlobalContext.Properties["user"] = model.Email.Trim();
            // ASP.NET Core: Construct URL from Request properties
            GlobalContext.Properties["page"] = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}";
            GlobalContext.Properties["ipaddress"] = ip;
            // uncomment for conformance test GlobalContext and logoff, autologoff
            //if (user == null)
            //{
            //    GlobalContext.Properties["practiceID"] = null;
            //}
            //else
            //{
            //    GlobalContext.Properties["practiceID"] = user.PracticeID;
            //}
            //GlobalContext.Properties["LoggedIn"] = "0";
            if (user == null)
            {
                ModelState.AddModelError("", "Invalid login attempt.");
                // ASP.NET Core: Update properties for compatibility
                var logmsgV = new
                {
                    userName = model.Email,
                    sessionId = HttpContext.Session.Id,
                    URL = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}",
                    HttpMethod = Request.Method,
                    Path = Request.Path,
                    Form = Request.QueryString.ToString(),
                    IPAddress = ip,
                    ComputerName = computername,
                    LogOnUser = User.Identity.Name ?? "unknown", // LogonUserIdentity doesn't exist in ASP.NET Core
                    Browser = Request.Headers["User-Agent"].ToString(), // Browser property doesn't exist in ASP.NET Core
                    Port = Request.Host.Port?.ToString() ?? "unknown",
                    IsAuthenticated = User.Identity.IsAuthenticated,
                    status = "Failed"
                };

                _log.Auth(JsonConvert.SerializeObject(logmsgV));
                return View(model);
            }
            else if (user.Status == UserStatus.Inactive)
            {
                ModelState.AddModelError("", "Your account is inactive. Please contact admin.");
                // ASP.NET Core: Update properties for compatibility
                var logmsgV = new
                {
                    userName = model.Email,
                    sessionId = HttpContext.Session.Id,
                    URL = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}",
                    HttpMethod = Request.Method,
                    Path = Request.Path,
                    Form = Request.QueryString.ToString(),
                    IPAddress = ip,
                    ComputerName = computername,
                    LogOnUser = User.Identity.Name ?? "unknown", // LogonUserIdentity doesn't exist in ASP.NET Core
                    Browser = Request.Headers["User-Agent"].ToString(), // Browser property doesn't exist in ASP.NET Core
                    Port = Request.Host.Port?.ToString() ?? "unknown",
                    IsAuthenticated = User.Identity.IsAuthenticated,
                    status = "Failed"
                };

                _log.Auth(JsonConvert.SerializeObject(logmsgV));
                return View(model);
            }
            else
            {
                var office = _userCredential.GetOfficeLoginSetting(user.PracticeID);
                maxFailedAttemps = office.NumberOfLoginsAllowed;
                defaultExpiredDays = office.NumberOfDaysPswdExp;
                // MaxFailedAccessAttemptsBeforeLockout is configured in Program.cs for ASP.NET Core Identity
                using (var context = new CerebrumContext())
                {
                    try
                    {
                        var oauthSessionResponse = HttpContext.Session.GetObject<OAuthSessionResponse>("oauthSessionResponse");
                        if (oauthSessionResponse != null)
                        {
                            // if user came after logged in to OneId site, save OneIdSession
                            var oneIdUserSession = oauthSessionResponse;

                            // if user came after logged in to OneId site, save OneIdSession
                            EconsultBLL _EconsultBLL = new EconsultBLL();
                            _EconsultBLL.UserId = user.UserID;
                            // Use fully qualified name to avoid ambiguity with Microsoft.AspNetCore.Http.SessionExtensions
                            Cerebrum30.Extensions.SessionExtensions.SetInt32(HttpContext.Session, "UserId", user.UserID);

                            await _EconsultBLL.SaveOneIdToken(_httpClientFactory, oneIdUserSession);

                            if (!string.IsNullOrEmpty(oneIdUserSession.FirstName) && !string.IsNullOrEmpty(oneIdUserSession.LastName))
                            {
                                if (Helper.ToLowerAndTrim(user.FirstName) != Helper.ToLowerAndTrim(oneIdUserSession.FirstName) || Helper.ToLowerAndTrim(user.LastName) != Helper.ToLowerAndTrim(oneIdUserSession.LastName))
                                {
                                    UtilityHelper.WriteEconsultError("Invalid user, login account controller" +
                                        Environment.NewLine + "user FirstName: " + user.FirstName + ", oneId FirstName: " + oneIdUserSession.FirstName +
                                        Environment.NewLine + "user LastName: " + user.LastName + ", oneId LastName: " + oneIdUserSession.LastName, user.UserID);
                                }
                            }
                        }
                        if (model.LinkToOneId)
                        {
                            // next time user will be autologin after signed in OneId Site, remove next if, move code inside above if statement 
                            var oauthSessionResponse2 = HttpContext.Session.GetObject<OAuthSessionResponse>("oauthSessionResponse");
                            if (oauthSessionResponse2 != null)
                            {
                                if (_isNewUaoImplementationActive)
                                {
                                    var oneIdUserSession = oauthSessionResponse2;

                                    //user.OneIdContactEmail = oneIdUserSession.UserName;
                                    user.OneIdSubjectNameId = oneIdUserSession.Subject;

                                    // call api to get practitionerId and save in aspnetusers
                                    //EconsultBLL _eConsultBLL = new EconsultBLL();
                                    //_eConsultBLL.UserId = user.UserID;
                                    //var otnPractitionerId = _eConsultBLL.GetPractitionerIdByName(oneIdUserSession.UserName, _httpClientFactory);
                                    //user.OtnPractitionerId = otnPractitionerId;

                                    // Use injected _userManager instead of UserManager property
                                    await _userManager.UpdateAsync(user);
                                    context.SaveChanges();
                                }
                                else
                                {
                                    var oneIdUserSession = oauthSessionResponse2;

                                    user.OneIdContactEmail = oneIdUserSession.UserName;
                                    user.OneIdSubjectNameId = oneIdUserSession.Subject;

                                    // call api to get practitionerId and save in aspnetusers
                                    EconsultBLL _eConsultBLL = new EconsultBLL();
                                    _eConsultBLL.UserId = user.UserID;
                                    var otnPractitionerId = _eConsultBLL.GetPractitionerIdByName(oneIdUserSession.UserName, _httpClientFactory);
                                    user.OtnPractitionerId = otnPractitionerId;

                                    // Use injected _userManager instead of UserManager property
                                    await _userManager.UpdateAsync(user);
                                    context.SaveChanges();
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        UtilityHelper.WriteEconsultError(ex);
                    }
                }
            }

            // Use injected _userManager instead of UserManager property
            // TODO: ipAddressLoginFrom property doesn't exist in ASP.NET Core UserManager
            // _userManager.ipAddressLoginFrom = ip;

            var isLockedOut = await _userManager.IsLockedOutAsync(user);
            var signInResult = SignInStatus.Failure;
            if (isLockedOut)
            {
                signInResult = SignInStatus.LockedOut;
            }
            else
            {
                var isPasswordCorrect = await _userManager.CheckPasswordAsync(user, model.Password);
                if (isPasswordCorrect)
                {
                    var strUserId = user.Id;
                    var isTwoFactorEnabled = GetTwoFactorEnabled(user.Id, ip);
                    if (isTwoFactorEnabled)
                    {
                        var providers = await _userManager.GetValidTwoFactorProvidersAsync(user);

                        if (providers.Count > 0)
                        {
                            // TODO: Implement two-factor browser remembering for ASP.NET Core
                            var twoFactorBroswer = false; // await AuthenticationManagerExtensions.TwoFactorBrowserRememberedAsync(AuthenticationManager, strUserId);
                            if (!twoFactorBroswer)
                            {
                                // TODO: Implement two-factor cookie for ASP.NET Core
                                signInResult = SignInStatus.RequiresVerification;
                            }
                            else
                            {
                                signInResult = SignInStatus.Success;
                            }
                        }
                    }
                    else
                    {
                        signInResult = SignInStatus.Success;
                    }
                }
                else
                {
                    var accessFailed = await _userManager.AccessFailedAsync(user);
                    isLockedOut = await _userManager.IsLockedOutAsync(user);
                    if (isLockedOut)
                    {
                        signInResult = SignInStatus.LockedOut;
                    }
                }

            }
            #region Log Events
            //log this login attempt in the login history table           
            AddLoginHistory(model.Email, signInResult);

            // ASP.NET Core: Update properties for compatibility
            var logmsg = new
            {
                sessionId = HttpContext.Session.Id,
                URL = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}",
                HttpMethod = Request.Method,
                Path = Request.Path,
                Form = Request.QueryString.ToString(),
                userName = model.Email,
                IPAddress = ip,
                ComputerName = computername,
                LogOnUser = User.Identity.Name ?? "unknown", // LogonUserIdentity doesn't exist in ASP.NET Core
                Browser = Request.Headers["User-Agent"].ToString(), // Browser property doesn't exist in ASP.NET Core
                Port = Request.Host.Port?.ToString() ?? "unknown",
                IsAuthenticated = User.Identity.IsAuthenticated,
                status = signInResult.ToString()
            };
            //GlobalContext.Properties["LoggedIn"] = "1";
            _log.Auth(JsonConvert.SerializeObject(logmsg));
            #endregion

            Microsoft.AspNetCore.Http.SessionExtensions.SetString(HttpContext.Session, "LoginAttempt", model.Email);
            switch (signInResult)
            {
                case SignInStatus.Success:
                    {
                        await SignInCerebrumUser(user);
                        if (user != null)
                        {
                            try
                            {
                                var idpAccessToken = _userCredential.GetAccessTokenOrNull(user.UserID, user.PracticeID);
                                if (idpAccessToken != null)
                                {
                                    new CerebrumUser(User as ClaimsPrincipal)
                                    {
                                        IdpAccessToken = idpAccessToken
                                    };
                                }
                            }
                            // allow us to continue if we can't get the access token
                            catch (Exception ex)
                            {
                                _log.Error("Failed to get idpAccessToken during user login:\n" + ex);
                            }

                            var oauthSessionResponse3 = HttpContext.Session.GetObject<OAuthSessionResponse>("oauthSessionResponse");
                            if (_isNewUaoImplementationActive && oauthSessionResponse3 != null)
                            {
                                var cerebrumUser = new CerebrumUser(User as ClaimsPrincipal);
                                if (cerebrumUser.UserUaoCount == 0)
                                {
                                    // test Delia, who has no UAO, userId: 3252
                                    return RedirectToAction("Error2", "OneIdLogin", new { error = "UAO is not set, please contact administrator" });
                                }
                                else if (cerebrumUser.UserUaoCount == 1)
                                {
                                    // redirect to One ID Login site with UAO, dr. Spencer (3250), dr. Rebecca (3253) has one UAO
                                    return RedirectToAction("OAuthLogin", "OneIdLogin", new { area = "" });
                                }
                                else
                                {
                                    // redirect to select UAO, dr. Renata (3251) has two UAO
                                    return RedirectToAction("Index", "UserUAO", new { area = "Admin" });
                                }
                            }

                            if (user.PasswordChangeRequired == true)
                            {
                                return RedirectToAction("ChangePassword", "Manage", new { area = "" });
                            }

                            if (_userPasswordService.IsPasswordExpired(user.UserID, defaultExpiredDays))
                            {
                                return RedirectToAction("ChangePassword", "Manage", new { area = "" });
                            }
                        }
                        return RedirectToAction("IdpAccessTokenInterceptor", "Manage");

                    }
                case SignInStatus.LockedOut:
                    {
                        return View("Lockout");
                    }
                case SignInStatus.RequiresVerification:
                    {
                        int resultTwoFactor = _userCredential.CheckTwoFactorAuthentication(user.Id, ip);
                        if (resultTwoFactor == 1)
                            return RedirectToAction("SendTwoAuthenticationCode", new { ReturnUrl = returnUrl });

                        ModelState.AddModelError("", "Two Factor Authentication is not allowed on this computer");
                        return View(model);
                    }
                case SignInStatus.Failure:
                default:
                    {
                        ModelState.AddModelError("", "Invalid login attempt.");
                        return View(model);
                    }
            }
        }

        [AllowAnonymous]
        public async Task<ActionResult> SendTwoAuthenticationCode(string ReturnUrl)
        {
            // Generate the token and send it
            // TODO: SendTwoFactorCodeAsync method doesn't exist in ASP.NET Core SignInManager
            // if (!await _signInManager.SendTwoFactorCodeAsync("EmailCode"))
            if (false) // Placeholder - two-factor code sending disabled
            {
                return View("Error");
            }
            return RedirectToAction("VerifyCode", new { Provider = "EmailCode", ReturnUrl = ReturnUrl });

        }

        [AllowAnonymous]
        public async Task<ActionResult> Send_Second_TwoAuthenticationCode(string ReturnUrl)
        {
            // Generate the token and send it
            // TODO: SendTwoFactorCodeAsync method doesn't exist in ASP.NET Core SignInManager
            // if (!await _signInManager.SendTwoFactorCodeAsync("SecondEmailCode"))
            if (false) // Placeholder - two-factor code sending disabled
            {
                return View("Error");
            }
            return RedirectToAction("VerifyCode", new { Provider = "SecondEmailCode", ReturnUrl = ReturnUrl });
        }

        //
        // GET: /Account/VerifyCode
        [AllowAnonymous]
        public async Task<ActionResult> VerifyCode(string provider, string returnUrl)
        {
            // Require that the user has already logged in via username/password or external login
            // TODO: HasBeenVerifiedAsync and GetVerifiedUserIdAsync don't exist in ASP.NET Core SignInManager
            // if (!await _signInManager.HasBeenVerifiedAsync())
            // {
            //     return View("Error");
            // }
            // var user = await _userManager.FindByIdAsync(await _signInManager.GetVerifiedUserIdAsync());
            var userId = _userManager.GetUserId(User);
            var user = await _userManager.FindByIdAsync(userId);
            if (user != null)
            {
                var code = await _userManager.GenerateTwoFactorTokenAsync(user, provider);
                ViewBag.Status = "For DEMO purposes the current " + provider + " code is: " + code;
                bool bool_ = Helper.SendCodeByMail(provider, user.Email, code);
                //ViewBag.Status = "For DEMO purposes the current " + provider + " code is: " + await _userManager.GenerateTwoFactorTokenAsync(user, provider);
            }
            return View(new VerifyCodeViewModel { Provider = provider, ReturnUrl = returnUrl });
        }

        //
        // POST: /Account/VerifyCode
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> VerifyCode(VerifyCodeViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var strUserId = await GetTwoFactorUserIdAsync();
            if (string.IsNullOrEmpty(strUserId))
            {
                return View("Timeout");
            }

            var twoFactorAuthenticationCodeExpiredTime = await GetTwoFactorAuthenticationCodeExpiredTime(strUserId);
            if (twoFactorAuthenticationCodeExpiredTime == null || twoFactorAuthenticationCodeExpiredTime < DateTime.Now)
                return View("Timeout");

            var user = await _userManager.FindByIdAsync(strUserId);
            var isLocked = user != null && await _userManager.IsLockedOutAsync(user);
            if (isLocked)
            {
                return View("Lockout");
            }
            else
            {
                var isVerified = user != null && await _userManager.VerifyTwoFactorTokenAsync(user, model.Provider, model.Code);
                var isPersistent = false;
                if (isVerified)
                {
                    // TODO: Implement specific cookie sign out for ASP.NET Core
                    await _signInManager.SignOutAsync();
                    await SignInCerebrumUser(user, isPersistent: isPersistent);
                    await _userManager.UpdateSecurityStampAsync(user);
                    await _userManager.ResetAccessFailedCountAsync(user);
                    return RedirectToLocal(model.ReturnUrl);
                }
                else
                {
                    // TODO: SignInManager.UserManager doesn't exist in ASP.NET Core - using _userManager
                    await _userManager.AccessFailedAsync(user);
                    ModelState.AddModelError("", "Invalid code.");
                    return View(model);
                }
            }
        }

        //
        // GET: /Account/Register
        [AllowAnonymous]
        public ActionResult Register()
        {
            return View();
        }

        public Tuple<bool, string> CreateNewUserForExternalDoctor(int docID, int practiceId, string userName, string password, object context)
        {
            // TODO: This method needs to be updated for EF Core and ASP.NET Core Identity
            // The old Entity Framework and ASP.NET Identity patterns are not compatible with .NET 8
            string errorMssg = "External doctor user creation is temporarily disabled during .NET 8 migration. Please use alternative user management methods.";
            bool errored = true;
            return new Tuple<bool, string>(errored, errorMssg);

            /*
            // Original implementation - disabled for .NET 8 migration
            string errorMssg = string.Empty;
            bool errored = false;

            try
            {
                // Original implementation - disabled for .NET 8 migration
                /*
                var store = new UserStore<Cerebrum.Data.ApplicationUser>(context);
                var userManager = new ApplicationUserManager(store);
                var rolesManager = new RoleManager<IdentityRole>(new RoleStore<IdentityRole>(context));

                var result = AddNewUserForExternalDoctor(

                                                                new VMC3User
                                                                {
                                                                    PracticeID = practiceId,
                                                                    UserName = userName,
                                                                    Password = password,
                                                                    Role = Helper.REFERRAL_ROLE,
                                                                    //FirstName = "Administrator",
                                                                    //Province = "ON",
                                                                    CellPhone = "911",
                                                                    CerebrumUserType = UserTypeEnum.ReferralDoctor,
                                                                    UserCountry = Country.Canada
                                                                },
                                             userManager,
                                             rolesManager
                                        );


                if (!result.Item1.Succeeded)
                {
                    foreach (var error in result.Item1.Errors)
                    {
                        errorMssg += error + Environment.NewLine;
                    }
                    errored = true;
                }
                else
                {
                    WebBookingRepository repo = new WebBookingRepository();

                    int newUserID = Int32.Parse(result.Item2.ToString());

                    repo.Associate_Doc_User(newUserID, docID);
                    //aassociate user with doctorID , docID

                }

            }
            catch (Exception exc)
            {

                errorMssg = exc.Message;
                errored = true;
            }

            return new Tuple<bool, string>(errored, errorMssg);
            */

        }
        private async Task<Tuple<IdentityResult, int>> AddNewUserForExternalDoctor(VMC3User NewUser, ApplicationUserManager _userManager, RoleManager<IdentityRole> _roleManager)
        {

            string name = NewUser.UserName;
            string password = NewUser.Password;
            string roleName = NewUser.Role;
            IdentityResult result;

            var role = await _roleManager.FindByNameAsync(roleName);
            if (role == null)
            {
                role = new Cerebrum.Data.ApplicationRole(roleName);
                var roleresult = await _roleManager.CreateAsync(role);
            }

            var user = _userManager.FindByName(name);
            if (user == null)
            {
                user = new Cerebrum.Data.ApplicationUser
                {
                    //PracticeID = NewUser.PracticeID.ToString(),
                    PracticeID = NewUser.PracticeID,
                    UserName = name,
                    Email = name,
                    FirstName = NewUser.FirstName,
                    LastName = NewUser.LastName,
                    Province = NewUser.Province,
                    CellPhone = NewUser.CellPhone,
                    CerebrumUserType = NewUser.CerebrumUserType,
                    PostalCode = NewUser.PostalCode,
                    UserCountry = NewUser.UserCountry,
                    UserLoginPersistance = NewUser.UserLoginPersistance
                };
                result = _userManager.Create(user, password);

                //  result = _userManager.SetLockoutEnabled(user.Id, false);
            }
            else
            {
                throw new Exception(string.Format("Username '{0}' already exists", name));
            }

            if (result.Succeeded)
            {
                var rolesForUser = _userManager.GetRoles(user.Id);
                if (!rolesForUser.Contains(role.Name))
                {
                    result = await _userManager.AddToRoleAsync(user, role.Name);
                }
            }
            //return result;

            return new Tuple<IdentityResult, int>(result, user.UserID);
        }
        public async Task<ActionResult> Register(RegisterViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new Cerebrum.Data.ApplicationUser { UserName = model.Email, Email = model.Email };

                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.MiddleName = model.MiddleName;
                // Add the Address properties:
                user.Address = model.Address;
                user.City = model.City;
                user.Province = model.Province;
                user.PostalCode = model.PostalCode;
                user.PhoneNumber = model.PhoneNumber;

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                    var callbackUrl = Url.Action("ConfirmEmail", "Account",
                        new { userId = user.Id, code = code }, protocol: Request.Scheme);
                    // TODO: SendEmailAsync doesn't exist in ASP.NET Core UserManager - commenting out
                    // await _userManager.SendEmailAsync(user,
                    //     "Confirm your account",
                    //     "Please confirm your account by clicking this link: <a href=\""
                    //     + callbackUrl + "\">link</a>");
                    ViewBag.Link = callbackUrl;
                    return View("DisplayEmail");
                }
                AddErrors(result);
            }

            // If we got this far, something failed, redisplay form
            return View(model);
        }

        //
        // GET: /Account/ConfirmEmail
        [AllowAnonymous]
        public async Task<ActionResult> ConfirmEmail(string userId, string code)
        {
            if (userId == null || code == null)
            {
                return View("Error");
            }
            var user = await _userManager.FindByIdAsync(userId);
            var result = await _userManager.ConfirmEmailAsync(user, code);
            return View(result.Succeeded ? "ConfirmEmail" : "Error");
        }

        //
        // GET: /Account/ForgotPassword
        [AllowAnonymous]
        public ActionResult ForgotPassword()
        {
            return View();
        }

        //
        // POST: /Account/ForgotPassword
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ForgotPassword(ForgotPasswordViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByNameAsync(model.Email);
                if (user == null || !(await _userManager.IsEmailConfirmedAsync(user)))
                {
                    // Don't reveal that the user does not exist or is not confirmed
                    return View("ForgotPasswordConfirmation");
                }

                var code = await _userManager.GeneratePasswordResetTokenAsync(user);
                // Fixed: Request.Url doesn't exist in ASP.NET Core - using Request.Scheme
                var callbackUrl = Url.Action("ResetPassword", "Account", new { userId = user.Id, code = code }, protocol: Request.Scheme);
                // TODO: SendEmailAsync doesn't exist in ASP.NET Core UserManager - commenting out
                // await _userManager.SendEmailAsync(user, "Reset Password", "Please reset your password by clicking here: <a href=\"" + callbackUrl + "\">link</a>");
                ViewBag.Link = callbackUrl;
                return View("ForgotPasswordConfirmation");
            }

            // If we got this far, something failed, redisplay form
            return View(model);
        }

        //
        // GET: /Account/ForgotPasswordConfirmation
        [AllowAnonymous]
        public ActionResult ForgotPasswordConfirmation()
        {
            return View();
        }

        //
        // GET: /Account/ResetPassword
        [AllowAnonymous]
        public ActionResult ResetPassword(string code)
        {
            return code == null ? View("Error") : View();
        }

        //
        // POST: /Account/ResetPassword
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ResetPassword(ResetPasswordViewModel model)
        {

            if (!ModelState.IsValid)
            {
                return View(model);
            }
            var user = await _userManager.FindByNameAsync(model.Email);

            //password validation

            if (user == null)
            {
                // Don't reveal that the user does not exist
                return RedirectToAction("ResetPasswordConfirmation", "Account");
            }

            var ip = GetIPAddress(Request);
            if (ip.Equals("::1"))
            {
                try
                {
                    ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork).ToString();
                }
                catch { }
            }

            string status = string.Empty;

            if (user != null)
            {
                status = _userPasswordService.ValidatePassword(user, model.Password);
            }

            if (status == "OK")
            {
                bool isminimumageover = true;
                isminimumageover = _userCredential.IsMinimumAgeOver(user.UserID); // min age validation 

                if (isminimumageover == true)
                {
                    // allow 
                    var result = await _userManager.ResetPasswordAsync(user, model.Code, model.Password);  // reset pass

                    if (result.Succeeded)
                    {
                        try
                        {
                            _userPasswordService.InsertPasswordHistory(user.UserID, model.Password, ip);  // insert into pass history
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex);
                        }
                        return RedirectToAction("ResetPasswordConfirmation", "Account");
                    }
                    AddErrors(result);
                }

                else
                {
                    // reject 
                    ModelState.AddModelError("", "You have  to wait total 2 minimum days to change password");
                }
            }
            else
            {
                ModelState.AddModelError("", status);
            }
            return View();
        }

        //
        // GET: /Account/ResetPasswordConfirmation
        [AllowAnonymous]
        public ActionResult ResetPasswordConfirmation()
        {
            return View();
        }

        //
        // POST: /Account/ExternalLogin
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public ActionResult ExternalLogin(string provider, string returnUrl)
        {
            // Request a redirect to the external login provider
            return new ChallengeResult(provider, Url.Action("ExternalLoginCallback", "Account", new { ReturnUrl = returnUrl }));
        }

        //
        // GET: /Account/SendCode
        [AllowAnonymous]
        public async Task<ActionResult> SendCode(string returnUrl)
        {
            // TODO: SignInManager.GetVerifiedUserIdAsync doesn't exist in ASP.NET Core - using placeholder
            var userId = _userManager.GetUserId(User);
            if (userId == null)
            {
                return View("Error");
            }
            var user = await _userManager.FindByIdAsync(userId);
            var userFactors = await _userManager.GetValidTwoFactorProvidersAsync(user);
            var factorOptions = userFactors.Select(purpose => new SelectListItem { Text = purpose, Value = purpose }).ToList();
            return View(new SendCodeViewModel { Providers = factorOptions, ReturnUrl = returnUrl });
        }

        //
        // POST: /Account/SendCode
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> SendCode(SendCodeViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View();
            }

            // Generate the token and send it
            // TODO: SignInManager.SendTwoFactorCodeAsync doesn't exist in ASP.NET Core - using placeholder
            if (false) // (!await SignInManager.SendTwoFactorCodeAsync(model.SelectedProvider))
            {
                return View("Error");
            }
            return RedirectToAction("VerifyCode", new { Provider = model.SelectedProvider, ReturnUrl = model.ReturnUrl });
        }

        //
        // GET: /Account/ExternalLoginCallback
        [AllowAnonymous]
        public async Task<ActionResult> ExternalLoginCallback(string returnUrl)
        {
            // TODO: Implement external login for ASP.NET Core
            ExternalLoginInfo loginInfo = null; // await AuthenticationManager.GetExternalLoginInfoAsync();
            if (loginInfo == null)
            {
                return RedirectToAction("Login");
            }

            // Sign in the user with this external login provider if the user already has a login
            // TODO: SignInManager.ExternalSignInAsync doesn't exist in ASP.NET Core - using placeholder
            var result = Microsoft.AspNetCore.Identity.SignInResult.Failed;
            // Fixed: Convert SignInResult to SignInStatus for compatibility
            var signInStatus = result.ToSignInStatus();
            switch (signInStatus)
            {
                case SignInStatus.Success:
                    return RedirectToLocal(returnUrl);
                case SignInStatus.LockedOut:
                    return View("Lockout");
                case SignInStatus.RequiresVerification:
                    return RedirectToAction("SendCode", new { ReturnUrl = returnUrl });
                case SignInStatus.Failure:
                default:
                    // If the user does not have an account, then prompt the user to create an account
                    ViewBag.ReturnUrl = returnUrl;
                    // Fixed: ExternalLoginInfo properties changed in ASP.NET Core
                    ViewBag.LoginProvider = loginInfo.LoginProvider;
                    // TODO: ExternalLoginInfo.Email doesn't exist in ASP.NET Core - using placeholder
                    return View("ExternalLoginConfirmation", new ExternalLoginConfirmationViewModel { Email = "" });
            }
        }

        //
        // POST: /Account/ExternalLoginConfirmation
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ExternalLoginConfirmation(ExternalLoginConfirmationViewModel model, string returnUrl)
        {

            if (User.Identity.IsAuthenticated)
            {
                _log.Auth($"External Login Attemp failed");
                return RedirectToAction("Index", "Manage");
            }

            if (ModelState.IsValid)
            {
                // Get the information about the user from the external login provider
                // TODO: Implement external login for ASP.NET Core
                ExternalLoginInfo info = null; // await AuthenticationManager.GetExternalLoginInfoAsync();
                if (info == null)
                {
                    _log.Auth($"External Login Attemp failed {model.Email}");
                    return View("ExternalLoginFailure");
                }
                var user = new Cerebrum.Data.ApplicationUser { UserName = model.Email, Email = model.Email };
                var result = await _userManager.CreateAsync(user);
                if (result.Succeeded)
                {
                    // Fixed: ExternalLoginInfo structure changed in ASP.NET Core
                    var loginInfo = new UserLoginInfo(info.LoginProvider, info.ProviderKey, info.ProviderDisplayName);
                    result = await _userManager.AddLoginAsync(user, loginInfo);
                    if (result.Succeeded)
                    {
                        _log.Auth($"External Login Attemp succeeded {model.Email}");
                        // TODO: SignInManager.SignInAsync with rememberBrowser doesn't exist in ASP.NET Core - using _signInManager
                        await _signInManager.SignInAsync(user, isPersistent: false);
                        return RedirectToLocal(returnUrl);
                    }
                }
                AddErrors(result);
            }

            ViewBag.ReturnUrl = returnUrl;
            return View(model);
        }

        /// <summary>
        /// Auto LogOff after some time 
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        public async Task<ActionResult> AutoLogOff()
        {
            bool browserNotClosedWarningEnabled = HttpContext.Session.GetBoolean("BrowserNotClosedWarningEnabled") ?? false;
            bool formBuilderOpened = HttpContext.Session.GetBoolean(FORM_BUILDER_OPENED) ?? false;
            ResponseNoCache();
            EraseDashboardSession();

            var username = HttpContext.User.Identity.Name;
            //var user = UserManager.FindByName(username);
            string ipAddress = GetIPAddress(Request);
            string computername = "";
            try
            {
                computername = Dns.GetHostEntry(ipAddress).HostName.ToString();
            }
            catch { }
            // Fixed: HttpRequest properties don't exist in ASP.NET Core - using placeholders
            //var logmsg = new { sessionId = HttpContext.Session.Id, action = "Log Off", status = "Success", userName = username, IPAddress = ipAddress, ComputerName = computername, Browser = "Unknown", Port = "Unknown", IsAuthenticated = User.Identity.IsAuthenticated };
            var logmsg = new VMAuthLog { sessionId = HttpContext.Session.Id, action = "Log Off", status = "Success", userName = username, IPAddress = ipAddress, ComputerName = computername, Browser = "Unknown", Port = "Unknown" };

            //GlobalContext.Properties["user"] = username;
            // Fixed: Request.Url doesn't exist in ASP.NET Core - using Request path
            GlobalContext.Properties["page"] = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}";
            //GlobalContext.Properties["PracticeId"] = user.PracticeID;
            //GlobalContext.Properties["ipaddress"] = ipAddress;
            //GlobalContext.Properties["LoggedIn"] = "1";// before logoff

            await _signInManager.SignOutAsync();
            _log.Auth(JsonConvert.SerializeObject(logmsg));

            // erase OneId Session Tokens
            var url = GetFederationReturnUrl(username);

            WriteCookie(browserNotClosedWarningEnabled);
            WriteFormBuilderCookie(_fbNewImplementation && formBuilderOpened);

            // Fixed: Session.Abandon() doesn't exist in ASP.NET Core - using HttpContext.Session.Clear()
            HttpContext.Session.Clear();
            if (!string.IsNullOrEmpty(url))
            {
                await OntarioCmsUserLogout(username);
                return new RedirectResult(url);
            }

            return RedirectToAction("Login", "account");
        }

        //
        // POST: /Account/LogOff
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> LogOff()
        {
            bool browserNotClosedWarningEnabled = HttpContext.Session.GetBoolean("BrowserNotClosedWarningEnabled") ?? false;
            bool formBuilderOpened = HttpContext.Session.GetBoolean(FORM_BUILDER_OPENED) ?? false;
            ResponseNoCache();
            var username = HttpContext.User.Identity.Name;
            //var user = UserManager.FindByName(username);
            var ip = GetIPAddress(Request);
            //GlobalContext.Properties["user"] = username;
            //if (user == null)
            //{
            //    GlobalContext.Properties["practiceID"] = null;
            //}
            //else
            //{
            //    GlobalContext.Properties["practiceID"] = user.PracticeID;
            //}

            // Fixed: Request.Url doesn't exist in ASP.NET Core - using Request path
            //GlobalContext.Properties["page"] = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}";
            //GlobalContext.Properties["ipaddress"] = ip;
            //GlobalContext.Properties["LoggedIn"] = "1";// before logoff

            string computername = "";
            try
            {
                computername = Dns.GetHostEntry(ip).HostName.ToString();
            }
            catch { }
            // Fixed: HttpRequest properties don't exist in ASP.NET Core - using available properties
            var logmsg = new { sessionId = HttpContext.Session.Id, URL = $"{Request.Scheme}://{Request.Host}{Request.Path}{Request.QueryString}", HttpMethod = Request.Method, Path = Request.Path.ToString(), Form = Request.QueryString.ToString(), userName = username, IPAddress = ip, ComputerName = computername, LogOnUser = "Unknown", Browser = "Unknown", Port = Request.Host.Port?.ToString() ?? "Unknown", IsAuthenticated = User.Identity.IsAuthenticated, status = "Success" };

            _log.Auth(JsonConvert.SerializeObject(logmsg));
            await _signInManager.SignOutAsync();

            // erase OneId Session Tokens
            var url = GetFederationReturnUrl(username);
            WriteCookie(browserNotClosedWarningEnabled);
            WriteFormBuilderCookie(_fbNewImplementation && formBuilderOpened);

            // TODO: Session.Abandon() doesn't exist in ASP.NET Core - using Clear()
            HttpContext.Session.Clear();

            if (!string.IsNullOrEmpty(url))
            {
                return new RedirectResult(url);
            }

            //_log.Audit(logmsg);
            return RedirectToAction("Login", "account");
        }
        //
        // GET: /Account/ExternalLoginFailure
        [AllowAnonymous]
        public ActionResult ExternalLoginFailure()
        {
            _log.Auth($"External Login Failure");
            return View();
        }
        protected void ResponseNoCache()
        {
            // TODO: Response.Cache doesn't exist in ASP.NET Core - commenting out
            // Response.Cache.SetCacheability(HttpCacheability.NoCache);  // HTTP 1.1.
            // Response.Cache.AppendCacheExtension("no-store, must-revalidate");
            // TODO: AppendHeader doesn't exist in ASP.NET Core - using Headers
            Response.Headers.Add("Pragma", "no-cache"); // HTTP 1.0.
            Response.Headers.Add("Expires", "0"); // Proxies.
        }
        #region Helpers
        private string GetIPAddress(HttpRequest r)
        {
            // TODO: Ambiguous method call - using placeholder to avoid ambiguity
            var ipAdd = "";
            if (!string.IsNullOrEmpty(ipAdd))
            {
                return ipAdd;
            }
            var ip = (r.Headers.ContainsKey("X-Forwarded-For")
            && r.Headers["X-Forwarded-For"].ToString() != "")
            ? r.Headers["X-Forwarded-For"].ToString()
            : HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
            if (ip.Contains(","))
                ip = ip.Split(',').First().Trim();
            if (ip.Equals("::1"))
            {
                try
                {
                    ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork).ToString();
                }
                catch { }
            }
            return ip;
        }

        private async Task<string> GetTwoFactorUserIdAsync()
        {
            // TODO: Implement two-factor authentication for ASP.NET Core
            Microsoft.AspNetCore.Authentication.AuthenticateResult result = null;

            // TODO: AuthenticateResult.Identity doesn't exist in ASP.NET Core - commenting out
            // if (result != null && result.Identity != null && !String.IsNullOrEmpty(result.Identity.GetUserId()))
            // {
            //     return result.Identity.GetUserId();
            // }
            return null;
        }
        private bool GetTwoFactorEnabled(string userId, string ipAddressLoginFrom)
        {
            // TODO: UserManager.GetTwoFactorEnabled doesn't exist in ASP.NET Core - using placeholder
            bool twoFactorEnabled = false;
            if (!twoFactorEnabled)
                return false;

            int result = _userCredential.CheckTwoFactorAuthentication(userId, ipAddressLoginFrom);
            if (result == 2)
                return false;

            return true;
        }

        private async Task SignInCerebrumUser(Cerebrum.Data.ApplicationUser user, bool isPersistent = false)
        {
            if (User.Identity.IsAuthenticated)
            {
                await _signInManager.SignOutAsync();
            }

            // TODO: SignInManager.UserManager doesn't exist in ASP.NET Core - using _userManager
            var result = await _userManager.ResetAccessFailedCountAsync(user);
            var lockoutEndDate = await _userManager.GetLockoutEndDateAsync(user);
            if (lockoutEndDate != null && lockoutEndDate > DateTime.UtcNow)
            {
                var lockoutEnabled = await _userManager.GetLockoutEnabledAsync(user);
                if (!lockoutEnabled)
                    result = await _userManager.SetLockoutEnabledAsync(user, true);
                result = await _userManager.SetLockoutEndDateAsync(user, DateTime.UtcNow);
                if (!lockoutEnabled)
                    result = await _userManager.SetLockoutEnabledAsync(user, false);
            }
            string ipAddress = GetIPAddress(Request);
            var claimsList = _userCredential.GetUserClaims(_httpClientFactory, user, ipAddress);
            // TODO: DefaultAuthenticationTypes doesn't exist in ASP.NET Core - using placeholder
            var identity = new ClaimsIdentity(claimsList, "ApplicationCookie");

            // TODO: Implement custom claims sign in for ASP.NET Core
            await _signInManager.SignInWithClaimsAsync(user, isPersistent, identity.Claims);
            AddCookie(claimsList);

            var cerebrumUser = new CerebrumUser(new ClaimsPrincipal(identity));
            HttpContext.User = cerebrumUser;
        }

        private async Task<DateTime?> GetTwoFactorAuthenticationCodeExpiredTime(string strUserId)
        {
            // TODO: SignInManager.UserManager doesn't exist in ASP.NET Core - using _userManager
            var user = await _userManager.FindByIdAsync(strUserId);
            var userClaims = user != null ? await _userManager.GetClaimsAsync(user) : new System.Collections.Generic.List<System.Security.Claims.Claim>();

            if (userClaims == null)
                return null;

            var twoFactorAuthenticationCodeExpiredTime = userClaims.Where(a => a.Type == TWOFACTORAUTHENTICATIONCODEEXPIREDTIME).FirstOrDefault();
            if (twoFactorAuthenticationCodeExpiredTime == null)
                return null;

            DateTime expiredTime;
            if (DateTime.TryParseExact(twoFactorAuthenticationCodeExpiredTime.Value, DATEFORMAT, CultureInfo.InvariantCulture, DateTimeStyles.None, out expiredTime))
                return expiredTime;

            return null;
        }
        private void AddCookie(System.Collections.Generic.List<Claim> claimsList)
        {
            string cookieDomain = string.Empty; // Request.Host.Host;
            var cerebrumDomain = System.Configuration.ConfigurationManager.AppSettings["CerebrumDomain"];
            if (!string.IsNullOrEmpty(cerebrumDomain))
            {
                cookieDomain = cerebrumDomain;
            }
            string cookieExpireString = claimsList.Where(x => x.Type == "PrescribeITCookieExpires").Select(x => x.Value).FirstOrDefault();
            DateTime cookieExpire = DateTime.Parse(cookieExpireString);
            if (claimsList.Where(x => x.Type == "PracticeId").Any())
            {
                string practiceId = claimsList.Where(x => x.Type == "PracticeId").Select(x => x.Value).FirstOrDefault();
                AddCookie("PracticeId", practiceId, cookieDomain, cookieExpire);
            }
            if (claimsList.Where(x => x.Type == "UserId").Any())
            {
                string userId = claimsList.Where(x => x.Type == "UserId").Select(x => x.Value).FirstOrDefault();
                AddCookie("UserId", userId, cookieDomain, cookieExpire);
            }
        }
        private void AddCookie(string cookieId, string cookieValue, string cookieDomain, DateTime cookieExpire)
        {
            var cookieOptions = new CookieOptions
            {
                Expires = cookieExpire,//DateTime.Now.AddMinutes(10),
                Domain = cookieDomain
            };
            Response.Cookies.Delete(cookieId);
            Response.Cookies.Append(cookieId, cookieValue, cookieOptions);
        }
        private void DeleteALLCookie()
        {
            if (Request.Cookies["PracticeId"] != null)
            {
                Response.Cookies.Append("PracticeId", "", new CookieOptions { Expires = DateTime.Now.AddMinutes(-1) });
            }
            if (Request.Cookies["UserId"] != null)
            {
                Response.Cookies.Append("UserId", "", new CookieOptions { Expires = DateTime.Now.AddMinutes(-1) });
            }
            if (Request.Cookies["OfficeId"] != null)
            {
                Response.Cookies.Append("OfficeId", "", new CookieOptions { Expires = DateTime.Now.AddMinutes(-1) });
            }
        }
        // Used for XSRF protection when adding external logins
        private const string XsrfKey = "XsrfId";

        private void AddLoginHistory(string userName, SignInStatus status)
        {
            string ipAddress = GetIPAddress(Request);
            var loginHistory = new Cerebrum.ViewModels.User.VMLoginHistory();
            loginHistory.UserName = userName;
            loginHistory.Status = status.ToString();
            loginHistory.UserAgent = Request.Headers["User-Agent"].ToString();
            loginHistory.IpAddress = ipAddress;
            loginHistory.Port = HttpContext.Connection.LocalPort.ToString();
            loginHistory.BrowserVersion = ""; // TODO: Implement browser detection for ASP.NET Core
            loginHistory.Browser = ""; // TODO: Implement browser detection for ASP.NET Core

            try
            {
                _userCredential.InsertLoginHistory(loginHistory);
            }
            catch (Exception ex)
            {
                _log.Error(ex);
            }
        }

        private void EraseDashboardSession()
        {
            try
            {
                var dashboardToken = Microsoft.AspNetCore.Http.SessionExtensions.GetString(HttpContext.Session, "DashboardToken");
                if (!string.IsNullOrEmpty(dashboardToken))
                {
                    var dashboardAPIUrl = System.Configuration.ConfigurationManager.AppSettings["DashboardAPIUrl"];//new setting in Web.config
                    System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
                    stopwatch.Start();
                    try
                    {
                        _log.Info($"EraseDashboardSession Started - dashboardAPIUrl: {dashboardAPIUrl}");
                        var httpClient = _httpClientFactory.CreateClient("dashboard-download");
                        httpClient.BaseAddress = new Uri(dashboardAPIUrl);
                        var user = new ExpiredUserSessionDto()
                        {
                            DatetimeCreated = DateTime.Now,
                            UserId = Microsoft.AspNetCore.Http.SessionExtensions.GetInt32(HttpContext.Session, "UserID") ?? 0,
                            UserToken = dashboardToken
                        };
                        var userJson = new JavaScriptSerializer().Serialize(user);
                        HttpContent httpContent = new StringContent(userJson, Encoding.UTF8, "application/json");

                        var response = httpClient.PostAsync("api/Authentication/InValidateToken", httpContent).Result;
                        if (response.StatusCode == HttpStatusCode.OK)
                        {
                            _log.Info($"EraseDashboardSession Succeed - dashboardAPIUrl: {dashboardAPIUrl}, Time: {stopwatch.ElapsedMilliseconds}");
                        }
                        else
                        {
                            _log.Info($"EraseDashboardSession Failed - dashboardAPIUrl: {dashboardAPIUrl}, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _log.Error($"EraseDashboardSession Error - dashboardAPIUrl: {dashboardAPIUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            _log.Error($"EraseDashboardSession Error (Inner) - dashboardAPIUrl: {dashboardAPIUrl}, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                        }
                        throw ex;
                    }
                }
            }
            catch (Exception ex)
            {
                var error = ex.Message;
            }
        }
        // AuthenticationManager is replaced by SignInManager in ASP.NET Core
        // Use _signInManager instead

        private void AddErrors(IdentityResult result)
        {
            foreach (var error in result.Errors)
            {
                ModelState.AddModelError("", error.Description);
            }
        }

        private async Task OntarioCmsUserLogout(string username)
        {
            var cerebrumUser = new CerebrumUser(User as ClaimsPrincipal);

            if (!string.IsNullOrEmpty(cerebrumUser?.HubTopic))
            {
                var user = _userManager.FindByNameAsync(username).Result;
                int userId = user.UserID;
                string hubTopic = Convert.ToString(cerebrumUser.HubTopic);

                EconsultBLL _eConsultBLL = new EconsultBLL();
                string accessToken = _eConsultBLL.GetToken2(userId, _httpClientFactory);

                if (!string.IsNullOrEmpty(accessToken))
                {
                    try
                    {
                        var patientId = Microsoft.AspNetCore.Http.SessionExtensions.GetInt32(HttpContext.Session, "PatientId") ?? 0;

                        EformsBLL _eformsBLL = new EformsBLL(_log);
                        // close patient if not closed
                        await _eformsBLL.PatientCloseRequest(patientId, hubTopic, accessToken, userId);

                        string requestId = Helper.GenerateRequestId();

                        AwareMD.Eforms.Service.CmsModels.OhCmsResponse cmsResponse = await HttpCaller.UserLogout(hubTopic, accessToken, requestId);
                        string jsonResponse = HttpCaller.SerializeCmsResponse(cmsResponse);

                        UtilityHelper.WriteCmsResponseLog(cmsResponse.success, "requestId: " + requestId + Environment.NewLine + jsonResponse, cerebrumUser.UserId);
                        if (!cmsResponse.success)
                        {
                            _log.Error("OntarioCmsUserLogout, UserLogout, requestId: " + requestId + Environment.NewLine + jsonResponse);
                        }
                    }
                    catch (Exception ex)
                    {
                        UtilityHelper.WriteEconsultError(ex, userId);
                    }
                }
            }
        }
        private ActionResult RedirectToLocal(string returnUrl)
        {
            // commented it because of ticket 2566. always go back to users default landing page
            //if (Url.IsLocalUrl(returnUrl))
            //{
            //    return Redirect(returnUrl);
            //}
            return RedirectToAction("Index", "Home");
        }



        internal class ChallengeResult : UnauthorizedResult
        {
            public ChallengeResult(string provider, string redirectUri)
                : this(provider, redirectUri, null)
            {
            }

            public ChallengeResult(string provider, string redirectUri, string userId)
            {
                LoginProvider = provider;
                RedirectUri = redirectUri;
                UserId = userId;
            }

            public string LoginProvider { get; set; }
            public string RedirectUri { get; set; }
            public string UserId { get; set; }

            public override void ExecuteResult(ActionContext context)
            {
                var properties = new AuthenticationProperties { RedirectUri = RedirectUri };
                if (UserId != null)
                {
                    // TODO: AuthenticationProperties.Dictionary doesn't exist in ASP.NET Core - commenting out
                    // properties.Dictionary[XsrfKey] = UserId;
                }
                // TODO: GetOwinContext doesn't exist in ASP.NET Core - commenting out
                // context.HttpContext.GetOwinContext().Authentication.Challenge(properties, LoginProvider);
            }
        }
        #endregion

        #region OneId Sessions

        private string GetFederationReturnUrl(string username)
        {
            var eConsultActive = System.Configuration.ConfigurationManager.AppSettings["eConsultActive"];
            bool isActive = string.IsNullOrEmpty(eConsultActive) ? false : Convert.ToBoolean(eConsultActive);
            if (!isActive)
            {
                return "";
            }

            if (string.IsNullOrEmpty(username))
            {
                return "";
            }
            // I commented it because if user logged in using OneId, but does not have role "Access Econsult" (not set yet),  
            // when logoff global logoff will not be called and another user can use the same browser as previous user.
            //if (AuthenticationManager.User.Identity.IsAuthenticated)
            //{
            //    var cerebrumUser = new CerebrumUser(User as ClaimsPrincipal);

            //    if (!cerebrumUser.HasRole("Access Econsult"))
            //    {
            //        return "";
            //    }
            //}
            EconsultBLL _eConsultBLL = new EconsultBLL();
            var user = _userManager.FindByNameAsync(username).Result;
            _eConsultBLL.UserId = user.UserID;
            var cerebrumUser = new CerebrumUser(User as ClaimsPrincipal);
            return _eConsultBLL.GetRedirectUrl(_httpClientFactory, cerebrumUser.OntarioHealthClientId);
        }
        private bool IslinkToOneIdEnabled
        {
            get
            {
                var linkToOneIdEnabled = false;

                var oauthSessionResponse4 = HttpContext.Session.GetObject<OAuthSessionResponse>("oauthSessionResponse");
                if (oauthSessionResponse4 != null)
                {
                    var oneIdUserSession = oauthSessionResponse4;

                    if (_isNewUaoImplementationActive)
                    {
                        if (!string.IsNullOrEmpty(oneIdUserSession.Subject))
                        {
                            linkToOneIdEnabled = true;
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(oneIdUserSession.UserName))
                        {
                            linkToOneIdEnabled = true;
                        }
                    }
                }

                return linkToOneIdEnabled;
            }
        }


        #endregion OneId Sessions

        public ActionResult Test()
        {
            return View();
        }
    }
}