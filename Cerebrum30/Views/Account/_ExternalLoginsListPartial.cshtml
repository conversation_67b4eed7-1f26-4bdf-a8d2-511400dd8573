﻿@model Cerebrum30.Models.ExternalLoginListViewModel

<h4>Use another service to log in.</h4>
<hr />
@{

    {
        <div>
            <p>
                There are no external authentication services configured. See <a

                for details on setting up this ASP.NET application to support logging in via external services.
            </p>
        </div>

    else
    {

        {
            @Html.AntiForgeryToken()
            <div id="socialLoginList">
                <p>
                    @foreach (AuthenticationDescription p in loginProviders)
                    {
                        <button type="submit" class="btn btn-default" id="@p.AuthenticationType" name="provider"

                    }
                </p>
            </div>

