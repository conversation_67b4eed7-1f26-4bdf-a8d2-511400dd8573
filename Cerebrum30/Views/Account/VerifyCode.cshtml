﻿@model Cerebrum30.Models.VerifyCodeViewModel
@{

}

<h2>@ViewBag.Title.</h2>

@using (Html.BeginForm("VerifyCode", "Account", new { ReturnUrl = @Model.ReturnUrl }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-horizontal", role = "form" }))
{
    @Html.AntiForgeryToken()
    @Html.Hidden("provider", @Model.Provider)
    @Html.Hidden("rememberMe", @Model.RememberBrowser)
    <h4>Enter verification code</h4>
    <style>
        .a_hidden {
/* visibility: hidden; */
        }

        .a_visible {
/* visibility: visible; */

        .removePadd {
/* padding-left: 0px; */
@* padding-right: 0px; *@
@* color: white; *@
@* height: 15px; *@
        }

            .removePadd a {
@* color: white; *@

        .progress-bar {
@* background-color: dimgrey; *@

    </style>
    <hr />
    @Html.ValidationSummary("", new { @class = "text-danger" })
    <div class="form-group">
        @Html.LabelFor(m => m.Code, new { @class = "col-md-2 control-label" })
        <div class="col-md-2">
            @Html.TextBoxFor(m => m.Code, new { @class = "form-control" })
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <div class="checkbox">
                @Html.CheckBoxFor(m => m.RememberBrowser)

                Maintain Browser Session
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <input type="submit" class="btn btn-default" value="Submit" />
        </div>
    </div>

<div class="form-group">
    <div class="progress col-md-offset-2 col-md-2 removePadd">
        <div class="progress-bar " aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%;">
            <span id="secondLogin" class="a_hidden">
                @Html.ActionLink("Delay? Click to send another code.", "Send_Second_TwoAuthenticationCode", "Account", new { ReturnUrl = @Model.ReturnUrl }, null)
            </span>
        </div>
    </div>
</div>
<script>

        $(".progress-bar")
        .css("width", value + "%")
@* .attr("aria-valuenow", value); *@

@* clearInterval(interval); *@
@* $("#secondLogin").removeClass("a_hidden").addClass("a_visible"); *@

@* }, 300); *@
</script>
@section Scripts {
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>

