﻿@using Cerebrum30.Models
@using System.Collections.Generic
@using System.Web
@using Microsoft.Extensions.Configuration
@model LoginViewModel
@{
    var practices = (List<Cerebrum.ViewModels.Practice.VMPractice>)ViewBag.Practices;
}
@{
    var applicationName = @Html.Raw(HttpUtility.HtmlDecode((ViewBag.ApplicationName?.ToString() ?? "Cerebrum").Replace(@"\u2122", "&trade;")));
    ViewBag.Title = applicationName + " - Log in";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
    var isShowPopup = Convert.ToBoolean(ViewBag.isShowPopup);
    var isFbLogoutRequired = Convert.ToBoolean(ViewBag.isFbLogoutRequired);
    var fbLogoutUrl = Convert.ToString(ViewBag.FbLogoutUrl);

}
@section customcss{}
@section topscripts{
    <script type="text/javascript">
        $(function () {
            if (sessionStorage.TimeOut != null) {
                $("#div-expiredsession").append('<div class="alert alert-warning">Session has expired. Please Login</div>');
                $("#div-expiredsession").show();
                sessionStorage.removeItem('TimeOut');
            }
            else {
                $("#div-expiredsession").hide();
            }
            @if (isShowPopup)
            {
                @: setTimeout(function () { showMessageModal("warning", "Please make sure that all browser windows are closed to prevent unintended PHI left on other screens", false); }, 1000);
            }
            @if(isFbLogoutRequired && !string.IsNullOrEmpty(fbLogoutUrl))
            {
                @: openFbLogout("@fbLogoutUrl");
            }

            $(document).on('click', '#btnLoginAsOneIdUser', function (e) {
                e.preventDefault();
                $('.form-group-login').hide();
                $('.form-group-login-as-one-id').show();
            });
            $(document).on('click', '#btnCancel', function (e) {
                e.preventDefault();
                $('.form-group-login').show();
                $('.form-group-login-as-one-id').hide();
                $("#ddlPractices").val("").change();
            });
            $(document).on('click', '#btnOneIdLogin', function (e) {
                e.preventDefault();
                $('#div-error-message').html("&nbsp;");
                var clientId = $('#ddlPractices').val();
                if (clientId == '') {
                    $('#div-error-message').html("* Please select practice!");
                    return;
                }

                window.location.href = "OneIdLoginAnonymous/index?clientId=" + clientId;
            });
            $('#ddlPractices').on('change', function () {
                $('#div-error-message').html("&nbsp;");
            });

            $('.form-group-login-as-one-id').hide();
        });

        function openFbLogout(fbUrl) {
            try {
                if (fbUrl) {
                    window.open(fbUrl);
                }
            } catch (e) {
                console.log(e);
            }
        }
    </script>
}
@*<span> your ip : @Request.UserHostAddress </span>*@
<div id="pnl_login">
    <h1 class="lbl-login-title"></h1>
    <h2 class="form-group-login" style="padding-top:20px;">Please log in</h2>
    <h4 class="form-group-login">Please enter your 'Email' and 'Password'.</h4>
    <div class="row">
        <div class="col-md-12">
            <section id="loginForm">
                @using (Html.BeginForm("Login", "Account", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { @class = "form-horizontal", role = "form" }))
                {
                    @Html.AntiForgeryToken()
                    <div class="form-group form-group-login">
                        <div class="col-md-12">
                            <div class="float-Left-Label">@Html.TextBoxFor(m => m.Email, new { @placeholder = "Email", @class = "form-control" }) </div>
                            <div class="float-Left-Label margin-top-more">@Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })</div>
                        </div>
                    </div>
                    <div class="form-group margin-top-less form-group-login">
                        @*@Html.LabelFor(m => m.Password, new { @class = "col-md-1" })*@
                        <div class="col-md-12">
                            <div class="float-Left-Label">@Html.PasswordFor(m => m.Password, new { @placeholder = "Password", @class = "form-control" })</div>
                            <div class="float-Left-Label margin-top-more">@Html.ValidationMessageFor(m => m.Password, "", new { @class = "text-danger" })</div>
                        </div>
                    </div>
                    <div class="form-group form-group-login">
                        <div class="col-md-12">
                            <label>
                                Your visit to this site constitutes your acceptance of the Agreement between you and AwareMD Inc.
                                <br />
                                Please take the time to review it carefully at <a href="https://www.awaremd.com/TOU.html" target="_blank">https://www.awaremd.com/TOU.html.</a>
                            </label>
                        </div>
                    </div>
                    if (Convert.ToBoolean(ViewBag.LinkToOneIdEnabled))
                    {
                        <div class="form-group">

                            <div class="col-md-12">
                                <div class="checkbox">
                                    <label>
                                        @Html.CheckBoxFor(m => m.LinkToOneId)
                                        @Html.DisplayNameFor(m => m.LinkToOneId)
                                    </label>
                                </div>
                            </div>
                        </div>
                    }
                    <div class="form-group">
                        <div class="col-md-12 form-group-login">
                            <div class="float_l">
                                <button type="submit" class="btn btn-default btn-primary" style="margin-right:17px" id="ctrlLogin">Log in</button>
                            </div>
                            <div class="float_l" id="cntForgotPwd">
                                @Html.ActionLink("Forgot your password?", "ForgotPassword")
                            </div>
                        </div>
                        @{
                            var eConsultActive = ViewBag.EConsultActive?.ToString();
                            bool isActive = string.IsNullOrEmpty(eConsultActive) ? false : Convert.ToBoolean(eConsultActive);
                            if (isActive && practices?.Count > 0)
                            {
                                <div class="col-md-12">
                                    <div class="float_l">
                                        @if (!Convert.ToBoolean(ViewBag.LinkToOneIdEnabled))
                                        {
                                            <button type="button" class="btn btn-default btn-primary form-group-login" id="btnLoginAsOneIdUser">Log in as OneId User</button>
                                            <div class="form-group-login-as-one-id">
                                                <h2>Please log in</h2>
                                                <h4>Please select practice you want to use</h4>
                                                <br />
                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <select class="form-control" id="ddlPractices">
                                                            <option value="">Select</option>
                                                            @foreach (var item in practices)
                                                            {
                                                                <option value="@item.OntarioHealthClientId|@item.PracticeName">@item.PracticeName</option>
                                                            }
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="text-danger" id="div-error-message">&nbsp;</div>
                                                <br />

                                                <div class="row form-group-login-as-one-id">
                                                    <div class="col-md-12">
                                                        <button class="btn btn-default btn-primary" id="btnOneIdLogin">Log in as OneId User</button>
                                                        <button class="btn btn-default btn-default" id="btnCancel">Cancel</button>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        }
                    </div>
                    <div class="form-group">
                        <div class="col-md-12">
                            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <p>
                        @*@Html.ActionLink("Register as a new user", "Register")*@
                    </p>
                }
            </section>
            @*<div id="div-expiredsession"></div>*@
            <div class="form-group">
                <div class="col-md-12">
                    <div class="validation-summary-errors text-danger">
                        <ul>
                            <li id="expiredsession"></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        @*<div class="col-md-4">
                <section id="socialLoginForm">
                    Html.Partial("_ExternalLoginsListPartial", new ExternalLoginListViewModel { ReturnUrl = ViewBag.ReturnUrl })
                </section>
            </div>*@
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        //if ($('.validation-summary-errors').length == 0) {
        //$('#Email').focus(); // on focus IE doesn't show placeholder > delay
        if($('.validation-summary-errors  li').length != 0 && $('.validation-summary-errors  li').html().length == 0) {
            setTimeout(function () {
                $('#Email').focus();
            }, 2500);
        }

        $('#ctrlLogin').on('click', function (e) {
            e.preventDefault();
            var s = true;

            $('[data-valmsg-for=Email],[data-valmsg-for=Password]').html('');

            if (!isValid.email($('#Email').val())) {
                s = false;
                $('[data-valmsg-for=Email]').html(messages.invalidEmail);
            }
            if (!isValid.str($('#Password').val())) {
                s = false;
                $('[data-valmsg-for=Password]').html(messages.invalidPassword);
            }
            if (s) {
                console.log('Login button clicked before form submit');
                $('form').submit();
            }
        });
    });

    //this was sending 2 login requests
    //$(document).keypress(function (e) {
    //    if (e.which == 13) {
    //        console.log('Enter pressed for login');
    //        $('#ctrlLogin').trigger('click');
    //    }
    //});
</script>
@section Scripts {
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"></script>
}