﻿@model Cerebrum30.Models.ForgotPasswordViewModel
@{

}
<div id="pnl_login">
    <h1 class="lbl-login-title"></h1>
    <h2>Forgot your password?</h2>
    <h4>Enter your 'Email'.</h4>
    <div class="row">
        <div class="col-md-12">
            <section id="forgotPasswordForm">
                @using (Html.BeginForm("ForgotPassword", "Account", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-horizontal", role = "form"  }))
            {
                    @Html.AntiForgeryToken()

                    <div class="form-group">
                        <div class="col-md-12">
                            <div class="float-Left-Label">@Html.TextBoxFor(m => m.Email, new { @placeholder = "Email", @class = "form-control" }) </div>

                            <div class="float-Left-Label margin-top-more">@Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-md-12">
                            <input type="submit" value="Email Link" class="btn btn-default btn-primary" style="margin-right:2px" id="btn_email" /> <input type="button" class="btn btn-default" value="Return to main" id="btn_returnToMain" />
                        </div>
                    </div>

            </section>
        </div>
    </div>
</div>
    <script type="text/javascript">
        $(document).ready(function () {
            $('#btn_email').on('click', function (e) {
@* e.preventDefault(); *@

                if (!isValid.email($('#Email').val())) {

                }

                if (s) {
@* $('form').submit(); *@

@* }); *@
@* }); *@
    </script>
    @section Scripts {
        <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>

