﻿@model IEnumerable<Cerebrum.ViewModels.Common.VMExternalDocAddress>

<style>
    #table-doc-addresses .expand-btn, 
    #table-doc-addresses .collapse-btn {
/* font-size: small; */
}

    .text-primary {
            color: #428bca !important; /*#186571*/

    .btn-default{
/* background-color:#f9f9f9; */
    }
    .btn-primary{
@* background-color:#428bca; *@
    }
    .tr-child-row-white-bg{
@* background-color:#ffffff; *@
    }

    .tr-child-row-white-bg td{
@* background-color:#ffffff; *@
@* font-size: 11px; *@
@* font-style:italic; *@
    }
</style>

 <table id="table-doc-addresses" class="table tbl-parent-child-rows">
            <thead>
                <tr>
                    <th></th>                      
                    <th>
                        Address
                    </th> 
                    <th>
                        Address Name
                    </th>   
                    <th>
                        Phone/Fax Numbers
                    </th>  
                    <th>
                       Active
                    </th>   
                    <th>
                        Print
                    </th>   
                    <th>
                    </th>              
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {

                    <tr id="<EMAIL>">
                        <td style="width:10px;">
                            @if (showExpandLink)

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.AddressLine1)
                            @if (!String.IsNullOrWhiteSpace(item.AddressLine2))
                            {
                                <span> @Html.DisplayFor(modelItem => item.AddressLine2)</span>
                            }
                            @if (!String.IsNullOrWhiteSpace(item.City))
                            {
                                <span>, @Html.DisplayFor(modelItem => item.City)</span>

                            @if (!String.IsNullOrWhiteSpace(item.Province))
                            {
                                <span> @Html.DisplayFor(modelItem => item.Province)</span>

                            @if (!String.IsNullOrWhiteSpace(item.PostalCode))
                            {
                                <span> @Html.DisplayFor(modelItem => item.PostalCode)</span>

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.AddressName)
                        </td>
                        <td class="td-doc-phone-fax-num">
                           @if (totalPhoneNumbers > 0)
                           {
                            @totalPhoneNumbers
                           }
                           else
                           {
                            <span class="text-danger"> Please add a Fax/Phone number</span>

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.IsActive)
                        </td>
                        <td>
                            <input type="checkbox" name="addressCheckboxPrinting" value="@item.Id" />
                        </td>
                        <td style="width:15%;">                           
                           
                        </td>
                        
                    </tr>

                    foreach(var phoneItem in item.PhoneNumbers)
                    {
                        <tr class="tr-child-row-white-bg" style="display:none;" id="<EMAIL><EMAIL>">
                            <td></td>
                            <td colspan="3">
                                @if (!String.IsNullOrWhiteSpace(phoneItem.PhoneNumber))
                                {
                                    <span><strong>Phone:</strong> @Html.DisplayFor(modelItem => phoneItem.PhoneNumber)</span>
                                }
                                @if (!String.IsNullOrWhiteSpace(phoneItem.PhoneNumber) && !String.IsNullOrWhiteSpace(phoneItem.PhoneExtension))
                                {
                                    <span> ext. @Html.DisplayFor(modelItem => phoneItem.PhoneExtension)</span>

                                @if (!String.IsNullOrWhiteSpace(phoneItem.FaxNumber))
                                {
                                    <span> <strong>Fax:</strong> @Html.DisplayFor(modelItem => phoneItem.FaxNumber)</span>

                            </td>                            
                            <td>@Html.DisplayFor(modelItem => phoneItem.IsActive)</td>
                            <td></td>
                            <td style="width:15%;">
                                
                            </td>
                        </tr>

            </tbody>
        </table>

