﻿@model IEnumerable<Cerebrum.ViewModels.Common.VMExternalDoctor>

<div class="panel panel-info">
    <div class="panel-heading">Results</div>
    <div class="panel-body">
        <table style="width:100%;" class="table table-condensed table-bordered">
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.CPSO)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.OHIP)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.FirstName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.LastName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Phone)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Fax)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Email)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Address)
                </th>
               
                <th>
                    @Html.DisplayNameFor(model => model.Description)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.IsActive)
                </th>
                <th></th>
            </tr>

            @foreach (var item in Model)
        {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.CPSO)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.OHIP)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.FirstName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.LastName)
                    </td>
                    <td>

                        @foreach(var p in item.PhoneNumbers)
                        {
                            if (!String.IsNullOrWhiteSpace(p.PhoneNumber))
                            {                                

                                <div>@p.PhoneNumber@ext</div>
                            }

                    </td>

                    <td>
                        @ Html.DisplayFor(modelItem => item.Fax)
                        @foreach (var p in item.PhoneNumbers.Where(w=> w.FaxNumber != null || w.FaxNumber != ""))
                        {                            
                            <div>@p.FaxNumber</div>
                        }
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Email)
                    </td>
                    <td>
                        *@Html.DisplayFor(modelItem => item.Address)
                        @foreach (var a in item.Addresses)
                        {*@ var address = "";
                            if(!String.IsNullOrWhiteSpace(a.AddressLine1))
                            { *@ address += a.AddressLine1; }

                            if (!String.IsNullOrWhiteSpace(a.AddressLine2))
                            {

                            }

                            if (!String.IsNullOrWhiteSpace(a.City))
                            {

                            if (!String.IsNullOrWhiteSpace(a.Province))
                            {

                            if (!String.IsNullOrWhiteSpace(a.PostalCode))
                            {

                            <div style="padding-bottom:3px;">@address</div>

                    </td>
                    
                    <td>
                        @Html.DisplayFor(modelItem => item.Description)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.IsActive)
                    </td>
                    <td>

                        <a data-modal-url="@Url.Action("edit", "externaldoctors", new { area = "", extDoctorId= item.Id})" class="btn btn-primary btn-xs btn-edit-ext-doctor"><span class=""> </span> Edit</a>

                    </td>
                </tr>

        </table>
    </div>
</div>