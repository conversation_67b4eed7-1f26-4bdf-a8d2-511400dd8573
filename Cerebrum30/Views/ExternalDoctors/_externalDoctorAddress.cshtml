﻿@model Cerebrum.ViewModels.Common.VMExternalDocAddress

@using (Html.BeginForm("SaveExternalDoctorAddress", "ExternalDoctors", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-ext-doctor-addresss-save" }))
{

    @Html.ModalHeader(modalHeader)

    <div class="modal-body content-height500">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.Id)
        @Html.HiddenFor(model => model.ExternalDoctorId)
        
        <div class="form-horizontal">       
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AddressName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.AddressName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.AddressName, "", new { @class = "text-danger" })
                </div>
            </div>
                 
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AddressType, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.AddressType, new SelectList(ViewBag.LKAddressTypes, "Value", "Text", @Model.AddressType), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AddressType, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AddressLine1, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.AddressLine1, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.AddressLine1, "", new { @class = "text-danger" })
                </div>                
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AddressLine2, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.AddressLine2, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.AddressLine1, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.City, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.City, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.City, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.PostalCode, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.PostalCode, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.PostalCode, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Province, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor(model => model.Province, new SelectList(ViewBag.LKProvinces, "Text", "Text", @Model.Province), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Province, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor(model => model.Country, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.DropDownListFor(model => model.Country, new SelectList(ViewBag.LKCountries, "Text", "Text", @Model.Country), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Country, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                <div class="col-md-2">
                </div>
                
                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => model.IsActive) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.IsActive, "", new { @class = "text-danger" })
                    </div>
                </div>
       
            </div>

        </div>
        
    </div><!--Modal body ends-->

    <div class="modal-footer ">        

        <button type="submit" class="btn btn-default btn-sm btn-primary modal-submit-btn">Save</button>
        <button type="button" class="btn btn-default btn-sm " data-dismiss="modal">Close</button>
    </div>

