﻿@model Cerebrum.ViewModels.Common.VMExternalDocPhone

@using (Html.BeginForm("SaveExternalDoctorPhone", "ExternalDoctors", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-ext-doctor-phone-save" }))
{

    @Html.ModalHeader(modalHeader)

    <div class="modal-body content-height500">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.Id)
        @Html.HiddenFor(model => model.ExternalDoctorAddressId)
        @Html.HiddenFor(model => model.ExternalDoctorId)
        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PhoneNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor(model => model.PhoneNumber, new { htmlAttributes = new { @class = "form-control"} })
                        @Html.ValidationMessageFor(model => model.PhoneNumber, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:3px;" class="pull-left">
                        @Html.LabelFor(model => model.PhoneExtension, htmlAttributes: new { @class = "control-label" })
                    </div>
                    <div class="pull-left">
                        @Html.EditorFor(model => model.PhoneExtension, new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                        @Html.ValidationMessageFor(model => model.PhoneExtension, "", new { @class = "text-danger" })
                    </div>  
                    
                    <div class="pull-left">
                        @Html.DropDownListFor(model => model.TypeOfPhoneNumber, new SelectList(ViewBag.LKPhoneTypes, "Value", "Text", @Model.TypeOfPhoneNumber), new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.TypeOfPhoneNumber, "", new { @class = "text-danger" })
                </div>
            </div>

        </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.FaxNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.FaxNumber, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FaxNumber, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                <div class="col-md-2">
                </div>

                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => model.IsActive) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.IsActive, "", new { @class = "text-danger" })
                    </div>
                </div>

            </div>

    </div>
       
        @if (!isEdit && @Model.UnlinkedPhones != null && @Model.UnlinkedPhones.Any())
        {
        <div>
            <div>Fax / Phone numbers that are not linked to an address</div>
            <table class="table table-condensed table-bordered">
                <thead>
                    <tr>
                        <th>
                            Phone
                        </th>
                        <th>
                            Fax
                        </th>                        
                        <th style="width:20%">Selected</th>
                    </tr>
                </thead>
                @for (int i = 0; i < @Model.UnlinkedPhones.Count; i++)
                {

                    <tr>
                        <td>
                            <input type="hidden" name="UnlinkedPhones.Index" value="@i" />
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].Id)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].PhoneExtension)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].PhoneNumber)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].FaxNumber)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].ExternalDoctorAddressId)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].ExternalDoctorId)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].TypeOfPhoneNumber)
                            @Html.HiddenFor(x => x.UnlinkedPhones[i].IsActive)
                            @Html.DisplayTextFor(model => model.UnlinkedPhones[i].PhoneNumber)

                            @if(!String.IsNullOrWhiteSpace(@Model.UnlinkedPhones[i].PhoneExtension))
                            {
                                <span> Ext. @Model.UnlinkedPhones[i].PhoneExtension</span>
                            }
                        </td>
                        <td>
                            @Html.DisplayTextFor(model => model.UnlinkedPhones[i].FaxNumber)
                        </td>
                        <td>
                            @Html.CheckBoxFor(model => model.UnlinkedPhones[i].IsSelected, new { @class = "" })
                        </td>                        
                    </tr>

            </table>

        </div>

    </div><!--Modal body ends-->
    <div class="modal-footer ">

        <button type="submit" class="btn btn-default btn-sm btn-primary modal-submit-btn">Save</button>
        <button type="button" class="btn btn-default btn-sm " data-dismiss="modal">Close</button>
    </div>

