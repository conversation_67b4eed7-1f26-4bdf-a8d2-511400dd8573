﻿@model Cerebrum.ViewModels.Common.VMExternalDoctorMain

@{

}

@section customcss{
    <link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />    

<div class="row">
    <div class="col-md-6">
        <h3>External Doctors</h3>

        <div class="container">
            <div class="row">
               
                @* Duplicate using removed: *@ Duplicate using removed: @using (Html.BeginForm("index", "ExternalDoctors", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Get, true, null)) *@
                {
                    <div class="col-md-2">Doctor Name:</div>
                    <div class="col-md-2">@Html.TextBox("Doctor", @Model.ExternalDoctorSearch.Doctor , new { @class = "form-control " }) @* @Html.Hidden("15") </div>*@
                    <div class="col-md-2"><input type="submit" value="Search" class="btn btn-default " /></div>
                }

          </div>
            <div class="row">

                @using (Html.BeginForm("index", "ExternalDoctors", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Get, true, null))
                {

                    <div class="col-md-2"> OHIP:</div>
                            <div class="col-md-2">@Html.TextBox("OHIP", @Model.ExternalDoctorSearch.OHIP, new { @class = "form-control " }) @ Html.Hidden("15") </div>
                            <div class="col-md-2"><input type="submit" value="Search" class="btn btn-default " /></div>

            </div>
            <div class="row">

                {
                    <div class="col-md-2">CPSO:</div>
                        <div class="col-md-2">@Html.TextBox("CPSO", @Model.ExternalDoctorSearch.CPSO, new { @class = "form-control " }) @* @Html.Hidden("15") *@  </div>
                        <div class="col-md-2"><input type="submit" value="Search" class="btn btn-default " /></div>
                }

            </div>
        </div>

    </div>
    <div class="col-md-6 text-right">
        <a class="btn btn-default btn-sm btn-add-ext-doctor btn-h3-top-align"><span class="default-text-color"> </span> Add Doctor</a>
    </div>
</div>

<hr/>

<div class="text-right" id="extdocs-paging-container">
    @Html.PartialAsync("_externalDoctorPaging", @Model.ExternalDoctorSearch)
</div>

<div id="external-doctors-container">
    @Html.PartialAsync("_externalDoctorList",@Model.ExternalDoctors)
</div>