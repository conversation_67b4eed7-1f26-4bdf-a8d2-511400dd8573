﻿@model <EMAIL>

<style>
    .scrollableDiv {
/* height: 500px; */
/* overflow: auto; */
/* padding: 10px; */
/* margin: 10px; */
    }

    .txtBox {
/* width: 80px; */

    .txtBoxSmall {
@* width: 40px; *@

    .dropdownSmall {
@* width: 40px; *@

    .txtArea {
@* width: 300px !important; *@
        /* height: 200px !important; */

    .smallLabel{

@* font-size:11px; *@
@* font-weight:bold; *@
@* margin-left:5px; *@
    }

    .coloredBorder {
        /*border: solid 1px green;*/

    .coloredBorderUpper {
        /*border-top: solid 1px green;*/

    .coloredBorderBottom {
        /*border-bottom: solid 1px green;*/

    .coloredBorderLeft {
        /*border-left: solid 1px green;*/

    .coloredBorderRight {
        /*border-right: solid 1px green;*/

    .Normal {
    }

    .MildlyAbnormal {
@* background-color: #FFC0C0; *@
    }

    .ModeratelyAbnormal {
@* background-color: #FF8080; *@

    .SeverelyAbnormal {
@* background-color: #FF8000; *@

    .NoNormalRange {
@* background-color: #EEEEFF; *@

    .OutOfRange {
@* background-color: #FFF8C6; *@

    .NoRangeAvailable {
@* background-color:  lightgray; *@

    .error {
@* color: red; *@
@* font-weight: bold; *@
@* font-size: 10px; *@
    }

    /* boot strap styles  */

    .dropdown-submenu {
@* position: relative; *@
    }

        .dropdown-submenu > .dropdown-menu {
@* top: 0; *@
@* left: 100%; *@
@* margin-top: -6px; *@
@* margin-left: -1px; *@
@* -webkit-border-radius: 0 6px 6px 6px; *@
@* -moz-border-radius: 0 6px 6px 6px; *@
@* border-radius: 0 6px 6px 6px; *@

        .dropdown-submenu:hover > .dropdown-menu {
@* display: block; *@

        .dropdown-submenu > a:after {
@* display: block; *@
@* content: " "; *@
@* float: right; *@
@* width: 0; *@
@* height: 0; *@
@* border-color: transparent; *@
@* border-style: solid; *@
@* border-width: 5px 0 5px 5px; *@
@* border-left-color: #cccccc; *@
@* margin-top: 5px; *@
@* margin-right: -10px; *@

        .dropdown-submenu:hover > a:after {
@* border-left-color: #ffffff; *@

        .dropdown-submenu.pull-left {
@* float: none; *@

            .dropdown-submenu.pull-left > .dropdown-menu {
@* left: -100%; *@
@* margin-left: 10px; *@
@* -webkit-border-radius: 6px 0 6px 6px; *@
@* -moz-border-radius: 6px 0 6px 6px; *@
@* border-radius: 6px 0 6px 6px; *@

    .spanRed {
@* color: red !important; *@

    .spanGreen {
@* color: green !important; *@

    .spanBlue {
@* color: blue !important; *@

    .txtArea {
@* font-size: 11px; *@
@* font-weight: normal; *@
@* color: saddlebrown; *@
@* background-color: beige; *@
@* font-family: "verdana","arial", "helvetica"; *@

</style>

<div class="jumbotron">
    <h1>Worksheet</h1>
    <p class="lead">Measurement and Report Phrases</p>
</div>

<script src="~/Scripts/jquery.fancybox.pack.js"></script>
<link href="~/Content/jquery.fancybox.css" rel="stylesheet" />

**  <!-- TODO: Replace Scripts.Render with direct script references for ASP.NET Core -->
@section scripts
{

    <script>

@* $('#' + cntrlID).val(''); *@

            //$('html, body').animate({
            //    scrollTop: $(cntrlID).offset().top
            //}, 1000);

            $('#divMeasurement').animate({
                scrollTop: $(cntrlID).offset().top
@* }, 1000); *@

@* $('#' + txtCntrlName).val(name); *@

@* console.log(phraseID); *@

            //var prevText = tinyMCE.get(txtCntrlID).getContent({ format: 'text' });

                //while (prevText.toLowerCase().indexOf(strVal.toLowerCase()) != -1)
                //    prevText = prevText.replace(strVal, "");

                //prevText = prevText.replace(strVal, "");

                //tinyMCE.get(txtCntrlID).setContent('');
                //tinyMCE.get(txtCntrlID).execCommand('mceInsertContent', false, prevText  );

@* $('#' + txtCntrlID).val(prevText); *@

@* $(cntrl).children("span").removeClass("spanBlue"); *@

            else {

@* $('#' + txtCntrlID).val(prevText + ' ' + strVal); *@

                //tinyMCE.get(txtCntrlID).setContent('');
                //tinyMCE.get(txtCntrlID).execCommand('mceInsertContent', false, prevText + ' ' + strVal);

@* $(cntrl).children("span").addClass("spanBlue"); *@

            $.ajax({

                method: 'GET',
                url: url,
                data: { apptID: $('#AppointmentID').val(), testID: $('#TestID').val(), reportPhraseID: phraseID, topLevelReportPhraseID: topLevelPhraseID },
                async: true,
                success: function (data) {

                    //alert('hey');
                    //$(txtManField).val(data.Result);
                },

                error: function (xhr, thrownError) {

@* alert("Error while tryng to call  'Measurement/AddReportPhraseAction'  " + xhr.status + " " + thrownError); *@

@* }); *@

            $.ajax({

                method: 'GET',
                async: true,
                url: 'Measurement/GetRangeForMeasurement',
                data: { val: $(txtCntrl).val(), mesCode: measureCode, catCode: categoryCode },
                //data: { val: 12.0, mesCode: 'a', catCode: 'a' },
                success: function (data) {

                    else

                        else

                            else

                                else

                                    else

@* $(txtCntrl).removeClass('MildlyAbnormal ModeratelyAbnormal SeverelyAbnormal NoNormalRange OutOfRange').addClass(rangeClass); *@
                    //$(txtManField).val(data.Result);
                },

                error: function (xhr, thrownError) {

@* alert("Error while tryng to retreive measurement range " + xhr.status + " " + thrownError); *@

@* }); *@

            $('#' + dd + ' option').each(function () {
@* ddvalues.push($(this).attr('value')); *@
@* }); *@

@* console.log(JSON.stringify(ddvalues)); *@

            $.ajax({
                method: "GET",
                url: "Measurement/GetValueForOperator",
                data: { values: JSON.stringify(ddvalues), Operator: $(ddOperator).val() },
                async: true,
                success: function (data) {

@* $('#' + txtManField).val(data.Result); *@
                    //$(txtManField).val(data.Result);
                },

                error: function (xhr, thrownError) {

@* alert("Error while tryng to apply operator to measurement " + xhr.status + " " + thrownError); *@

@* }); *@

        $(document).ready(function () {

            $("#btnNormalAll").click(function () {
                 
@* $(".normalLinks").click(); *@

@* }); *@

            $('#dd').on('change', function () {

                //console.log('hi');
@* $(this).closest("form").submit(); *@

@* }); *@

            $('#chkShowAllMeasurements').on('change', function () {

                //console.log('hi');
@* $(this).closest("form").submit(); *@

@* }); *@

            $(".fancybox1").fancybox({
                'width': '700px',
                'height': '800px',
                'autoScale': false,
                'transitionIn': 'none',
                'transitionOut': 'none',
                'type': 'iframe',
                'onClosed': function () {
@* alert("onclosed"); *@
@* window.location.reload(true); *@
                },
                'onCleanup': function () {
@* alert("oncleanup"); *@
@* return window.location.reload(); *@

@* }); *@

            $(".fancybox2").fancybox({
                openEffect: 'none',
                closeEffect: 'none'
@* }); *@

@* }); *@

        @*$('#txt').tinymce({*@

                script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
                theme: "advanced",

                height: "500",
                width: "790",
                verify_html : false,
                plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",

                 Theme options
                theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
                theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
                theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
                theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
                theme_advanced_toolbar_location : "top",
                theme_advanced_toolbar_align : "left",
                theme_advanced_statusbar_location : "bottom",
                theme_advanced_resizing : false,

                // Example content CSS (should be your site CSS)
                content_css : "@Url.Content("~/content/bootstrap-theme.min.css")",
            convert_urls : false,

            // Drop lists for link/image/media/template dialogs
            template_external_list_url : "lists/template_list.js",
            external_link_list_url : "lists/link_list.js",
            external_image_list_url : "lists/image_list.js",
            media_external_list_url : "lists/media_list.js"

@* *@
            ); //tinymce.init({
        //    selector: 'textarea',
        //    theme: "modern",
        //    width: 360,
        //            height: 200,
        //            resize: false,
        //    toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | l      ink image | print preview media fullpage | forecolor backcolor emoticons",
        //    style_formats: [
        //            { title: 'Bold text', inline: 'b' },
        //            { title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
        //            { title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
        //            { title: 'Example 1', inline: 'span', classes: 'example1' },
        //            { title: 'Example 2', inline: 'span', classes: 'example2' },
        //            { title: 'Table styles' },
        //            { title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
        //        ]
        //});

        //$(document).ready(function () {
        //    // ### Initialisation from TinyMCE Richtexteditor ### //
        //    tinymce.init({
        //        mode: "specific_textareas",
        //        editor_selector: "#txt",
        //        theme: "modern",
        //        width: 360,
        //        height: 200,
        //        resize: false,
        //        toolbar: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | l      ink image | print preview media fullpage | forecolor backcolor emoticons",
        //        style_formats: [
        //            { title: 'Bold text', inline: 'b' },
        //            { title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
        //            { title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
        //            { title: 'Example 1', inline: 'span', classes: 'example1' },
        //            { title: 'Example 2', inline: 'span', classes: 'example2' },
        //            { title: 'Table styles' },
        //            { title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
        //        ]
        //    });
        //});

    </script>

@using (Html.BeginForm("Index", "Measurement", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { model = @Model }))
{

    //writing operator list*@ for (int i = 0; i < @Model.MeasurementOperators.Count; i++)
    {

        @Html.HiddenFor(m => m.MeasurementOperators[i].Text)
        @Html.HiddenFor(m => m.MeasurementOperators[i].Value)
    }

    //writing log list

    {

        @Html.HiddenFor(m => m.AppointmentTestLogs[i].Text)
        @Html.HiddenFor(m => m.AppointmentTestLogs[i].Value)
    }

    //showmode list

    {

        @Html.HiddenFor(m => m.ShowModeList[i].Text)
        @Html.HiddenFor(m => m.ShowModeList[i].Value)
    }

    //test list

    {

        @Html.HiddenFor(m => m.TestList[i].Text)
        @Html.HiddenFor(m => m.TestList[i].Value)
    }
    //appointment list

    {

        @Html.HiddenFor(m => m.AppointmentList[i].Text)
        @Html.HiddenFor(m => m.AppointmentList[i].Value)
    }

    <div class="navbar ">
        <div class="navbar-inner">
            <div class="container">
                <h2>
                    Report Phrases
                </h2>

                <div class="col-md-12">
                    <div class="col-md-4">
                        <span>Test ID</span>
                        @Html.DropDownListFor(m => m.TestID, new SelectList(@Model.TestList, "Value", "Text", 1), new { @class = "dropdown", style = "width:100px" })

                    </div>
                    <div class="col-md-4">
                        <span>Appointment ID</span>
                        @Html.DropDownListFor(m => m.AppointmentID, new SelectList(@Model.AppointmentList, "Value", "Text", 1), new { @class = "dropdown", style = "width:100px" })
                    </div>
                    <div class="col-md-4">
                        <span>Log :</span>
                        @Html.DropDownListFor(m => m.AppointmentTestLogID,
                                                                    new SelectList(@Model.AppointmentTestLogs, "Value", "Text", @Model.AppointmentTestLogID),

                    </div>
                </div>
                <br /><br />
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="submit" formaction="Measurement/Go" value="Go" id="btnGo" class="btn btn-primary" />
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <input type="submit" formaction="Measurement/InsertAutoPhrases" value="Insert Auto Phrases" id="btnAutoPhrase" class="btn btn-primary" />
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <input type="button"  value="Set Normal For All" id="btnNormalAll" class="btn btn-primary" />
                    </div>
                </div>

                <div class="nav-collapse">

                    @{

                        <div class="container">
                            <div class="row">

                                @for (; index < 3; index++)
                                {

                                    <div class="col-md-4">

                                        <a href='@Url.Action("ReportPhraseHistory", "Measurement", new { reportPhraseID = @item.Id, appointmentID = @Model.AppointmentID, testID = @Model.TestID })'

                                        @{

                                            {

                                                <a href="#" class="normalLinks"  onclick="@jsNormalCall"><img height="20" width="20" src="~/Content/Images/th.jpg" /></a>

                                        <ul class="nav">

                                            <li class="dropdown">

                                                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                                    @item.name, @item.Id
                                                    @if (@<EMAIL>() > 0)
                                                    {
                                                        <b class="caret"></b>
                                                    }
                                                </a>

                                                @Html.HiddenFor(m => <EMAIL>[index].TopLevelReportPhraseID)

                                                @Html.HiddenFor(m => <EMAIL>[index].Id)
                                                @Html.HiddenFor(m => <EMAIL>[index].name)
                                                @Html.HiddenFor(m => <EMAIL>[index].ordernumber)
                                                @Html.HiddenFor(m => <EMAIL>[index].parent)
                                                @Html.HiddenFor(m => <EMAIL>[index].root)
                                                @Html.HiddenFor(m => <EMAIL>[index].status)
                                                @Html.HiddenFor(m => <EMAIL>[index].test)
                                                @Html.HiddenFor(m => <EMAIL>[index].type)
                                                @Html.HiddenFor(m => <EMAIL>[index].value)

                                                <ul class="dropdown-menu">
                                                    @{int secIndex = 0;
                                                    }
                                                    @foreach (var subItem in @Model.ReportPhraseView@Model.lstLevel2)
                                                    {

                                                        {

                                                            //var lstThird = @Model.ReportPhrases.Where(r => r.parent == subItem.Id && r.test == 1 && r.status == 0).OrderBy(r => r.ordernumber).ToList();

                                                            //var lstThird = @<EMAIL>(i => i.parent == subItem.Id);

                                                            //var lstThirdHasValues = false;

                                                            if (!string.IsNullOrEmpty(@<EMAIL>[index].Value) &&
                                                                    @<EMAIL>[index].Value.Contains(subItem.name)
                                                                )
                                                            {

                                                            }

                                                            <li class=@className>
                                                                <a tabindex="-1" href="#" onclick="@jsCall">
                                                                    <span class='@colorClass @selectedClass'>@subItem.name   </span>
                                                                </a>

                                                                @if (@<EMAIL>() > 0)
                                                                {

                                                                    <ul class="dropdown-menu">
                                                                        @foreach (var thirdLevelItem in lstThird)
                                                                        {

                                                                            //if (thirdLevelItem.parent == subItem.Id)
                                                                            //{
                                                                            <li>
                                                                                <a tabindex="-1" href="#" onclick="@jsCallInner">
                                                                                    <span class="spanGreen">@thirdLevelItem.name   </span>

                                                                                </a>
                                                                            </li>
                                                                            //}

@* thirdIndex++; *@

                                                                    </ul>

                                                            </li>

@* secIndex++; *@

                                                </ul>

                                            </li>

                                        </ul>

                                        @{

                                            {

                                            }

                                            @Html.TextAreaFor(m => <EMAIL>[index].Value)
                                                                                                        new
                                                                                                        {

                                                                                                            @class = " txtArea",
                                                                                                            @id = "txtReportPhrase_" + @<EMAIL>[index].Id,
                                                                                                            @name = "txtReportPhrase_" + @<EMAIL>[index].Id,
                                                                                                            @onfocus = funcScroll
                                                                                                        })

                                    </div>

                            </div>
                            <br />
                            <div class="row">

                                @for (; index < 6; index++)
                                {

                                    <div class="col-md-4">

                                        <a href='@Url.Action("ReportPhraseHistory", "Measurement", new { reportPhraseID = @item.Id, appointmentID = @Model.AppointmentID, testID = @Model.TestID })'

                                        @{

                                            {

                                                <a href="#" class="normalLinks" onclick="@jsNormalCall"><img height="20" width="20" src="~/Content/Images/th.jpg" /></a>

                                        <ul class="nav">

                                            <li class="dropdown">

                                                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                                    @item.name, @item.Id
                                                    @if (@<EMAIL>() > 0)
                                                    {
                                                        <b class="caret"></b>
                                                    }
                                                </a>

                                                @Html.HiddenFor(m => <EMAIL>[index].TopLevelReportPhraseID)

                                                @Html.HiddenFor(m => <EMAIL>[index].Id)
                                                @Html.HiddenFor(m => <EMAIL>[index].name)
                                                @Html.HiddenFor(m => <EMAIL>[index].ordernumber)
                                                @Html.HiddenFor(m => <EMAIL>[index].parent)
                                                @Html.HiddenFor(m => <EMAIL>[index].root)
                                                @Html.HiddenFor(m => <EMAIL>[index].status)
                                                @Html.HiddenFor(m => <EMAIL>[index].test)
                                                @Html.HiddenFor(m => <EMAIL>[index].type)
                                                @Html.HiddenFor(m => <EMAIL>[index].value)

                                                <ul class="dropdown-menu">
                                                    @{int secIndex = 0;
                                                    }
                                                    @foreach (var subItem in @Model.ReportPhraseView@Model.lstLevel2)
                                                    {

                                                        {

                                                            //var lstThird = @Model.ReportPhrases.Where(r => r.parent == subItem.Id && r.test == 1 && r.status == 0).OrderBy(r => r.ordernumber).ToList();

                                                            //var lstThird = @<EMAIL>(i => i.parent == subItem.Id);

                                                            //var lstThirdHasValues = false;

                                                            if (!string.IsNullOrEmpty(@<EMAIL>[index].Value) &&
                                                                    @<EMAIL>[index].Value.Contains(subItem.name)
                                                                )
                                                            {

                                                            }

                                                            <li class=@className>
                                                                <a tabindex="-1" href="#" onclick="@jsCall">

                                                                    <span class='@colorClass @selectedClass'>@subItem.name   </span>
                                                                </a>

                                                                @if (@<EMAIL>() > 0)
                                                                {

                                                                    <ul class="dropdown-menu">
                                                                        @foreach (var thirdLevelItem in lstThird)
                                                                        {

                                                                            //if (thirdLevelItem.parent == subItem.Id)
                                                                            //{
                                                                            <li>
                                                                                <a tabindex="-1" href="#" onclick="@jsCallInner">
                                                                                    <span class="spanGreen">@thirdLevelItem.name   </span>

                                                                                </a>
                                                                            </li>
                                                                            //}

@* thirdIndex++; *@

                                                                    </ul>

                                                            </li>

@* secIndex++; *@

                                                </ul>

                                            </li>

                                        </ul>

                                        @{

                                            {

                                            }

                                            @Html.TextAreaFor(m => <EMAIL>[index].Value)
                                                                     new
                                                                     {

                                                                         @class = " txtArea",
                                                                         @id = "txtReportPhrase_" + @<EMAIL>[index].Id,
                                                                         @name = "txtReportPhrase_" + @<EMAIL>[index].Id,
                                                                         @onfocus = funcScroll
                                                                     })

                                    </div>

                            </div>
                            <br />
                            <div class="row">
                                @for (; index < 9; index++)
                                {

                                    <div class="col-md-4">

                                        <a href='@Url.Action("ReportPhraseHistory", "Measurement", new { reportPhraseID = @item.Id, appointmentID = @Model.AppointmentID, testID = @Model.TestID })'

                                        @{

                                            {

                                                <a href="#" class="normalLinks" onclick="@jsNormalCall"><img height="20" width="20" src="~/Content/Images/th.jpg" /></a>

                                        <ul class="nav">

                                            <li class="dropdown">

                                                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                                    @item.name, @item.Id
                                                    @if (@<EMAIL>() > 0)
                                                    {
                                                        <b class="caret"></b>
                                                    }
                                                </a>

                                                @Html.HiddenFor(m => <EMAIL>[index].TopLevelReportPhraseID)

                                                @Html.HiddenFor(m => <EMAIL>[index].Id)
                                                @Html.HiddenFor(m => <EMAIL>[index].name)
                                                @Html.HiddenFor(m => <EMAIL>[index].ordernumber)
                                                @Html.HiddenFor(m => <EMAIL>[index].parent)
                                                @Html.HiddenFor(m => <EMAIL>[index].root)
                                                @Html.HiddenFor(m => <EMAIL>[index].status)
                                                @Html.HiddenFor(m => <EMAIL>[index].test)
                                                @Html.HiddenFor(m => <EMAIL>[index].type)
                                                @Html.HiddenFor(m => <EMAIL>[index].value)

                                                <ul class="dropdown-menu">
                                                    @{int secIndex = 0;
                                                    }
                                                    @foreach (var subItem in @Model.ReportPhraseView@Model.lstLevel2)
                                                    {

                                                        {

                                                            //var lstThird = @Model.ReportPhrases.Where(r => r.parent == subItem.Id && r.test == 1 && r.status == 0).OrderBy(r => r.ordernumber).ToList();

                                                            //var lstThird = @<EMAIL>(i => i.parent == subItem.Id);

                                                            //var lstThirdHasValues = false;

                                                            if (!string.IsNullOrEmpty(@<EMAIL>[index].Value) &&
                                                                    @<EMAIL>[index].Value.Contains(subItem.name)
                                                                )
                                                            {

                                                            }

                                                            <li class=@className>
                                                                <a tabindex="-1" href="#" onclick="@jsCall">

                                                                    <span class='@colorClass @selectedClass'>@subItem.name   </span>
                                                                </a>

                                                                @if (@<EMAIL>() > 0)
                                                                {

                                                                    <ul class="dropdown-menu">
                                                                        @foreach (var thirdLevelItem in lstThird)
                                                                        {

                                                                            //if (thirdLevelItem.parent == subItem.Id)
                                                                            //{
                                                                            <li>
                                                                                <a tabindex="-1" href="#" onclick="@jsCallInner">
                                                                                    <span class="spanGreen">@thirdLevelItem.name   </span>

                                                                                </a>
                                                                            </li>
                                                                            //}

@* thirdIndex++; *@

                                                                    </ul>

                                                            </li>

@* secIndex++; *@

                                                </ul>

                                            </li>

                                        </ul>

                                        @{

                                            {

                                            }

                                            @Html.TextAreaFor(m => <EMAIL>[index].Value)
                                                                 new
                                                                 {

                                                                     @class = " txtArea",
                                                                     @id = "txtReportPhrase_" + @<EMAIL>[index].Id,
                                                                     @name = "txtReportPhrase_" + @<EMAIL>[index].Id,
                                                                     @onfocus = funcScroll
                                                                 })

                                    </div>

                            </div>
                        </div>

                    <ul class="nav pull-right"></ul>
                </div><!--/.nav-collapse -->
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h2>
                Measurements
            </h2>
            <div class="checkbox">
                @Html.DropDownListFor(m => m.ShowMode, new SelectList(@Model.ShowModeList, "Value", "Text", 1), new { @class = "dropdown", style = "width:250px" })
                <input type="submit" formaction="Measurement/ChangeShowMode" value="Go" id="btnGo" class="btn btn-primary" />
            </div>

            <div class="scrollableDiv" id="divMeasurement">

                @Html.HiddenFor(m => m.TestID)
                @Html.HiddenFor(m => m.AppointmentID)
                @Html.HiddenFor(m => m.AppointmentTestID)

                <table cellpadding="5">

                    @{

                        //@for (int i = 0; i < @Model.MeasurementCategories - 1; i++)
                        foreach (var cat in @Model.MeasurementCategories)
                        {
                            <tr>
                                <td class="control-label">
                                    <h3> <span id='<EMAIL>' class="label label-success">@cat.name,@cat.Id </span></h3>

                                </td>

                                @Html.HiddenFor(x => x.MeasurementCategories[catNum].Id)
                                @Html.HiddenFor(x => x.MeasurementCategories[catNum].name)
                                @Html.HiddenFor(x => x.MeasurementCategories[catNum].order)
                                @Html.HiddenFor(x => x.MeasurementCategories[catNum].status)
                                @Html.HiddenFor(x => x.MeasurementCategories[catNum].categoryCode)

    // Duplicate variable removed: // Duplicate variable removed: // Duplicate variable removed: // Duplicate variable removed: @for (int i = 0; i < cat.Measurements.Count; i++)
                                {

                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].Id)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].isCompulsory)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].measurementCode) Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].categoryCode)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].name)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].order)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].status)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].units)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].mask)
                                    @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].MeasurementRange)

                                    <td class="coloredBorderLeft   ">
*@"Measurent" :   @cat.categoryCode @measure.measurementCode* <h4><span class="label  label-primary">@measure.name,@measure.Id</span><span class="smallLabel" >@measure.units</span></h4>
                                        
                                    </td>
                                    <td>
                                        <a href='@Url.Action("History", "Measurement", new { measurementID = measure.Id, appointmentID = @Model.AppointmentID, testID = @Model.TestID })'

                                    </td>
                                    
                                    <td>

                                        @{

                                            {

                                            }
                                            else

                                            {

                                            else

                                            {

                                            else

                                            {

                                            else

                                            {

                                            else

                                            {

                                            else

                                            {

                                                    if (@measure.MeasurementSavedValues.Count > 1)
                                                    {
                                            <table>
                                                <tr>
                                                    <td>

                                                        @Html.DropDownList("ddMulti_" + @cat.Id + "_" + @measure.Id,
                                                                                new SelectList(@Model.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues,
                                                                                "Value", "Value", @Model.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].Value),

                                                    </td>
                                                    <td>

                                                        <span class="error">@measure.MeasurementSavedValues[0].ErrorMessage</span>

                                                        @Html.TextBoxFor(x => x.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].Value)
                                                            new
                                                            {
                                                                @class = "form-control txtBox " + @rangeClass,
                                                                @id = "txtManVal_" + @cat.Id + "_" + @measure.Id,
                                                                @onblur = "GetRange(this,'" + @measure.measurementCode + "','" + @cat.categoryCode + "')"
                                                            })
                                                    </td>
                                                    <td>
                                                        @Html.DropDownListFor(m => m.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].MeasurementOperatorId,
                                                                                        new SelectList(@Model.MeasurementOperators, "Value", "Text", @operatorID),
                                                                                        new
                                                                                        {
                                                                                            @class = "dropdown",
                                                                                            @id = "ddOper_" + @cat.Id + "_" + @measure.Id,
                                                                                            @onchange = "OpeartorChanged(this,"
                                                                                                    + "'txtManVal_" + @cat.Id + "_" + @measure.Id + "',"
                                                                                                    + "'ddMulti_" + @cat.Id + "_" + @measure.Id + "')"
                                                                                        })
                                                    </td>
                                                </tr>
                                            </table>

                                        else
                                        {

                                            {

                                                <table>
                                                    <tr>
                                                        <td>

                                                            <span class="error">@measure.MeasurementSavedValues[0].ErrorMessage</span>

                                                            @Html.TextBoxFor(x => x.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].Value)
                                                                    new
                                                                    {

                                                                        @class = "form-control txtBox  " + @rangeClass,
                                                                        @Value = @measure.MeasurementSavedValues[0].Value,
                                                                        @onblur = "GetRange(this,'" + @measure.measurementCode + "','" + @cat.categoryCode + "')"
                                                                    })
                                                        </td>
                                                        <td>
                                                            @Html.DropDownList("ddOpear", new SelectList(@Model.MeasurementOperators, "Value", "Text", @operatorID), new { @class = "dropdown", @disabled = "disabled" })
                                                        </td>
                                                    </tr>
                                                </table>

                                            else
                                            {

                                                @*<span>@measure.measurementCode + "','" + @cat.categoryCode</span>* <span class="error">@measure.MeasurementSavedValues[0].ErrorMessage </span>

                                                    <table>
                                                        <tr>
                                                            <td>
                                                                @Html.CheckBoxFor(   x => x.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].Discard)
                                                                @*new { @onclick = clearFucn })* </td>
                                                            <td>
                                                                @Html.TextBoxFor(x => x.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].Value)
                                                                new
                                                                {
                                                                    @id = "txtVal_" + @cat.Id + "_" + @measure.Id,
                                                                    @class = "form-control txtBox  " + @rangeClass,
                                                                    @Value = @measure.MeasurementSavedValues[0].Value,
                                                                    @onblur = "GetRange(this,'" + @measure.measurementCode + "','" + @cat.categoryCode + "')"

                                                                })
                                                            </td>

                                                        </tr>
                                                    </table>
*@ if (@Model.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].MeasurementBSARange != null)
                                                    {

                                                        {

                                                        }
                                                        else

                                                        {

                                                        else

                                                        {

                                                        else

                                                        {

                                                        else

                                                        {

                                                        else

                                                        {

                                                        else

                                                        {

                                                    @Html.TextBox("txtIndex")
                                                                                   @Model.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].Value,
                                                                                   new
                                                                                   {
                                                                                       @class = "form-control txtBox  " + @rangeClassBSA,
                                                                                       @disabled = "disabled"
                                                                                   })

                                        @Html.HiddenFor(x => x.MeasurementCategories[catNum].Measurements[i].MeasurementSavedValues[0].MeasurementId)
                                    </td>

@* measureNum++; *@

                            </tr>

@* catNum++; *@

                </table>
            </div>

        </div>
    </div>

    <br />

    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <input type="submit" formaction="Measurement/CreateNewLog" value="Save" id="btnAppointment" class="btn btn-primary" />
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <input type="submit" formaction="Measurement/Import" value="Import" id="btnAppointment" class="btn btn-primary" />
            </div>
        </div>
    </div>

            <div class="col-md-12">
                <div class="form-group">
                    <input type="submit" formaction="Measurement/Customize" value="Customize" id="btnCustomize" class="btn btn-primary" />
                </div>
            </div>
        </div>

                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <input type="submit" formaction="Measurement/Import" value="Import" id="btnImport" class="btn btn-primary" />
                                                        </div>
                                                    </div>
                                                </div>

@*foreach (var item in @<EMAIL>)*@
                                                      {

                                                          <div class="container">
                                                              <div class="row">
                                                                  <div class="col-xs-6">
                                                                      <ul class="nav">

                                                                          <li class="dropdown">

                                                                              <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                                                                  @item.name
                                                                                  @if (@<EMAIL>() > 0)
                                                                                  {
                                                                                      <b class="caret"></b>
                                                                                  }
                                                                              </a>

                                                                              @Html.HiddenFor(m => <EMAIL>[index].TopLevelReportPhraseID)

                                                                              @Html.HiddenFor(m => <EMAIL>[index].Id)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].name)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].ordernumber)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].parent)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].root)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].status)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].test)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].type)
                                                                              @Html.HiddenFor(m => <EMAIL>[index].value)

                                                                              <ul class="dropdown-menu">
                                                                                  @{int secIndex = 0;
                                                                                  }
                                                                                  @foreach (var subItem in @Model.ReportPhraseView@Model.lstLevel2)
                                                                                  {

                                                                                      {

                                                                                          //var lstThird = @Model.ReportPhrases.Where(r => r.parent == subItem.Id && r.test == 1 && r.status == 0).OrderBy(r => r.ordernumber).ToList();

                                                                                          //var lstThird = @<EMAIL>(i => i.parent == subItem.Id);

                                                                                          //var lstThirdHasValues = false;

                                                                                          if (!string.IsNullOrEmpty(@<EMAIL>[index].Value) &&
                                                                                               @<EMAIL>[index].Value.Contains(subItem.name)
                                                                                             )
                                                                                          {

                                                                                          }

                                                                                          <li class=@className>
                                                                                              <a tabindex="-1" href="#" onclick="@jsCall">

                                                                                                  <span class='@colorClass @selectedClass'>@subItem.name   </span>
                                                                                              </a>

                                                                                              @if (@<EMAIL>() > 0)
                                                                                              {

                                                                                                  <ul class="dropdown-menu">
                                                                                                      @foreach (var thirdLevelItem in lstThird)
                                                                                                      {

                                                                                                          //if (thirdLevelItem.parent == subItem.Id)
                                                                                                          //{
                                                                                                          <li>
                                                                                                              <a tabindex="-1" href="#" onclick="@jsCallInner">
                                                                                                                  <span class="spanGreen">@thirdLevelItem.name   </span>

                                                                                                              </a>
                                                                                                          </li>
                                                                                                          //}

@* thirdIndex++; *@

                                                                                                  </ul>

                                                                                          </li>

@* secIndex++; *@

                                                                              </ul>

                                                                          </li>

                                                                      </ul>
                                                                  </div>
                                                                  <div class="col-xs-6">
                                                                      @Html.TextAreaFor(m => <EMAIL>[index].Value)
    new
    {

        @class = " txtArea",
        @id = "txtReportPhrase_" + @<EMAIL>[index].Id,
        @name = "txtReportPhrase_" + @<EMAIL>[index].Id
    })
                                                                  </div>
                                                              </div>
                                                          </div>

@* index++; *@

        <a class="dropdown-toggle" href="#" data-toggle="dropdown">Account Settings<b class="caret"></b></a>
        <ul class="dropdown-menu">
            <li><a href="#">Login</a></li>
            <li class="dropdown-submenu">
                <a tabindex="-1" href="#">More options</a>
                <ul class="dropdown-menu">
                    <li><a tabindex="-1" href="#">Second level</a></li>
                    <li class="dropdown-submenu">
                        <a href="#">More..</a>
                        <ul class="dropdown-menu">
                            <li><a href="#">3rd level</a></li>
                            <li><a href="#">3rd level</a></li>
                        </ul>
                    </li>
                    <li><a href="#">Second level</a></li>
                    <li><a href="#">Second level</a></li>
                </ul>
            </li>
            <li><a href="#">Register</a></li>
            <li class="divider"></li>
            <li><a href="#">Logout</a></li>
        </ul>
    </li>*@<script>

        (function(){

            $(function() {

                $('#txtReportPhrase_154').tinymce({

                     Location of TinyMCE script
                    script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
                    theme: "advanced",

                    height: "300",
                    width: "300",
                    verify_html : false,
                    plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",

                    // Theme options
                    theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
                    theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
                    theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
                    theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
                    theme_advanced_toolbar_location : "top",
                    theme_advanced_toolbar_align : "left",
                    theme_advanced_statusbar_location : "bottom",
                    theme_advanced_resizing : false,

                    // Example content CSS (should be your site CSS)
                    content_css : "@Url.Content("~/Scripts/tinymce/css/content.css")",
                convert_urls : false,

                // Drop lists for link/image/media/template dialogs
                template_external_list_url : "lists/template_list.js",
                external_link_list_url : "lists/link_list.js",
                external_image_list_url : "lists/image_list.js",
                media_external_list_url : "lists/media_list.js"

@* }); *@

@* }); *@

        })();@* *@
    </script>