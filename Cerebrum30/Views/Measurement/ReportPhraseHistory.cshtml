﻿@model IEnumerable<Cerebrum30.DataObjects.ReportPhraseHistoryData>

@{

}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>History</title>
</head>
<body>

    <div style="text-align:center;">

                <tr>

                    <th>
                        @Html.DisplayNameFor(model => model.Value)
                    </th>

                    <th>
                        @Html.DisplayNameFor(model => model.LogDate)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.AppointmentID)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.TestID)
                    </th>
                </tr>
                @foreach (var item in Model)
                {
                    <tr>

                        <td>
                            @Html.TextAreaFor(modelItem => item.Value,

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.LogDate)
                        </td>

                        <td>
                            @Html.DisplayFor(modelItem => item.AppointmentID)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.TestID)
                        </td>

                    </tr>

            </table>
        <table class="table" cellpadding="5">
            <tr>

                <th>
                    @Html.DisplayNameFor(model => model.Value)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.AppointmentID)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.TestID)
                </th>
            </tr>
            @foreach (var item in Model)
            {
                <tr>

                    <td>
                        @Html.TextAreaFor(modelItem => item.Value,

                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.AppointmentID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.TestID)
                    </td>

                </tr>

        </table>

    </div>
</body>
</html>
