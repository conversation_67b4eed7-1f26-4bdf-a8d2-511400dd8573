﻿@model Cerebrum.ViewModels.Practice.VMPractice

@{

}

<h2>@( Model != null && @Model.Id > 0 ? "Edit" : "Add New")</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">
        <h4>Practice</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            @Html.LabelFor(model => model.PracticeName, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.PracticeName, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.PracticeName, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.PracticeNumber, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.PracticeNumber, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.PracticeNumber, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.PracticeFolder, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.PracticeFolder, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.PracticeFolder, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.OntarioHealthClientId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.OntarioHealthClientId, new { htmlAttributes = new { @class = "form-control", @maxlength = 255 } })
                @Html.ValidationMessageFor(model => model.OntarioHealthClientId, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.SendOutReportMethod, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.CustomEnumDropDownListFor(model => model.SendOutReportMethod, new { @class = "form-control" }, new SelectListItem { Text = "Reporting Method", Value = "" })
                @Html.ValidationMessageFor(model => model.SendOutReportMethod, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.BrowserNotClosedWarningEnabled, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.CheckBoxFor(model => model.BrowserNotClosedWarningEnabled)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.IsDhdrEnabled, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.CheckBoxFor(model => model.IsDhdrEnabled)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.IsEformsEnabled, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.CheckBoxFor(model => model.IsEformsEnabled)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.TenantId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TenantId, new { htmlAttributes = new { @class = "form-control", @maxlength = 36, placeholder = "00000000-0000-0000-0000-000000000000" } })
                @Html.ValidationMessageFor(model => model.TenantId, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Us2AiSubdomain, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Us2AiSubdomain, new { htmlAttributes = new { @class = "form-control", @maxlength = 100 } })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.RolesExists, new { Value = "false" })
                @{

                }
                <input type="submit" value="@save" class="btn btn-default" />
            </div>
        </div>
    </div>

<div>
    @Html.ActionLink("Back to List", "Index")
</div>
