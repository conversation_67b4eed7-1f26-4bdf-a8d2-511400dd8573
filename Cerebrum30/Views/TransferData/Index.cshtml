﻿@model IList<Cerebrum30.Models.TransferedTb>
@{

}
<link href="~/Content/transfer.css" rel="stylesheet" />

<h2>TRANSFER TABLES</h2>
<div id="mainDiv">
@using (Html.BeginForm("Transfer", "TransferData", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { tblList = Model }))
{
    <table class="table">
        @for (var i = 0; i < @Model.Count(); i++)
        {
            <tr>
                <td>
                    <div class="form-group">
                        <div class="col-sm-1">
                            @Html.CheckBoxFor(model => model[i].isTransfer)
                        </div>
                        <div class="col-sm-4">*@ @Html.LabelFor(model => model[i].name, Model[i].name)
                            @Html.DisplayTextFor(model => Model[i].name)
                            @Html.HiddenFor(model => Model[i].name)
                        </div>
                        <div class="col-sm-8">
                            @Html.DisplayTextFor(model => Model[i].transDescription)
                        </div>
                    </div>

                </td>
            </tr>

    </table>
    <div class="form-group">
        <input type="submit" value="Transfer" />
    </div>

</div>

