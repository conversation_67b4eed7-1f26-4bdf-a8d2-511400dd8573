﻿
@model Cerebrum30.Models.VM_Demogr_Cohort
<style>
    #addCohort input, select, button {
/* height: 25px !important; */
/* padding-top: 0px !important; */
/* padding-bottom: 0px !important; */
    }

    .ui-widget-header {
/* background-color: white; */

    .marginTop2 {
@* margin-top: 2px; *@

    .marginBott2 {
@* margin-bottom: 2px; *@

</style>

@using (Html.BeginForm("Dem_cohort", "DemographicsGB", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "dem_cohort_id" }))
@using Cerebrum.ViewModels.Patient
{
    @Html.ModalHeader("Add Cohort")

    @Html.HiddenFor(x => x.pat_cohort_Id)
    @Html.HiddenFor(x => x.coh_patientRecordId)
    <div id="addCohort" class="modal-body container form-group form-group-sm">
        <div class="row marginTop2" style="margin-top: 10px;">
            <div class="col-md-2">
                <span>Patient:</span>
            </div>
            <div class="col-md-10 marg_bott height25">
                @Html.EditorFor(model => model.coh_name, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Cohort:</span>
            </div>
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.SelectedCohort_1_Id, htmlAttributes: new { @class = "form-control selec_", tabindex = 2 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2 ">
                <span>Started:</span>
            </div>
            <div class="col-md-10">
                @Html.EditorFor(model => model.dateStarted, new { htmlAttributes = new { @class = "form-control coh_d", tabindex = 3 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Terminated:</span>
            </div>
            <div class="col-md-10">
                @Html.EditorFor(model => model.dateTerminated, new { htmlAttributes = new { @class = "form-control coh_d", tabindex = 4 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Doctor:</span>
            </div>
            <div class="col-md-10">
                @Html.DropDownListFor(model => @Model.SelectedDoctor_1_Id, @Model.doctorsList_1, htmlAttributes: new { @class = "form-control selec_", tabindex = 5 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Notes:</span>
            </div>
            <div class="col-md-10">
                @Html.TextAreaFor(model => model.Notes, new { @class = "form-control", tabindex = 6, @rows = 3 })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                
            </div>
            <div class="col-md-10 green">
                <span id="coh_messageId">@Html.LabelFor(x => x.message, @Model.message ?? "")</span>
            </div>
            *@<div class="col-md-12 text-center green">
                <span id="coh_messageId">@Html.LabelFor(x => x.message, @Model.message ?? "")</span>
            </div>* </div>
        <div class="row" style="border-bottom:1px solid #e2dada;">
                <div class="col-md-2 text-left">
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="dem_cohort_submit">Save</button>
                </div>
        </div>
    </div>

