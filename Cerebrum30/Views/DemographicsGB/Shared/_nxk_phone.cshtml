﻿@model Cerebrum30.Models.VM_NextOfPhone
<style>
        #contPhoneList td {
/* padding-bottom: 0px; */
/* padding-top: 0px; */
/* border-bottom: 1px solid #ddd; */
/* font-weight: 700; */
    }

        #contPhoneList td:hover {
/* background-color: gainsboro; */
/* cursor: pointer; */

    #nxk_phone_dialog input, select, button {
@* height: 25px !important; *@
@* padding-top: 0px !important; *@
@* padding-bottom: 0px !important; *@
    }

    #nxk_phone_list input {
@* height: 15px !important; *@

    .ui-widget-header {
@* background-color: white; *@
    }

    .marginTop2 {
@* margin-top: 2px; *@

    .marginBott2 {
@* margin-bottom: 2px; *@

    .t_a {
@* height:50px !important; *@
    }
    .selectW {
@* width:125px; *@
    }
</style>
@using (Html.BeginForm("nkx_phone", "DemographicsGB", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "nkx_phone_id" }))
{
    @* @Html.ModalHeader("Add Contact Phone") *@
    @Html.HiddenFor(x => x.nxk_p_type)
    @Html.HiddenFor(x => x.nxk_p_id)
    @Html.HiddenFor(x => x.nxk_p_patientId)
    @Html.HiddenFor(x => x.nxk_p_contactId)
    @Html.HiddenFor(x => x.nxk_p_activePhNum)
    <div id="nxk_phone_dialog" title="Add Next Of Keen Phone" style="padding-left:40px;padding-right:40px;">
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Contact Phone
                </div>
                <div class="col-md-8">
                    @Html.EditorFor(model => model.nxk_p_phone, new { htmlAttributes = new { @class = "form-control nxk_p_ph", tabindex = 1 } })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Phone Number Type
                </div>
                <div class="col-md-2 marginTop2">
                    @Html.CustomEnumDropDownListFor(model => model.dem_typeOfPhoneNumber, htmlAttributes: new { @class = "form-control selectW", tabindex = 2 })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    
                </div>
                <div class="col-md-8 marginTop2">
                    <div class="col-md-4">
                        <div class="checkbox">
                            <label class="chb_corr">
                                @Html.EditorFor(model => model.IsActive) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="checkbox">
                            <label class="chb_corr">
                                @Html.EditorFor(model => model.IsRemoved) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsRemoved)</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="checkbox">
                            <label class="chb_corr">
                                @Html.EditorFor(model => model.showAll) <span class="checkbox-text">@Html.DisplayNameFor(model => model.showAll)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="nkx_p_messageId">@Html.LabelFor(x => x.message_p, @Model.message_p ?? "")</span>

                </div>
            </div>
        </div>
        <div class="row marginTop2" style="border-bottom:1px solid #e2dada;">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    <button class="btn btn-default marginTop2 marginBott2" type="button" id="newContact_p">New</button>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="newContact_p_sub">Save</button>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-1">

                </div>
                <div id="nxk_phone_list" class="col-md-11 pull-left">
                    <table class="table" id="contPhoneList" name="contPhoneList" style="font-size: 12px;">
                        @if (Model != null && @Model.nxksList != null && @Model.nxksList.Count > 0)
                        {
                            <thead>
                                <tr class="vertical-center" style="background-color:#dddddd;">
                                    <th width="40%">Phone</th>
                                    <th width="40%">Phone Type</th>
                                    <th width="20%">Prime</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < @Model.nxksList.Count; i++)
                                {
                                    <tr>
                                        <td class="contPhList">
                                            @Html.DisplayFor(model => model.nxksList[i].name)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = @Model.nxksList[i].id })
                                        </td>
                                        <td class="contPhList">
                                            @Html.DisplayFor(model => model.nxksList[i].phoneType)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = @Model.nxksList[i].id })
                                        </td>
                                        <td class="contPhList">
                                            @Html.DisplayFor(model => model.nxksList[i].IsActive)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = @Model.nxksList[i].id })
                                        </td>
                                    </tr>

                            </tbody>

                    </table>
                </div>

            </div>
        </div>
    </div>

