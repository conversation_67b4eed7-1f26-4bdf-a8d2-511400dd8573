﻿@model Cerebrum30.Models.VM_Demogr_Phone
<style>
    #dem_phone_List td {
/* padding-bottom: 0px; */
/* padding-top: 0px; */
/* border-bottom: 1px solid #ddd; */
/* font-weight: 700; */
    }

        #dem_phone_List td:hover {
/* background-color: gainsboro; */
/* cursor: pointer; */

    #phone_dialog_edit input, select, button {
@* height: 25px !important; *@
@* padding-top: 0px !important; *@
@* padding-bottom: 0px !important; *@
    }

    .ui-widget-header {
@* background-color: white; *@

    .marginTop2 {
@* margin-top: 2px; *@

    .marginBott2 {
@* margin-bottom: 2px; *@

</style>

@using (Html.BeginForm("dem_phone", "DemographicsGB", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "dem_phone_id" }))
{
    @Html.ModalHeader("Add Phone")
    <div class="modal-body">
        @Html.HiddenFor(x => x.dem_p_type)
        @Html.HiddenFor(x => x.dem_p_id)
        @Html.HiddenFor(x => x.dem_p_demId)
        <div id="phone_dialog_edit" title="Add Doctor's Address">
            <div class="form-group">
                <div class="col-md-4">
                    @Html.LabelFor(model => model.dem_phoneNumber)
                </div>
                <div class="col-md-4">
                    @Html.EditorFor(model => model.dem_phoneNumber, new { htmlAttributes = new { @class = "form-control dem_phone", tabindex = 1 } })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.dem_extention)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.dem_extention, new { htmlAttributes = new { @class = "form-control numbersOnly", tabindex = 2 } })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-4">
                    @Html.LabelFor(model => model.dem_typeOfPhoneNumber)
                </div>
                <div class="col-md-2 marginTop2">
                    @Html.CustomEnumDropDownListFor(model => model.dem_typeOfPhoneNumber, htmlAttributes: new { @class = "form-control", tabindex = 3 })
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <div class="col-md-12 text-center green">
                        <span id="dem_p_messageId">@Html.LabelFor(x => x.dem_p_message, @Model.dem_p_message ?? "")</span>

                    </div>
                </div>
            </div>
            <div class="row" style="border-bottom:1px solid #e2dada;">
                <div class="form-group">
                    <div class="col-md-2 text-left">
                        <button class="btn btn-default marginTop2 marginBott2" type="button" id="newDemPhone">New</button>
                    </div>
                    <div class="col-md-8">
                    </div>
                    <div class="col-md-2 text-right">
                        <button class="btn btn-default marginTop2 marginBott2" type="submit" id="dem_phone_submit">Save</button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <div class="col-md-1">

                    </div>
                    <div class="col-md-11 pull-left">
                        <table class="table" id="dem_phone_List" name="dem_phone_List" style="font-size: 12px;margin-bottom: 0px;">
                            @if (Model != null && @Model.dem_p_List != null && @Model.dem_p_List.Count > 0)
                {
                        <tbody>
                            @for (int i = 0; i < @Model.dem_p_List.Count; i++)
                                {
                                <tr>
                                    <td class="demPhList">
                                        @Html.DisplayFor(model => model.dem_p_List[i].name)
                                        @Html.HiddenFor(model => model.dem_p_List[i].id, new { @id = @Model.dem_p_List[i].id })
                                    </td>
                                </tr>

                        </tbody>

                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">        
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
    </div>

