﻿
@model IEnumerable<Cerebrum30.Models.DemographicsViewModel>
<table class="table">
    <tr>
        <th></th>
        <th>
            OHIP
        </th>
        <th>
           FullName
        </th>
        
        <th>
            @Html.DisplayNameFor(model => model.gender)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.dateOfBirth)
        </th>

        <th></th>
    </tr>
    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.HiddenFor(modelItem => item.patientRecordId)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.ohip)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.FullName)
            </td>
            
            <td>
                @Html.DisplayFor(modelItem => item.gender)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.dateOfBirth)
            </td>
            <td>
                <a href="#">Select</a>
            </td>
        </tr>

</table>

