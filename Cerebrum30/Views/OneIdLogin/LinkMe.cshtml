﻿@{

}
<style>
    body {
/* background-color: #f8f8f8 !important; */

    .btn-primary {
/* color: #fff; */
/* background-color: #0495c9 !important; */

    .btn-primary:hover,
    .btn-primary:focus,
    .btn-primary:active,
    .btn-primary.active,
    .open>.dropdown-toggle.btn-primary {
@* color: #fff; *@
@* background-color: #00b3db !important; *@
    }
</style>
<div class="row">
    <div class="col-md-12">
        <section id="loginForm">
            @using (Html.BeginForm("LinkMe", "OneIdLogin", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new
            {

            }))
            {
                @Html.AntiForgeryToken()
                <h3>Would you like to link your account to One Id?</h3>
                <div class="form-group">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-default btn-primary" style="margin-right:17px" id="btnLinkMe">
@* &nbsp;&nbsp;Yes&nbsp;&nbsp; *@
                        </button>
                        <button type="button" class="btn btn-default btn-primary" style="margin-right:17px"

@* &nbsp;&nbsp;&nbsp;No&nbsp;&nbsp; *@
                        </button>
                    </div>
                </div>
                <input type="hidden" id="uaction" name="uaction" value="0" />

        </section>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {

        $('#btnLinkMe').on('click', function (e) {
@* e.preventDefault(); *@

@* document.frmLinkMe.submit(); *@
@* }); *@

        $('#btnNoLinkMe').on('click', function (e) {
@* e.preventDefault(); *@

@* document.frmLinkMe.submit(); *@
@* }); *@
@* }); *@

</script>
@section scripts {
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
}
