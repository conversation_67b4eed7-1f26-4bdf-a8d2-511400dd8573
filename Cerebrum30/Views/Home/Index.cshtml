@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum30.Models.HomeViewModels
@{

}
<div>
    <p></p>
    @if (@Model.ShowAdmin)
    {

    }
@* &nbsp;&nbsp;&nbsp; *@
    @if (@Model.ShowWebApiHelp)
    {
        @Html.ActionLink("API", "Index", "Help", new { area = "" }, new { target = "_blank" })

</div>

  <div class="jumbotron">
            <h1>Cerebrum 2.3</h1>
            <p class="lead"> &copy; @DateTime.Now.Year - WELL Health Technologies</p>
            <p><a href="http://chrc.net" class="btn btn-primary btn-lg">Learn more &raquo;</a></p>
        </div>

        <div class="row">
            <div class="col-md-4">
                <h2>
                    Contact Manager and External Document
                </h2>
                <p>
                    <label>You need to use “Admin” module to assign user to office, then you can see recipient list in that office</label>
                    @Html.ActionLink("Add new message", "NewTask", "ContactManagers", new { area = "ContactManagers" }, null)
                    <br />
                    @Html.ActionLink("Message list page", "Index", "ContactManagers", new { area = "ContactManagers" }, null)
                    <br />
                    @Html.ActionLink("Incoming fax", "Assignment", "ExternalDocument", new { area = "ExternalDocument", externaldocument = "fax", officeId = 2 }, null)
                    <br />
                    @Html.ActionLink("External document list page (doctor view)", "Index", "ExternalDocument", new { area = "ExternalDocument", PracticeDoctorId = 3 }, null)
                    <br />
                    @Html.ActionLink("External document list page (patient view)", "Index", "ExternalDocument", new { area = "ExternalDocument", PatientRecordId = 3 }, null)
                    <br />
                </p>
            </div>
            @ *@Html.ActionLink("WorkSheet", "Index", "Measurement", new { area = "Measurements", AppointmentID = @Model.Id, TestID = @t.Id }, null)
            <div class="col-md-4">
                <h2>Admin User</h2>
                <p>
                    @Html.ActionLink("Users page", "Index", "AdminUsers",  new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("Roles page ", "adminRoles", "AdminUsers", new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("Office page ", "adminOffice", "AdminUsers", new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("Practice Doctors page ", "practiceDocs", "AdminUsers", new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("Cohort", "Index", "Cohort", new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("New Patient", "NewPatient", "DemographicsGB", new { area = "", practiceId = "3" }, null)
                    <br>
                    @Html.ActionLink("Patient Edit", "NewPatientEdit", "DemographicsGB", new { area = "", demographicId = "3" }, null)
                    <br>
                    @Html.ActionLink("Inventory", "Index", "StoreInventory", new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("Sign of Inventory", "Index", "Appointments", new { area = "Daysheet" }, null)
                    <br>
                    @Html.ActionLink("Patient Merge", "PatientMerge", "PatientMerge", new { area = "AdminUser"}, null)
                    <br>
                    @Html.ActionLink("Age and Gender", "AgeGender", "EMRMetrics", new { area = "AdminUser" }, null)
                    <br>
                    @Html.ActionLink("EMR Statistics", "PatientVisitsStatistics", "EMRMetrics", new { area = "AdminUser" }, null)
                    <br> Html.ActionLink("Practice Booking Confirmation Message ", "Create", "Admin/BookingConfirmationMessage", null, new { @class = "modal-link " })
                </p>
            </div>

            <div class="col-md-4">
                <h2>Schedule</h2>
                <p>
                    @Html.ActionLink("Admin Office", "Index", "Office", new { area = "Schedule" }, null)
                    <br>
                    @Html.ActionLink("Admin Staff", "Index", "Staff", new { area = "Schedule" }, null)
                    <br>
                    @Html.ActionLink("Appointments", "Index", "Appointments", new { area = "Schedule" }, null)
                </p>
            </div>

            <div class="col-md-4">
                <h2>Worksheet</h2>
                <p>
                    @Html.ActionLink("Worksheet", "Index", "Measurement", new { area = "Measurements" , AppointmentID = 1 , TestID = 1 , AppointmentTestID =1}, null)
                    <br>
            </div>
            <div class="col-md-4">
                <h2>VP</h2>
                <p>
                    @Html.ActionLink("VP", "Index", "VP", new { area = "VP" , AppointmentID = 1, AppointmentTestID = 1 }, null)
                    <br>
                    @Html.ActionLink("WorkList", "WorkList", "Measurement", new { area = "Measurements" }, null)
                    <br>
                    @Html.ActionLink("WebBooking", "editdoctor", "doctor", new { area = "daysheet", doctorID = 1 , Name = "Khan" }, null)
            </div>
            <div class="col-md-4">
                <h2>Daysheet</h2>
                <p>
                    @Html.ActionLink("Daysheet", "day", "appointments", new { area = "schedule" }, null) 
                    <br/>
                    @Html.ActionLink("Office Schedule", "index", "appointments", new { area = "schedule" }, null)                    
                </p>
            </div>
            <div class="col-md-4">
                <h2>Tests</h2>
                <p>
                    @Html.ActionLink("Tests", "Index", "Tests", new { area = "Admin" }, null)
                    <br />
                    @Html.ActionLink("Practice Tests", "Index", "PracticeTests", new { area = "Admin" }, null)
                </p>
            </div>
            <div class="col-md-4">
                <h2>Triage</h2>
                <p>
                    @Html.ActionLink("Triage", "Index", "waitlist", new { area = "triage" }, null)
                    
                </p>
            </div>
<div id="modal-container" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-content">
                </div>
            </div>

            <style>
                .modal-content {
/* width: 600px !important; */
/* margin: 30px auto !important; */
                }
            </style>

        $(function () {
            // Initialize numeric spinner input boxes
            //$(".numeric-spinner").spinedit();
            // Initialize modal dialog
            // attach modal-container bootstrap attributes to links with .modal-link class.
            // when a link is clicked with these attributes, bootstrap will display the href content in a modal dialog.
            $('body').on('click', '.modal-link', function (e) {
@* e.preventDefault(); *@
@* $(this).attr('data-target', '#modal-container'); *@
@* $(this).attr('data-toggle', 'modal'); *@
@* }); *@
            // Attach listener to .modal-close-btn's so that when the button is pressed the modal dialog disappears
            $('body').on('click', '.modal-close-btn', function () {
@* $('#modal-container').modal('hide'); *@
@* }); *@
            //clear modal cache, so that new content can be loaded
            $('#modal-container').on('hidden.bs.modal', function () {
@* $(this).removeData('bs.modal'); *@
@* }); *@
            $('#CancelModal').on('click', function () {
@* return false; *@
@* }); *@
@* }); *@
            </script>

                <div id='gameContainer'>
                </div>
            </div>

            <button id='showGame'>Show Game Listing</button>
            @*<script>*@

                $(document).ready(function () {
                    $('#showGame').click(function () {

                        $.get(url, function (data) {
@* $('#gameContainer').html(data); *@

@* $('#gameModal').modal('show'); *@
@* }); *@
@* }); *@
@* }); *@

            </script>
             @*<script>*@

                $(document).ready(function () {
                    $("#globalModal").on("show.bs.modal", function (e) {

                       // alert("Hello");
@* $(this).find(".modal-body").load("BookingConfirmationMessage/Create"); *@
@* }); *@
@* }); *@

            </script>

            <a href="Area/BookingConfirmationMessage/Create" data-toggle="modal" data-target="#globalModal" data-remote="false" class="btn btn-default">
                Launch Modal
            </a>*@<div id="dialog1" title="Dialog Title"></div>

            <button id="but">User Ban</button>

            <script>
            $(function () {
            $( "#dialog1" ).dialog({
            autoOpen: false
@* }); *@

            $("#but").click(function() {

            $.get('@Url.Action("BookingConfirmationMessage/Create", "Admin")', { id: selectedId }, function (partialView) {
@* $("#dialog1").html(partialView); *@
@* }); *@

@* $("#dialog1").dialog('open'); *@
@* }); *@
@* }); *@
@* *@
            </script>
        </div>
        <br />

