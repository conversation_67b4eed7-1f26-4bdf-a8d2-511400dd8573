﻿@model Cerebrum.ViewModels.Common.VMShowHide

<button type="button" class="btn btn-link @Model.css btn-connecting-ontario" data-url="@Url.Action("Form","ConnectingOntario",new {area="" })">Connecting Ontario</button>

<div class="modal fade" id="connectinOntarioModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Connecting Ontario</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {
        $(document).on('click', '.btn-connecting-ontario', function (e) {

            $.ajax({
                url: url,
                type: "GET",
                success: function (result) {
@* $("#patient-appointments-srch").html(result); *@
@* $('.btn-patient-appointment-srch').prop('disable', false); *@
                }
            }).fail(function (jqXHR, textStatus, errorThrown) {
@* checkAjaxError(jqXHR, null); *@
@* }); *@

@* }); *@
@* }); *@
</script>