@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Patient.VMPatientInfo
@{

    if (!String.IsNullOrEmpty(@Model.DOB))
    {

    }

<script>
    //LoadMedications();         

    //var loadEChartDialogeFromPatientInfo = function (obj) {   
    //    var id = obj.id;
    //    var firstname = obj.firstname;
    //    var lastname = obj.lastname;       

    //    var url = '/Patients/GetPatientMenu/' + id;
    //    $.get(url, function (data) { 
    //        $('#dialog7').dialog({           
    //            title: lastname + ', ' + firstname, 
    //            open: function( event, ui ) {
    //                $('#patientechartcontent7').closest('div[role="dialog"]').css({top:100,left:100});  
    //            }
    //        });
    //        $('#patientechartcontent7').empty();
    //        $('#patientechartcontent7').append(data); 
    //    });
    //}

    $(document).ready(function () {

@* return $(this).width(); *@
@* }).get(); *@

@* $('.Z').width(maxWidth); *@

        $(document).on('click','#dialog7 a',function(){         
@* $('#dialog7').dialog('close'); *@
@* }); *@

        $('.ctrl-eChart7').on('click', function(){
@* loadEChartDialogeFromPatientInfo(patientObj); *@
@* }); *@
@* }); *@
</script>
<script src="~/Areas/Schedule/Scripts/appointments.js"></script>
<div class="row __patient-top-pnl __7896545659">
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-3">
        <div class="float-Left-Label" style="font-size: 22px;"><span><b>Patient:</b></span><span id="cbr-patient-fullname" > @Model.LastName.ToUpper(), @Model.FirstName @Model.MiddleName</span></div>   
        <div class="ctrl-eChart7 float-Left-Label"><span class="glyphicon glyphicon-duplicate"></span>&nbsp; e-Chart</div>
    </div>
    <div class="col-xs-12 col-sm-12 col-md-8 col-lg-9">   
           
            <div class="float-Left-Label Z"><span><b>Age:</b> @Model.AgeAccurate &nbsp;&nbsp;(DOB: @Model.DOB - mm/dd/yyyy)</span></div>
            <div class="float-Left-Label Z"><span><b>Gender:</b> @Model.Gender </span></div>
            <div class="float-Left-Label Z"><span><b>HIN:</b> @Model.OHIP @Model.OHIPVersionCode</span></div>
            <div class="float-Left-Label Z"><span><b>Family Dr.:</b> @Model.FamilyDoctor</span></div>
            <div class="float-Left-Label Z"><span><b>Referral Dr.:</b> @Model.ReferralDoctor</span></div>
            <div class="float-Left-Label Z"><span><b>MRP.:</b> @Model.MRPDoctor</span></div>            
            <div class="float-Left-Label Z"><span><b>Test Name:</b> @Model.TestName</span></div>
            <div class="float-Left-Label Z">
                @if (@Model.AppointmentDate.HasValue)
                {
                    <span><strong>Visit Date:</strong> @Model.AppointmentDate.Value.ToLongDateString() - @Model.AppointmentDate.Value.ToShortTimeString()</span>
                }
@* &nbsp; *@
            </div>          
    </div>
</div>

    <div id="patientechartcontent7"></div>
</div>@* *@
<!-- --------------->

    <div class="col-xs-11 col-sm-11 col-md-11 col-lg-11">
        <div>
            <div class="float-Left-Label adjust-width" style="min-width: 350px; font-size: 24px; margin-top:-4px"><span><strong>Patient:</strong></span><span id="cbr-patient-fullname" onclick="return LoadEChartDialogeFromPatientInfo(patientObj);"> @Model.LastName.ToUpper(), @Model.FirstName </span></div>
            <div class="float-Left-Label adjust-width"><span><strong>Birth Date</strong> (mm/dd/yyyy)<strong>:</strong> @Model.DOB </span></div>
            <div class="float-Left-Label adjust-width"><span><strong>Age:</strong> @Model.Age </span></div>
            <div class="float-Left-Label adjust-width"><span><strong>Gender:</strong> @Model.Gender </span></div>
            <div class="float-Left-Label adjust-width"><span><strong>Ohip:</strong> @Model.OHIP</span></div>
            <div class="float-Left-Label adjust-width"><span><strong>Family Dr.:</strong> @Model.FamilyDoctor</span></div>
            <div class="float-Left-Label adjust-width"><span><strong>Referral Dr.:</strong> @Model.ReferralDoctor</span></div>
            <div class="float-Left-Label adjust-width"><span><strong>MRP.:</strong> @Model.MRPDoctor</span></div>
            @if(@Model.AppointmentDate.HasValue ){
            <div class="float-Left-Label adjust-width"><span><strong>Visit Date:</strong> @Model.AppointmentDate.Value.ToLongDateString() - @Model.AppointmentDate.Value.ToShortTimeString()</span></div>
            }
            <div class="float-Left-Label adjust-width"><span><strong>Test Name:</strong> @Model.TestName</span></div>         
        </div>
    </div>

    <div class="col-xs-1 col-sm-1 col-md-1 col-lg-1">
        <div class="margin-left-15 float_r" style="white-space: nowrap">
            <div id="sidebar-right-open"><div class="c-pointer" onclick="openSidebar()"><span class="glyphicon glyphicon-list f25"></span> <span>e-Chart</span></div></div>
        </div>
    </div>
</div>

    <a href="javascript:void(0)" class="closebtn" onclick="closeSidebar()">&times;</a>
    @* TODO: Convert Html.RenderAction to ViewComponent - Convert Html.RenderAction to ViewComponent - @ * *
     use white fff
    <span class="c-pointer" onclick="openSidebar()"><span class="glyphicon glyphicon-list f25"></span> <span style="font-size:14px;">e-Chart</span></span>
</div>*

    <div id="div-medications"></div>
</div>*@<div class="container-fluid spacer-top-10">
    <div class="row __H0789MED">
        <div id="div-medications"><img src="Content/fancybox_loading.gif" /></div>
    </div>@* *@
</div>