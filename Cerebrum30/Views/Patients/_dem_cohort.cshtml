﻿
@model Cerebrum.ViewModels.Cohort.PatientCohortVM
<style>
    #addCohort input, select, button {
/* height: 25px !important; */
/* padding-top: 0px !important; */
/* padding-bottom: 0px !important; */
    }

    .ui-widget-header {
/* background-color: white; */

    .marginTop2 {
@* margin-top: 2px; *@

    .marginBott2 {
@* margin-bottom: 2px; *@

</style>
<script>

    $(document).ready(function () {
@* $("#patientCohortStartDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true }); *@
@* $("#patientCohortEndDate").datepicker({ dateFormat: DATEFORMAT, changeMonth: true, changeYear: true }); *@

@* getPatientCohortClass(); *@
@* }); *@

    function getPatientCohortClass() {

        }

        ajaxCall(url, data, false, function (result) {

@* showPatientCohortClass(result); *@
            } else {
@* showMessageModal("error", result.errorMessage, false); *@
            }
@* }); *@

@* return false; *@

    function showPatientCohortClass(result) {
@* $("#patientCohortClassId").html(""); *@

        }

            $.each(result.CohortClasses, function (i, cohortClass) {

@* }); *@

@* getPatientCohort(); *@

    function getPatientCohort() {

@* showPatientCohort(null); *@
@* return false; *@
        }

        ajaxCall(url, data, false, function (result) {

@* showPatientCohort(result); *@
            } else {
@* showMessageModal("error", result.errorMessage, false); *@
            }
@* }); *@

@* return false; *@

    function showPatientCohort(result) {
@* $("#patientCohortCohortId").html(""); *@

        }

            $.each(result.Cohorts, function (i, cohort) {

@* }); *@

    function checkAjaxError(url, xhr, thrownError) {

@* showMessageModal("error", "No permission to execute this task", false); *@
        } else {
@* showMessageModal("error", "Error while tryng to call '" + url + "' " + xhr.status + "  " + thrownError, false); *@

</script>
@using (Html.BeginForm("Dem_cohort", "Patients", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "dem_cohort_id" }))
@using Cerebrum.ViewModels.Patient
{
    @Html.ModalHeader("Add Cohort")

    @Html.HiddenFor(x => x.patientRecordId)
    <div id="addCohort" class="modal-body container form-group form-group-sm">
        <div class="row marginTop2" style="margin-top: 10px;">
            <div class="col-md-2">
                <span>Patient:</span>
            </div>
            <div class="col-md-10 marg_bott height25">
                @Html.EditorFor(model => model.patientName, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Doctor:</span>
            </div>
            <div class="col-md-10">
                @if (@Model.PracticeDoctors.Count() == 1)
                {
                    @Html.DropDownList("patientCohortPracticeDoctorId", new SelectList(@Model.PracticeDoctors, "value", "text"), htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohortClass();" })
                }
                else
                {
                    @Html.DropDownList("patientCohortPracticeDoctorId", new SelectList(@Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohortClass();" })

            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Class:</span>
            </div>
            <div class="col-md-10">
                @if (@Model.CohortClasses.Count() == 1)
                {
                    @Html.DropDownList("patientCohortClassId", new SelectList(@Model.CohortClasses, "value", "text"), htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohort();" })
                }
                else
                {
                    @Html.DropDownList("patientCohortClassId", new SelectList(@Model.CohortClasses, "value", "text"), "Select Class", htmlAttributes: new { @class = "form-control selec_", @onchange = "getPatientCohort();" })

            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Cohort:</span>
            </div>
            <div class="col-md-10">
                @if (@Model.Cohorts.Count() == 1)
                {
                    @Html.DropDownList("patientCohortCohortId", new SelectList(@Model.Cohorts, "value", "text"), htmlAttributes: new { @class = "form-control selec_" })
                }
                else
                {
                    @Html.DropDownList("patientCohortCohortId", new SelectList(@Model.Cohorts, "value", "text"), "Select Cohort", htmlAttributes: new { @class = "form-control selec_" })

            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2 ">
                <span>Started:</span>
            </div>
            <div class="col-md-10">
                <input type="text" class="form-control input-sm" id="patientCohortStartDate">
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Terminated:</span>
            </div>
            <div class="col-md-10">
                <input type="text" class="form-control input-sm" id="patientCohortEndDate">
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-2">
                <span>Notes:</span>
            </div>
            <div class="col-md-10">
                <textarea rows="3" style="width: 100%;" id="patientCohortNote" name="patientCohortNote"></textarea>
            </div>
        </div>
        <div class="row marginTop2">           
            <div class="col-md-12 text-center green">
                <span id="coh_messageId">@Model.errorMessage</span>
            </div>
        </div>
        <div class="row" style="border-bottom:1px solid #e2dada;">
                <div class="col-md-2 text-left">
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="dem_cohort_submit">Save</button>
                </div>
        </div>
    </div>

