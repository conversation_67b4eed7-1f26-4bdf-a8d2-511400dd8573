﻿@model IEnumerable<Cerebrum.ViewModels.Patient.VMPatientInfo>
@{

}
@section customcss{   
    <link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
    <link href="~/Areas/Schedule/Content/appointments-modal.css" rel="stylesheet" />

<div class="row">
    <div class="col-md-6">
        <h3>Patient Legacy Chart Search</h3>
    </div>
    <div class="col-md-6 text-right">
        
    </div>
</div>

<div style="margin-bottom:10px;" class="row">

    @using (Html.BeginForm("PatientSearchLegacyNumber", "patients", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Get, true, new { @class = "form-inline" }))
@using Cerebrum.ViewModels.Patient
    {

        <div class="form-group form-group-sm">
            <div class="col-md-4">
                <input name="chartNumber" id="chartNumber" type="text" class="form-control" placeholder="Chart Search" value="@search">
            </div>
        </div>
        <button type="submit" class="btn btn-default btn-xs">Search</button>

</div>

<div class="panel panel-info">
    <div class="panel-heading">Results (@Model.Count())</div>
    <table id="tbl-patient-search" class="table table-bordered">
        <tr>       
            <th> Html.DisplayNameFor(model => model.MRN)Chart Number
            </th>  
            <th>
                @Html.DisplayNameFor(model => model.LastName)
            </th>    
            <th>
                @Html.DisplayNameFor(model => model.FirstName)
            </th>   
            
            <th>
                @Html.DisplayNameFor(model => model.MiddleName)
            </th>                        
            <th>
                @Html.DisplayNameFor(model => model.DateOfBirth)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.OHIP)
            </th>  
            
            <th>
                Status
            </th>           
            <th></th>
        </tr>
        @foreach (var item in Model) {
            <tr>          
                <td>
                    @Html.DisplayFor(model => item.MRN)
                </td>           
                <td>
                    @Html.DisplayFor(modelItem => item.LastName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.FirstName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.MiddleName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.DateOfBirth)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.OHIP) @Html.DisplayFor(modelItem => item.OHIPVersionCode)
                </td>       

                <td>
                    @Html.DisplayFor(model => item.StatusStr)
                </td>           
                <td>              
                    <div class="btn-popover-container">
                        <button type="button" class="btn btn-default btn-xs popover-btn">
                            <span class="glyphicon glyphicon-option-vertical text-primary"></span>
                        </button>
                        <div class="btn-popover-title">
                            <span class="default-text-color">Patient Menu</span>
                        </div>
                        <div class="btn-popover-content">*@
                            @{

                            }
                            @{Html.RenderPartial("_PatientMenu", patientMenu); }
                        </div>
                    </div>                   
                </td>
            </tr>

    </table>
</div>