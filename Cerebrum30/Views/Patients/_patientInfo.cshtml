@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Patient.VMPatientInfo
@* Convert.ToBoolean(ViewBag.IsPriorityExists) && cerebrumUser.IsPracticeAppointmentPriorityEnabled; *@
<link href="~/Content/patient-info.css" rel="stylesheet" />
<script src="~/Scripts/patient-info.js"></script>
<script>
    $(document).ready(function () {

@* return $(this).width(); *@
@* }).get(); *@

@* $('.Z').width(maxWidth); *@

        $(document).on('click', '#dialog7 a', function () {
@* $('#dialog7').dialog('close'); *@
@* }); *@

        $('.ctrl-eChart7').on('click', function () {
@* loadEChartDialogeFromPatientInfo(patientObj); *@
@* }); *@

@* setPatientInfoContainerHeight(); *@
@* }); *@

    function setPatientInfoContainerHeight() {

@* $('#patient-info-container').css('height', contentHeight); *@

</script>
<script src="~/Areas/Schedule/Scripts/appointments.js"></script>
@* Html.RenderPartial("_patientInfoAppointmentPriorities", Model); *@
<div id="patient-info-content" class="row __patient-top-pnl __997967887345">
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-3">
        <div class="float-Left-Label"><span><b>Patient:</b></span><span style="font-size: 20px;"

                @Html.Raw(string.IsNullOrWhiteSpace(@Model.PreferredName) ? "" : "[" + @Model.PreferredName + "]") </span>
        </div>
        <div class="ctrl-eChart7 float-Left-Label"><span class="glyphicon glyphicon-duplicate"></span>&nbsp; e-Chart
        </div>
    </div>
    <div class="col-xs-12 col-sm-12 col-md-8 col-lg-9">
        @{

        }
        <div id="head-patient-info-menu-dob" data-patient-dob="@Model.DOB_MM_dd_yyyy" class="float-Left-Label Z">
            <span><b>Age:</b> @Model.AgeAccurate &nbsp;&nbsp;(DOB: @Model.DOB - mm/dd/yyyy)</span></div>
        <div class="float-Left-Label Z"><span><b>Gender:</b> @Model.Gender </span></div>
        <div class="float-Left-Label Z"><span><b>@ohipLabel</b> @Model.OHIP @Model.OHIPVersionCode</span></div>
        <div class="float-Left-Label Z"><span><b>Family Dr.:</b> @Model.FamilyDoctor</span></div>
        @if (showAppointmentPriority)
        {
            <div class="float-Left-Label Z">
                <span><b>Priority:</b> <span id="patient-info-appointment-priority-name">@Model.PriorityName</span></span>
                <div data-appointment-id="@Model.AppointmentID"

            </div>

        <div class="float-Left-Label Z"><span><b>Referral Dr.:</b> @Model.ReferralDoctor</span></div>
        <div class="float-Left-Label Z"><span><b>MRP.:</b> @Model.MRPDoctor</span></div>
        @if (!String.IsNullOrWhiteSpace(@Model.TestName))
        {
            <div class="float-Left-Label Z"><span><b>Test Name:</b> @Model.TestName</span></div>
        }

        @if (@Model.AppointmentDate.HasValue)
        {
            <div class="float-Left-Label Z">
                <span><strong>Visit Date:</strong> @Model.AppointmentDate.Value.ToLongDateString() -
                    @Model.AppointmentDate.Value.ToShortTimeString()</span>
@* &nbsp; *@
            </div>

        <div class="float-Left-Label Z" style="color:#23527c ">
            <div id="_placeHolder17"></div>
        </div>
    </div>
</div>
