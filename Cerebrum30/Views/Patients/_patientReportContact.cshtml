@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Common.VMExternalDoctorReportContact

@{ 

}

<style>
    #table-ref-doc-rep-addresses .expand-btn, 
    #table-ref-doc-rep-addresses .collapse-btn {
/* font-size: small; */
}

.tr-child-row-white-bg-rp{
/* background-color:#ffffff; */

    .tr-child-row-white-bg-rp td{
@* background-color:#ffffff; *@
@* font-size: 11px; *@
@* font-style:italic; *@
    }
</style>

@Html.ModalHeader("Doctor: "+ doctor<PERSON><PERSON> +" for " + @Model.PatientName)

<div class="modal-body">
    @if (@Model.AppointmentId > 0)
    {
        <div style="font-size:16px;">Appointment: @Model.AppointmentId</div>
    }

    <div class="panel panel-info content-height300">
        <div class="panel-heading">
            <h3 class="panel-title">Addresses <span class="badge cbadge">@Model.ExternalDoctor.Addresses.Count</span></h3>
        </div>
        <table data-refresh-app="@refreshApp" id="table-ref-doc-rep-addresses" class="table tbl-parent-child-rows">
            <thead>
                <tr>
                    <th></th>
                    <th>
                        Address
                    </th>    
                    <th>
                        Phone/Fax Numbers
                    </th>   
                    <th>
                        Active
                    </th> 
                    <th></th>              
                </tr>
            </thead>
            <tbody>
                @foreach (var item in @Model.ExternalDoctor.Addresses)
                {

                    <tr id="<EMAIL>">
                        <td style="width:10px;">
                            @if (showExpandLink)

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.AddressLine1)
                            @if (!String.IsNullOrWhiteSpace(item.AddressLine2))
                            {
                                <span> @Html.DisplayFor(modelItem => item.AddressLine2)</span>
                            }
                            @if (!String.IsNullOrWhiteSpace(item.City))
                            {
                                <span>, @Html.DisplayFor(modelItem => item.City)</span>

                            @if (!String.IsNullOrWhiteSpace(item.Province))
                            {
                                <span> @Html.DisplayFor(modelItem => item.Province)</span>

                            @if (!String.IsNullOrWhiteSpace(item.PostalCode))
                            {
                                <span> @Html.DisplayFor(modelItem => item.PostalCode)</span>

                        </td>
                        <td class="td-doc-phone-fax-num">                           
                            @if (totalPhoneNumbers > 0)
                            {
                                @totalPhoneNumbers
                            }
                            else
                            {
                                <span class="text-danger"> Please add a Fax/Phone number in the edit doctor page.</span>

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.IsActive)
                        </td>
                        <td style="width:15%;">                           
                           
                        </td>
                    </tr>

                    foreach(var phoneItem in item.PhoneNumbers)
                    {

                        <tr class="tr-child-row-white-bg-rp" id="<EMAIL><EMAIL>">
                            <td></td>
                            <td colspan="2">
                                @if (!String.IsNullOrWhiteSpace(phoneItem.PhoneNumber))
                                {
                                    <span><strong>Phone:</strong> @Html.DisplayFor(modelItem => phoneItem.PhoneNumber)</span>
                                }
                                @if (!String.IsNullOrWhiteSpace(phoneItem.PhoneNumber) && !String.IsNullOrWhiteSpace(phoneItem.PhoneExtension))
                                {
                                    <span> ext. @Html.DisplayFor(modelItem => phoneItem.PhoneExtension)</span>

                                @if (hasFax)
                                {
                                    <span> <strong>Fax:</strong> @Html.DisplayFor(modelItem => phoneItem.FaxNumber)</span>

                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => phoneItem.IsActive)
                            </td>
                            <td style="width:15%;">
                                @if (hasFax && isActivePhone)
                                {
                                    <form data-form-type="phone" class="frm-ref-doc-rep-phone" method="post" action="@Url.Action("SetPatientDoctorReportContact", "Patients", new { area = "" })">
                                        @Html.AntiForgeryToken()
                                        @Html.Hidden("PatientId", @Model.PatientId)
                                        @Html.Hidden("ExternalDoctorId", item.ExternalDoctorId)
                                        @Html.Hidden("ExternalDoctorAddressId", item.Id)
                                        @Html.Hidden("ExternalDoctorPhoneId", phoneItem.Id)
                                        @Html.Hidden("ExternalDoctorLocationId", phoneItem.ExternalDoctorLocationId)
                                        @Html.Hidden("AppointmentId", @Model.AppointmentId)
                                        @Html.Hidden("DoctorType", @Model.DoctorType)
                                        @Html.Hidden("IsEditPatient", @Model.IsEditPatient)
                                        <button data-item-id="@phoneItem.Id" type="submit" class="btn btn-xs btn-primary btn-set-ref-doc-rep-phone" @disabledPhone>@btnTextPhone</button>
                                    </form>

                                    @*if (isPhoneSelected)*@
                                    {
                                        <button data-item-id="@phoneItem.Id" type="submit" class="btn btn-xs btn-primary btn-set-ref-doc-rep-phone" @disabledPhone>@btnTextPhone</button>@* *@
                                    }

                            </td>
                        </tr>

            </tbody>
        </table>
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)
