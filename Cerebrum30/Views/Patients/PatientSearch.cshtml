@using Cerebrum.ViewModels.Patient
﻿@{

@functions{
    public string GetSearchText(int searchType)
    {

        {
// return "Name"; 
        }

        {
@* return "OHIP"; *@

        {
@* return "Phone"; *@

        {
@* return "Patient Id"; *@

        {
@* return "DOB"; *@

        else
        {
@* return ""; *@

@section customcss{
    <link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
    <link href="~/Areas/Schedule/Content/appointments-modal.css" rel="stylesheet" />

@section scripts{
    <script src="~/Scripts/patient-search-by-practice.js"></script>
    <script src="~/Areas/Schedule/Scripts/appointments.js"></script>

<input type="hidden" id="hdPatientSearch" value="@ViewBag.PatientSearch" />
<input type="hidden" id="hdPracticeId" value="@ViewBag.PracticeId" />
<input type="hidden" id="hdSearchType" value="@searchType" />
<div class="row">
    <div class="col-md-6">
        <h3>Patient Search<i>&nbsp;@searchFor for @ViewBag.PatientSearch</i></h3>
    </div>
    <div class="col-md-6 text-right">
        <a class="btn btn-default btn-sm btn-add-patient btn-h3-top-align"><span class="default-text-color"> </span> Add Patient</a>
    </div>
</div>
<div class="panel panel-info">
    <div class="panel-heading">Results</div>
    <div id="divPatientSearchByPractice">
        <div class="col-md-12 text-center">
            <img src="~/Content/Images/loading.gif" style="width:20px;padding-top:6px;" />
        </div>
    </div>
</div>
<br /><br />
