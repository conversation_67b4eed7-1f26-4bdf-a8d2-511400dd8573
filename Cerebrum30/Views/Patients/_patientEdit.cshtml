﻿@model Cerebrum.ViewModels.Patient.VMPatientDemo

@using (Html.BeginForm("Edit", "Patients", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @id = "frm-edit-patient" }))
@using Cerebrum.ViewModels.Patient
{

    @Html.ModalHeader(patientFullName + "<span class='green' style='display:inline-block;margin-left:200px;font-weight:700;'>" + @Model.message ?? "" + "</span>")
    <style>
        .text-danger {
            /* font-size: 10px; */ display: block;

        .padding_lr_0 {
/* padding-right: 0px; */
/* padding-left: 0px; */

        .showHand {
@* cursor: pointer; *@

            .showHand:hover {
@* text-decoration: underline; *@

        .width_90 {
@* width: 90px; *@

        .width_60 {
@* width: 60px; *@

        .popover {
@* z-index: 1060; *@

        .popover-content {
@* padding: 0px; *@
@* font-size: 12px; *@

        .w_border {
@* background-color: #ddd; *@
@* border: none; *@

        .dem_addr1:hover {
@* background-color: #fff !important; *@

        #notesId {
@* width: 190px; *@

        .contactPurpos {
@* background-color: rgb(162, 191, 203) !important; *@
            border-width: 0px

        .contactPurpos_ {
@* background-color: #d3d3d3 !important; *@

    </style>
    <style>
        .required {
@* color: #a94442; *@

        .inline {
@* display: inline-block; *@

        .displayBlock {
/* display: block; */

@* margin: 0px !important; *@

        .modal-footer2 {
@* padding-top: 15px; *@
@* border-top: 1px solid #e5e5e5; *@

        #Notes {
@* min-height: 70px !important; *@

        .modal-body table, .modal-content table {
@* border: 3px solid #ced2ca !important; *@

    </style>

    <div class="modal-body">

        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.PracticeId)
        @Html.HiddenFor(model => model.PatientRecordId)
        @Html.HiddenFor(model => model.FamDoctorId)
        @Html.HiddenFor(model => model.ReferralDoctorId)
        @Html.HiddenFor(model => model.AppointmentId)
        @Html.HiddenFor(model => model.AppointmentReferralDoctorId)
        @Html.HiddenFor(model => model.AssociatedDoctorId)
        @Html.HiddenFor(model => model.MainDoctorId)
        @Html.HiddenFor(model => model.IsMainDoctorNew)
        @Html.HiddenFor(model => model.OldOhipForUpdate)
        @Html.HiddenFor(model => model.Kin_phone_hid)
        @Html.HiddenFor(model => model.NXK_Save_Type)
        @Html.HiddenFor(model => model.PH_Save_Type)
        @Html.HiddenFor(model => model.EnrolledDocName)
        @Html.HiddenFor(m => m.activeOld)
        @Html.HiddenFor(m => m.additionalInfo)
        @Html.HiddenFor(m => m.uniqueVendorIdSequence)

        <div class="row">
            <div class="col-sm-12"><div class="form-group form-group-sm col-sm-12">All fields marked with an asterisk (@*) are required</div></div>*@
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.SalutationId)"> @Html.DisplayNameFor(model => model.SalutationId) </label>
                    @Html.DropDownListFor(model => model.SalutationId, new SelectList(ViewBag.LKSalutations, "Value", "Text", @Model.SalutationId), new { @class = "form-control salutation" })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label highlight" title="@Html.DisplayNameFor(model => model.LastName)"> @Html.DisplayNameFor(model => model.LastName) @*</label>*@
                    @Html.EditorFor(model => model.LastName, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label highlight" title="@Html.DisplayNameFor(model => model.FirstName)"> @Html.DisplayNameFor(model => model.FirstName) *</label>
                    @Html.EditorFor(model => model.FirstName, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.MiddleName)"> @Html.DisplayNameFor(model => model.MiddleName) </label>
                    @Html.EditorFor(model => model.MiddleName, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PreferredName)"> @Html.DisplayNameFor(model => model.PreferredName) </label>
                    @Html.EditorFor(model => model.PreferredName, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Alias)"> @Html.DisplayNameFor(model => model.Alias) </label>
                    @Html.EditorFor(model => model.Alias, new { htmlAttributes = new { @class = "form-control" } })

                </div>
                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label displayBlock">&nbsp; </label>
                    @Html.EditorFor(model => model.UseAliases) <span class="checkbox-text">@Html.DisplayNameFor(model => model.UseAliases)</span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label"> @Html.DisplayNameFor(model => model.DateOfBirth) (mm/dd/yyyy)</label>
                    @Html.EditorFor(model => model.DateOfBirth, new { htmlAttributes = new { @class = "form-control dob_clas" } })
                </div>
                <div class="grp">
                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Ohip)"> @Html.DisplayNameFor(model => model.Ohip) </label>&nbsp;&nbsp;&nbsp;&nbsp;
                        <label>@Html.EditorFor(model => model.skipOHIPCheck) <span class="checkbox-text">@Html.DisplayNameFor(model => model.skipOHIPCheck)</span></label>
                        <br />
                        @Html.EditorFor(model => model.Ohip, new { htmlAttributes = new { @class = "form-control text-box single-line", maxlength = "10" } })
                    </div>

                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Version)">@Html.DisplayNameFor(model => model.Version)</label>
                        @Html.EditorFor(model => model.Version, new { htmlAttributes = new { @class = "form-control custom-input-xs" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ProvinceHC_Id)">@Html.DisplayNameFor(model => model.ProvinceHC_Id)</label>
                        @Html.DropDownListFor(model => model.ProvinceHC_Id, new SelectList(ViewBag.LKProvincesHC, "Value", "Text", @Model.ProvinceHC_Id), new { @class = "form-control provinceHC" })
                    </div>

                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.IssueDate)">@Html.DisplayNameFor(model => model.IssueDate)</label>
                        @Html.EditorFor(model => model.IssueDate, new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    </div>

                    <div class="form-group form-group-sm col-sm-2">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ValidDate)">@Html.DisplayNameFor(model => model.ValidDate)</label>
                        @Html.EditorFor(model => model.ValidDate, new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    </div>

                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.ExpiryDate)">@Html.DisplayNameFor(model => model.ExpiryDate)</label>
                        @Html.EditorFor(model => model.ExpiryDate, new { htmlAttributes = new { @class = "form-control custom-input-sm date-picker" } })
                    </div>
                    <div class="form-group form-group-sm col-sm-1">
                        <label class="control-label" title="">&nbsp;</label>
                        <button style="margin-left:5px;margin-top:2px;" class="btn btn-xs btn-default checkOHIPClk" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="OHIP CHECK" href="#">OHIP CHECK</button>

                    </div>
                </div>

            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.GenderId)"> @Html.DisplayNameFor(model => model.GenderId) </label>
                    @Html.DropDownListFor(model => model.GenderId, new SelectList(ViewBag.LKGenders, "Value", "Text", @Model.GenderId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.RMB)"> @Html.DisplayNameFor(model => model.RMB) </label>
                    @Html.EditorFor(model => model.RMB, new { htmlAttributes = new { @class = "form-control rmbHC" } })
                </div>
                <div class="form-group form-group-sm col-sm-1">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.statusDate_)"> @Html.DisplayNameFor(model => model.statusDate_) </label>
                    @Html.DisplayFor(model => model.statusDate_)
                </div>

                <div class="form-group form-group-sm col-sm-1">
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.ActiveId)"> @Html.DisplayNameFor(model => model.ActiveId) </label>
                    @Html.DropDownListFor(model => model.ActiveId, new SelectList(ViewBag.LKActives, "Value", "Text", @Model.ActiveId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.SIN)"> @Html.DisplayNameFor(model => model.SIN) </label>
                    @Html.EditorFor(model => model.SIN, new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    @*<label class="control-label" title="@Html.DisplayNameFor(model => model.ActiveId)"> @Html.DisplayNameFor(model => model.ActiveId) </label>* @if (cerebrumUser.HasPermission("Connecting Ontario"))
                    {
                        <button style="margin-left:5px;margin-top:20px;" class="btn btn-xs btn-default btn-connecting-ontario" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Connecting Ontario" href="#" data-patient-record-id="@Model.PatientRecordId" data-url="@Url.Action("Form", "ConnectingOntario", new { area = "ExternalComm" })">Connecting Ontario</button>
                    }
                    <div>@Html.DisplayFor(model => model.additionalInfo)</div>
                    <div>@Html.DisplayFor(model => model.uniqueVendorIdSequence)</div>
                </div>

            </div>
        </div>

        <hr />

        <div class="row">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.MainDoctor)"> @Html.DisplayNameFor(model => model.MainDoctor) </label>
                    @Html.EditorFor(model => model.MainDoctor, new { htmlAttributes = new { @class = "form-control demographic-doctors" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock">&nbsp; </label>
                    <button style="width:80px; margin-left:5px;margin-top:2px;@(@Model.IsPatientHasEnroll == true ? "Background-color:#ccc" : "")" class="btn btn-xs btn-default enrlClk" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Enroll" href="#">Enroll</button>
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock">&nbsp; </label>
                    @Html.DisplayFor(model => model.EnrolledDocName)
                </div>

                <div class="form-group form-group-sm col-sm-2">

                </div>

                <div class="form-group form-group-sm col-sm-2">

                </div>

                <div class="form-group form-group-sm col-sm-2">

                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.FamDoctor)"> @Html.DisplayNameFor(model => model.FamDoctor) (~)</label>
                    @Html.EditorFor(model => model.FamDoctor, new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                </div>
                <div class="form-group form-group-sm col-sm-2"> </div>
                <div class="form-group form-group-sm col-sm-2">

                        @Html.EditorFor(model => model.ReferralDoctor, new { htmlAttributes = new { @class = "form-control  demographic-doctors" } }) </div>
                <div class="form-group form-group-sm col-sm-2"> </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.AssociatedDoctor)"> @Html.DisplayNameFor(model => model.AssociatedDoctor) (^)</label>
                    @Html.EditorFor(model => model.AssociatedDoctor, new { htmlAttributes = new { @class = "form-control  demographic-doctors" } })
                </div>
                <div class="form-group form-group-sm col-sm-2"> </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-4">
                    <table class="table" id="fmList" name="fmList" style="width:100%">
                        <thead>
                            <tr class="vertical-center">
                                <th width="70%">Family Doctor Name (~)</th>
                                <th width="10%"></th>
                                <th width="10%">Activate</th>
                                <th width="10%">Deactive</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model != null && @Model.familyDocList != null && @Model.familyDocList.Count > 0)
                            {*@ for (int i = 0; i < @Model.familyDocList.Count; i++)
                                {
                                    <tr>
                                        <td class="fm_List btn-edit-ext-doctor" data-modal-url=@("/externaldoctors/edit?extDoctorId=" + @Model.familyDocList[i].Id)>
                                            <a class="showHand" title="@Html.DisplayFor(model => model.familyDocList[i].text)">@Html.DisplayFor(model => model.familyDocList[i].text)</a>
                                            @Html.HiddenFor(model => model.familyDocList[i].Id, new { @id = @Model.familyDocList[i].Id })

                                        </td>
                                        <td>
                                            <span data-external-doc-id="@Model.familyDocList[i].Id"

                                        </td>
                                        <td class="fm_List">
                                            @Html.CheckBoxFor(model => model.familyDocList[i].isActive)
                                            @Html.HiddenFor(model => model.familyDocList[i].Id, new { @id = @Model.familyDocList[i].Id, @class = "fm_List_class" })
                                        </td>
                                        <td class="fm_List">
                                            @Html.CheckBoxFor(model => model.familyDocList[i].isRemoved)
                                            @Html.HiddenFor(model => model.familyDocList[i].Id, new { @id = @Model.familyDocList[i].Id, @class = "fm_List_class_rem" })
                                        </td>

                                    </tr>

                        </tbody>
                    </table>
                </div>
                <div class="form-group form-group-sm col-sm-4">
                    <table class="table" id="refList" name="refList" style="width:100%">

                        <thead>
                            <tr class="vertical-center" style="background-color:#dddddd;">
                                <th width="80%">Referring Doctor</th>
                                <th width="10%"></th>
                                <th width="10%">Activate</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && @Model.refDocList != null && @Model.refDocList.Count > 0)
                            {

                                {

                                    <tr>
                                        <td>
                                            <a class="showHand ref_List btn-edit-ext-doctor" data-modal-url=@("/externaldoctors/edit?extDoctorId=" + @Model.refDocList[i].Id)>@Html.DisplayFor(model => model.refDocList[i].text)</a>
                                            @Html.HiddenFor(model => model.refDocList[i].Id, new { @id = @Model.refDocList[i].Id })

                                        </td>
                                        <td>
                                            <span data-external-doc-id="@Model.refDocList[i].Id"

                                        </td>
                                        <td>
                                            @Html.CheckBoxFor(model => model.refDocList[i].isActive, new { @id = @Model.refDocList[i].Id, @class = "refDocActivate" })
                                        </td>
                                    </tr>

                        </tbody>

                    </table>

                </div>
                <div class="form-group form-group-sm col-sm-4">
                    <table class="table" id="assList" name="assList" style="width:100%">
                        <thead>
                            <tr class="vertical-center">
                                <th width="70%">Associated Doctor Name (^)</th>
                                <th width="10%"></th>
                                <th width="10%">Activate</th>
                                <th width="10%">Deactive</th>

                            </tr>
                        </thead>
                        <tbody>
                            @if (Model != null && @Model.assocDocList != null && @Model.assocDocList.Count > 0)
                            {

                                {
                                    <tr>
                                        <td class="ass_List btn-edit-ext-doctor" data-modal-url=@("/externaldoctors/edit?extDoctorId=" + @Model.assocDocList[i].Id)>
                                            <a class="showHand" title="@Html.DisplayFor(model => model.assocDocList[i].text)">
                                                @Html.DisplayFor(model => model.assocDocList[i].text)
                                            </a>
                                            @Html.HiddenFor(model => model.assocDocList[i].Id, new { @id = @Model.assocDocList[i].Id })

                                        </td>
                                        <td>
                                            <span data-external-doc-id="@Model.assocDocList[i].Id"

                                        </td>
                                        <td class="ass_List">
                                            @Html.CheckBoxFor(model => model.assocDocList[i].isActive)
                                            @Html.HiddenFor(model => model.assocDocList[i].Id, new { @id = @Model.assocDocList[i].Id, @class = "ass_List_class" })
                                        </td>
                                        <td class="ass_List">
                                            @Html.CheckBoxFor(model => model.assocDocList[i].isRemoved)
                                            @Html.HiddenFor(model => model.assocDocList[i].Id, new { @id = @Model.assocDocList[i].Id, @class = "ass_List_class_rem" })
                                        </td>
                                    </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <hr />

        <div class="row">
            <!-- phone-->
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-12">
                    <!-- 00000000000000000000000000000000000 -->
                    <table class="table pListClass" id="phList" name="phList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center">
                                <th width="20%">Phone <a class="showHand p-phone-new" title="Add New Phone" style="color: #337ab7 !important">Add New</a></th>
                                <th width="10%">Ext</th>
                                <th width="15%">Phone Type</th>
                                <th width="15%">Notes</th>
                                <th width="10%"></th>
                                <th width="10%"></th>
                                <th width="10%">Primary</th>
                                <th width="10%">Action</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && @Model.phoneList != null && @Model.phoneList.Count > 0)
                            {

                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.phoneList[i].phone)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.phoneList[i].ext)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.phoneList[i].phoneType)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.phoneList[i].note)
                                        </td>
                                        <td class="pList"></td>
                                        <td class="pList"></td>
                                        <td class="pList is-active">
                                            @Html.DisplayFor(model => model.phoneList[i].isActive)
                                        </td>
                                        <td class="pList">
                                            <a id="<EMAIL>[i].phoneId" class="showHand p-phone-edit" title="Edit Phone" style="color: #337ab7 !important" data-edit-phone='{"phoneId":"@Model.phoneList[i].phoneId", "phone":"@Model.phoneList[i].phone","phoneNumberTypeId":"@Model.phoneList[i].phoneNumberTypeId","ext":"@Model.phoneList[i].ext","note":"@Model.phoneList[i].note"}'>Edit</a>
                                            @Html.HiddenFor(model => model.phoneList[i].phoneNumberTypeId, new { value = @Model.phoneList[i].phoneNumberTypeId, @class = "p-phone-type-id" })
                                            @Html.HiddenFor(model => model.phoneList[i].ext, new { value = @Model.phoneList[i].ext, @class = "p-phone-ext" })
                                            @Html.HiddenFor(model => model.phoneList[i].phone, new { value = @Model.phoneList[i].phone, @class = "p-phone-phone" })
                                            @Html.HiddenFor(model => model.phoneList[i].note, new { value = @Model.phoneList[i].note, @class = "p-note-note" })
                                            @Html.HiddenFor(model => model.phoneList[i].phoneId, new { value = @Model.phoneList[i].phoneId, @class = "p-phone-id" })
                                        </td>
                                    </tr>

                            <tr class="p_editRow" @(!@Model.showPhoneDiv ? "hidden" : "") style="background-color: #a2bfcb !important">
                                <td class="pList">
                                    <input style="width: 100%;" class="form-control width_90 p_phone_class fax_p_p" id="p_phone_" type="text" />
                                    <input class="p_id_class" id="p_phone_id" type="hidden" />
                                </td>
                                <td class="pList">
                                    <input style="width: 100%;" id="p_ext_id" type="text" class="form-control custom-input-xs" />
                                </td>
                                <td class="pList">
                                    @Html.DropDownListFor(model => model.P_PhoneTypeId, new SelectList(ViewBag.LKP_PhoneTypes, "Value", "Text", @Model.P_PhoneTypeId), new { @class = "form-control", height = "21px !important" })
                                </td>
                                <td class="pList">
                                    <input style="width: 100%;" id="p_note_" type="text" class="form-control" />
                                </td>
                                <td class="pList" style="text-align: right; padding-top:8px">Delete</td>
                                <td class="pList" style="padding-top:6px">
                                    <input type="checkbox" id="checkBoxRemId" />
                                </td>
                                <td class="pList" style="padding-top:6px">
                                    <input type="checkbox" id="checkBoxPrimId" />
                                </td>
                                <td class="pList er_save" style="padding-top:8px">
                                    <a id="p_editDivId" class="showHand p-edit-row-save" style="color: #337ab7 !important;">Save</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <!-- 000000000000000000000000000000000 -->
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Email)"> @Html.DisplayNameFor(model => model.Email) </label>
                    @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control" } })
                </div>

                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label displayBlock" title="@Html.DisplayNameFor(model => model.ConsentEmailId)">@Html.DisplayNameFor(model => model.ConsentEmailId) </label>
                    @Html.DropDownListFor(model => model.ConsentEmailId, new SelectList(ViewBag.LKYesNo, "Value", "Text", @Model.ConsentEmailId), new { @class = "form-control custom-input-xs" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <br />
                    <label>@Html.CheckBoxFor(model => model.noEmail, @Model.noEmail) <span class="checkbox-text">@Html.DisplayNameFor(model => model.noEmail)</span></label>
                    @Html.HiddenFor(model => model.hasEmail, true)
                </div>
                <div class="form-group form-group-sm col-sm-2">
                </div>

                <div class="form-group form-group-sm col-sm-2">

                </div>
                <div class="form-group form-group-sm col-sm-2">

                </div>
            </div>
        </div>

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-12">
                    <!-- NOK -->
                    <div id="show_NXK_Div" class="form-group form-group-sm">
                        <table class="table conListClass" id="conList" name="conList">
                            <thead>
                                <tr class="vertical-center" style="background-color:#ddd;">
                                    <th width="15%">Contact Purpose <span class="showHand row-con-new btn-info-7" title="New Contact" style="color: #337ab7 !important;">Add New</span></th>
                                    <th width="15%">Name</th>
                                    <th width="@*%">Notes</th>*@
                                    <th width="10%">E-mail</th>
                                    <th width="10%">Phone</th>
                                    <th width="1%"></th>
                                    <th width="1%"></th>
                                    <th width="5%">Type</th>
                                    <th width="3%">P</th>
                                    <th width="12%" colspan="2">D <div class="showHand row-con-new btn-info-7" title="New Contact">New</div></th>
                                </tr>
                            </thead>
                            <tbody>

                                @if (Model != null && @Model.nxksList != null && @Model.nxksList.Count > 0)
                                {
*@ for (int i = 0; i < @Model.nxksList.Count; i++)
                                    {

                                        <tr>
                                            <td class="cList">
                                                @Html.DisplayFor(model => model.nxksList[i].purpose)
                                                @Html.HiddenFor(model => model.nxksList[i].purpose, new { value = @Model.nxksList[i].purpose, @class = "row-cont-purpose" })
                                                @Html.HiddenFor(model => model.nxksList[i].id, new { value = @Model.nxksList[i].id, @class = "row-cont-id" })
                                                @Html.HiddenFor(model => model.nxksList[i].purposeEnumId, new { value = @Model.nxksList[i].purposeEnum, @class = "row-purpose-id" })
                                            </td>
                                            <td class="cList n-ame">
                                                @Html.DisplayFor(model => model.nxksList[i].name)
                                                @Html.HiddenFor(model => model.nxksList[i].name, new { value = @Model.nxksList[i].name })
                                            </td>
                                            <td class="cList">
                                                @Html.DisplayFor(model => model.nxksList[i].notes)
                                                @Html.HiddenFor(model => model.nxksList[i].notes, new { value = @Model.nxksList[i].notes })
                                            </td>
                                            <td class="cList">
                                                @Html.DisplayFor(model => model.nxksList[i].emailAddress)
                                                @Html.HiddenFor(model => model.nxksList[i].emailAddress, new { value = @Model.nxksList[i].emailAddress })
                                            </td>
                                            <td class="cList">
                                                <div style="float:left;" class="div_for_phid">
                                                    @Html.DisplayFor(model => model.nxksList[i].phone)
                                                    @Html.HiddenFor(model => model.nxksList[i].phone, new { value = @Model.nxksList[i].phone, @class = "row-ph-number" })
                                                    @Html.HiddenFor(model => model.nxksList[i].phoneId, new { value = @Model.nxksList[i].phoneId, @class = "row-ph-id" })
                                                </div>
                                            </td>
                                            <td class="cList">
                                                <div data-toggle="popover" class="showHand abo" style="float:left;margin-left:3px;" @isAllHidden>All</div>
                                                <div class="for_popover" hidden>
                                                    @Html.Raw(asdf)
                                                </div>
                                                <div class="commentPopoverHiddenContent" style="display: none"><div><table border="1" style="width:100%"><tr><th>Number</th><th>Type</th></tr></table></div></div>
                                            </td>
                                            <td class="cList">
                                                <div class="showHand row-phone-new" title="New Phone" style="color: #337ab7 !important;">N</div>
                                                @Html.HiddenFor(model => model.nxksList[i].id, new { @id = @Model.nxksList[i].id, @class = "row-phone-new-id" })
                                            </td>
                                            <td class="cList">
                                                @Html.DisplayFor(model => model.nxksList[i].phoneType)
                                                @Html.HiddenFor(model => model.nxksList[i].phoneTypeEnumId, new { value = @Model.nxksList[i].phoneTypeEnum, @class = "row-ph-type-id" })
                                            </td>
                                            <td class="cList">
                                                @Html.DisplayFor(model => model.nxksList[i].IsActive)
                                                @Html.HiddenFor(model => model.nxksList[i].id, new { @id = @Model.nxksList[i].id })
                                            </td>
                                            <td class="cList"></td>
                                            <td class="cList">
                                                <div class="showHand row-con-edit" title="Edit Contact" style="color: #337ab7 !important;">Edit</div>
                                                @Html.HiddenFor(model => model.nxksList[i].id, new { @id = @Model.nxksList[i].id, @class = "row-con-edit-id" })
                                            </td>
                                        </tr>

                                <tr class="editRow" @(!@Model.showNXKDiv ? "hidden" : "") style="background-color: #a2bfcb !important">
                                    <td class="cList">
                                        <input type="hidden" id="contactIdForSend" />
                                        @Html.DropDownListFor(model => model.contactPurposeId, new SelectList(ViewBag.LKContactPurposes, "Value", "Text", @Model.contactPurposeId), new { @class = "form-control", height = "21px !important" })
                                        <span id="contactPur" class="form-control contactPurpos"></span>
                                    </td>
                                    <td class="cList">

                                    </td>
                                    <td class="cList">
                                        <textarea id="notesId" rows="1" cols="40" class="form-control text-box single-line"></textarea>
                                    </td>
                                    <td class="cList">
                                        <input id="emailId" type="text" class="form-control text-box single-line" />
                                    </td>
                                    <td class="cList">
                                        <input type="hidden" id="phoneIdForSend" />

                                    </td>
                                    <td class="cList"></td>
                                    <td class="cList">
                                        <input type="checkbox" id="checkBoxPhPrId" />
                                    </td>
                                    <td class="cList">
                                        @Html.DropDownListFor(model => model.PhoneTypeId, new SelectList(ViewBag.LKPhoneTypes, "Value", "Text", @Model.PhoneTypeId), new { @class = "form-control", height = "21px !important" })
                                    </td>
                                    <td class="cList">
                                        <input type="checkbox" id="checkBoxId" />
                                    </td>
                                    <td class="cList">
                                        <input type="checkbox" id="checkBoxRemId" />
                                    </td>
                                    <td class="cList er_save">
                                        <div id="editDivId" class="showHand edit-row-save" title="Save" style="color: #337ab7 !important;">Save</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- /NOK -->
                </div>

            </div>
        </div>

        <hr />

        <div class="row">
            <!-- address -->
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-12">
                    <!-- 00000000000000000000000000000000000 -->
                    <table class="table a_ListClass" id="a_hList" name="a_hList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center">
                                <th width="40%">Address <a class="showHand dem_addr1" title="Add New / Edit" style="color: #337ab7 !important; font-weight: bold">Add New / Edit</a></th>
                                <th width="10%">City</th>
                                <th width="15%">Address Type</th>
                                <th width="15%">Postal Code</th>
                                <th width="10%">Province</th>
                                <th width="10%">Country</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && @Model.addressesList != null && @Model.addressesList.Count > 0)
                            {

                                {
                                    <tr id="<EMAIL>[i].id" class="p_editRow_r">
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.addressesList[i].name)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.addressesList[i].name4)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.addressesList[i].value)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.addressesList[i].name1)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.addressesList[i].name2)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.addressesList[i].name3)
                                        </td>
                                        <td class="pList">
                                            @{

                                            }
                                            <a class="showHand dem_addr1_active" title="Active" style="color: #337ab7 !important; font-weight: bold" data-isactive="@Convert.ToString(isactive).ToLower()" data-demo-address-id="@Model.addressesList[i].id" data-demo-id="@Model.Id" data-patient-id="@Model.PatientRecordId">@activeLable</a>
                                        </td>
                                    </tr>

                        </tbody>
                    </table>
                    <!-- 00000000000000000000000000000000000 -->
                </div>
            </div>
        </div>

        <hr />
        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PreferedLanguageId)"> @Html.DisplayNameFor(model => model.PreferedLanguageId) </label>
                    @Html.DropDownListFor(model => model.PreferedLanguageId, new SelectList(ViewBag.LKLanguagesTb, "Value", "Text", @Model.PreferedLanguageId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.OfficialLanguageId)"> @Html.DisplayNameFor(model => model.OfficialLanguageId) </label>
                    @Html.DropDownListFor(model => model.OfficialLanguageId, new SelectList(ViewBag.LKLanguagesTbO, "Value", "Text", @Model.PreferedLanguageId), new { @class = "form-control" })

                        @Html.EditorFor(model => model.chartNumber, new { htmlAttributes = new { @class = "form-control" } }) </div>
                <div class="form-group form-group-sm col-sm-2">

                    <label class="control-label" title="@Html.DisplayNameFor(model => model.PaymentMethodId) "> @Html.DisplayNameFor(model => model.PaymentMethodId) </label>
                    @Html.DropDownListFor(model => model.PaymentMethodId, new SelectList(ViewBag.LKPaymentMethods, "Value", "Text", @Model.PaymentMethodId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.InsuranceKindId) "> @Html.DisplayNameFor(model => model.InsuranceKindId) </label>
                    @Html.DropDownListFor(model => model.InsuranceKindId, new SelectList(ViewBag.LKInsuranceTypes, "Value", "Text", @Model.InsuranceKindId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.InsuranceCompanyId)"> @Html.DisplayNameFor(model => model.InsuranceCompanyId) </label>
                    @Html.DropDownListFor(model => model.InsuranceCompanyId, new SelectList(ViewBag.LKInsurances, "Value", "Text", @Model.InsuranceCompanyId), new { @class = "form-control" })
                </div>
                <div class="form-group form-group-sm col-sm-2">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.chartNumber)"> @Html.DisplayNameFor(model => model.chartNumber) </label>
                    @Html.EditorFor(model => model.chartNumber, new { htmlAttributes = new { @class = "form-control" } })
                </div>

            </div>
        </div>

        <hr />

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-4">
                    <div class="content-height200">
                        <table class="table" id="mrnList" name="mrnList" style="font-size: 12px;">
                            <thead>
                                <tr class="vertical-center" style="background-color:#dddddd;">
                                    <th width="20%">Hospital</th>
                                    <th width="40%">MRN</th>
                                    <th width="10%"></th>
                                </tr>
                            </thead>

                            <tbody>
                                @if (Model != null && @Model.mrnList != null && @Model.mrnList.Count > 0)
                                {*@ for (int i = 0; i < @Model.mrnList.Count; i++)
                                    {
                                        <tr>
                                            <td class="mrn_List row-mrn-stop">
                                                <div class="row-mrn-stop">@Html.DisplayFor(model => model.mrnList[i].text)</div>
                                            </td>
                                            <td class="mrn_List td_mrnClass">
                                                @Html.EditorFor(model => model.mrnList[i].value, new { htmlAttributes = new { @value = @Model.mrnList[i].Id, @class = "mrnClass w_border" } })
                                            </td>
                                            <td class="mrn_List">
                                                <div class="showHand row-mrn-save" title="Save MRN">Save</div>
                                                @Html.HiddenFor(model => model.mrnList[i].Id, new { @id = @Model.mrnList[i].Id })
                                            </td>
                                        </tr>

                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="form-group form-group-sm col-sm-8" style="padding: 0">

                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.FederatedId)"> @Html.DisplayNameFor(model => model.FederatedId) </label>
                        @Html.EditorFor(model => model.FederatedId, new { htmlAttributes = new { @class = "form-control" } })
                    </div>

                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.FaxPharmacy)"> @Html.DisplayNameFor(model => model.FaxPharmacy) </label>
                        @Html.EditorFor(model => model.FaxPharmacy, attributesWithFax_p_p)
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.PhonePharmacy) "> @Html.DisplayNameFor(model => model.PhonePharmacy) </label>
                        @Html.EditorFor(model => model.PhonePharmacy, attributesWithFax_p_p)
                    </div>

                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.FavoritePharmacy)"> @Html.DisplayNameFor(model => model.FavoritePharmacy) </label>
                        @Html.EditorFor(model => model.FavoritePharmacy, attributes)
                    </div>

                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_Address)">@Html.DisplayNameFor(model => model.Phar_Address)</label>
                        @Html.EditorFor(model => model.Phar_Address, attributes)
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_City)">@Html.DisplayNameFor(model => model.Phar_City)</label>
                        @Html.EditorFor(model => model.Phar_City, attributes)
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_CountryId) ">@Html.DisplayNameFor(model => model.Phar_CountryId)</label>
                        @Html.DropDownListFor(model => model.Phar_CountryId, new SelectList(ViewBag.LKPhar_Country, "Value", "Text", @Model.Phar_CountryId), attributesDisabled)
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_ProvinceId)">@Html.DisplayNameFor(model => model.Phar_ProvinceId)</label>
                        @Html.DropDownListFor(model => model.Phar_ProvinceId, new SelectList(ViewBag.LKPhar_Province, "Value", "Text", @Model.Phar_ProvinceId), attributesDisabled)
                    </div>

                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_PostalCode)">@Html.DisplayNameFor(model => model.Phar_PostalCode)</label>
                        @Html.EditorFor(model => model.Phar_PostalCode, attributes)
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_AddressTypeId)">@Html.DisplayNameFor(model => model.Phar_AddressTypeId)</label>
                        @Html.DropDownListFor(model => model.Phar_AddressTypeId, new SelectList(ViewBag.LKPharmAddressTypes, "Value", "Text", @Model.Phar_AddressTypeId), attributesDisabled)
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label" title="@Html.DisplayNameFor(model => model.Phar_Email)">@Html.DisplayNameFor(model => model.Phar_Email)</label>
                        @Html.EditorFor(model => model.Phar_Email, attributes)
                    </div>
                    <div class="col-sm-3">
                    </div>

                </div>

            </div>
        </div>

        <hr />

        <div class="row" style="background-color:#fff">
            <div class="col-sm-12">
                <div class="form-group form-group-sm col-sm-6">
                    <label class="control-label" title="@Html.DisplayNameFor(model => model.Notes)"> @Html.DisplayNameFor(model => model.Notes) </label>
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control ", @rows = 3 })
                </div>

                <div class="form-group form-group-sm col-sm-6">
                    <label class="control-label addPatCohort" title="Cohort">Cohort</label>
                    <span id="addCohort" class="showHand row-con-new addPatCohort" title="Add New" style="color: #337ab7 !important; font-weight: bold">Add New</span>
                    <div class="col-md-4" id="cohortsList" style=" width:350px;height:65px;background-color:antiquewhite; overflow: auto;">
                        @if (@Model.addedCohorts != null)
                        { *@ for (int i = 0; i < @Model.addedCohorts.Count; i++) {
                                <div class="row">
                                    <div class="col-md-1 ch_t" id="@Model.addedCohorts[i].Value">
                                        <input type="checkbox" class="<EMAIL>[i].Value" checked />
                                    </div>
                                    <div class="col-md-9">
                                        @Html.LabelFor(m => m.addedCohorts[i].Text, @Model.addedCohorts[i].Text, new { })
                                    </div>
                                </div>

                    </div>
                </div>

            </div>
        </div>

        <div class="form-group">
            <div class="col-md-12 text-center green">

            </div>
        </div>
    </div>

    <div class="modal-footer">
        <div class="col-sm-9">
            <div class="text-danger" style="text-align: left; font-size: 13px !important; padding-top: 6px;">
                @*VALIDATION CONTAINER*@
                @Html.ValidationMessageFor(model => model.SalutationId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PreferredName, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Alias, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.UseAliases, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.DateOfBirth, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.skipOHIPCheck, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Ohip, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Version, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.IssueDate, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ValidDate, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ExpiryDate, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.GenderId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.RMB, "", new { @class = "text-danger" })

                @Html.ValidationMessageFor(model => model.MainDoctor, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ConsentEmailId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PreferedLanguageId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PaymentMethodId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.InsuranceCompanyId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.chartNumber, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FederatedId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FaxPharmacy, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.PhonePharmacy, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.FavoritePharmacy, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.ActiveId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="col-sm-3">
            <div>
                <button type="submit" class="btn btn-default btn-sm btn-primary submitBtn-edit-ptn">Update Patient</button>
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div>@*>*
    <script>
        $(document).ready(function () {

                html: true,
                placement: 'right',
                content: function () {
@* return $('.commentPopoverHiddenContent').html(); *@
                }
@* }); *@

            // value is True or False, 
            // !hasEmail or hasEmail===False did not work 

@* $("#Email").val(''); *@
@* $("#Email").attr('disabled', noEmail); *@

@* }); *@
    </script>

