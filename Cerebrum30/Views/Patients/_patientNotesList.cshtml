@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.Patient.VMPatientNote>

@{ 

}

<div class="panel panel-info">
    <div class="panel-heading">Total Notes (@totalNotes)</div>
    <table id="tbl-patient-additional-notes" class="table table-bordered">
        <tr>       
            <th >
                @Html.DisplayNameFor(model => model.PatientNoteType)
            </th>    
            <th>
                @Html.DisplayNameFor(model => model.NoteDate)
            </th>   
            <th style="width:85%;">
                @Html.DisplayNameFor(model => model.Notes)
            </th>                         
            <th></th>
        </tr>
        @foreach (var item in Model) {
            <tr>                    
                <td>
                    @Html.DisplayFor(modelItem => item.PatientNoteType)
                </td>
                <td>
                    @if (item.NoteDate != null)
                    {
                        <span>@item.NoteDate.Value.ToShortDateString()</span>
                    }
                </td>
                <td>

                    @Html.Raw(System.Web.System.Net.WebUtility.HtmlDecode(item.Notes))
                </td>
                <td>
                   
                </td>                    
               
            </tr>

    </table>
</div>

<div style="margin-bottom:65px;"></div>