@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.RadDicom.VMRadExam>

@if (Model != null && @Model.Count() > 0)
{
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Id)
        </th>
       
        <th>
            @Html.DisplayNameFor(model => model.AccessionNumber)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.TestName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PatientFullName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PatientDOB)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.StudyDate)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.ImageCount)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model)
{
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.Id)
        </td>
        <td>
            <a href="/schedule/daysheet?OfficeId=@item.OfficeId&scheduleTypeId=3&Date=@item.appointmentDateTime" target="_blank">@item.AccessionNumber</a>
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.TestName)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.PatientFullName)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.PatientDOB)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.StudyDate)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.ImageCount)
        </td>
        <td>
            <button class="btn btn-default btn-sm rad-dicom-patient-study" data-studyid="@item.Id" data-accession="@item.AccessionNumber" data-radPatientId="@item.PatientId">Select</button>
            <button class="btn btn-default btn-sm search-rad-accession" data-accession-Number="@item.AccessionNumber">Search Appointments</button>
        </td>
    </tr>

</table>

else
{
    <p class="no-patient-found alert-danger">No study found for this patient</p>

