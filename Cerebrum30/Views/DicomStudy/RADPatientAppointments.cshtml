@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.RadDicom.VMRADPatientAppointment>
@if (Model != null && @Model.Count() > 0)
{
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.AccessionNumber)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.TestName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PatientFullName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PatientDOB)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.TestDate)
        </th>
        
        <th></th>
    </tr>

@foreach (var item in Model)
{
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.AccessionNumber)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.TestName)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.PatientFullName)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.PatientDOB)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.TestDate)
        </td>
        <td>
            <button class="btn btn-default patient-appointment-test-reattach" data-appointment-test-Id="@item.AppointmentTestId" data-accession="@item.AccessionNumber" data-Patient-Id="@item.PatientId">Attach To</button>
            <button class="btn btn-default patient-dicom-exam-import" data-appointment-test-Id="@item.AppointmentTestId" data-accession="@item.AccessionNumber" data-Patient-Id="@item.PatientId">Import</button>
        </td>
    </tr>

</table>

else
{
    <p class="no-patient-found alert-danger">No study found for this patient</p>
