﻿@{

}
<script src="~/Scripts/c3-dicom-study.js"></script>

<h2>Manage Dicom Exam</h2>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading">
                <h4> Step 1 : Find DICOM Exam to move</h4>
            </div>
            <div class="panel-body">
                @await Html.PartialAsync("FindDicomExam")
            </div>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading">
                <h4> Step 2 : Find Appointment / Test to move</h4>
            </div>
            <div class="panel-body">
                @await Html.PartialAsync("FindAppointment")
            </div>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading">
                <h4> Step 3 : Attach/Reattach Exam to correct test</h4>
            </div>
            <div class="panel-body">
                @await Html.PartialAsync("AttachReattachDicomExam")
            </div>
        </div>

    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-default ">
            <div class="panel-heading">
                <h4> Step 4 : Import Dicom Measurements</h4>
            </div>
            <div class="panel-body">
                @* Html.RenderPartial("ImportDicomMeasurements"); *@
            </div>
        </div>
    </div>
</div>
<br />
