@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.RadDicom.VMAccessionSearch

<script type="text/javascript">

         $(function() {

             $("#goodpatientsearch").autocomplete({
                 source: function (request, response) {

                     $.ajax({
                         url: searchUrl,
                         type: 'GET',
                         data:
                             {
                                 patientSearch: search
                             },
                         success: function (data) {
@* response(data); *@
                         },
                         error: function (jqXHR, textStatus, errorThrown) {
@* checkAjaxError(jqXHR); *@
                         },
                         complete: function () {

                         },
@* }); *@
                 },
                 minLength: 2,
                 select: function (event, ui) {

@* return false; *@

                      + '</div>'
                     + '</li>'
@* ).appendTo(ul); *@
@* }; *@

@* }); *@

         function clearpatientnamegood() {

         }

</script> 
@* using (Html.BeginForm("FixLostDicomExam", "DICOM", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-inline", @id = "frm-triage-search" }))
{
    @Html.AntiForgeryToken() *@
    <div class="form-group form-group-sm">
        @Html.Label("AccessionNumber", "Accession number", new { @class = "control-label" })
        @Html.EditorFor(model => model.goodaccessionnumber, new { htmlAttributes = new { @class = "form-control" } })
    </div>

    <div class="form-group form-group-sm">
        @Html.Label("AccessionNumber", "Test date", new { @class = "control-label" })
        @Html.EditorFor(model => model.goodexamdate, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>

    <div class="form-group form-group-sm">
        <input name="goodpatientsearch" id="goodpatientsearch" type="text" class="form-control" placeholder="Patient Search " onclick="clearpatientnamegood();w">
        <input name="goodpatientid" id="goodpatientid" type="hidden" />
    </div>

    <div class="form-group form-group-sm">
        @Html.Label("AccessionNumber", "Patient DOB", new { @class = "control-label" })
        @Html.EditorFor(model => model.goodPatDOB, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>

    <input type="hidden" name="accentype" value="goodaccession">
    <button type="submit" style="margin-left: 20px;" class="btn btn-default btn-sm btn-dicom">Search</button>

@*}*@
<div>*@Html.ValidationMessageFor(model => model.DoctorId, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.TriageUrgencyId, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.EndDate, "", new { @class = "text-danger" })* @Html.ValidationMessageFor(model => model.Patient, "", new { @class = "text-danger" }) * @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
</div>