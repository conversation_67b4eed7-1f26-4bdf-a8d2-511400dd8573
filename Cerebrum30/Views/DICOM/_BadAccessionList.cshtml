@using Cerebrum.ViewModels.Patient
﻿@model  IEnumerable<Cerebrum.ViewModels.RadDicom.VMBadAccessionList>
<p></p>

<table  id="badaccessionlisttable"  class="table">
    <tr >
       
        <th>
            @Html.DisplayName("ID")
        </th>

         <th >
            @Html.DisplayName("Accession Number")
        </th>
    
        <th >
            @Html.DisplayName("Patient FirstName")
        </th>

        <th>
            @Html.DisplayName("Patient LastName")
        </th>

        <th >
            @Html.DisplayName("Patient DOB")
        </th>

        <th>
            @Html.DisplayName("Study Date")
        </th>

       <th >
            @Html.DisplayName("Image Count")
        </th>

        <th>
            @Html.DisplayName("Select")
        </th>

    </tr>

    @foreach (var item in Model)
    {
        <tr >
           
            <td>
                @Html.DisplayFor(modelItem => item.id)

            </td>

             <td  >
                @Html.DisplayFor(modelItem => item.AccessionNum)

                </td>
          
            <td >
                @Html.DisplayFor(modelItem => item.PatientFirstName)
            </td>
           
            <td>
                @Html.DisplayFor(modelItem => item.PatientLastName)
            </td>

            <td >
                @Html.DisplayFor(modelItem => item.PatientBD)
            </td>
            <td >
                @Html.DisplayFor(modelItem => item.StudyDate)
            </td>

            <td>
                @Html.DisplayFor(modelItem => item.Image_Count)
            </td>

            <td>
                <a  class="btn btn-info btn-sm">
                    <span class="glyphicon glyphicon-check"></span> Check
                </a>
            </td>

        </tr>

</table>
