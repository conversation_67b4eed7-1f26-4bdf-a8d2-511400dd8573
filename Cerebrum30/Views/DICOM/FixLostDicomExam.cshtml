﻿@model Cerebrum.ViewModels.RadDicom.VMFixAccessionNumber
@{

}

<h2>Manage DICOM Exam</h2>
<script>
$(document).ready(function () {
        $("#badaccessionlisttable td").click(function () {

@* }); *@
@* }); *@

  $(document).ready(function () {
        $("#goodaccessionlisttable td").click(function () {

            {

            }

@* }); *@
@* }); *@

 $(document).on('click', '#reattachdicomassessionnumber', function (e) {

@* e.preventDefault(); *@

        $.ajax({

            method: 'POST',
            url: 'DICOM/FixandUpdateLostDicomExam',

            data: $("#frm-lostdicom-update").serialize(),

            success: function (data) {

                if (data.Errored) {

@* showNotificationMessage('Error', 'Message Cannot  be empty'); *@
                    }
                    else{

@* showNotificationMessage('success', 'Changes saved'); *@

            },
            error: function (xhr, thrownError) {
@* checkAjaxError(jqXHR); *@

@* }); *@

@* }); *@

 $(document).on('click', '#importaccessionnumberupdate', function (e) {

@* e.preventDefault(); *@

     $.ajax({

         method: 'POST',
         url: 'DICOM/ImportMeasurements',

         data: $("#import-measurement-update").serialize(),

         success: function (data) {

             if (data.Errored) {

@* showNotificationMessage('Error', 'Message Cannot  be empty'); *@
                 }
                 else {

@* showNotificationMessage('success', 'Changes saved'); *@

         },
         error:function (xhr, thrownError) {
@* checkAjaxError(jqXHR); *@

@* }); *@

@* }); *@

</script>

<style>
    .btn-dicom {
/* padding: 2px 20px 2px 20px; */
/* font-weight: bold; */
/* background-color: #ddd; */
    }
</style>

@using (Html.BeginForm("FixLostDicomExam", "DICOM", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-inline", @id = "frm-triage-search" }))
{

    <div class="panel panel-default ">
        <div class="panel-heading"><h4> Step 1 : Find DICOM Exam to move</h4></div>
        <div class="panel-body">

            <div>
                @Html.PartialAsync("_BadAccessionSearch", @Model.vmaccessionsearch)
            </div>

            <div>
                @Html.PartialAsync("_BadAccessionList", @Model.badaccessionlist)
            </div>

        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading"><h4> Step 2 : Find Appointment / Test To Move to</h4></div>
        <div class="panel-body">

            <div>
                @Html.PartialAsync("_GoodAccessionSearch", @Model.vmaccessionsearch)
            </div>
            <div>
                @Html.PartialAsync("_GoodAccessionList", @Model.goodaccessionlist)
            </div>

        </div>
    </div>

@using (Html.BeginForm("FixandUpdateLostDicomExam", "DICOM", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-inline", @id = "frm-lostdicom-update" }))
{

    <div class="panel panel-default">
        <div class="panel-heading"><h4> Step 3: Attach/Reattach Exam to correct test </h4></div>
        <div class="panel-body">
            <div>

                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <a href="#" id="reattachdicomassessionnumber" class="btn btn-default btn-sm btn-dicom">Reattach</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

@using (Html.BeginForm("ImportMeasurements", "DICOM", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-inline", @id = "import-measurement-update" }))
{

    <div class="panel panel-default">
        <div class="panel-heading"><h4> Step 4: Import Measurement </h4></div>
        <div class="panel-body">
            <div>

                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <a href="#" id="importaccessionnumberupdate"  style="margin-left: 20px;" class="btn btn-default btn-sm btn-dicom">Import</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

