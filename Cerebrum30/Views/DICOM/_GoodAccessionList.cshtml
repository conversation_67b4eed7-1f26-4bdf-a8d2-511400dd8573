@using Cerebrum.ViewModels.Patient
﻿@model  IEnumerable<Cerebrum.ViewModels.RadDicom.VMGoodAccessionList>

<p></p>
<table id="goodaccessionlisttable" class="table">
    <tr>
        <th>
            @Html.DisplayName("Accession Number")
        </th>

        <th>
            @Html.DisplayName("First name")
        </th>

        <th>
            @Html.DisplayName("Last Name")
        </th>

        <th>
            @Html.DisplayName("Patient DOB")
        </th>
        <th>
            @Html.DisplayName("Test Date")
        </th>

        <th>
            @Html.DisplayName("Reattach")
        </th>

        <th>
            @Html.DisplayName("Import")
        </th>

    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.AccessionNumber)

            </td>

            <td>
                @Html.DisplayFor(modelItem => item.firstName)
            </td>

            <td>
                @Html.DisplayFor(modelItem => item.lastName)
            </td>

            <td>
                @Html.DisplayFor(modelItem => item.dateOfBirth)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.startTime)
            </td>

            <td>
                <a class="btn btn-info btn-sm">
                    <span class="glyphicon glyphicon-check"></span> ReAttach
                </a>
            </td>

            <td>
                <a class="btn btn-info btn-sm">
                    <span class="glyphicon glyphicon-check"></span> Import
                </a>
            </td>

        </tr>

</table>
