@using Cerebrum.ViewModels.Patient
﻿@{

}

<div class="row content-font padding-top padding-bottom">
    <div class="row bg-light-blue font-color-white">
        <div class="col-xs-12">
@* Flow Sheet for Jason Leo &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; *@
@* Birthday: January 18, 1938   OHIP ID: @ViewBag.HealthCard.number &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; *@
@* M Age:78 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; *@
            <a href=""><img src="~/Content/Images/h.png" border="0" /></a>
        </div>
    </div>
    <div class="row bg-light-grey padding-top padding-bottom vertical-center">
        <div class="col-xs-3">
            Porepa July 26, 2016 Newmarket
        </div>
        <div class="col-xs-2">

        </div>
        <div class="col-xs-3">
            <label style="float:left;">Diag: </label>
            <select class="form-control" style="width: 75%;" id="">
                <option><PERSON><PERSON>'s disease</option>
                <option>Gynecomastia</option>
                <option>Hirsutism</option>
                <option>Infertility</option>
                <option>Kyphosis</option>
                <option>Menopause</option>
                <option>Sarcoidosis</option>
                <option>Sex dysfunction</option>
                <option>Vit D deficiency</option>
                <option>other rheumatic heart disease</option>
                <option>essential, benign hypertension</option>
                <option>hypertensive heart disease</option>
                <option>hypertensive renal disease</option>
                <option>acute myocardial infarction</option>
            </select>
        </div>
        <div class="col-xs-4">
@* R.Dr: GUPTA ANUP &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; *@
            F.Dr: BEDARD ANDRE
        </div>
    </div>
    <div class="row bg-light-blue font-color-white padding-top">
        <div class="col-xs-12">
            <button type="button" class="btn-sm btn-warning btn-small-c btn-small-c">Patient</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Visits</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Medication</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Refresh</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Save</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">End Visit</button>
        </div>
        <div class="col-xs-12 height-4"></div>
        <div class="col-xs-12">
            <button type="button" class="btn-sm btn-warning btn-small-c">Upload MP3 Letter</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Play Letter</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Upload Letter</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Show Letter</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Upload MP3</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Clinical Data</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Upload Internal Report</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Upload Loose Report</button>
            <button type="button" class="btn-sm btn-warning btn-small-c">Loose Report List</button>
        </div>
        <div class="col-xs-12 height-8"></div>
        <div>
            <div class="col-xs-3">

            </div>
            <div class="col-xs-3">
                <label style="float:left; padding: 6px 12px 2px 12px;">Dr:</label>
                <select class="form-control" style="width: 75%;" id="">
                    <option>Hershkop</option>
                    <option>Gollob</option>
                    <option>Kumar</option>
                    <option>Shen</option>
                    <option>Pizzale</option>
                    <option>Kang</option>
                    <option>Anderson</option>
                </select>
            </div>
            <div class="col-xs-2">
                <label style="float:left; padding: 6px 12px 2px 12px;">Off.:</label>
                <select class="form-control" style="width: 60%;" id="Office">
                    <option>Newmarket</option>
                    <option>Hospital</option>
                    <option>waitinglist</option>
                    <option>Alliston</option>
                    <option>Beca</option>
                    <option>YYC waitlist</option>
                </select>
            </div>
            <div class="col-xs-1">
                Appt.Dates:
            </div>
            <div class="col-xs-3">
                <button type="button" class="btn-sm btn-success btn-small-c">Attention</button>
                <button type="button" class="btn-sm btn-success btn-small-c">Appoint</button>
            </div>
        </div>
    </div>
    <div class="row bg-light-grey padding-top padding-bottom">
        <div class="col-xs-12">
            Get Results:
            <br />
            <input type="text" size="60" />
            <button type="button" class="btn-sm btn-info">Upload</button>
        </div>
        <div class="col-xs-12">
            Outside Referral:
            <br />
            <input type="text" size="60" />
            <button type="button" class="btn-sm btn-info">Upload</button>
        </div>
        <div class="col-xs-12">
            Comments:
            <br />
            <textarea cols="200" rows="3" name="comment"></textarea>
        </div>
    </div>
    <br />
    <div class="row">
        <table class="table table-striped">
            <thead>
                <tr class="font-color-white" style="background-color: cadetblue;">
                    <th>Date</th>
                    <th>Doctor</th>
                    <th>Tests Done</th>
                    <th>Tests Ordered</th>
                    <th>Diagnosis</th>
                    <th>Consult</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>July 12, 2016</td>
                    <td>Doe</td>
                    <td>ECG</td>
                    <td>To do blood work ASAP. Internal { E1 }    Blood&Urine </td>
                    <td>428</td>
                    <td>A601 VP</td>
                </tr>
                <tr>
                    <td>July 01, 2016</td>
                    <td>Porepa</td>
                    <td>ECG</td>
                    <td>Echo f/u bked with E1 on w/l , Pt. advised to call us Oct'14 to schedule f/u -SF  </td>
                    <td></td>
                    <td>L VP</td>
                </tr>
                <tr>
                    <td>May 25, 2015</td>
                    <td>Porepa</td>
                    <td></td>
                    <td>Follo up on thallium viability </td>
                    <td>428</td>
                    <td>A601 L VP</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>