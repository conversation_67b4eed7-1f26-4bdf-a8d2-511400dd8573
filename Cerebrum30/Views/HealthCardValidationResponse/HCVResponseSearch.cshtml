@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.ViewModels.HealthCardValidation.VMAppointmentHealthCardValidation>

@if (Model != null && @Model.Any())
{
    <div class="panel panel-default">
        <div class="panel-heading">Total @Model.Count() response(s) found.</div>
        <div class="panel-body">
            <table class="table">
                <tr>
                    <th>
                        @Html.DisplayNameFor(model => model.PatientName)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.DoctorName)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.AppointmentDateTime)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.HealthCard)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.ResponseCode)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.ResponseDescription)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.ResponseAction)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.IsValid)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.CreateDate)
                    </th>

                </tr>

                @foreach (var item in Model)
                {

                    if (!item.IsValid)
                    {

                    }
                    <tr class="@cls">

                        <td>
                            @Html.DisplayFor(modelItem => item.PatientName)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.DoctorName)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.AppointmentDateTime)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.HealthCard)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.ResponseCode)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.ResponseDescription)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.ResponseAction)
                        </td>
                        <td>
                            @if (item.IsValid)
                            {
                                <span class="alert-success">YES</span>
                            }
                            else
                            {
                                <span class="alert-danger">NO</span>

                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.CreateDate)
                        </td>
                    </tr>

            </table>
           
        </div>
    </div>

else
{
    <p class="alert alert-danger">No record found</p>

