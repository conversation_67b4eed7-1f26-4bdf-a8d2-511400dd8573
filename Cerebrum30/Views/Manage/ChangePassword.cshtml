﻿@model Cerebrum30.Models.ChangePasswordViewModel
@{

}

<div id="pnl_login">
    <h1 class="lbl-login-title"></h1>
    <h2>Change Password</h2>

    <div class="row">
        <div class="col-md-12">
            <section id="forgotChangePassword">

    @using (Html.BeginForm("ChangePassword", "Manage", null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { @class = "form-horizontal", role = "form"  }))
            {
        @Html.AntiForgeryToken()
        @Html.ValidationSummary("", new { @class = "text-danger" })

        <div class="form-group">
            <div class="col-md-12">
                <div class="float-Left-Label">@Html.PasswordFor(m => m.OldPassword, new { @placeholder = "Current password", @class = "form-control" }) Html.LabelFor(m => m.OldPassword, new { @class = "" }) </div>
                <div class="float-Left-Label margin-top-more"></div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-12">
                <div class="float-Left-Label"> @Html.PasswordFor(m => m.NewPassword, new { @placeholder = "New password", @class = "form-control" })</div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-12">
                <div class="float-Left-Label"> @Html.PasswordFor(m => m.ConfirmPassword, new { @placeholder = "Confirm New password", @class = "form-control" })</div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-12">
                <div class="float-Left-Label"> <input type="submit" value="Change password" class="btn btn-default btn-primary" /> <input type="button" class="btn btn-default" value="Return to main" id="btn_returnToMain" /></div>
            </div>
        </div>

                </section>
        </div>
 </div>

            @section Scripts {

    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
            }
        </div>
