﻿@model Cerebrum30.Models.ManageLoginsViewModel
@{

}

<h2>@ViewBag.Title.</h2>

<p class="text-success">@ViewBag.StatusMessage</p>
@{

    {
        <div>
            <p>
                There are no external authentication services configured. See <a

                for details on setting up this ASP.NET application to support logging in via external services.
            </p>
        </div>

    else
    {
        if (@Model.CurrentLogins.Count > 0)
        {
            <h4>Registered Logins</h4>
            <table class="table">
                <tbody>
                    @foreach (var account in @Model.CurrentLogins)
                    {
                        <tr>
                            <td>@account.LoginProvider</td>
                            <td>
                                @if (ViewBag.ShowRemoveButton)
                                {
                                    using (Html.BeginForm("RemoveLogin", "Manage"))
                                    {
                                        @Html.AntiForgeryToken()
                                        <div>
                                            @Html.Hidden("loginProvider", account.LoginProvider)
                                            @Html.Hidden("providerKey", account.ProviderKey)
                                            <input type="submit" class="btn btn-default" value="Remove"

                                        </div>

                                else
                                {
                                    @: &nbsp;

                            </td>
                        </tr>

                </tbody>
            </table>

        if (@Model.OtherLogins.Count > 0)
        {
            using (Html.BeginForm("LinkLogin", "Manage"))
            {
                @Html.AntiForgeryToken()
                <div id="socialLoginList">
                    <p>
                        @foreach (AuthenticationDescription p in @Model.OtherLogins)
                        {
                            <button type="submit" class="btn btn-default" id="@p.AuthenticationType" name="provider"

                        }
                    </p>
                </div>

