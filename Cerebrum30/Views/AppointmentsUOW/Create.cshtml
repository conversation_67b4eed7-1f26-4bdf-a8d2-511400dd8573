﻿
@model Cerebrum.Data.Appointment
<script type="text/javascript">

    $(document).ready(function () {
@* $('#Doctor').dblclick(DoctorLookUp); *@
        $('#Doctor').on("keypress", function (e) {

@* DoctorLookUp(); *@
@* e.preventDefault(); *@
            }
@* }); *@
        function DoctorLookUp() {
@* $("#globalModal").modal('show').find('.modal-body').html(""); *@
@* $("#globalModal").modal('show').find('.modal-footer').html(""); *@
@* $("#globalModal").modal('show').find('#globalModalLabel').text("Doctor Lookup"); *@
            $("#globalModal").modal('show').find('.modal-body').load('/ExternalDoctorsDK/PracticeDoctors/').on('click', "tr", function (f) {

@* $("#globalModal").modal('hide'); *@
@* $('#Doctor').val(selectedDoctor.trim()); *@
@* $('#ExternalDoctorId').val(selectedDoctorId.trim()); *@
@* }); *@

        $('#OhipOrName').on("keypress", function (e) {

@* OHIP(ohipornametxt); *@
@* e.preventDefault(); *@
            }
@* }); *@
        $('#OhipOrName').focusout(function () {

@* OHIP(ohipornametxt); *@
@* }); *@

@* cerebrum3.GetDropDownData("/PracticeDoctorsDK/ByPractice/", "#referralDoctorList"); *@
        $('#addNewAppointment').submit(function (e) {

            $.post($(this).attr('action'), frm, function (ff) {
@* toastr.info(ff); *@
@* }); *@

            //e.preventDefault();
@* }); *@
@* }); *@
</script>

@using (Html.BeginForm(null, null, null, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Post, true, new { id = "addNewAppointment" }))
@using Cerebrum.ViewModels.Patient
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.PatientRecordId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-12">
                    @{

                        <input type="text" class="form-control" name="OhipOrName" id="OhipOrName" value="@(demo!=null? demo.lastName+" ,"+demo.firstName:"")" />
                    }
                    @Html.HiddenFor(model => model.Id)
                    @Html.HiddenFor(model => model.PatientRecordId)
                    @Html.ValidationMessage("OhipOrName", "", new { @class = "text-success" })
                    @Html.ValidationMessage("OhipExpiry", "", new { @class = "text-success" })
                    @Html.ValidationMessageFor(model => model.PatientRecordId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-3">
            <div class="form-group">
                @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-11">
                    @Html.DropDownList("OfficeId", null, htmlAttributes: new { @class = "form-control" })

                    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.Label("AccessionNumber", "Doctor", new { @class = "control-label col-md-2" })
                <div class="col-md-12">
                    <input type="text" class="form-control" name="Doctor" id="Doctor" />
                    @Html.Hidden("ExternalDoctorId")
                    @Html.ValidationMessageFor(model => model.appointmentProviders, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.appointmentTime, htmlAttributes: new { @class = "control-label col-md-5" })
                <div class="col-md-12 input-append date">
                    @Html.EditorFor(model => model.appointmentTime, new { htmlAttributes = new { @class = "form-control  input-append date", data_format = "MM/dd/yyyy HH:mm:ss PP" } })
                    <span class="add-on">
                        <i data-time-icon="icon-time" data-date-icon="icon-calendar">
                        </i>
                    </span>
                    @Html.ValidationMessageFor(model => model.appointmentTime, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="col-xs-3">
            <div class="form-group">

                @Html.LabelFor(model => model.referralDoctorId, htmlAttributes: new { @class = "control-label col-md-7" })
                <div class="col-md-11">
                    @Html.HiddenFor(model => model.referralDoctorId)
                    <select name="referralDoctorList" id="referralDoctorList" class="form-control"></select>
                    @Html.ValidationMessageFor(model => model.referralDoctorId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">

                @Html.LabelFor(model => model.AppointmentTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-11">
                    @Html.DropDownList("AppointmentTypeId", null, htmlAttributes: new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AppointmentTypeId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.appointmentConfirmation, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-11">
                    @Html.CustomEnumDropDownListFor(model => model.appointmentConfirmation, htmlAttributes: new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.appointmentConfirmation, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="col-xs-2">
            <div class="form-group">

                @Html.LabelFor(model => model.appointmentPaymentMethod, htmlAttributes: new { @class = "control-label col-md-10" })
                <div class="col-md-8">
                    @Html.CustomEnumDropDownListFor(model => model.appointmentPaymentMethod, htmlAttributes: new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.appointmentPaymentMethod, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">

                @Html.LabelFor(model => model.appointmentNotes, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-12">
                    @Html.TextAreaFor(model => model.appointmentNotes, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.appointmentNotes, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-15">
            <div class="form-group">
                <label class="control-label col-md-7" for="tests">Test(s):</label>
                <div class="col-md-11">

                    <div class="btn-group" id="tests" data-toggle="buttons">

                        @foreach (var t in ViewBag.Tests)
                        {

                            <label class="btn btn-md btn-default @selected">
                                <input type="checkbox" autocomplete="off" data-value="@t.Value" checked="@t.Selected" name="appointmentTests" value="@t.Value"> @t.Text
                            </label>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">

        <div class="col-xs-2">
            <div class="form-group">
                @Html.HiddenFor(model => model.appointmentPurpose)
                @Html.HiddenFor(model => model.appointmentRegistrar)
                <br />
                <div class="col-md-offset-2 col-md-10">
                    <input type="submit" value="New Appointment" id="btnAppointment" class="btn btn-primary" />
                </div>
            </div>
        </div>
    </div>

