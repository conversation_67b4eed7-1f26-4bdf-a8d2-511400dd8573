@using Cerebrum.ViewModels.Patient
﻿@model IEnumerable<Cerebrum.Data.Appointment>

@{

}
<script type="text/javascript">
    $(document).ready(function () {

            $.post('/AppointmentsDK/ChangeAppointmentStatus/', { appointmentId: appid, status: appstatus }, function (ret) {

@* toastr.info("Status Changed: " + appstatus); *@
@* }); *@
@* }); *@
@* }); *@
</script>
<h2>Appointments</h2>

<div class="panel panel-default">
    <div class="panel-heading">
        <h4 class="panel-title">
            <a data-toggle="collapse" href="#appointmentCollapse" id="appointmentPanelTitle">New Appointment</a>
        </h4>
    </div>

    <div class="panel-body panel-collapse collapse" id="appointmentCollapse" style="padding:5px">
        @Html.Action("Create", "Daysheet")
    </div>
</div>

<div class="container-fluid">
    <div class="navbar-header">
        <h3>  @DateTime.Now.ToLongDateString()</h3>
    </div>
</div>
<table class="table">
    <tr>
        <th>
            Status
        </th>
        <th>
            Time
        </th>

        <th>
            Patient
        </th>
        <th>
            @Html.DisplayNameFor(model => model.appointmentTests)
        </th>
        <th>

            @Html.DisplayNameFor(model => model.appointmentNotes)
        </th>

        <th>
            @Html.DisplayNameFor(model => model.referralDoctorId)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.appointmentConfirmation)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.appointmentPaymentMethod)
        </th>
        <th></th>
    </tr>

    @foreach (var item in Model)
            {
        <tr>
            <td>
                @{

                    @Html.DropDownList("AppointmentStatus#" + item.Id, slst, htmlAttributes: new { @class = "form-control ", @id = "AppointmentStatus#" + item.Id })
                }
            </td>

            <td>
                @item.appointmentTime.ToShortTimeString()
            </td>

            <td>
                @{

                    {
                        @hc.First().number
                    }
                    <br />
                    @dmo.FullName

            </td>
            <td>
                <div class="btn-group" id="tests" data-toggle="buttons">
                    @foreach (var t in item.appointmentTests)
                    {
                        <label class="btn btn-md btn-default ">
                        </label>
                    }
                </div>
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AppointmentTypeId)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.referralDoctorId)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.appointmentConfirmation)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.appointmentPaymentMethod)
            </td>
            <td>
                <a href="#" onclick="OnAppointmentEdit('/AppointmentsDK/Edit/@item.Id');" data-toggle="collapse" data-target="#appointmentCollapse">Edit</a>
            </td>
        </tr>

</table>
