﻿<!DOCTYPE html>
<html>

<head>
    <base href="@Url.Content("~/")">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - Cerebrum 3</title>
    <link rel="stylesheet" href="~/Content/css/site.css" />
    <script src="~/Scripts/modernizr-2.8.3.js"></script>
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <script src="~/Scripts/cerebrum.js"></script>

    <script>
        $(function () {
            $("body").on('click keypress', function () { @* ResetThisSession(); *@ });
        @* }); *@
        }); //seconds @* var secondTick = 0; *@
        function ResetThisSession() {

        function StartThisSessionTimer() {
            $.ajax({
                type: 'GET',
                url: '../api/Helper/GetAutoLogoutTime',
                dataType: 'json',

                success: function (data) {
                    //alert("Saved successfully");
                },
                error: function (request, status, error) {
                    @* alert(request.responseText); *@
                }
            });
        }

        @* secondTick++; *@

        @* $("#spanTimeLeft").html(message); *@

        @* if (secondTick > timeInSecondsAfterSessionOut) {
            clearTimeout(tick);
            return;
        } *@

            //console.log('Time left: ' + timeLeft + '\n' + 'Second Tick: ' + secondTick + '\n' + 'Tick: ' + tick);

        //StartThisSessionTimer();

        function ajaxCall(url, data, isFormData, callBack) {
            $.ajax({
                method: "POST",
                url: url,
                data: data,
                headers: { "RequestVerificationToken": requestVerificationToken },
                async: true,
                processData: isFormData ? false : undefined,
                contentType: isFormData ? false : undefined,
                beforeSend: function () { $("#ajax-loader").show(); },
                complete: function () { $("#ajax-loader").hide(); },
                success: function (result) { callBack(result); },
                error: function (xhr, thrownError) { checkAjaxError(url, xhr, thrownError); }
            });
        }

    </script>
</head>

<body>
    <div class="navbar navbar-inverse navbar-fixed-top">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                @Html.ActionLink("Cerebrum 2.3", "Index", "Home", new { area = "" }, new { @class = "navbar-brand" })
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav">
                    <li>@Html.ActionLink("Home", "Index", "Home", new { area = "" }, new { @class = "navbar-brand" })
                    </li>
                    <li>@Html.ActionLink("Daysheet", "Index", "Daysheet", new { area = "schedule" }, null)</li>

                        <li>@Html.ActionLink("Transfer Measurments", "Index", "TransferData", new { area = "" }, new { @class = "navbar-brand" })</li>
                        <li>@Html.ActionLink("Medications", "Index", "BuildDB", new { area = "" }, new { @class = "nav navbar-nav" })</li>
                </ul>
                @Html.PartialAsync("_LoginPartial")
            </div>
        </div>
    </div>

    <div id="ajax-loader">
        <img src="@Url.Content("~/Content/images/ajax-loader.gif")" />
    </div>

    <div class="container-fluid body-content __7778654">
        <hr />
        @RenderBody()
        <hr />

        <footer>
            <p>&copy; @DateTime.Now.Year - WELL Health Technologies</p>
        </footer>
        <div>
            <table style="width:100%;">
                <tr>
                    <td style="text-align:right;"><span id="spanTimeLeft"></span> seconds left</td>
                </tr>
            </table>
        </div>
    </div>
    <div id="layoutDialog"></div>
    <div class="modal fade modal-lg" id="globalModal" tabindex="-1" role="dialog" aria-labelledby="globalModalLabel">
        <div class="modal-dialog " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="globalModalLabel"></h4>
                </div>
                <div class="modal-body" id="global-modal-body">
                    <br />
                    <span id="spanInfo"></span>
                </div>
                <div class="modal-footer" id="model-footer-id">
                    <h4 class="modal-title" id="globalModalMessage"></h4>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="appointmentSwipHealthcardAddNew" disabled>Add
                        New</button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Scripts/bootstrap.min.js"></script>
    @RenderSection("scripts", required: false)
</body>

</html>
