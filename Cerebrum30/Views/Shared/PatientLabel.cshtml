@model Cerebrum.ViewModels.Schedule.VMPatientLabel
@using Cerebrum.ViewModels.Schedule
@using Cerebrum.ViewModels.Patient

<script>
    $(document).ready(function(){

@* window.print(); *@
@* }); *@
</script>
<style>
    :root {
/* --label-width: 83mm; */
/* --label-height: 22mm; */
/* --label-padding: 2mm; */
    }
    .flex {
/* display: flex; */

    .justify-between {
/* justify-content: space-between; */
    }
    .justify-end {
@* justify-content: flex-end; *@
    }

    @@media print {
        @@page{
@* font-size: 10pt; *@
@* padding: var(--label-padding); *@
@* margin: 0; *@
@* size: var(--label-height) var(--label-width); *@
@* size: landscape; *@

        /* hacks to hide various extra padding that messes with content size  */
        body .body-content {
@* padding: 0; *@
@* margin: 0; *@
@* height: 0; *@
        }
        body > @*:not(.body-content),*@
        .body-content > @*:not(#print-label) {*@
@* display: none; *@

        #print-label {
@* display: block; *@
@* visibility: visible; *@
@* position: absolute; *@
@* left: 0; *@
@* top: 0; *@
@* padding: var(--label-padding); *@
@* margin: 0; *@
@* width: var(--label-width); *@
@* height: var(--label-height); *@

</style>
<div id="print-label">
    @if (@Model.TypeOfLabel == VMPatientLabel.LabelType.Patient)
    {
        <div class="line-1 flex justify-between">
            <span>@Model.FullName()</span>
            <span>DOB: @Model.DOB</span>
        </div>
        <div class="line-2 flex justify-between">
            <span>Sex: @Model.Gender</span>
            <span>Age: @Model.Age</span>
            <span>OHIP#: @Model.OHIPNumber @Model.OHIPVersion</span>
        </div>
        <div class="line-3 flex justify-between">
            <span>@Model.AddressLine</span>
        </div>
        <div class="line-4 flex justify-between">
            <span>@Model.AddressAdditionalInfo()</span>
        </div>
        <div class="line-5 flex justify-between">
            <span>@Model.Phone</span>
            <span>MRP: @Model.Doctor</span>
        </div>

    @if (@Model.TypeOfLabel == VMPatientLabel.LabelType.Demo)
    {
        <div class="line-1 flex justify-between">
            <span>@Model.FullName()</span>
        </div>
        <div class="line-2 flex justify-between">
            <span>DOB: @Model.DOB</span>
        </div>

    @if (@Model.TypeOfLabel == VMPatientLabel.LabelType.Address)
    {
        <div class="line-1 flex justify-between">
            <span>@Model.FullName()</span>
        </div>
        <div class="line-2 flex justify-between">
            <span>@Model.AddressLine</span>
        </div>
        <div class="line-3 flex justify-between">
            <span>@Model.City, @Model.Province</span>
        </div>
        <div class="line-4 flex justify-between">
            <span>@Model.PostalCode</span>
        </div>

</div>
