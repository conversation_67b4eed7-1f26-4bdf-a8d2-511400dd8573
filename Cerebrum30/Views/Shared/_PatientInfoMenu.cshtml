@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Patient.VMPatientInfoRequest
<script type="text/javascript">

    function loadPatientMenu() {

        $('#patient-info-container').load(url, {PatientId:@Model.PatientId,AppointmentId:@Model.AppointmentId,AppointmentTestId:@Model.AppointmentTestId,TestId:@Model.TestId}, function (response, status, xhr) {

@* $('#patient-info-container').html(errorMessage); *@
            }
@* }); *@

    $(function () {
@* loadPatientMenu(); *@
@* }); *@
</script>
<script src="~/Areas/Schedule/Scripts/appointments.js"></script>
<div class="container-fluid printNot">
    <div id="patient-info-container" class="__8873453"></div>
</div>@*container-fluid*@