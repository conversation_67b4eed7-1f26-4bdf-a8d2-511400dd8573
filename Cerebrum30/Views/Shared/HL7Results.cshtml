﻿@model IEnumerable<Cerebrum.ViewModels.HL7.HL7ResultVM>
@using Cerebrum30.Utility
@if (Model != null && @Model.Count() > 0)
{

    foreach (var item in Model)
    {

        <tr id="<EMAIL>" class="@((flg.Trim() == "" || flg.Trim().Equals("N")) ? "" : "danger")">
            <td id="<EMAIL>">
                @{

                }
                @if (item.showResult && item.TestName != "")
                {
                    @Html.ActionLink(item.TestName, "Index", "HL7ResultChart", new { area = "labs", vid = item.HL7ReportVersionId, testCode = item.testCodeIdentifier, returnUrl = url }, new { target = "_parent", data_toggle = "tooltip", data_placement = "left", title = item.LabRequestCode })

            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @flg
                }
            </td>
            @{

            <td id="<EMAIL>" class="@lengthClass">
                <div id="<EMAIL>">
                    @if (item.showResult)
                    {

                        @Html.Raw(result)
                    }
                    else
                    {
                        <button type="button" class="btn btn-danger" data-resultid="@item.Id" data-toggle="modal" data-target="#model-consent">Display Sensitive Information</button>

                </div>
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @Html.DisplayFor(modelItem => item.refRange)
                }
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @Html.DisplayFor(modelItem => item.units)
                }
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @Html.DisplayFor(modelItem => item.resultStatus)
                }
            </td>
            <td id="<EMAIL>">
                <button id="@item.Id" class="btn-HL7-Result-Note btn btn-default btn-xs printNot" data-value="@item.Id"> Add Note</button>
            </td>
            <td>
                @if (!item.showResult)
                {
                    <a href="#" data-resultid="@item.Id" data-URL="@Url.Action("HL7SensitiveResultAccessedLog", new { controller = "HL7Report", area = "Labs" })" class="btn-sensitive-accessed-info">  <span class="glyphicon glyphicon-eye-open"></span></a>
                }
            </td>
            <td>
                @if (!string.IsNullOrEmpty(item.ReportURL))
                {
                    <a href="@item.ReportURL" target="_blank" class="btn btn-default btn-xs">Download Report</a>
                }
            </td>
        </tr>

        @Html.PartialAsync("HL7ResultNotes", item.HL7ResultNotes);

            else
            {
                    <hr />
                    <p style="color:#da7373">Result not found</p>
