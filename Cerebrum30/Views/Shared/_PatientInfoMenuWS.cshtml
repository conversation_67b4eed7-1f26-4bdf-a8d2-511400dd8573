﻿@model  Cerebrum30.Areas.Measurements.Models.ViewModels.WorkSheetVM
@using AwareMD.Cerebrum.Shared.Enums
@using Cerebrum.ViewModels.Patient
@{ 

}
<script type="text/javascript">
    function loadPatientMenu() {

        $('#patient-info-container').load(url, {patientId:@Model.PatientID,appointmentID:@Model.AppointmentID,testID:@Model.TestID}, function (response, status, xhr) {

@* $('#patient-info-container').html(errorMessage); *@
            }
@* }); *@

    $(function () {
@* loadPatientMenu(); *@
@* }); *@
</script>
<div class="container-fluid">
    <div id="patient-info-container" class="__32456"></div>
</div>

<!-- vplinks -->
<div class="container-fluid spacer-top-10">
    <div class="_placeHolder_VPlinks _34564567" style="float:left"></div>
    <div class="" style="float:right">
        @if (@Model.IsAmended)
        {

            {
                <div style="margin-right:15px;padding:5px;margin-top:2px;" class="div-test-status pull-left BeingSent">
                    <span class="status-desc-holder">Status: Being Sent</span>
                </div>
            }
            else
            {
                <div style="margin-right:15px;padding:5px;margin-top:2px;" class="div-test-status pull-left ReportCompleted">
                    <span class="status-desc-holder">Status: Report Completed</span>
                </div>
            }

        <a href="#" class="btn  btn-default btn-sm icon-color custom-btn" id="btn-save" data-ds-url='@Url.Action("index", "daysheet",
                            new
                            {

            <i class="glyphicon glyphicon-floppy-save"></i>Save
        </a>
        @if (!@Model.IsAmended)
        {
        <a data-ds-url='@Url.Action("index", "daysheet",
                     new
                     {

                     })'

            <i class="glyphicon glyphicon-floppy-save"></i>Save & Change Status
        </a>
        <a target="_blank"

                     new
                     {

                     })'

            <i class="glyphicon glyphicon-share-alt"></i>
            Send Interim Report
        </a>

        @if (!isBeingSent)
        {
        <a target="_blank"

                     new
                     {

                     })'

            <i class="glyphicon glyphicon-share-alt"></i>
            Send Report
        </a>

        <a class="btn btn-default btn-sm"  href="javascript:void(0)" id="_swictStateWS">&nbsp;</a>
    </div>
</div>

    <div class="row __H789MED">
        <div id="div-medications"><img src="Content/fancybox_loading.gif" style="margin-left: 15px"/></div>
    </div>@* *@
</div>
    <script>

        //var medicationHeight = function(){
        //    var height = 170;
        //    var medicationSelector = '.__H789MED';

        //    if (!allergyVisible && !medicationVisible){
        //        height = 0;
        //    }                
        //    $(medicationSelector).css('min-height',height+'px');
        //};       

        $(document).ready(function(){*@ var clone9 = $('._toCloneIntoWS').clone();
@* clone9.show().appendTo($("._placeHolder_VPlinks")); *@
@* LoadMedications(); *@

@* $('#_swictStateWS').html(lbl); *@
                
            $('#_swictStateWS').on('click', function (e) {  
@* e.preventDefault(); *@
@* var h; *@
                if (state) {

@* $('.__H789MED').css('min-height','0px'); *@
@* $('#div-medications').fadeOut('easeInOutExpo'); *@

                }
                else {

@* $('.__H789MED').css('min-height','170px'); *@
@* $('#div-medications').fadeIn('easeInOutExpo'); *@

                }
@* $('#_swictStateWS').html(lbl); *@
                
@* }); *@
@* }); *@
    </script>
