﻿@model Microsoft.AspNetCore.Diagnostics.IExceptionHandlerFeature
@{

    //Layout = "~/Views/Shared/_Layout.cshtml";   

}
@*<h3>*@
    @ViewBag.Title
</h3>
@if (Model?.Error != null) {
    <div class="list-sfs-holder spacer-top-15">
        <div class="alert alert-error">
            An unexpected error has occurred. Please contact the system administrator.
        </div>
        <br />
        <div>
            <p>
                <b>Exception:</b> @Model.Error.Message<br />
                <b>Path:</b> @Context.Request.Path
            </p>
            <div style="overflow:scroll">
                <pre>
                    @Model.Error.StackTrace
                </pre>
            </div>
        </div>

    </div>
    <script>
        $(function(){
@* $('.navbar-nav').hide(); *@
@* }); *@

    </script>
