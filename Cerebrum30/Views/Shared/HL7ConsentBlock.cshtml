@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.OLIS.VMConsentReportFor

<div id="hl7consentblock-container">
    <div class="row">
        <div class="alert alert-warning col-md-5" role="alert">
            <p>Consent to access laboratory information has been withdrawn by the patient.<br /> Select button to view options</p>
        </div>
        <div class="col-sm-2">
            <button data-toggle="modal" data-target="#model-consent" class="btn-sm btn-primary btn-consent-block" data-accession-number="@Model.AccessionNo" data-physician-no="@Model.PhysicianNo" data-hl7-patient-id="@Model.HL7PatientId">Temporary consent</button>
        </div>
    </div>
    <div class="row alert alert-danger" role="alert">
        WARNING: Limited to lab results submitted to OLIS
    </div>
    
</div>
<div class="modal fade" id="model-consent" tabindex="-1" role="dialog" aria-labelledby="model-consentLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="gridSystemModalLabel">Consent Form</h4>
            </div>
            <div class="modal-body">

            </div>
        </div>
    </div>
</div>
<script>
    $('#model-consent').on('show.bs.modal', function (e) {

@* $('#ajax-loader').show(); *@
        $.ajax({
            url: url,
            type: 'GET',
            cache: false,
            data: vals
        }).done(function (data) {

@* modalbody.html(data); *@
@* $('#ajax-loader').hide(); *@
        }).fail(function (jqXHR, textStatus, errorThrown) {
@* alert('Error:' + errorThrown); *@
@* checkAjaxError(jqXHR, null); *@
@* }); *@
@* }); *@
</script>

