﻿<!DOCTYPE html>
<html>

<head>
    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/Content/css/site.css" />
    <script src="~/Scripts/modernizr-2.8.3.js"></script>
    @RenderSection("head", required: false)
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <script src="~/Scripts/bootstrap.min.js"></script>
</head>

<body>
    @RenderBody()

    @RenderSection("scripts", required: false)
    <script src="~/Scripts/jquery.validate.min.js"></script>
    <script src="~/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <script type="text/javascript">
        function ajaxCall(url, data, isFormData, callBack) {
            $.ajax({
                method: "POST",
                url: url,
                data: data,
                headers: { "RequestVerificationToken": requestVerificationToken },
                async: true,
                processData: isFormData ? false : undefined,
                contentType: isFormData ? false : undefined,
                beforeSend: function () { $("#ajax-loader").show(); },
                complete: function () { $("#ajax-loader").hide(); },
                success: function (result) { callBack(result); },
                error: function (xhr, thrownError) { checkAjaxError(url, xhr, thrownError); }
@* }); *@

    </script>
</body>

</html>
