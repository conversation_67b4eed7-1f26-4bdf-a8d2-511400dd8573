@using Cerebrum.ViewModels.Patient
﻿@model  Cerebrum.ViewModels.VP.VP_VM
<script type="text/javascript">
    function loadPatientMenu() {

        $.ajax({
            method: "POST",
            url: url,
            data: {patientId:@Model.PatientID,appointmentID:@Model.AppointmentID,testID:@Model.TestID},
            headers: { "RequestVerificationToken": requestVerificationToken },
            async: true,
            processData: undefined,
            contentType: undefined,
            beforeSend: function () { $("#ajax-loader").show(); },
            complete: function () { $("#ajax-loader").hide(); },
            success: function (result) { 
@* $('#patient-info-container').html(result); *@
            },
            error: function (xhr, thrownError) { 

@* $('#patient-info-container').html(errorMessage); *@

@* }); *@

    $(function () {
@* loadPatientMenu(); *@
@* }); *@
</script>

<div class="container-fluid">
    <div id="patient-info-container" class="__776543"></div>
</div>

<!-- vplinks -->

<div class="container-fluid spacer-top-10">
    @Html.PartialAsync("BillingCode", @Model)

    <div class="_placeHolder_VPlinksV _34564567" style="float:left; padding-right:50px"></div>
   
    <div class="" style="float:left">
        <a class="btn btn-default btn-sm" href="javascript:void(0)" id="_swictStateVPAllergy">&nbsp;</a>
        <a class="btn btn-default btn-sm" href="javascript:void(0)" id="_swictStateVPMedication">&nbsp;</a>
    </div>

    <div class="_placeHolder_VPlinksV2 _34564567345" style="float:right;"></div>
</div>

<div class="container-fluid spacer-top-10">
    <div class="row __H789MED">
        <div id="div-medications"><img src="Content/fancybox_loading.gif" style="margin-left: 15px" /></div>
    </div>
</div>

<script>

        $(document).ready(function(){

@* clone9.show().appendTo($("._placeHolder_VPlinksV")); *@

@* clone9_2.appendTo($("._placeHolder_VPlinksV2")); *@

@* LoadMedications(); *@

            //var stateAllergy = true;
            //var stateMedication = true;

            if (!allergyVisible) {
                //stateAllergy = false;

@* $('.div-medications-allergies').fadeOut('easeInOutExpo'); *@
            }
@* $('#_swictStateVPAllergy').html(lblAllergy); *@

            if (!medicationVisible) {
                //stateMedication = false;

@* $('.div-medications-medications').fadeOut('easeInOutExpo'); *@
            }
@* $('#_swictStateVPMedication').html(lblMedication); *@

                if (!allergyVisible && !medicationVisible){

                }                
@* $(medicationSelector).css('min-height',height+'px'); *@
@* }; *@
            
            $('#_swictStateVPAllergy').on('click', function (e) {
@* e.preventDefault(); *@
                if (allergyVisible) {

@* $('.div-medications-allergies').fadeOut('easeInOutExpo'); *@

                }
                else {

@* $('.div-medications-allergies').fadeIn('easeInOutExpo'); *@

@* medicationHeight(); *@
@* $('#_swictStateVPAllergy').html(lblAllergy); *@
@* }); *@

            $('#_swictStateVPMedication').on('click', function (e) {
@* e.preventDefault(); *@
                if (medicationVisible) {

@* $('.div-medications-medications').fadeOut('easeInOutExpo'); *@

                }
                else {

@* $('.div-medications-medications').fadeIn('easeInOutExpo'); *@

@* medicationHeight(); *@
@* $('#_swictStateVPMedication').html(lblMedication); *@
@* }); *@
@* }); *@
</script>
