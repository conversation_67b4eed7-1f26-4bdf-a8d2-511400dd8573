﻿@{

    //id, text, background color

                                 { { "segmentColor_2", "Hypokinetic", "#f0e68c" } },/* fea9a9 */
                                 { { "segmentColor_3", "Akinetic", "#fa8072" } },/@*fe00fe*/*@
                                 { { "segmentColor_4", "Dyskinetic", "#ee82ee" } }, /*fe7f00*/
                                 { { "segmentColor_5", "Aneurysmal", "#00ffff" } },/*fe3333*/
@* { { "segmentColor_6", "Not Well Seen", "#ffffff" } } }; *@

<style type="text/css">
    #information table {
@* border-collapse: collapse; *@

    #information table, #information th, #information td {
@* border: 1px solid black; *@

</style>

<script type="text/javascript">

    if (@(svgNumber) > 1) {

    }

@* links.push({ id: 1, group: groupId, value: "m1,tc1,lr1,lt1" }); *@
@* links.push({ id: 2, group: groupId, value: "m2,la2,lr2,lt2" }); *@
@* links.push({ id: 3, group: groupId, value: "m3,fc3,lr3,lt3" }); *@
@* links.push({ id: 4, group: groupId, value: "m4,tc4,lr4,lt4" }); *@
@* links.push({ id: 5, group: groupId, value: "m5,la5,lr5,lt5" }); *@
@* links.push({ id: 6, group: groupId, value: "m6,fc6,lr6,lt6" }); *@
@* links.push({ id: 7, group: groupId, value: "m7,tc7,lr7,lt7" }); *@
@* links.push({ id: 8, group: groupId, value: "m8,la8,lr8,lt8" }); *@
@* links.push({ id: 9, group: groupId, value: "m9,fc9,lr9,lt9" }); *@
@* links.push({ id: 10, group: groupId, value: "m10,tc10,lr10,lt10" }); *@
@* links.push({ id: 11, group: groupId, value: "m11,la11,lr11,lt11" }); *@
@* links.push({ id: 12, group: groupId, value: "m12,fc12,lr12,lt12" }); *@
@* links.push({ id: 13, group: groupId, value: "m13,tc13,lr13,lt13" }); *@
@* links.push({ id: 14, group: groupId, value: "m14,fc14,la14,lr14,lt14" }); *@
@* links.push({ id: 15, group: groupId, value: "m15,tc15,lr15,lt15" }); *@
@* links.push({ id: 16, group: groupId, value: "m16,fc16,la16,lr16,lt16" }); *@
@* links.push({ id: 17, group: groupId, value: "m17,fc17,tc17,la17,lr17,lt17" }); *@

        @{

            {

                {
                    @:["@(segmentColors[i, 0, 0])", "@(segmentColors[i, 0, 1])", "@(segmentColors[i, 0, 2])"]
                }
                else
                {
                    @:["@(segmentColors[i, 0, 0])", "@(segmentColors[i, 0, 1])", "@(segmentColors[i, 0, 2])"],

@* ]; *@

    @{

        {

            {

                {
                    @:selectedSegments.push({ groupId: @i, segmentId: @(m + 1), segmentColorIndex: 0 });
                }
            }

    function AddSegment(groupId, segmentId) {

@* break; *@
            }

@* selectedSegments.push({ groupId: groupId, segmentId: segmentId, segmentColorIndex: currentSegmentColorIndex }); *@

        //change ui
@* updateSegemntUI(groupId, segmentId, currentSegmentColorIndex); *@

    function SelectSegment(groupId, segmentId) {

@* AddSegment(groupId, segmentId); *@

@* AddSegment(otherGroupId, segmentId); *@

        } else {

@* updateActiveFlag(groupId); *@
@* return false; *@

    function SelectSegmentDefault(segmentId) {
@* SelectSegment(groupIdDefault, segmentId); *@
@* return false; *@

    function ChangeSegmentColor(segmentColorIndex) {

        segmentColors_j.forEach(function(scj) {

@* svgElement.setAttribute("height", "650"); *@
@* svgElement.setAttribute("y", "1100"); *@
@* }); *@

@* svgElement.setAttribute("height", "1300"); *@
@* svgElement.setAttribute("y", "775"); *@

    function updateSegemntUI(groupId, segmentId, colorIndex) {

        links.forEach(function(link) {

                svgIds.forEach(function(svgId) {

                    if (svgId.indexOf("lt") > -1) {

                    } else {
@* svgElement.setAttribute("fill", color); *@
                    }
@* }); *@
@* return false; *@

@* }); *@

";

    function updateActiveFlag(groupId) {

"; }

    $(document).ready(function () {
        selectedSegments.forEach(function(ss) {*@ updateSegemntUI(ss.groupId, ss.segmentId, ss.segmentColorIndex);
@* }); *@

@* ChangeSegmentColor(currentSegmentColorIndex); *@
        @{

            {
                @:copySelection = 0;
            }
        }
@* }); *@

</script>

<table id="segmentTable">
    <tr>
        <td align="center">
            @{

            }
            <svg id="svgContainer" name="svgContainer" width="@(svgWidth)" height="@svgHeight">
                @{

                    {

                    }

                    {

                        {

                            {

                            }
                            else
                            {

                            }

                        <g transform="translate(@(svgTranslateX), @(svgTranslateY)) scale(@(scale), @(scale))">
                            <text x="346" y="50" fill="#000000" font-size="30">@captionText</text>
                            <!--Main-->
                            <g transform="translate(68, 50) scale(0.05, 0.05)">
                                <a href="#"><path id="@(groupId)m6" onclick="return SelectSegment('@(groupId)', 6)" fill="white" stroke="black" stroke-width="20" d="M8700 3500 A 2700 2700 0 0 0 7350 1161.7314097820158L 6950 1854.5517328095666 A 1900 1900 0 0 1 7900 3500 Z" /></a>
                                <a href="#"><path id="@(groupId)m1" onclick="return SelectSegment('@(groupId)', 1)" fill="white" stroke="black" stroke-width="20" d="M7350 1161.7314097820158 A 2700 2700 0 0 0 4650.000000000001 1161.7314097820154L 5050 1854.5517328095664 A 1900 1900 0 0 1 6950 1854.5517328095666 Z" /></a>
                                <a href="#"><path id="@(groupId)m2" onclick="return SelectSegment('@(groupId)', 2)" fill="white" stroke="black" stroke-width="20" d="M4650.000000000001 1161.7314097820154 A 2700 2700 0 0 0 3300 3499.9999999999995L 4100 3499.9999999999995 A 1900 1900 0 0 1 5050 1854.5517328095664 Z" /></a>
                                <a href="#"><path id="@(groupId)m3" onclick="return SelectSegment('@(groupId)', 3)" fill="white" stroke="black" stroke-width="20" d="M3300 3499.9999999999995 A 2700 2700 0 0 0 4649.999999999999 5838.268590217984L 5049.999999999999 5145.448267190433 A 1900 1900 0 0 1 4100 3499.9999999999995 Z" /></a>
                                <a href="#"><path id="@(groupId)m4" onclick="return SelectSegment('@(groupId)', 4)" fill="white" stroke="black" stroke-width="20" d="M4649.999999999999 5838.268590217984 A 2700 2700 0 0 0 7350 5838.268590217984L 6950 5145.448267190433 A 1900 1900 0 0 1 5049.999999999999 5145.448267190433 Z" /></a>
                                <a href="#"><path id="@(groupId)m5" onclick="return SelectSegment('@(groupId)', 5)" fill="white" stroke="black" stroke-width="20" d="M7350 5838.268590217984 A 2700 2700 0 0 0 8700 3500.0000000000005L 7900 3500.0000000000005 A 1900 1900 0 0 1 6950 5145.448267190433 Z" /></a>
                                <a href="#"><path id="@(groupId)m12" onclick="return SelectSegment('@(groupId)', 12)" fill="white" stroke="black" stroke-width="20" d="M7900 3500 A 1900 1900 0 0 0 6950 1854.5517328095666L 6550 2547.3720558371174 A 1100 1100 0 0 1 7100 3500 Z" /></a>
                                <a href="#"><path id="@(groupId)m7" onclick="return SelectSegment('@(groupId)', 7)" fill="white" stroke="black" stroke-width="20" d="M6950 1854.5517328095666 A 1900 1900 0 0 0 5050 1854.5517328095664L 5450 2547.3720558371174 A 1100 1100 0 0 1 6550 2547.3720558371174 Z" /></a>
                                <a href="#"><path id="@(groupId)m8" onclick="return SelectSegment('@(groupId)', 8)" fill="white" stroke="black" stroke-width="20" d="M5050 1854.5517328095664 A 1900 1900 0 0 0 4100 3499.9999999999995L 4900 3500 A 1100 1100 0 0 1 5450 2547.3720558371174 Z" /></a>
                                <a href="#"><path id="@(groupId)m9" onclick="return SelectSegment('@(groupId)', 9)" fill="white" stroke="black" stroke-width="20" d="M4100 3499.9999999999995 A 1900 1900 0 0 0 5049.999999999999 5145.448267190433L 5450 4452.627944162882 A 1100 1100 0 0 1 4900 3500 Z" /></a>
                                <a href="#"><path id="@(groupId)m10" onclick="return SelectSegment('@(groupId)', 10)" fill="white" stroke="black" stroke-width="20" d="M5049.999999999999 5145.448267190433 A 1900 1900 0 0 0 6950 5145.448267190433L 6550 4452.627944162882 A 1100 1100 0 0 1 5450 4452.627944162882 Z" /></a>
                                <a href="#"><path id="@(groupId)m11" onclick="return SelectSegment('@(groupId)', 11)" fill="white" stroke="black" stroke-width="20" d="M6950 5145.448267190433 A 1900 1900 0 0 0 7900 3500.0000000000005L 7100 3500.0000000000005 A 1100 1100 0 0 1 6550 4452.627944162882 Z" /></a>
                                <a href="#"><path id="@(groupId)m13" onclick="return SelectSegment('@(groupId)', 13)" fill="white" stroke="black" stroke-width="20" d="M6952.627944162883 2950 A 1100 1100 0 0 0 5047.372055837117 2950L 5740.192378864668 3350 A 300 300 0 0 1 6259.807621135332 3350 Z" /></a>
                                <a href="#"><path id="@(groupId)m14" onclick="return SelectSegment('@(groupId)', 14)" fill="white" stroke="black" stroke-width="20" d="M5047.372055837117 2950 A 1100 1100 0 0 0 5047.372055837118 4050L 5740.192378864668 3650 A 300 300 0 0 1 5740.192378864668 3350 Z" /></a>
                                <a href="#"><path id="@(groupId)m15" onclick="return SelectSegment('@(groupId)', 15)" fill="white" stroke="black" stroke-width="20" d="M5047.372055837118 4050 A 1100 1100 0 0 0 6952.627944162882 4050.0000000000005L 6259.807621135332 3650 A 300 300 0 0 1 5740.192378864668 3650 Z" /></a>
                                <a href="#"><path id="@(groupId)m16" onclick="return SelectSegment('@(groupId)', 16)" fill="white" stroke="black" stroke-width="20" d="M6952.627944162882 4050.0000000000005 A 1100 1100 0 0 0 6952.627944162883 2950.000000000001L 6259.807621135332 3350 A 300 300 0 0 1 6259.807621135332 3650 Z" /></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 17)">
                                    <circle id="@(groupId)m17" cx="6000" cy="3500" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5790" y="3620" fill="black" font-size="420">17</text>
                                </a>
                                <line x1="8700" y1="3500" x2="6300" y2="3500" stroke="black" stroke-width="20" />
                                <line x1="9031.088913245536" y1="1750.0000000000002" x2="6259.807621135332" y2="3350" stroke="red" stroke-width="20" />
                                <line x1="7350" y1="1161.7314097820158" x2="6550" y2="2547.3720558371174" stroke="black" stroke-width="20" />
                                <line x1="6000" y1="320" x2="6000" y2="3200" stroke="green" stroke-width="20" />
                                <line x1="4650.000000000001" y1="1161.7314097820154" x2="5450" y2="2547.3720558371174" stroke="black" stroke-width="20" />
                                <line x1="2968.9110867544646" y1="1750.0000000000002" x2="5740.192378864668" y2="3350" stroke="blue" stroke-width="20" />
                                <line x1="3300" y1="3499.9999999999995" x2="5700" y2="3500" stroke="black" stroke-width="20" />
                                <line x1="2276.0907637269142" y1="5650" x2="5740.192378864668" y2="3650" stroke="red" stroke-width="20" />
                                <line x1="4649.999999999999" y1="5838.268590217984" x2="5450" y2="4452.627944162882" stroke="black" stroke-width="20" />
                                <line x1="5999.999999999999" y1="7000" x2="6000" y2="3800" stroke="green" stroke-width="20" />
                                <line x1="7350" y1="5838.268590217984" x2="6550" y2="4452.627944162882" stroke="black" stroke-width="20" />
                                <line x1="9723.909236273084" y1="5650.000000000002" x2="6259.807621135332" y2="3650" stroke="blue" stroke-width="20" />
                                <a href="#" onclick="return SelectSegment('@(groupId)', 6)">
                                    <circle cx="7991.858428704209" cy="2350" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="7883.858428704209" y="2470" fill="black" font-size="420">6</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 1)">
                                    <circle cx="6000" cy="1200" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5892" y="1320" fill="black" font-size="420">1</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 2)">
                                    <circle cx="4008.141571295791" cy="2350" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="3900.141571295791" y="2470" fill="black" font-size="420">2</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 3)">
                                    <circle cx="4008.1415712957914" cy="4650" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="3900.1415712957914" y="4770" fill="black" font-size="420">3</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 4)">
                                    <circle cx="6000" cy="5800" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5892" y="5920" fill="black" font-size="420">4</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 5)">
                                    <circle cx="7991.858428704209" cy="4650.000000000001" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="7883.858428704209" y="4770.000000000001" fill="black" font-size="420">5</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 12)">
                                    <circle cx="7299.038105676658" cy="2750" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="7089.038105676658" y="2870" fill="black" font-size="420">12</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 7)">
                                    <circle cx="6000" cy="2000" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5892" y="2120" fill="black" font-size="420">7</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 8)">
                                    <circle cx="4700.961894323342" cy="2750" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="4592.961894323342" y="2870" fill="black" font-size="420">8</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 9)">
                                    <circle cx="4700.961894323342" cy="4250" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="4592.961894323342" y="4370" fill="black" font-size="420">9</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 10)">
                                    <circle cx="6000" cy="5000" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5790" y="5120" fill="black" font-size="420">10</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 11)">
                                    <circle cx="7299.038105676657" cy="4250.000000000001" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="7089.038105676657" y="4370.000000000001" fill="black" font-size="420">11</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 16)">
                                    <circle cx="6700" cy="3500" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="6490" y="3620" fill="black" font-size="420">16</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 13)">
                                    <circle cx="6000" cy="2800" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5790" y="2920" fill="black" font-size="420">13</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 14)">
                                    <circle cx="5300" cy="3500" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5090" y="3620" fill="black" font-size="420">14</text>
                                </a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 15)">
                                    <circle cx="6000" cy="4200" r="300" fill="white" stroke="green" stroke-width="20" />
                                    <text x="5790" y="4320" fill="black" font-size="420">15</text>
                                </a>
                            </g>

                            <!--Four chamber-->
                            <g transform="translate(50, 632) scale(0.06, -0.06)" fill="#ffffff" stroke="none">
                                <rect x="-100" y="1000" width="3000" height="4000" style="stroke: red; stroke-width: 16;" />
                                <text x="250" y="0" fill="red" font-size="400" transform="scale(1,-1)">Four chamber</text>
                                <path fill="#000000" d="M1745 4494
							        c-174 -18 -362 -72 -514 -149 -71 -35 -133 -70 -138 -78  -4 -7 -28 -28 -53 -47 -104 -80 -444 -407 -477 -460 -20 -30 -70 -99 -112 -153 -72 -93 -78 -104 -119 -235 -46
								        -147 -48 -161 -77 -557 -14 -192 -14 -224 -2 -254 14 -33 13 -36 -21 -90 -58 -91 -72 -139 -72 -250
							        l0 -98 58 -95
							        c31 -52 77 -139 101 -194 39 -89 49 -104 107 -152 34 -29 87 -61 116 -72
							        l53 -20 160 23
							        c142 21 166 22 210 10 28 -7 129 -29 225 -49 96 -19 240 -52 320 -72
							        l145 -36 114 24
							        c153 30 378 107 471 160 82 47 165 122 270 246 68 79 70 84 95 185 35 146 33 181 -15 285 -22 48 -40 92 -40 99 0 7 15 32 34 56 18 24 49 81 69 126
							        l35 83 -68 267
							        c-58 229 -100 385 -175 649 -10 35 -44 90 -110
							        178 -52 71 -95 137 -95 147 0 10 -38 53 -85 95 -47 43 -85 84 -85 91 0 7 -4
							        13 -8 13 -5 0 -33 48 -63 108 -45 90 -115 201 -138 222 -3 3 -55 1 -116 -6z
						        " />

                                <!--17-->
                                <a href="#">
                                    <path id="@(groupId)fc17" onclick="return SelectSegment('@(groupId)', 17)" fill="#ffffff" d="M1745 4494 m148 -82
								        c22 -34 38 -64 36 -67 -7 -6 -249 22 -349 41
								        l-95 18 58 18
								        c73 22 254 57 287 55 18 -2 34 -17 63 -65z
							        " />
                                </a>

                                <!--14-->
                                <a href="#">
                                    <path id="@(groupId)fc14" onclick="return SelectSegment('@(groupId)', 14)" fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46
								        c48 -8 90 -17 93 -20 3 -2 2 -22 -1 -43 -5 -28 -23 -56 -67 -103 -38 -41 -86 -113 -134 -200 -41 -74 -78 -141 -83 -148 -7 -10 -31 -4 -113 28 -57 23 -109 47
									        -115 53 -30 33 -37 298 -8 327 5 5 63 34 129 65 134 61 154 64 299 41z
							        " />
                                </a>

                                <!--16-->
                                <a href="#">
                                    <path id="@(groupId)fc16" onclick="return SelectSegment('@(groupId)', 16)" fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37
								        c69 -7 122 -17 131 -25 9 -7 31 -46 50 -85 26 -53 54 -90 108 -143 78 -75 118 -124 109 -132 -7 -7 -227 -64 -247 -64 -8 0 -30 27 -48 60 -18 33 -30 60 -26
									        60 4 0 -2 10 -13 23 -11 12 -36 51 -56 87 -29 51 -49 73 -97 105 -61 40 -67 52 -54 103 7 26 -3 25 143 11z
							        " />
                                </a>

                                <path fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131
							        c57 -42 62 -48 134 -197
							        l74 -154 0 -397 0 -397 -165 5 -165 5 0 -52
							        c0 -34 -4 -51 -12 -51 -7 -1 -43 -62 -80 -138 -87 -179 -134 -244 -199 -277 -28 -14 -51 -25 -52 -25 -8 0 47 517 97 915 19 149 37 299 41
								        335 5 55 18 88 82 210 49 95 92 164 126 201 29 31 54 57 56 58 2 0 30 -18 63 -41z
							        m-625 -49
							        c-4 -44 -9 -85 -13 -90 -5 -9 -148 -117 -317 -240 -42 -30 -54 -48 -97 -142 -32 -71 -75 -140 -125 -206 -62 -81 -81 -114 -100 -178 -25 -81 -65 -338 -86 -548 -9 -92 -17 -132 -37 -170
							        l-25 -50 -15 35
							        c-10 26 -13 57 -8 115 3 44 13 181 21 305 16 216 18 231 62 375 44 147 46 152 117 240 40 50 89 117 109 150 25 40 88 107 193 205 195 181 306 279 318 280 5 0 6 -35 3 -81z
							        m-69 -487
							        l-80 -285 5 -336
							        c4 -253 9 -352 20 -401 9 -36 14 -66 12 -68 -2 -2 -27 41 -54 97 -43 85 -57 104 -89 120
							        l-38 19 10 -71
							        c8 -51 22 -91 48 -137 34 -59 45 -70 115 -109 103 -58 116 -69 99 -86 -8 -9 -16 -56 -20 -132 -4 -65 -14 -168 -21 -230
							        l-13 -112 -53 -60
							        c-51 -58 -59 -62 -150 -91 -79 -25 -113 -30 -193 -30
							        l-97 0 -50 53
							        c-27 28 -73 97 -103 152 -45 84 -57 116 -70 196 -20 121 -19 180 3 244 17 49 18 50 101 83 46 18 93 40 104 48 11 8 30 14 41 14 15 1 28 18 53 73 27 61 32 84 33 147 0 41 6 104
								        14 140 20 92 21 90 -28 90
							        l-44 0 -16 -112
							        c-10 -62 -17 -138 -17 -168 0 -50 -4 -60 -44 -111 -34 -43 -52 -58 -78 -62 -83 -14 -122 -18 -113 -10 28 22 54 114 65 228 19 203 59 461 83 538 19 57 41 96 103 178 54 71 92 134
								        119 198 26 61 50 101 70 117 17 13 100 76 185 139
							        l155 116 7 -47
							        c6 -41 -4 -86 -74 -332z
						        " />

                                <!--9-->
                                <a href="#">
                                    <path id="@(groupId)fc9" onclick="return SelectSegment('@(groupId)', 9)" fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197
								        c58 -23 108 -47 110 -53 3 -7 -10 -143 -28 -302 -30 -259 -36 -289 -52 -292 -11 -2 -79 4 -152 13
								        l-134 18 0 56
								        c0 31 14 113 30 181 36 148 112 420 118 420 2 0 51 -18 108 -41z
							        " />
                                </a>

                                <!--12-->
                                <a href="#">
                                    <path id="@(groupId)fc12" onclick="return SelectSegment('@(groupId)', 12)" fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12
								        c111 -127 122 -146 162 -287 59 -208 147 -553 142 -558 -5 -5 -344 -81 -361 -82 -7 0 -15 24 -18 53 -4 28 -24 129 -45 222 -47 211 -59 222 -91 82 -20 -87 -25 -98 -68 -140
								        l-47 -46 3 272
								        c2 150 -1 320 -5 380
								        l-9 107 31 1
								        c16 0 73 8 125 18 139 27 137 27 181 -22z
							        " />
                                </a>

                                <path fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12 m-136 -753
							c19 -98 35 -183 35 -188 0 -4 -12 0 -27 10 -16 10 -64 38 -108 62 -44 25 -82 46 -84 48 -2 2 24 36 56 76 45 54 63 87 73 126 7 29 14 51 16 49 2 -2 20 -84 39 -183z
						" />

                                <!--3-->
                                <a href="#">
                                    <path id="@(groupId)fc3" onclick="return SelectSegment('@(groupId)', 3)" fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12 m-136 -753 m-1000 101
								        c141 -10 129 5 122 -154 -6 -134 -39 -415 -57 -479 -5 -19 -28 -58 -50 -89
								        l-42 -55 -40 39
								        c-53 51 -62 71 -87 192 -23 111 -25 172 -14 410
								        l6 152 36 -5
								        c20 -3 77 -8 126 -11z
							        " />
                                </a>

                                <path fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12 m-136 -753 m-1000 101 m763 -161
							        c12 -4 22 -12 22 -20 0 -7 6 -11 13 -8 8 3 64 -25 125 -61
							        l112 -66 -29 -57
							        c-29 -58 -51 -171 -51 -262 0 -44 4 -52 36 -78 43 -36 135 -58 191 -46 21 4 47 8 58 9 11 1 30 5 42 9 24 8 19 17 67 -153 11 -39 10 -56 -9 -135 -12 -50 -39 -126 -61 -168 -32 -62 -52 -87
								        -109 -134 -125 -102 -441 -231 -631 -257 -96 -14 -101 -13 -214 14 -167 40 -262 78 -332 132 -16 12 -16 18 -3 68 39 143 61 399 45 523
							        l-5 40 69 13
							        c137 25 253 122 320 268 26 57 35 129 46 368 0 8 265 9 298 1z
							        m-1272 -88
							        c-11 -46 -26 -162 -26 -213 0 -74 -37 -149 -90 -180 -23 -14 -43 -23 -46 -21 -2 3 16 31 41 64 42 55 44 63 45 122 0 57 13 169 26 230 3 16 12 22 30 22 23 0 25 -3 20 -24z
						        " />

                                <path fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12 m-136 -753 m-1000 101 m763 -161
							        c12 -4 22 -12 22 -20 0 -7 6 -11 13 -8 8 3 64 -25 125 -61
							        l112 -66 -29 -57
							        c-29 -58 -51 -171 -51 -262 0 -44 4 -52 36 -78 43 -36 135 -58 191 -46 21 4 47 8 58 9 11 1 30 5 42 9 24 8 19 17 67 -153 11 -39 10 -56 -9 -135 -12 -50 -39 -126 -61 -168 -32 -62 -52 -87
								        -109 -134 -125 -102 -441 -231 -631 -257 -96 -14 -101 -13 -214 14 -167 40 -262 78 -332 132 -16 12 -16 18 -3 68 39 143 61 399 45 523
							        l-5 40 69 13
							        c137 25 253 122 320 268 26 57 35 129 46 368 0 8 265 9 298 1z
							        m-1272 -88
							        c-11 -46 -26 -162 -26 -213 0 -74 -37 -149 -90 -180 -23 -14 -43 -23 -46 -21 -2 3 16 31 41 64 42 55 44 63 45 122 0 57 13 169 26 230 3 16 12 22 30 22 23 0 25 -3 20 -24z
						        " />

                                <!--6-->
                                <a href="#">
                                    <path id="@(groupId)fc6" onclick="return SelectSegment('@(groupId)', 6)" fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12 m-136 -753 m-1000 101 m763 -161 m-1272 -88 m1976 -96
								        l17 -115 -57 -112
								        c-60 -119 -64 -122 -152 -143 -28 -7 -42 -4 -82 18 -48 27 -49 29 -78 122 -26 81 -54 257 -44 269 6 6 348 80 364 78 11 -2 19 -32 32 -117z
							        " />
                                </a>

                                <path fill="#ffffff" d="M1745 4494 m148 -82 m-339 -46 m265 -37 m-107 -131 m-625 -49 m-69 -487 m218 197 m1045 -12 m-136 -753 m-1000 101 m763 -161 m-1272 -88 m1976 -96
							        m-1034 -17
							        c-4 -95 -8 -116 -36 -173 -62 -128 -175 -222 -297 -246 -33 -6 -69 -15 -79 -20 -16 -6 -13 1 12 37 30 45 62 78 62 65 0 -4 27 7 60 25 50 27 69 45 109 105 27 40 75 127 107 194 32 66 60 120 63 120 2 0 2 -48 -1 -107z
							        m1061 -176
							        c-16 -39 -47 -94 -69 -123 -22 -29 -40 -58 -40 -63 0 -6 18 -51 40 -101 29 -66 40 -105 40 -140 0 -57 -33 -208 -55 -250 -8 -16 -69 -85 -135 -154 -102 -105 -133 -131 -202 -167 -105 -54 -312 -124 -451 -151
							        l-109 -22 -151 37
							        c-84 20 -219 50 -302 67 -82 17 -181 38 -220 48 -47 12 -83 16 -110 11 -22 -4 -95 -14 -162 -23 -113 -15 -125 -14 -168 2 -26 9 -75 40 -109 69 -50 42 -68 64 -87 110 -31 78 -87 186 -132 256 -37 57 -37 58
								        -37 166 0 114 5 131 76 248 40 68 54 18 23 -88
							        l-22 -78 17 -128
							        c17 -121 21 -133 74 -232 33 -62 78 -128 110 -163
							        l55 -58 100 0
							        c82 0 117 5 196 30 90 27 100 33 158 92
							        l62 63 15 130
							        c8 72 18 175 22 230 3 55 8 103 10 106 2 4 21 2 43 -4 36 -10 39 -13 45 -55 15 -114 -6 -345 -45 -489 -11 -41 -19 -76 -17 -77 1 -1 26 -18 53 -38 54 -38 193 -88 340 -122 83 -20 97 -20 185 -9 201 27 514
								        155 646 264 92 76 136 152 174 304
							        l26 99 -28 100
							        c-15 55 -28 102 -28 105 0 4 12 12 28 18 21 10 42 41 87 130 33 65 59 123 59 130 -1 6 -4 37 -7 70
							        l-6 58 18 -68 19 -69 -29 -71z
							        m-410 -27
							        c27 -104 28 -105 75 -137
							        l47 -32 -27 -7
							        c-46 -12 -144 12 -176 43 -24 22 -28 35 -28 77 1 85 19 185 42 229
							        l22 42 9 -55
							        c5 -30 21 -102 36 -160z
						        " />

                                <a href="#" onclick="return SelectSegment('@(groupId)', 17)"><text x="1950" y="-4400" fill="#000000" font-size="200" transform="scale(1,-1)">17</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 14)"><text x="1200" y="-4100" fill="#000000" font-size="200" transform="scale(1,-1)">14</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 16)"><text x="1800" y="-4100" fill="#000000" font-size="200" transform="scale(1,-1)">16</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 9)"><text x="1100" y="-3400" fill="#000000" font-size="200" transform="scale(1,-1)">9</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 12)"><text x="2150" y="-3400" fill="#000000" font-size="200" transform="scale(1,-1)">12</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 3)"><text x="1050" y="-2800" fill="#000000" font-size="200" transform="scale(1,-1)">3</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 6)"><text x="2350" y="-2680" fill="#000000" font-size="200" transform="scale(1,-1)">6</text></a>
                            </g>

                            <!--Two chamber-->
                            <g transform="translate(100, 668) scale(0.06, -0.06)" fill="#ffffff" stroke="none">
                                <rect x="3000" y="460" width="3000" height="4000" style="stroke: green; stroke-width: 16;" />
                                <text x="3350" y="360" fill="green" font-size="400" transform="scale(1,-1)">Two chamber</text>
                                <path fill="#000000" d="M4334 3927
							        c-54 -31 -86 -61 -128 -114 -31 -39 -56 -74 -56 -77 0 -3 -19 -36 -43 -73 -52 -81 -129 -144 -215 -175 -59 -21 -64 -26 -117 -106 -31 -46 -76 -110 -100 -144 -25 -33 -42 -64 -39 -69 6 -10 -27 -71 -88
								        -161 -21 -31 -54 -101 -73 -154 -29 -81 -38 -123 -50 -252 -8 -85 -15 -181 -15 -213 0 -40 -5 -63 -17 -74 -12 -13 -13 -19 -4 -25 9 -5 12 -39 10 -126 -4 -179 30 -366 91 -504 44 -99 49 -105 76 -105
							        l29 0 5 -235
							        c3 -129 9 -242 15 -250 75 -123 193 -210 352 -260
							        l97 -30 289 0
							        c201 0 329 -5 425 -16 97 -12 171 -14 256 -9 107 6 126 10 182 38 53 27 72 44 121 112 93 128 189 288 310 521
							        l75 145 -6 82
							        c-6 70 -33 225 -41 235 -2 2 -29 -4 -61 -14 -39 -11 -82 -35 -127 -70 -64 -50 -197 -108 -212 -92 -3 3 5 36 19 74 13 38 37 127 52 198 37 173 31 213 -70 501 -43 121 -110 326 -151 455 -61 196 -71 237
								        -59 246 16 13 8 25 -12 17 -8 -3 -14 0 -14 6 0 6 -31 53 -69 105 -86 116 -133 214 -182 380 -42 143 -38 138 -166 226 -70 49 -74 50 -145 50 -68 0 -78 -3 -144 -43z
						        " />

                                <!--17-->
                                <a href="#">
                                    <path id="@(groupId)tc17" onclick="return SelectSegment('@(groupId)', 17)" fill="#ffffff" d="M4334 3927 m182 1
								    c70 -13 158 -67 191 -117 29 -43 29 -46 -5 -60 -22 -9 -50 -9 -113 -1 -54 7 -107 8 -148 2 -56 -8 -226 0 -237 10 -15 15 197 177 232 178 11 0 47 -5 80 -12z
							    " />
                                </a>

                                <!--15-->
                                <a href="#">
                                    <path id="@(groupId)tc15" onclick="return SelectSegment('@(groupId)', 15)" fill="#ffffff" d="M4334 3927 m182 1 m-60 -221
								        c2 -2 -8 -47 -23 -100
								        l-27 -96 -30 19 -30 18 -73 -136
								        c-103 -190 -224 -393 -239 -399 -25 -9 -344 155 -344 178 0 5 37 58 81 118 80 106 84 110 189 170
								        l107 61 53 91 53 92 140 -6
								        c77 -3 141 -8 143 -10z
							        " />
                                </a>

                                <!--13-->
                                <a href="#">
                                    <path id="@(groupId)tc13" onclick="return SelectSegment('@(groupId)', 13)" fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39
								        c32 -123 91 -247 174 -362 31 -43 56 -81 56 -85 -1 -20 -67 -47 -168 -68 -63 -13 -116 -22 -117 -21 -10 11 -115 261 -115 272 0 18 -35 46 -89 70 -47 22 -46 19 -30 148
								        l12 88 133 0 133 0 11 -42z
							        " />
                                </a>

                                <path fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39 m-357 -190
							        c23 -12 47 -21 52 -20 6 0 30 -9 55 -21
							        l45 -22 78 -195
							        c43 -107 93 -240 113 -295 19 -55 50 -134 69 -175 99 -217 122 -344 109 -620 -14 -310 -15 -296 34 -374 24 -38 41 -71 38 -73 -2 -2 -53 2 -113 11 -150 21 -261 72 -358 165 -38 36 -100 84 -137 105 -36 21 -86 58 -110 83
							        l-43 44 0 162
							        c0 161 -1 163 -34 245 -27 67 -45 94 -90 137 -35 33 -69 79 -90 120
							        l-32 66 54 97
							        c30 53 73 140 97 192 40 90 141 288 182 358 23 38 26 38 81 10z
						        " />

                                <!--7-->
                                <a href="#">
                                    <path id="@(groupId)tc7" onclick="return SelectSegment('@(groupId)', 7)" fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39 m-357 -190 m726 -668
								        c63 -198 112 -363 108 -366 -6 -6 -279 -66 -283 -62 -1 2 -9 46 -18 98 -12 70 -34 135 -81 245 -69 164 -125 317 -125 344 0 14 24 25 108 50 59 18 118 36 132 41 14 5 30 9 35 9 6 1 61 -161 124 -359z
							        " />
                                </a>

                                <!--10-->
                                <a href="#">
                                    <path id="@(groupId)tc10" onclick="return SelectSegment('@(groupId)', 10)" fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39 m-357 -190 m726 -668 m-1293 245
								        c92 -41 172 -79 178 -85 7 -7 0 -33 -21 -80
								        l-31 -70 35 -82
								        c26 -62 49 -98 92 -143 32 -33 73 -89 90 -124
								        l32 -62 -7 -150 -7 -149 -27 0
								        c-15 0 -47 3 -72 6 -43 6 -47 10 -88 76 -24 38 -48 72 -53 75 -4 2 -33 -5 -63 -18
								        l-56 -22 -126 27
								        c-70 15 -162 36 -205 48 -70 18 -78 23 -73 42 2 11 8 102 12 201 6 179 7 181 46 279 61 155 143 306 165 306 6 0 87 -34 179 -75z
							        " />
                                </a>

                                <!--1-->
                                <a href="#">
                                    <path id="@(groupId)tc1" onclick="return SelectSegment('@(groupId)', 1)" fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39 m-357 -190 m726 -668 m-1293 245 m1443 -692
								        c5 -21 21 -83 35 -139
								        l25 -100 -64 -203
								        c-35 -112 -67 -207 -72 -211 -4 -4 -44 -12 -87 -17
								        l-79 -9 -49 75 -50 76 6 130
								        c8 171 18 375 20 375 3 2 292 58 298 59 4 1 12 -16 17 -36z
							        " />
                                </a>

                                <!--4-->
                                <a href="#">
                                    <path id="@(groupId)tc4" onclick="return SelectSegment('@(groupId)', 4)" fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39 m-357 -190 m726 -668 m-1293 245 m1443 -692 m-1749 -104
								        c131 -28 251 -58 275 -68
								        l21 -9 -19 -203
								        c-25 -278 -19 -255 -88 -327
								        l-60 -63 -57 3 -57 3 -42 81
								        c-37 70 -45 100 -68 235 -20 115 -25 172 -20 224 6 75 15 145 19 145 1 0 44 -9 96 -21z
							        " />
                                </a>

                                <path fill="#ffffff" d="M4334 3927 m182 1 m-60 -221 m314 -39 m-357 -190 m726 -668 m-1293 245 m1443 -692 m-1749 -104
							        m459 -90
							        c24 -33 53 -67 66 -75 27 -17 203 -382 189 -392 -5 -4 -18 -24 -29 -45 -25 -50 -47 -68 -219 -176
							        l-145 -90 -90 41
							        c-86 39 -91 43 -91 72 0 44 10 74 25 78 8 2 39 35 69 73
							        l56 70 0 80
							        c0 84 13 216 31 323 10 58 14 64 48 82 20 10 38 19 41 19 3 0 25 -27 49 -60z
							        m256 -122
							        c34 -37 80 -72 141 -106 44 -25 84 -86 61 -94 -7 -2 33 -49 88 -104
							        l100 -100 117 -31
							        c115 -31 119 -31 240 -22 68 5 165 10 216 10 108 0 187 27 260 89 84 69 77 69 99 4
							        l20 -58 -23 -67
							        c-15 -44 -43 -95 -85 -149 -34 -45 -86 -124 -115 -176 -30 -52 -87 -134 -128 -181 -71 -85 -76 -88 -167 -124
							        l-94 -37 -335 -2
							        c-224 -2 -350 1 -380 9 -25 6 -97 12 -160 12 -185 2 -208 8 -275 68 -108 95 -131
							        131 -145 221 -12 79 -12 81 14 126
							        l27 46 67 -37 67 -37 75 35
							        c50 24 108 64
							        175 122 96 83 101 90 133 169
							        l32 82 -85 180
							        c-47 99 -85 184 -85 189 0 5 26 6 58 3 49 -5 61 -11 87 -40z
							        m428 -312
							        c84 -39 115 -48 215 -61 64 -9 124 -19 133 -24 24 -13 187 16 299 52 74 24 112 43 172 88 73 55 141 82 158 63 11 -13
							        29 -112 36 -199
							        l7 -82 -73 -138
							        c-104 -198 -233 -416 -302 -510 -50 -70 -67 -86 -121 -112 -54 -27 -77 -32 -177 -38 -81 -4 -156 -1 -252 10 -96 11 -224
							        16 -424 16
							        l-287 0 -96 30
							        c-124 39 -233 109 -294 188 -52 67 -58 96 -58 302 0 260 -2 242 24 238 12 -2 21 -9 20 -15 -2 -7 -5 -30 -8 -52 -4 -35 -2 -41 14 -41 10 0 57 -18 105 -41 48 -22 90 -39 94 -37 5 2
								        81 50 170 107 90 57 165 101 168 98 12 -11 -213 -190 -276 -218 -71 -33 -65 -33 -156 20
							        l-51 29 -32 -56 -33 -57 12 -83
							        c14 -98 44 -144 153 -240 64 -57 69 -59 134 -65 38 -4 111 -7 163 -7 52 -1 127 -7 166 -14 52 -9 160 -12 390 -9
							        l319 4 96 40
							        c93 39 98 43 169 127 41 47 98 129 128 182 29 52 81 131 115 175 42 55 70 104 89 157
							        l27 77 -26 76 -26 76 -26 -23
							        c-14 -12 -42 -37 -61 -55 -19 -18 -65 -46 -102 -63 -59 -26 -79 -30 -155 -30 -49 0 -144 -5 -213 -10 -122 -10 -127 -9 -240 21
							        l-115 30 -77 77
							        c-43 42 -78 82 -78 90 0 7 -8 25 -17 40 -10 16 8 4 43 -30 43 -42 85 -69 157 -103z
						        " />

                                <a href="#" onclick="return SelectSegment('@(groupId)', 17)"><text x="4850" y="-3800" fill="#000000" font-size="200" transform="scale(1,-1)">17</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 15)"><text x="3950" y="-3300" fill="#000000" font-size="200" transform="scale(1,-1)">15</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 13)"><text x="4600" y="-3450" fill="#000000" font-size="200" transform="scale(1,-1)">13</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 10)"><text x="3650" y="-2600" fill="#000000" font-size="200" transform="scale(1,-1)">10</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 7)"><text x="4950" y="-2650" fill="#000000" font-size="200" transform="scale(1,-1)">7</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 4)"><text x="3550" y="-1900" fill="#000000" font-size="200" transform="scale(1,-1)">4</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 1)"><text x="5100" y="-1980" fill="#000000" font-size="200" transform="scale(1,-1)">1</text></a>
                            </g>

                            <!--Long axis-->
                            <g transform="translate(130, 628) scale(0.06, -0.06)" fill="#ffffff" stroke="none">
                                <rect x="6500" y="930" width="3000" height="4000" style="stroke: blue; stroke-width: 16;" />
                                <text x="7200" y="0" fill="blue" font-size="400" transform="scale(1,-1)">Long axis</text>
                                <path fill="#000000" d="M7990 4496
							        l-84 -74 -105 -22
							        c-59 -11 -119 -28 -136 -37 -63 -34 -152 -107 -230 -191 l-82 -87 -22 -100
							        c-16 -70 -48 -156 -107 -288 -80 -177 -86 -196 -109 -332 -14 -80 -37 -211 -51 -292 -22 -129 -25 -165 -19 -278 3 -71 8 -146 11 -165 4 -30 -2 -45 -46 -105 -28 -38 -82 -101 -119 -140 -67 -70
								        -68 -70 -99 -185 -17 -63 -36 -160 -43 -215 -12 -98 -11 -102 19 -212 l30 -113 79 -84
							        c43 -47 114 -118 159 -160 80 -74 82 -75 180 -100 145 -36 344 -66 491 -75
							        l128 -8 104 44
							        c74 31 106 41 111 32 6 -8 42 7 121 49 104 56 119 67 167 131 l53 70 -23 112 -23 113 -140 142
							        c-77 78 -159 157 -182 175
							        l-43 34 38 49
							        c20 27 54 63 74 79 l37 29 62 -21
							        c35 -11 66 -21 70 -21 5 0 18 28 30 61 36 99 104 167 167 169 16 0 22 -6 22 -22 0 -20 1 -20 18 -5 18 16 20 15 39 -26 12 -23 24 -58 28 -77 11 -50 60 -145 115 -220 31 -43 52 -84 60 -120 10 -42
								        26 -71 69 -122 63 -76 64 -76 95 14 l19 56 -41 53
							        c-22 29 -39 56 -37 59 2 4 30 15 62 24 50 16 66 27 115 85 145 170 203 281 243 463 34 159 34 266 -1 362 -14 39 -74 170 -135 291 -104 209 -112 223 -173 278 -60 53 -73 60 -180 91
							        l-116 33 -1 -66
							        c-1 -64 -2 -62 -25 69 -13 74 -22 141 -19 148 3 7 0 20 -5 30 -10 19 -60 281 -60 318 0 13 -13 39 -30 57 -16 19 -30 42 -30 51 0 9 -7 16 -16 16 -9 0 -43 26 -77 58 -41 39 -89 71 -142 96 -44 20
								        -101 50 -126 67 -25 16 -48 29 -50 29 -2 0 -42 -33 -89 -74z
						        " />

                                <!--17-->
                                <a href="#">
                                    <path id="@(groupId)la17" onclick="return SelectSegment('@(groupId)', 17)" fill="#ffffff" d="M7990 4496 m153 3
								        c42 -22 243 -153 262 -171 6 -6 -17 -8 -65 -4 -41 3 -180 8 -308 12 -293 7 -315 12 -201 43 67 18 103 35 170 82 46 32 88 58 92 59 4 0 27 -10 50 -21z
							        " />
                                </a>

                                <!--16-->
                                <a href="#">
                                    <path id="@(groupId)la16" onclick="return SelectSegment('@(groupId)', 16)" fill="#ffffff" d="M7990 4496 m153 3 m-122 -216
								        c5 -15 12 -46 16 -69
								        l5 -42 -63 -7
								        c-95 -11 -183 -81 -302 -240 -49 -66 -93 -124 -97 -128 -7 -8 -214 75 -234 93 -5 5 4 42 19 85 32 88 113 201 203 282
								        l57 53 193 0 192 0 11 -27z
							        " />
                                </a>

                                <!--14-->
                                <a href="#">
                                    <path id="@(groupId)la14" onclick="return SelectSegment('@(groupId)', 14)" fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14
							            c56 -37 62 -57 101 -336
							            l6 -42 -42 -6
							            c-24 -3 -81 -5 -128 -3
							            l-84 3 -11 55
							            c-6 30 -12 58 -13 63 -1 4 -2 16 -2 28 0 35 -98 129 -167 161 -35 15 -63 32 -63 37 0 5 -3 23 -6 40
							            l-6 31 184 0 185 0 46 -31z
							            " />
                                </a>

                                <path fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162
							        c50 -47 58 -60 70 -113 7 -32 13 -92 13 -131 1 -40 4 -73 7 -73 4 0 12 -39 19 -87 16 -121 89 -554 95 -563 6 -10 52 -400 61 -519 7 -90 6 -94 -12 -87 -26 11 -117 36 -129 36 -12 0 -75 -94 -75 -112
								        0 -16 -33 -105 -43 -114 -3 -4 -12 -2 -19 4 -42 33 -89 51 -123 45 -43 -7 -59 -28 -90 -116 -36 -104 -36 -104 -78 -49 -32 43 -37 56 -43 129 -8 96 5 144 77 276 40 74 79 190 67 202 -3 3 -86
								        -65 -168 -139 -28 -24 -45 -56 -79 -150
							        l-44 -119 29 -121
							        c25 -104 37 -133 84 -208 55 -87 56 -88 215 -195
							        l160 -108 32 -84
							        c18 -47 33 -91 33 -99 0 -7 -21 -44 -47 -81 -45 -66 -49 -70 -158 -120 -104 -49 -118 -52 -225 -61 -63 -5 -164 -10 -225 -10 -96 0 -125 5 -225 34 -63 19 -158 54 -210 79 -80 38 -107 58 -175 126
							        l-79 81 -4 225
							        c-4 225 -4 226 23 303 15 42 55 120 89 172 34 52 69 112 79 132 19 43 93 96 183 133 33 13 87 48 120 77
							        l62 53 3 78
							        c2 42 1 77 -3 77 -4 0 -28 -25 -55 -55 -44 -51 -117 -105 -142 -105 -7 0 -11 29 -11 78 0 144 72 356 269 791 34 74 61 138 61 142 0 5 41 64 91
								        131 63 85 113 139 164 180 73 58 74 59 172 72 54 7 105 14 113 14 8 1 40 -22 71 -51z
						        " />

                                <!--11-->
                                <a href="#">
                                    <path id="@(groupId)la11" onclick="return SelectSegment('@(groupId)', 11)" fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301
								        l114 -54 -59 -132
								        c-33 -72 -83 -188 -111 -258 -29 -70 -53 -128 -54 -130 -2 -2 -49 9 -104 24 -70 19 -101 31 -101 42 0 54 53 222 116 370 41 95 74 177 74 182 0 15 0 15 125 -44z
							        " />
                                </a>

                                <!--8-->
                                <a href="#">
                                    <path id="@(groupId)la8" onclick="return SelectSegment('@(groupId)', 8)" fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301 m1169 -141
							            c14 -93 39 -239 55 -322 15 -84 25 -155 23 -158 -3 -3 -65 -5 -138 -5
							            l-132 0 -6 28
							            c-12 58 -84 509 -88 547
							            l-3 39 125 27
							            c69 14 128 23 131 20 4 -3 18 -82 33 -176z
							            " />
                                </a>

                                <path fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301 m1169 -141 m194 -30
							        c60 -17 85 -31 137 -78 60 -54 69 -68 173 -279 144 -291 152 -315 152 -433 0 -196 -69 -411 -178 -550 -107 -138 -133 -164 -186 -181 -28 -9 -56 -19 -62 -21 -7 -2 -17
								        10 -23 28 -21 60 -15 75 47 120 146 107 223 209 260 346 19 68 21 97 16 165 -8 114 -54 255 -114 348 -28 43 -58 107 -71 150 -13 44 -41 104 -68 144 -43 65 -51 72 -128 107
							        l-83 38 0 66 0 65 28 -6
							        c15 -4 60-17 100 -29z
						        " />

                                <path fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301 m1169 -141 m194 -30 m-48 -153
							        c66 -32 80 -45 117 -99 23 -36 52 -97 66 -142 14 -47 42 -105 67 -142 25 -36 56 -102 75 -158 93 -279 32 -482 -200 -664 -55 -43 -99 -83 -97 -88 1 -6 -1 -8 -6 -5 -5 3 -19 34 -32
								        68 -24 66 -89 195 -119 236 -18 25 -17 26 30 116 36 68 54 120 74 210 26 118 26 118 10 220 -26 168 -76 460 -80 474 -7 19 14 14 95 -26z
						        " />

                                <!--5-->
                                <a href="#">
                                    <path id="@(groupId)la5" onclick="return SelectSegment('@(groupId)', 5)" fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301 m1169 -141 m194 -30 m-48 -153 m-1460 -299
								        c0 -6 -14 -69 -31 -140 -23 -98 -30 -149 -30 -218
								        l2 -90 -77 -48
								        c-41 -27 -79 -46 -82 -42 -4 4 -9 75 -10 158 -3 130 0 173 22 298 14 80 26 148 26 152 0 3 40 -9 90 -26 49 -18 90 -37 90 -44z
							        " />
                                </a>

                                <!--2-->
                                <a href="#">
                                    <path id="@(groupId)la2" onclick="return SelectSegment('@(groupId)', 2)" fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301 m1169 -141 m194 -30 m-48 -153 m-1460 -299 m1400 -47
								        c0 -2 7 -40 15 -83 8 -43 15 -95 15 -116 0 -64 -38 -196 -87 -303 -44 -97 -48 -102 -85 -114 -22 -6 -42 -9 -45 -6 -10 9 -22 106 -29 216 -4 87 -22 224 -49 383
								        l-4 27 134 0
								        c74 0 135 -2 135 -4z
							        " />
                                </a>

                                <path fill="#ffffff" d="M7990 4496 m153 3 m-122 -216 m442 -14 m-262 -162 m-756 -301 m1169 -141 m194 -30 m-48 -153 m-1460 -299 m1400 -47 m-1302 -418
							        c-58 -52 -110 -80 -133 -72 -8 4 -15 2 -15 -3 0 -6 -16 -18 -35 -29 -19 -10 -35 -22 -35 -26 0 -5 -7 -8 -15 -8 -22 0 -63 -44 -81 -87 -9 -20 -42 -76 -75 -125 -33 -48 -73 -125 -90 -170
							        l-31 -83 5 -231 4 -230 89 -89
							        c76 -76 101 -94 184 -132 52 -25 148 -60 212 -79 132 -38 210 -43 441 -24 122 10 129 12 240 64
							        l114 54 55 79 54 78 -38 99
							        c-20 54 -40 101 -43 105 -4 3 -77 53 -163 111 -153 103 -157 107 -208 188 -41 65 -57 104 -78 187 -14 57 -26 115 -26 127 1 13 20 72 43 132 42 110 43 110 121 175
							        l79 66 -5 -35
							        c-4 -19 -25 -69 -48 -110 -23 -41 -53 -104 -67 -140 -22 -58 -24 -74 -18 -150 7 -84 8 -86 62 -157 32 -42 90 -100 138 -137 76 -60 215 -199 271 -272 15 -20 31 -66 45 -131
							        l22 -101 -52 -66
							        c-45 -57 -67 -73 -158 -122 -72 -39 -107 -53 -112 -45 -5 8 -39 -2 -114 -33
							        l-107 -45 -133 9
							        c-148 10 -335 39 -474 74 -110 28 -126 39 -289 209
							        l-120 124 -30 114 -31 114 18 116
							        c9 64 30 158 45 210
							        l28 94 123 127
							        c68 70 123 134 123 142 0 9 5 16 11 16 6 0 8 7 5 16 -4 12 -2 15 9 10 8 -3 23 1 33 9 60 50 96 75 108 75 8 0 14 4 14 8 0 5 23 21 51 37 28 15 68 46 88 68
							        l36 40 3 -48
							        c3 -48 2 -50 -50 -97z
							        m993 -184
							        c30 -9 58 -18 63 -21 13 -8 -16 -23 -44 -23 -33 0 -110 -97 -136 -171 -10 -27 -23 -48 -29 -46 -5 3 -36 13 -67 22
							        l-57 18 -45 -37
							        c-25 -20 -60 -57 -76 -82 -36 -52 -56 -58 -36 -11 52 120 66 156 66 165 0 25 90 31 110 6 20 -24 49 -36 70 -29 12 4 26 28
							        41 73 20 62 67 151 80 152 3 0 30 -7 60 -16z
							        m226 -68
							        c32 -36 88 -143 122 -231 35 -89 67 -147 125 -230
							        l39 -54 -17 -46
							        c-10 -25 -21 -45 -24 -45 -4 0 -26 22 -49 49 -30 35 -46 66 -58 111 -11 43 -31 82 -59 119 -56 73 -103 166 -120 239 -7 32 -18 68 -24 79 -6 11 -8 23 -5 27 14 14 51 5 70 -18z
						        " />

                                <a href="#" onclick="return SelectSegment('@(groupId)', 17)"><text x="8300" y="-4500" fill="#000000" font-size="200" transform="scale(1, -1)">17</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 16)"><text x="7500" y="-4000" fill="#000000" font-size="200" transform="scale(1, -1)">16</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 14)"><text x="8300" y="-4000" fill="#000000" font-size="200" transform="scale(1, -1)">14</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 11)"><text x="7250" y="-3500" fill="#000000" font-size="200" transform="scale(1, -1)">11</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 8)"><text x="8450" y="-3500" fill="#000000" font-size="200" transform="scale(1, -1)">8</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 5)"><text x="7100" y="-2900" fill="#000000" font-size="200" transform="scale(1, -1)">5</text></a>
                                <a href="#" onclick="return SelectSegment('@(groupId)', 2)"><text x="8550" y="-2800" fill="#000000" font-size="200" transform="scale(1, -1)">2</text></a>
                            </g>

                            <!--Legend-->
                            <g id="lgnd" transform="translate(@legendTranslateX, @legendTranslateY) scale(0.05, 0.05)">
                                @{

                                    {

                                    }

                                    if (!string.IsNullOrEmpty(captionText))
                                    {
                                        @: <text x="@(x1+600)" y="@(y1-1000)" fill="black" font-size="600">@captionText</text>
                                        @: <text id="@(groupId)ActiveFlag" x="@(x1+800)" y="@(y1-100)" fill="black" font-size="600">@*</text>*@
                                    }

                                    {
 recHeight;
                                        {

                                            {
                                                @: <text x="@(x1 + 800)" y="@(y1 - 1000)" fill="black" font-size="600">Segment</text>
                                            }
                                            <a href="#" style="text-decoration-line: none;">
                                                <rect fill="#fff" stroke-width="20" height="@recHeight" width="@recWidth1" y="@y" x="@x1" stroke="#000000" onclick="return SelectSegmentDefault('@(m+1)')" />
                                                <text x="@(x1 + 250)" y="@(y + 500)" fill="blue" font-size="400" onclick="return SelectSegmentDefault('@(m+1)')">@(m + 1). @legend[m]</text>
                                            </a>

                                        <a href="#" style="text-decoration-line: none;">
                                            <rect id="@(groupId)lr@(m+1)" fill="#fff" stroke-width="20" height="@recHeight" width="@recWidth2" y="@y" x="@x1" stroke="#000000" onclick="return SelectSegment('@(groupId)', '@(m+1)')" />
                                            <text id="@(groupId)lt@(m+1)" x="@(x1+100)" y="@(y+500)" fill="blue" font-size="400" onclick="return SelectSegment('@(groupId)', '@(m+1)')"></text>
                                        </a>

                            </g>
                        </g>

                    <g transform="translate(2, 480) scale(0.05, 0.05)">
                        @{

                            {
                                <a href="#" style="text-decoration-line: none;" onclick="return false;">
                                    <rect id="@(segmentColors[i, 0, 0])" fill="@(segmentColors[i, 0, 2])" stroke-width="2" height="650" width="2900" y="@cy" x="@(cx+i*xStep)" stroke="#000000" onclick="ChangeSegmentColor('@(i)');" />
                                    <text x="@(cx+i*xStep+250)" y="@(cy+500)" fill="blue" font-size="400" onclick="ChangeSegmentColor('@(i)');">@(segmentColors[i, 0, 1])</text>
                                </a>
                            }

                    </g>

                Sorry, your browser does not support inline SVG.
            </svg>
        </td>
    </tr>
</table>
