﻿<!DOCTYPE html>
<html>
<head>
    <base href="@Url.Content("~/")">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title class="hidden-print">@ViewBag.Title</title>
    @*<!-- TODO: Replace Styles.Render with direct link references for ASP.NET Core -->*@
    @RenderSection("customcss", required: false)*@<script src="~/Scripts/modernizr-2.8.3.js"></script>
        <script src="~/Scripts/jquery-3.4.1.js"></script>
        <script src="~/Scripts/bootstrap.js"></script>
        <!-- TODO: Replace Scripts.Render with direct script references for ASP.NET Core -->
    @RenderSection("topscripts", required: false)
    <style>
        .container-history .table th {*@ text-align: center;
        }
        .container-history .table > thead > tr > th {
/* border-top: solid 0px black; */
/* border-bottom: 0px solid #ddd; */
        }
    </style>
</head>
<body>
    @{

        {

        }

        {

    <span id="c-return-url-holder" data-return-url="@Html.GetCurrentUrl()" class="hidden"></span>
    <div id="main-nav" class="navbar navbar-fixed-top @navBGColorClass">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="@Url.Action("old","home",new { area="" })"><span>@moduleName</span></a>
            </div>
        </div>
    </div>
    @RenderSection("patientinfo", required: false)
    @RenderBody()
    <div class="body-content-footer text-center __4536456">
        <div id="notification-placeholder"></div>
    </div>
    @RenderSection("scripts", required: false)
</body>
</html>
