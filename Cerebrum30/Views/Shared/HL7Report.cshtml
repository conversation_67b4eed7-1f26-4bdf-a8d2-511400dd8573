﻿
@model Cerebrum.ViewModels.HL7.HL7ReportVM
@using Cerebrum30.Utility
@using Cerebrum.ViewModels.Patient
<link href="~/Areas/Labs/CSS/olis-viewer-print.css" rel="stylesheet" />
<script src="~/Scripts/ShowSensitiveResult.js"></script>

<div>
    @if (Model == null)
    {
        <div><h2> Report Not Found</h2> </div>
    }
    else
    {
        { Html.RenderPartial("~/Views/Shared/HL7ReportHeader.cshtml", Model); }

        if (anyconsentblock)
        {
            <input id="btn-consent1" type="button" class="btn btn-default btn-sm btn-primary" data-orderID='@Model.ORC4' data-IDNumber1='@Model.physicianNo' data-PatientRecordId='@Model.HL7Patient.PatientRecordId' value="Patient Consent override">

        }
        { Html.RenderPartial("~/Views/Shared/HL7ReportMarkedSeen.cshtml", @Model.MarkedSeenBy); }
        <div>
            <ul class="nav nav-tabs">
                @{

                    {

                        {
                            <li class="active">
                                <a class="hl7-version" data-teststatus="@v.status" data-hl7reportid="@v.HL7ReportId" data-id="@v.Id"

                                <input type="hidden" id="HL7_ReportVersionID" value="@v.Id" />
                                <input type="hidden" id="HL7_ReportID" value="@v.HL7ReportId" />
                            </li>

                        else
                        {
                            <li>
                                <a class="hl7-version" data-teststatus="@v.status" data-hl7reportid="@v.HL7ReportId" data-id="@v.Id"

                            </li>
                        }

            </ul>
            <div class="tab-content" style="height:500px;overflow-y:scroll;">
                @{

                    {

                        <div id="V@(r.Id)" class="tab-pane fade @active">
                            @{Html.RenderPartial("~/Views/Shared/HL7ReportVersion.cshtml", r);
                            }
                        </div>

            </div>
        </div>

</div>

@{Html.RenderPartial("HL7ShowSensitiveResult");

<script type="text/javascript">

    $(document).on('click', '.hl7-print', function (e) {
@* e.preventDefault(); *@

@* newWin.document.open(); *@

@* newWin.document.close(); *@

@* setTimeout(function () { newWin.close(); }, 10); *@

@* }); *@

    function OnVersionClick(status, rid, rvid) {
@* $("#reportStatus").html(status); *@
@* $("#HL7_ReportID").val(rid); *@
@* $("#HL7_ReportVersionID").val(rvid); *@
@* $(document).trigger("HL7ReportVersionChange"); *@
    }
    function MarkSeen() {
        $.post("/Labs/HL7/MarkSeenHL7Report/", { hl7RptId: $("#reportID").val(), hl7RptVersionId: $("#reportVersionID").val() }, function (data) {
@* alert(data); *@
@* }); *@

    $(document).on('click', '.hl7-version', function (e) {
@* e.preventDefault(); *@

@* $("#reportStatus").html(Data.teststatus); *@
@* $("#HL7_ReportID").val(Data.hl7reportid); *@
@* $("#HL7_ReportVersionID").val(Data.id); *@
@* $(document).trigger("HL7ReportVersionChange"); *@
@* }); *@
    $(document).on('click', '.btn-HL7-Result-Note', function (e) {

@* e.preventDefault(); *@

@* $('#ajax-loader').show(); *@
        $.ajax({
            url: url,
            type: 'GET',
            cache: false,
            data:
                {
                    hl7resultid: itemId
                },
            complete: function () {
@* $('#ajax-loader').hide(); *@
            },
            success: function (data) {
                loadCBModal(modalID, data, "modal-md")
            },
            error: function (jqXHR, textStatus, errorThrown) {
@* checkAjaxError(jqXHR); *@
            }
@* }); *@

@* }); *@

    $(document).on('submit', '#frm-add-hl7-comment', function (e) {
@* e.preventDefault(); *@

@* removeErrorNotifications(); *@

@* $('.modal-submit-btn').prop('disabled', true); *@
@* $('#ajax-loader').show(); *@
        $.ajax({
            url: this.action,
            type: this.method,
            data: $(this).serialize(),
            complete: function () {
@* $('.modal-submit-btn').prop('disabled', false); *@
@* $('#ajax-loader').hide(); *@
            },
            success: function (result) {

                if (result.success) {
@* hideModal(modalID); *@
@* showNotificationMessage("success", result.message); *@
@* location.reload(); *@
                }
                else {
@* $(modalContent).html(result); *@

            },
            error: function (jqXHR, textStatus, errorThrown) {
@* checkAjaxError(jqXHR); *@

@* }); *@

@* }); *@
    $(document).on('click', '#btn-consent1', function (e) {
@* e.preventDefault(); *@

@* }); *@
</script>