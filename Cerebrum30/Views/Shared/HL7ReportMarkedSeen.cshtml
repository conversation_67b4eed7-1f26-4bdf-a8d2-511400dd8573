﻿
@model IEnumerable<Cerebrum.ViewModels.HL7.ReportMarkedSeenVM>
@using Cerebrum30.Utility

  @if (Model != null && @Model.Count() > 0)
  {
    <div class="report-marked-seen">
    <table class="table">
        <tr><th> Marked Seen By</th><th>Date Time</th><th>Note</th> </tr>
        @foreach (var m in Model)
        {
            <tr>
                <td>@m.UserName</td>
                <td>
                    @{

                        {

                        }
                        else
                        {

                    @m.seenAt
                </td>

                <td>@m.comment</td>

            </tr>

    </table>
    </div>

