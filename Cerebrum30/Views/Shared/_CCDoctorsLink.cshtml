@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Common.VMCCDoctorLink

@{

    {

@* }); *@

@if (@Model.IsModal)
{
    <a href="#" class="btn-view-ccdoctors" data-patient-record-id="@patientId" data-patient-name="@patientName" data-app-id="@appointmentId" data-modal-url="@url"><span class="">CC Doctors</span></a>

else
{
    <div  id="cc-doctor-layout-holder" data-patient-record-id="@patientId" data-patient-name="@patientName" data-app-id="@appointmentId" data-load-url="@url"></div>
    <script type="text/javascript">
        $(function () {
@* loadCCDoctorsLayout(); *@
@* }); *@

        function loadCCDoctorsLayout()
        {            

            $.ajax({
                url: url,
                type: 'GET',
                complete: function () {
                },
                success: function (data) {
@* $(divId).html(data); *@
                },
                error: function (jqXHR, textStatus, errorThrown) {
@* $(divId).html(''); *@
@* checkAjaxError(jqXHR); *@

@* }); *@

    </script>

