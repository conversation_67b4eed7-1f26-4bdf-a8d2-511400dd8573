﻿@{    

}
@section metatags{
    @RenderSection("metatags", required: false)
}
@section BaseHref {
    @{ 
        if (IsSectionDefined("BaseHref"))
        {
            @RenderSection("BaseHref", required: false)
        } else {
            <base href="@Url.Content("~/")">
        }

@section customcss{
    @RenderSection("customcss", required: false)

@section topscripts{
    @RenderSection("topscripts", required: false)
}
@section patientinfo{
    @RenderSection("patientinfo", required: false)
}
@section toplinks{
    @RenderSection("toplinks", required: false)
}
@section AwareServicesMicroUiLoaderSlot {
    @RenderSection("AwareServicesMicroUiLoaderSlot", required: false)
}
<div class="container-fluid  __9987234">@*body-content*@
    @RenderBody()
</div>
@section scripts{
    @RenderSection("scripts", required: false)
}
