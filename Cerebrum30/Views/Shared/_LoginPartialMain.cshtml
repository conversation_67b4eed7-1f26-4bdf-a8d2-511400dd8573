﻿@using Microsoft.AspNetCore.Identity
@if (User.Identity.IsAuthenticated)
{

    if (userName.Contains("@"))
    {"

    }
    else
    {

"
    <div id="modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-content"></div>
    </div>
    <div id="dialog" title="Patient E-Chart" style="display: none;" class="__00092675">
        <div id="patientechartcontent" class="__778765"></div>
    </div>
    <ul class="nav navbar-nav navbar-right" id="nav_user">
        <li class="divider-vertical"></li>
        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                <span style="font-size: 8px;position: absolute;display:block;clear:both;top: 0px">Logged in as:</span>
                <span id="lbl-loggedInUser" data="@fullName"> @displayName</span>
            </a>

            <ul class="dropdown-menu">
                <li><a href="@Url.Action("index","manage",new { area="" })">Manage</a></li>
                <li><a href="#" data-modal-url="@Url.Action("LandingPages","users",new { area="admin" })" class="btn-set-landing-page">Set Default Page</a></li>
                @if (CerebrumUser.IsPrescribeITenabled)
                {
                    <li><a href="@Url.Action("index","AuthRequest",new { area="PrescribeIT" })" target="_blank">PrescribeIT Log off</a></li>
                }
                <li>
                    @Html.PartialAsync("_PunchCard", new ViewDataDictionary(ViewData) { { "area", "Employees" } })
                </li>
                @if (CerebrumUser.IsUserLinkedToOneId)
                {
                    <li id="link-unlink-oneid-account"><a href="javascript:void(null)" class="btn-unlink-oneid-account">Unlink OneId Account</a></li>
                }
                <!-- uncommment when ready a new implementation, ticket - 2479-->
                @if (CerebrumUser.UserUaoCount > 1 && _isNewUaoImplementationActive)
                {

                    <li><a href="@Url.Action("index","UserUAO", new { area="Admin" })">Select / Change UAO</a></li>
                }
                <li><a target="_blank" href="/Help/index.html">Help</a></li>
                <li><a href="@Url.Action("autologoff","account",new { area="" })" onclick="closeDashboardWindow();">Log off</a></li>
            </ul>
        </li>
        <li class="divider-vertical"></li>
    </ul>
    <ul class="nav navbar-nav navbar-right" id="nav_toDo">
        <li class="divider-vertical"></li>
        <li class="dropdown">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">To Do <div id="count-to-do" class="badge">&nbsp;</div></a>
            <ul class="dropdown-menu">
                <li><a href="@Url.Action("worklist", "measurement", new { area = "measurements", DoctorID = CerebrumUser.IsDoctor ?  CerebrumUser.PracticeDoctorId : 0  })">Worklist <div id="count-worklist" class="badge badgePad">&nbsp;</div></a></li>
                @if (CerebrumUser.HasPermission("ViewReports"))
                {
                    <li><a href="@Url.Action("index", "externaldocument", new { area="externaldocument", PracticeDoctorId=CerebrumUser.PracticeDoctorId })">Inbox <div id="count-inbox" class="badge badgePad">&nbsp;</div></a></li>
                }
                <li><a href="@Url.Action("index", "waitlist", new { area = "triage" })">Triage <div id="count-triage" class="badge badgePad">&nbsp;</div></a></li>
                <li><a href="@Url.Action("Index", "HealthCardValidationResponse", new { area = "" })">HCV Response</a></li>
                <li><a href="@Url.Action("Recall", "VP", new { area = "VP", dociD=CerebrumUser.IsDoctor?CerebrumUser.UserId:0 })" target="_blank">Recall List</a></li>
                <li><a href="@Url.Action("requisitionsearch", "requisition", new { area = "requisition" })">Open work items</a></li>
                @if (CerebrumUser.HasPermission("HospitalPersonalDaysheetAdmin,HospitalDaysheetAdmin"))
                {
                    <li><a href="@Url.Action("index", "admissions", new { area = "HD" })">Hospital Billing</a></li>
                }
                <li><a href="@Url.Action("PatientSearchLegacyNumber", "Patients", new { area = "" })">Legacy Chart Search</a></li>
                @if (CerebrumUser.HasPermission("HrmUser"))
                {
                    <li><a href="@Url.Action("Index", "HRMManagement", new { area = "Admin" })">HRM Management</a></li>
                    <li><a href="@Url.Action("ClassMapping", "HRMManagement", new { area = "Admin" })">HRM Class Mapping</a></li>
                }
                @if (!string.IsNullOrWhiteSpace(CerebrumUser.HFiDOC))
                {
                    <li><a href="@Url.Action("HFiDOC", "Study", new { area = "Study", PracticeDoctorId = CerebrumUser.PracticeDoctorId })" target="_blank">@CerebrumUser.HFiDOC</a></li>

                <li class="dropdown-submenu">
                    <a href="#" tabindex="-1">LDL TAPP</a>
                    <ul class="dropdown-menu" style="margin-left: -368px;">
                        <li><a id="btnCurrentLipidGuideline" style="cursor: pointer;">Current Lipid Guidelines</a></li>
                        @if (CerebrumUser.PracticeDoctorId > 0)
                        {
                            if (CerebrumUser.TAPP)
                            {
                                <li><a href="@Url.Action("TappData", "Study", new { area = "Study", PracticeDoctorId = CerebrumUser.PracticeDoctorId })" target="_blank">My data</a></li>
                            }
                            else
                            {
                                <li><a id="btnTappData" style="cursor: pointer;">My data</a></li>

                        else
                        {
                            <li><a><span style="color: darkgray;">My data</span></a></li>

                        <li><a id="btnAboutLDLTAPP" style="cursor: pointer;">About LDL TAPP</a></li>
                    </ul>
                </li>
                @if (CerebrumUser.HasPermission("OLISUser"))
                {
                    <li><a href="@Url.Action("OLISReportSearch", "OLIS", new { area = "Labs" })" target="_blank">OLIS Report</a></li>
                    <li><a tabindex="-1" href="@Url.Action("Index", "OLISCommunicationLog", new { area = "Labs" })" target="_blank">OLIS Log</a></li>
                }
                @if (CerebrumUser.HasPermission("AccessEdashboard"))
                {
                    <li><a href="javascript:void(null)" onclick="openDashboardWindow();">eDashboard</a></li>

                @{

                    if (isActive)
                    {
                        if (CerebrumUser.HasRole("Access Econsult")) // change role here
                        {
                            <li><a href="@Url.Action("Index", "Consult", new { area = "eConsult" })">eConsult</a></li>
                        }
                        if (CerebrumUser.HasPermission("View Medications"))
                        {
                            <li><a href="@Url.Action("Search", "PatientConsult", new { area = "eConsult" })">Search Metadata eConsult</a></li>

                        if (CerebrumUser.HasRole("Access Econsult")) // change role here
                        {
                            <li><a href="@Url.Action("Index", "ConsultSearch", new { area = "eConsult" })">Search Active eConsult</a></li>

            </ul>
        </li>
    </ul>
    <ul class="nav navbar-nav navbar-right" id="nav_myTasks">
        <li class="divider-vertical"></li>
        <li><a href="@Url.Action("index", "contactmanagers", new { area = "contactmanagers" })">My Tasks<div id="count-contact-tasks" class="badge badge-warning">&nbsp;</div></a></li>
    </ul>
    if (CerebrumUser.HasPermissionType("Admin"))
    {
        //Admin Menu
        <ul class="nav navbar-nav navbar-right" id="nav_admin">
            <li class="divider-vertical"></li>
            <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Admin </a>
                <ul class="dropdown-menu">
                    @if (CerebrumUser.HasPermission("UserAdmin"))
                    {
                        <li class="dropdown-submenu">
                            <a href="#" class="submenu-header" tabindex="-1">User Management <span class="caret"></span></a>
                            <ul class="dropdown-menu">
                                <li><a tabindex="-1" href="@Url.Action("index", "adminusers", new { area = "adminuser" })" target="_blank">Users</a></li>
                                <li><a tabindex="-1" href="@Url.Action("adminroles", "adminusers", new { area = "adminuser" })" target="_blank">Roles</a></li>

                                <li><a tabindex="-1" href="@Url.Action("userpatientaccess", "users", new { area = "admin" })">User Patient Denial</a></li>
                            </ul>
                        </li>

                    @if (CerebrumUser.HasPermission("AuditLog"))
                    {
                        <li class="dropdown-submenu">
                            <a id="user_audit" href="#" class="submenu-header" tabindex="-1">Audit <span class="caret"></span></a>
                            <ul class="dropdown-menu">
                                <li><a tabindex="-1" href="@Url.Action("index", "audit", new { area = "admin" })" target="_blank">Audit Log</a></li>
                                @if (CerebrumUser.IsDhdrEnabled && Convert.ToBoolean(eConsultActive))
                                {
                                    <li><a tabindex="-1" href="@Url.Action("GetDhdrAudit", "audit", new { area = "admin" })" target="_blank">DHDR Log</a></li>
                                }
                            </ul>
                        </li>

                    @if (CerebrumUser.HasPermission("PageOpeningReport"))
                    {
                        <li><a tabindex="-1" href="@Url.Action("index", "PageOpenLog", new { area = "admin" })">Login History</a></li>

                    @if (CerebrumUser.HasPermission("AddDoctorOrEditDoctor"))
                    {

                        <li><a href="@Url.Action("practiceDocs", "adminusers", new { area = "adminuser" })" target="_blank">Practice Doctors</a></li>
                    } if (CerebrumUser.HasPermission("EditPracticeRootCategories"))
                        {
                        <li><a href="@Url.Action("index", "PracticeRootCategories", new { area = "admin" })" target="_blank">Practice Root Categories</a></li>
                        } @if (CerebrumUser.HasPermission("Super Admin"))
                    {
                        <li><a href="@Url.Action("Index", "AppointmentType", new { area = "admin" })" target="_blank">Appointment Types</a></li>
                        <li><a href="@Url.Action("Index", "Tests", new { area = "admin" })" target="_blank">Tests</a></li>
                        <li class="dropdown-submenu">
                            <a href="#" class="submenu-header" tabindex="-1">OLIS <span class="caret"></span></a>

                            <ul class="dropdown-menu">
                                <li><a href="@Url.Action("Index", "OLISTestResultCategory", new { area = "OLIS" })" target="_blank">Result Categories</a></li>
                            </ul>
                        </li>
                        if (_isNewUaoImplementationActive)
                        {
                            <li><a href="@Url.Action("Index", "AdminUAO", new { area = "Admin" })" target="_blank">UAO Settings</a></li>
                        }
                        <li><a href="@Url.Action("Index", "ApplicationSettings", new { area = "Admin" })" target="_blank">Application Settings</a></li>

                    @if (CerebrumUser.HasPermission("Super Admin,Admin Lost HL7 Reports"))
                    {
                        <li><a href="@Url.Action("Index", "HL7LostReports", new { area = "admin" })" target="_blank">HL7 Lost Reports</a></li>

                    <li class="dropdown-submenu">
                        <a href="#" class="submenu-header" tabindex="-1">Office <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            @if (CerebrumUser.HasPermission("AddClinicOrEditClinic"))//offices
                            {
                                <li><a href="@Url.Action("adminoffice", "adminusers", new { area = "adminuser" })" target="_blank">Offices</a></li>
                            }
                            @if (CerebrumUser.HasPermission("UserAdmin"))
                            {
                                <li><a href="@Url.Action("Index", "PracticeAppointmentType", new { area = "admin" })">Practice Appointment Types</a></li>

                            @if (CerebrumUser.HasPermission("UserAdmin"))//office room
                            {
                                <li><a href="@Url.Action("index", "OfficeRoom", new { area = "admin" })" target="_blank">Office Rooms</a></li>

                            @if (CerebrumUser.HasPermission("UserAdmin"))
                            {
                                <li><a href="@Url.Action("Index", "OfficeRoomType", new { area = "admin" })">Office Room Types</a></li>

                            <li><a href="@Url.Action("Index", "Timesheet", new { area = "Employees" })">Timesheet</a></li>
                            <li><a href="@Url.Action("Index", "OfficeEmail", new { area = "admin" })">Office Email</a></li>
                        </ul>
                    </li>
                    @if (CerebrumUser.HasPermission("ExternalDoctors"))//offices
                    {
                        <li><a href="@Url.Action("index", "externaldoctors", new { area = "" })" target="_blank">External Doctors</a></li>
                    }

                    @if (CerebrumUser.HasPermission("ScheduleAdmin"))//offices
                    {
                        <li><a href="@Url.Action("index", "PracticeTests", new { area = "admin" })">Practice Tests</a></li>

                    @if (CerebrumUser.HasPermission("BookingConfirmationMessage"))//offices
                    {
                        <li><a data-modal-url="@Url.Action("Create", "BookingConfirmationMessage", new { area = "admin" })" class="btn-create-bk-confim-msg">Booking Confirmation Message</a></li>

                    @if (CerebrumUser.HasPermission("ManageDicom"))//offices
                    {

                        <li><a href="@Url.Action("Index", "Admin", new { area = "Dicom" })" target="_blank">Manage DICOM Exam</a></li>

                    @if (CerebrumUser.HasPermission("ScheduleAdmin"))
                    {
                        <li class="dropdown-submenu">
                            <a href="#" class="submenu-header" tabindex="-1">Schedule <span class="caret"></span></a>
                            <ul class="dropdown-menu">
                                <li><a tabindex="-1" href="@Url.Action("index", "office", new { area = "schedule" })" target="_blank">Admin Office</a></li>
                                <li><a tabindex="-1" href="@Url.Action("index", "staff", new { area = "schedule" })">Admin Staff</a></li>
                                <li><a tabindex="-1" href="@Url.Action("staffnotes", "staff", new { area = "schedule" })">Staff Notes</a></li>
                                <li><a tabindex="-1" href="@Url.Action("index", "scheduleview", new { area = "schedule" })">View Schedule</a></li>
                            </ul>
                        </li>

                    <li class="dropdown-submenu">
                        <a href="#" class="submenu-header" tabindex="-1">Admin Reports <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a tabindex="-1" href="@Url.Action("AgeGender", "EMRMetrics", new { area = "AdminUser" })" target="_blank">Age and Gender</a></li>
                            <li><a tabindex="-1" href="@Url.Action("BonusReport", "VP", new { area = "VP" })" target="_blank">Bonus Report</a></li>
                            <li><a href="@Url.Action("Index", "OfficeDailyRegister", new { area = "admin" })">Clinic Daily Register</a></li>
                            <li><a tabindex="-1" href="@Url.Action("Index", "Cohort", new { area = "AdminUser" })" target="_blank">Cohort</a></li>
                            <li><a tabindex="-1" href="@Url.Action("PatientVisitsStatistics", "EMRMetrics", new { area = "AdminUser" })" target="_blank">EMR Statistics</a></li>
                            <li><a tabindex="-1" href="@Url.Action("Index", "StoreInventory", new { area = "AdminUser" })" target="_blank">Inventory</a></li>
                            <li><a tabindex="-1" href="@Url.Action("PatientMerge", "PatientMerge", new { area = "AdminUser" })" target="_blank">Patient Merge</a></li>
                            <li><a tabindex="-1" href='@Url.Action("Index", "PersonnelPerformance" , new { area="AdminUser" })' target="_blank">Performance Report</a></li>
                            <li><a tabindex="-1" href="@Url.Action("Recall", "VP", new { area = "VP" })" target="_blank">Recall List</a></li> if (CerebrumUser.HasPermission("OpenSummary"))
                                {
                                <li><a tabindex="-1" href="@CerebrumUser.GetClinicalSearchUrl()" target="_blank">Clinical Search</a></li>
                                }
                            @if (CerebrumUser.HasPermission("CDS Export"))
                            {
                                <li><a tabindex="-1" href="@Url.Action("ExportCDS", "ExportCDS", new { area = "AdminUser" })" target="_blank">Export CDS</a></li>

                            <li><a tabindex="-1" href="@Url.Action("ImportXML", "ImportCDS", new { area = "AdminUser" })" target="_blank">Import CDS V5</a></li>
                            <li><a tabindex="-1" href="@Url.Action("ImportCDS_MISS", "ImportCDSMississauga", new { area = "AdminUser" })" target="_blank">Import CDS V4</a></li>
                            <li><a tabindex="-1" href="@Url.Action("Index", "VVReports", new { area = "VirtualVisit" })" target="_blank">Virtual Visit Report</a></li>
                        </ul>
                    </li>
                    <li class="dropdown-submenu">
                        <a href="#" class="submenu-header" tabindex="-1">Fax Sent Reports <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            @{

                            }
                            @foreach (var uo in offices)
                            {
                                <li><a tabindex="-1" href=@uo.sentFaxUrl target="_blank">@uo.Name</a></li>

                        </ul>
                    </li>
                    @if (CerebrumUser.HasPermission("Super Admin"))
                    {

                        <li><a href="@Url.Action("Index", "LOINCMapping", new { area = "labs" })">HL7 LOINC Mapping</a></li>
                    }
                    <li><a href="@Url.Action("patientsearchall", "patients", new { area = "" })" target="_blank">Search Patients (active and inactive)</a></li>
                    @if (CerebrumUser.HasPermission("BillingAdmin,Super Admin"))
                    {
                        <li><a href="@Url.Action("Index", "BillingAdmin", new { area = "bill" })">Billing Admin</a></li>

                    @if (CerebrumUser.HasPermission("Super Admin"))
                    {
                        <li><a href="@Url.Action("Index", "Practice", new { area = "" })">Practice Settings</a></li>

                    @if (CerebrumUser.HasPermission("FormbuilderAdmin"))
                    {
                        <li><a href="@Url.Action("index", "formbuilder", new { area = "admin" })">Form Builder Admin</a></li>

                    @if (CerebrumUser.HasPermission("GeneralAdmin"))
                    {
                        <li><a href="@Url.Action("Index", "Reminder", new { area = "Reminder" })">Reminder</a></li>
                        if (_isNewUaoImplementationActive)
                        {
                            <li class="dropdown-submenu">
                                <a href="#" class="submenu-header" tabindex="-1">User's UAO Settings <span class="caret"></span></a>
                                <ul class="dropdown-menu">
                                    <li><a tabindex="-1" href="@Url.Action("index", "AdminUsersUao", new { area = "AdminUser" })">List of Users</a></li>
                                    <li><a tabindex="-1" href="@Url.Action("UaoAssociatedUsers", "AdminUsersUao", new { area = "AdminUser" })">List of UAO Users</a></li>
                                </ul>
                            </li>

                    <li class="dropdown-submenu">
                        <a id="practice" href="#" class="submenu-header" tabindex="-1">Practice <span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li><a id="practice_connecting_ontario" tabindex="-1" href="@Url.Action("index", "ConnectingOntarioConfig", new { area = "admin" })" target="_blank">Connecting Ontario</a></li>
                            @if (CerebrumUser.IsAppointmentPriorityEnabled && CerebrumUser.HasPermission("Appointment Priority Configuration"))
                            {
                                <li><a id="practice_appointment_priority" tabindex="-1" href="@Url.Action("index", "AppointmentPriorityConfiguration", new { area = "admin" })" target="_blank">Appointment Priority</a></li>
                            }
                        </ul>
                    </li>
                    <li><a id="letter_report_queue" href="@Url.Action("Index", "ReportQueue", new { area = "PracticeAdmin" })">Letter / Report Queue</a></li>
                    @{

                        if (isPharmacyModuleActive)
                        {
                            <li><a href="@Url.Action("Index", "Pharmacy", new { area = "Pharmacy" })" target="_blank">Pharmacies</a></li>
                        }

                </ul>
            </li>
        </ul>

    if (CerebrumUser.HasPermission("Billing Admin (Office),Billing Admin (Hospital),Personal Billing Admin (Office),Personal Billing Admin (Hospital),RA Group Billing,RA Solo Billing") || CerebrumUser.HasRole($"Admin {CerebrumUser.PracticeId},Practice Admin {CerebrumUser.PracticeId},Super Admin {CerebrumUser.PracticeId}"))
    {
        //Billing Menu
        <ul class="nav navbar-nav navbar-right" id="nav_biling">
            <li class="divider-vertical"></li>
            <li>
                <a href="@Url.Action("index", "report", new { area = "bill" })">Billing</a>
            </li>
        </ul>

    <ul class="nav navbar-nav navbar-right" id="nav_dataSheet">
        <li class="divider-vertical"></li>
        <li>
            <a id="ds-top-link" href="@Url.Action("index", "daysheet", new { area = "schedule",

        </li>
    </ul>
    @using (Html.BeginForm("patientsearch", "patients", new { area = "" }, Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Get, true, true, new { @class = "navbar-form navbar-right", @id = "main-patient-search" }))
@using Cerebrum.ViewModels.Medications
@using Cerebrum.ViewModels.Patient
    {
        <div class="form-group form-group-sm">
            <span style="color:white">Search By:</span>
            <select id="searchType" name="searchType" class="form-control">
                <option value="0"></option>
                <option value="1">Name</option>
                <option value="2">HIN</option>
                <option value="3">Phone</option>
                <option value="4">Patient Id</option>
                <option value="5">DOB (mm/dd/yyyy)</option>
            </select>
            <input name="patientSearch" id="patientSearch" type="text" class="form-control" placeholder="Patient Search">
        </div>
        <button id="btn_search" type="submit" class="btn btn-default btn-xs hgt-24">Search</button>

else // not authenticated
{

if (!String.IsNullOrWhiteSpace(env) && env.ToLower() == "development")
    { <ul class="nav navbar-nav navbar-right">
            @if (Html.GetControllerName().ToLower() != "account")
            {
                <li class="divider-vertical"></li>
                <li>
                    <a href="@Url.Action("login", "account", new { area = "" })">login</a>
                </li>
            }
            <li class="divider-vertical"></li>
        </ul>
    @* } *@

<script>
    $('#signin').click(function (event) {
@* event.preventDefault(); *@

@* $('#signin').html("Sign Out"); *@

            $.ajax({
                url: url,
                data: {},
                type: 'GET',
                datatype: 'json',
                success: function (data) { alert('TimeSheet Signed-In At:  ' + Date($.now())) },
                error: function () { alert('System Error - Maintenance notified'); }
@* }); *@

        else {
@* $('#signin').html("Sign In"); *@

            $.ajax({
                url: url,
                data: {},
                type: 'GET',
                datatype: 'json',
                success: function (data) { alert('TimeSheet Signed-Out At:  ' + Date($.now())) },
                error: function () { alert('System Error - Maintenance notified'); }
@* }); *@

@* }); *@

    $('#btnCurrentLipidGuideline').click(function (event) {
@* event.preventDefault(); *@

PCSK9 inhibitor therapy for all secondary prevention CVD patients in whom LDL-C
remains ≥ 1.8 mmol/L (or non-HDL-C ≥ 2.4 mmol/L or ApoB ≥ 0.7 g/L) on maximally
tolerated statin dose. (Strong recommendation; High Quality Evidence). If ezetimibe is
used initially and LDL-C remains ≥ 1.8 mmol/L (or non-HDL-C ≥ 2.4 mmol/L or ApoB ≥
0.7 g/L) PCSK9 inhibitor therapy is recommended (Strong Recommendation; High-
Quality Evidence).<br /><br />
<a href='https://www.onlinecjc.ca/article/S0828-282X(21)00165-3/fulltext' target='_blank'> Read more ...</a>`, false);
@* }); *@

    $('#btnTappData').click(function (event) {
@* event.preventDefault(); *@

@* }); *@

    $('#btnAboutLDLTAPP').click(function (event) {
@* event.preventDefault(); *@

@* }); *@

@* var openedWindow; *@

    function closeDashboardWindow() {
        try {
            if (openedWindow) {
                // erase if exists
@* sessionStorage.removeItem("eDashboardOpened"); *@
@* openedWindow.close(); *@
            }
            else {

                    // remove flag
@* sessionStorage.removeItem("eDashboardOpened"); *@
@* newWin.close(); *@

        } catch (e) {
@* console.log(e.message); *@

    function openDashboardWindow() {
        // set flag to prevent refreshing page
@* sessionStorage.setItem("eDashboardOpened", "true"); *@

    $(document).ready(function () {
        window.addEventListener('beforeunload', function (e) {
@* closeDashboardWindow(); *@
@* }); *@
        $("#main-patient-search").on("submit", function (e) {

@* return date.isValid(); *@

@* showMessageModal("error", "DOB is not valid date!", false); *@
@* return false; *@

            //return false;
@* }); *@
@* }); *@
</script>
