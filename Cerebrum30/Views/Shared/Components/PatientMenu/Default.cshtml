@using Cerebrum.ViewModels.Patient
﻿@model Cerebrum.ViewModels.Patient.VMPatientMenu
@* (int)@Model.AppointmentReferralDoctorId : 0; *@
<ul class="ul-patient-menu __776432 ul-patient-menu-inline">

    <li><a class="btn-edit-patient" data-el="modal-lg" href="#"

    <li><a class="btn-patient-appointments" data-el="modal-lg" href="#"

    </li>

    <li><a class=""

    <li>
        @Html.ActionLink("Letter History", "VPReportHistory", "Reports", new { area = "Documents", patientID =
        @Model.PatientId, appointmentId = appointmentId }, new { target = "_blank" })
    </li>
    <li>
        @Html.ActionLink("VP History", "VPHistory", "reports", new { area = "documents", patientID = @Model.PatientId },

    </li>
    <li>
        @Html.ActionLink("Additional Notes", "Notes", "Patients", new { area = "", patientId = @Model.PatientId }, new {

    </li>
    <li>
        @Html.ActionLink("CPP", "CPP_Patient", "VP", new { area = "VP", PatientID = @Model.PatientId }, new { target =
                "_blank" })
    </li>
    <li>
        @Html.ActionLink("CDF", "TemplateData", "VP", new { area = "VP", PatientID = @Model.PatientId }, new { target =
                "_blank" })
    </li>

    <li><a class=""

    <li><a href="ContactManagers/Index?patientRecordId=@Model.PatientId" target="_blank">Contact Manager</a></li>

    <li><a class="" href="Requisition?PatientRecordId=@Model.PatientId&closePage=1" target="_blank">Flow Sheet</a></li>

    <li><a class="" href="Bill/report?reportType=billinghistory&patientRecordId=@Model.PatientId">Billing History</a>
    </li>

    <li><a class="" href="Bill/report?reportType=statement&patientRecordId=@Model.PatientId">Patient Statement</a></li>

    <li><a class="btn-loose-report-upload" href="#"
>Upload
            Loose Reports</a></li>

    <li><a class="" href="/AdminUser/PatientChart/GetPatientDataById?patientRecordId=@Model.PatientId"
>Patient Chart</a></li>

    @{

        {

        }

        <li><a target="_blank"

                Data</a></li>

    @if (CerebrumUser.HasPermission("OLISUser"))
    {

    <li>
        <a class="btn-view-comments c-pointer" data-cb-tp-placement="top" data-app-id="@Model.AppointmentId">
            Patient Comments
        </a>
    </li>
    <li>
        <a class="btn-view-patient-alert c-pointer" data-cb-tp-placement="top">
            <span @spanAlertClass id="<EMAIL>">Patient Alert</span>
        </a>
    </li>
    @{

@* Convert.ToBoolean(isPharmacyModuleActiveVal); *@
        if (isPharmacyModuleActive)
        {
            <li><a class=""

        }

    @{

        if (isActive)
        {
            if (CerebrumUser.HasRole("Access Econsult")) // change role here
            {
                <li><a href="@Url.Action("LoadPatient", "Consult", new { area = "eConsult", patientId = @Model.PatientId })">Request
                        eConsult</a></li>
            }
            if (CerebrumUser.HasPermission("View Medications"))
            {
                <li><a

                        eConsult Drafts</a></li>
                <li><a href="@Url.Action("Index", "PatientConsult", new { area = "eConsult", patientId = @Model.PatientId })">View
                        eConsults</a></li>

    @{

    @if (isEFormsActive && CerebrumUser.IsEformsEnabled)
    {
        <li><a href="javascript:void(0);" onclick="go(0)">EForms</a></li>

                <li><a href="javascript:void(0);" onclick="go(2)">HPG</a></li>@* *@
                <li><a href="javascript:void(0);" onclick="go(3)">OTN</a></li>
    }

</ul>

<script type="text/javascript">
    function go(typeId) {*@ var params = {

@* }; *@

</script>

<style type="text/css">
    .ul-patient-menu-inline {
@* display: inline-block; *@
    }
</style>