﻿
@model Cerebrum.ViewModels.HL7.HL7ReportVM
@using Cerebrum30.Utility
@using Cerebrum.ViewModels.Patient
<link href="~/Areas/Labs/CSS/olis-viewer-print.css" rel="stylesheet" />

@{

}

@if (Model != null && @Model.Id > 0)
{

    <div id="patient-info" class="row" style="background-color:#efecec">
        <div class="col-md-4">
            <table class="table">
                <tr>
                    <td><strong> <i> Patient Name: </i></strong></td>
                    <td>
                        @(pat.familyName + "," + pat.firstName)
                    </td>
                </tr>
                <tr>
                    <td>
                        <strong>
                            <i>
                                @(

                        )
                            </i>
                        </strong>
                    </td>
                    <td>
                        @(

                                ? pat.healthCardNo
                                : pat.healthCardNo + " " + pat.versionNo
                        )

                    </td>
                </tr>
                <tr>
                    <td><strong> <i> Gender: </i></strong></td>
                    <td>@pat.sex</td>
                </tr>
                <tr>
                    <td><strong> <i> Date of Birth: </i></strong></td>
                    <td>
                        @{

                        }
                        @dob
                    </td>
                </tr>
                @if (!string.IsNullOrWhiteSpace(pat.Reference))
                {
                    <tr>
                        <td><strong><i>Reference#:</i></strong></td>
                        <td>@pat.Reference</td>
                    </tr>
                }
            </table>
        </div>
        <div class="col-md-4">
            <table class="table">
                <tr>
                    <td><strong><i>Accession# </i></strong></td>
                    <td>
                        @{

                            if (@Model.IsOLISReport)
                            {

                            }

                        @Html.Raw(accession)
                    </td>
                </tr>
                <tr>
                    <td><strong><i>Laboratory: </i></strong></td>
                    <td>
                        @if (@Model.HL7ReportVersions.Count > 0)
                        {
                            @(

                                ? "MDS"
                                : firstReport?.HL7Message?.sendingApp
                            )

                    </td>
                </tr>
                <tr>
                    <td><strong><i>Requsition Date: </i></strong></td>
                    <td>
                        @{

                        }
                        @reqDt
                    </td>
                </tr>
                <tr>
                    <td><strong><i>Collection Date: </i></strong></td>
                    <td>
                        @{

                            {

                                {

                                }
                                else
                                {

                        @colDt
                    </td>
                </tr>
                <tr>
                    <td><strong><i>Results Received: </i></strong></td>
                    <td>
                        @{

                            {

                                {

                                }
                                else
                                {

                        @rrevddt
                    </td>
                </tr>
                <tr>
                    <td><strong><i>Status: </i></strong></td>
                    <td>
                        <span id="reportStatus">@Model.status </span>
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-md-4">
            <table class="table">
                <tr>
                    <td><strong><i>Requesting Physician : </i></strong></td>
                    <td>@Model.physicianName (@Model.physicianNo)</td>
                </tr>
                <tr>
                    <td><strong><i>Copied to : </i></strong></td>
                    <td>@Model.CopiedTo</td>
                </tr>
                <tr>
                    <td><strong><i>Next Appointment: </i></strong></td>
                    <td>@Model.NextAppointmentDate</td>
                </tr>
            </table>
        </div>
    </div>

    <hr />

