@{

}
<!-- adjust root font size so that micro ui renders correctly -->
<style>
    :root {
/* font-size: 12px; */

</style>

<!-- Micro UI -->
<link rel="stylesheet" type="text/css" href="@($"{servicesUrl}/micro-ui/styles.css")">
<script src="@($"{servicesUrl}/micro-ui/polyfills.js")" type="module"></script>
<script src="@($"{servicesUrl}/micro-ui/main.js")" type="module"></script>
@if (servicesUrl == "http://services.localhost")
{
    // if running locally, use vite client for hot module reloading
    <script src="@($"{servicesUrl}/micro-ui/@vite/client")" type="module"></script>
}

<!-- ConfigCat -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/configcat-js@latest/dist/configcat.min.js"></script>
<script type="text/javascript">

    // configure config cat 

        // appointments script needed for create-appointment event
@* loadScriptIfNeeded('/Areas/Schedule/Scripts/appointments.js'); *@

        if (idpAccessToken) {

                userClaims.sub,
                userClaims["well.email"],
                undefined,
                {
                    tenantId: userClaims["well.app_meta"].tenantId,
                    permissions: userClaims.permissions
                }
@* ); *@
        } else {

                "@(((ClaimsIdentity)User.Identity).FindFirst(ClaimTypes.NameIdentifier).Value)",
@* "@(((ClaimsIdentity)User.Identity).FindFirst(ClaimTypes.Email).Value)"); *@

            configcat.PollingMode.AutoPoll,
            {
                logger: configcat.createConsoleLogger(configcat.LogLevel.Warn),
                defaultUser: currentUser
@* }); *@

        // Ensure ConfigCat is an object without causing ReferenceError

@* return await configCatClient.getValueAsync(flagKey, false); *@
@* }; *@

                    if (value) {
@* $(element).show(); *@
                    } else {
@* $(element).hide(); *@
                    }
@* }); *@
@* }); *@
@* }; *@

@* ConfigCat.showHideFlags(); *@
        // allow ui to easily access the fhir api url

@* localStorage.setItem("FhirBaseUrl", servicesUrl); *@

        /@* bit of a hack to allow event trigger to open the demographic modal.*@
        * custom event name must match the event name in the micro-ui config
        */
@* document.removeEventListener('event.c3.edit-demographic', function (e) { }); *@
        document.addEventListener('event.c3.edit-demographic', function (e) {

@* $(document.body).append($tempElem); *@
@* $tempElem.click(); *@
@* $tempElem.remove(); *@
@* }); *@

        /@* bit of a hack to allow event trigger to open the appointment modal.*@
        * custom event name must match the event name in the micro-ui config
        */
@* document.removeEventListener('event.c3.create-appointment', function (e) { }); *@
        document.addEventListener('event.c3.create-appointment', function (e) {
@* console.debug("create-appointment event triggered", e.detail); *@

            if (e.detail.patientMainRecordId) {

            }
            if (e.detail.practiceId) {

            }
            if (e.detail.officeId) {

            }
            if (e.detail.appointmentDay) {

                    year: "numeric",
                    month: "2-digit",
                    day: "2-digit",
@* }); *@

            if (e.detail.serviceRequestId) {

            if (e.detail.referralDoctorId) {

            }

@* $(document.body).append($tempElem); *@
@* $tempElem.click(); *@
@* $tempElem.remove(); *@
@* }); *@
@* }); *@

    function loadScriptIfNeeded(scriptPath) {

            .from(document.querySelectorAll('script'))

        if (!existingScript) {

@* document.getElementsByTagName('body')[0].appendChild(tag); *@
        }

</script>