@using Cerebrum.ViewModels.Patient
﻿@{

        //var applicationName =
    @WebUtility.HtmlDecode(@Configuration["applicationName"])
@* ; *@
    // Duplicate variable removed: // Duplicate variable removed: // Duplicate variable removed: // Duplicate variable removed: var applicationName = @Html.Raw(@Configuration["applicationName"]);

    // Frontend Application Insights Settings. Only include when authenticated.

    @Configuration["FrontendApplicationInsightsConnectionString"];

@* string.Empty; *@

    @Configuration["CerebrumSVNBuild"].ToString();

@* Convert.ToBoolean(Configuration["NewUaoImplementationActive"]); *@

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="google" content="notranslate" />
    @RenderSection("metatags", required: false)
    <link href="https://fonts.googleapis.com/css?family=Oswald:400,600" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Cutive+Mono" rel="stylesheet">

        <link href="https://fonts.googleapis.com/css?family=Fira+Sans" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Roboto+Slab" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Roboto:400,500" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Francois+One" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Heebo:400,500" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Khand:400,500,600" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Average" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Merriweather+Sans:400,700" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Didact+Gothic" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Bree+Serif" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Coustard" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Unna" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Share+Tech+Mono" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Cutive" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Neuton:400,700" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=PT+Serif+Caption" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Patua+One" rel="stylesheet">@* *@
    
        else
        {
            @RenderSection("BaseHref", required: false)
        }

    @*<meta charset="iso-8859-1" />* <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=.9">
    <title class="hidden-print">@ViewBag.Title</title>
    <title class="hidden-print">@applicationName - @ViewBag.Title</title>
    <link href="~/Content/layoutbase.css" rel="stylesheet" />
    @RenderSection("customcss", required: false)
    <script src="~/Scripts/modernizr-2.8.3.js"></script>
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <script src="~/Scripts/jquery.validate.min.js"></script>
    <script src="~/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <script src="~/Scripts/bootstrap.min.js"></script>
    <script src="~/Scripts/layoutbase.js"></script>

    @if (controllerName.ToLower() != "account" && controllerName.ToLower() != "manage") {*  <script src="~/Scripts/jquery.signalR-2.4.2.js"></script>
            <script src="/signalr/hubs" type="text/javascript"></script>
            <script src="~/Scripts/cerebrum-signalR.js"></script>
    }
    @if (_isNewUaoImplementationActive)
    {

    @if (!string.IsNullOrEmpty(frontendApplicationInsightsConnectionString))
    {
        <script type="text/javascript">

        </script>
        <script src="~/Scripts/application-insights.js"></script>

    <script type="text/javascript">

            $('.lbl-name').each(function (i, item) {

@* $(this).html(arr[0].toUpperCase() + ', ' + arr[1]); *@
@* }); *@
@* }; *@
    </script>
    <style>
        @@layer base {

            body,
            html {
/* height: 100%; */
            }

            body {
/* background-color: #fff; */
/* width: 100%; */
@* font-family: 'Arial', sans-serif; *@
@* font-size: 14px; *@
@* font-weight: 500; *@
@* color: #AFA593; *@
@* background-repeat: no-repeat; *@
@* background-position: 1% 97%; *@
@* background-attachment: fixed; *@
@* background-size: cover; *@

            h2,
            h4 {
@* font-weight: normal; *@

            @* {*@
@* margin: 0; *@
@* padding: 0; *@

            #cntForgotPwd {
@* margin-top: 8px; *@

            #overlayDiv {
@* height: 100% !important; *@
@* width: 100% !important; *@
@* background-color: #a2bfcb !important; *@
@* border: 0px !important; *@
@* top: 0px !important; *@
@* position: fixed; *@
@* z-index: 7500 !important; *@
@* top: 0px !important; *@
@* left: 0px !important; *@
@* display: none; *@

            #overlayDiv2 {
@* height: 100% !important; *@
@* width: 100% !important; *@
@* background-color: #333 !important; *@
@* border: 0px !important; *@
@* top: 0px !important; *@
@* position: fixed; *@
@* z-index: 500 !important; *@
@* top: 0px !important; *@
@* left: 0px !important; *@

@* -moz-opacity: 0.9; *@
@* opacity: 0.9; *@
@* display: none; *@

            #msgDiv {
@* top: 50% !important; *@
@* left: 50% !important; *@
@* transform: translate(-50%, -50%) !important; *@
@* z-index: 7501 !important; *@
@* border: 7px solid #fff; *@
@* display: none; *@
@* padding: 20px; *@
@* margin: 0 auto; *@
@* position: fixed; *@
@* color: #fff; *@
@* font-size: 17px; *@

            #ajax-loader {
@* height: 100% !important; *@
@* background-color: #d1cecb !important; *@
@* border: 0px !important; *@
@* margin-top: 0px !important; *@
@* top: 0px !important; *@
@* left: 0px !important; *@
@* position: fixed; *@

@* -moz-opacity: 0.9; *@
@* opacity: 0.9; *@
@* z-index: 1051 !important; *@
                /*500  m^* */

            #ajax-loader img {
@* margin-top: 200px; *@

            body,
            .popover,
            #ui-datepicker-div,
            #ui-datepicker-div select,
            select,
            option,
            sup,
            .ui-dialog,
            .tooltip,
            .tooltip-inner,
            #patmain-top-info,
            #patmain-top-info-vp,
            #patmain-top-info-vp span,
            #patmain-top-info-vp span strong,
            #table-prescription,
            .ui_tpicker_time_input,
            .ui-datepicker-current,
            .ui-datepicker-close,
            .ui-autocomplete-input,
            ul.ui-autocomplete,
            #buttonPrintRemittanceDetail .__item {
@* font-family: Arial, sans-serif !important; *@
@* /*@ font-weight:400; , Consolas,'Heebo', 'Francois One','Didact Gothic','Patua One','Share Tech Mono','Fjalla One', 'Bree Serif', 'Roboto','PT Serif Caption', Neuton, 'Roboto', 'Bree Serif', Georgia, 'Lucida Grande',Calibri, 'Trebuchet MS', 'Fira Sans', Unna, Coustard, 'Roboto Slab', 'Merriweather Sans', 'Bree Serif', 'Fira Sans', Coustard,
                @*/*@
            }

            .tooltip,
            .tooltip-inner {*@ font-weight: normal !important;
@* font-size: 13px !important; *@

            .lbl-login-title,
            .module-name,
            .__patient-top-pnl,
            .lbl-datestamp,
            #main-nav sup {
@* font-family: Arial, sans-serif !important; *@
@* text-decoration: none !important; *@
            }

            .navbar .navbar-brand1 {
@* font-family: Arial, sans-serif !important; *@
@* color: #fff !important; *@
@* text-decoration: none !important; *@
            }

            .nav-bg-color {
@* background-color: #a2bfcb !important; *@
@* box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48) !important; *@
@* -moz-box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48) !important; *@
@* -webkit-box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48) !important; *@
            }

            .badge {
@* margin-left: 7px; *@
@* padding: 3px 0px 3px 0px !important; *@
@* min-width: 35px !important; *@
            }

            #lbl_totalAppointments {
@* margin-left: 15px; *@
@* font-style: normal; *@
@* color: #eb5817; *@
            }

            #lbl_currentDate {
@* font-weight: normal; *@
@* padding: 0px; *@
@* margin: 10px 0px 0px 0px; *@
            }

            .body-content {
@* padding-top: 10px !important; *@

            #patientSearch {
@* width: 180px; *@

            .navbar {
@* border: 0px !important; *@

            hr {
@* width: 100% !important; *@
@* margin-left: 0px; *@
@* border-top: 1px solid #e6e6e6 !important; *@
            }

            .btn-primary {
@* background-color: #5a5a5a !important; *@

            .top-legend li:nth-child(2) {
@* border-left: 1px solid #e0e0e0 !important; *@

            .top-legend .li-color-desc {
@* border-right: 1px solid #e0e0e0 !important; *@
@* border-top: 1px solid #e0e0e0 !important; *@
@* border-bottom: 1px solid #e0e0e0 !important; *@
            }

            div .form-group,
            .btn,
            .badge,
            .form-control {
@* border-radius: 0; *@

                .form-control {
@* border-radius: 0; *@
                }

            .resources-info-wrapper span {
@* padding-left: 2px !important; *@

            .td-status {
@* padding-left: 3px !important; *@
@* text-align: left; *@

            .test-container {
@* padding: 3px !important; *@

            .td-appointment div:nth-child(1) {}

            .btn-add-test,
            .btn-apptest-status,
            .btn-edit-ext-doctor,
            .btn-pay-method,
            .btn-app-status,
            .btn-set-lefttime,
            .btn-set-arrivaltime,
            .btn-room,
            .btn-edit-arrivaltime,
            .btn-left1,
            .btn-app-confirmation {
@* margin-left: 6px !important; *@
            }

            .text-danger {
                /@*margin-right: 6px !important;*/*@

            .glyphicon {
@* font-size: 13px !important; *@

            ._subLink small,
            ._subLink small:hover,
            .appointment-edit,
            .appointment-edit:hover {}

            .btn_editPatient,
            .btn_patientE {
@* color: #333 !important; *@
@* margin-right: 3px; *@
            }

            .btn_editPatient:hover,
            .btn_patientE:hover {
@* color: #333 !important; *@

            ._subLink small:hover,
            .prereq-desc a:hover {
@* color: #eb5817 !important; *@
@* text-decoration: underline !important; *@
            }

            .lbl-name {
@* color: #797b93 !important; *@
@* font-weight: normal !important; *@
@* font-size: 16px !important; *@
            }

            .lbl-font-size {
@* font-weight: normal !important; *@
@* font-size: 16px !important; *@

            thead,
            th {
@* border: 0px; *@

            .btn-view-comments,
            .btn-app-history,
            .btn-popover-container {
@* margin-right: 6px !important; *@
            }

            .btn-popover-container {
@* padding-left: 0px; *@

            .ui-datepicker {
@* padding: 0px !important; *@

            .ui-state-highlight,
            .ui-widget-content .ui-state-highlight,
            .ui-widget-header .ui-state-highlight {
@* border: 1px solid #f8d2c2 !important; *@
@* background: #eb5817 !important; *@
@* color: #fff !important; *@
            }

            .ui-datepicker-next-hover {
@* right: 2px !important; *@
@* top: 2px !important; *@
@* cursor: pointer; *@
            }

            .ui-datepicker-prev-hover {
@* left: 2px !important; *@
@* top: 2px !important; *@
@* cursor: pointer; *@
            }

            .ui-state-active,
            .ui-widget-content .ui-state-active,
            .ui-widget-header .ui-state-active,
            a.ui-button:active,
            .ui-button:active,
            .ui-button.ui-state-active:hover {
@* border: 1px solid #a7b4ba !important; *@
@* background: #a2bfcb !important; *@
@* font-weight: normal !important; *@
@* color: #fff !important; *@
            }

            .ui-icon,
            .ui-widget-content .ui-icon {}

            .ui-dialog {
@* padding: 0px !important; *@
            }

            .ds-filter-container {
@* padding: 8px !important; *@
@* border: 3px solid #f2f2f2 !important; *@
@* margin-top: 5px; *@
            }

            .form-group-sm .form-control {
@* height: 25px !important; *@
@* padding: 5px 5px !important; *@
@* font-size: 11px !important; *@
@* line-height: 1.25 !important; *@
            }

            #tbl_templateData td {
@* padding: 3px 0px 3px 0px !important; *@

            #tbl_templateData tr:first-child td {
@* padding: 0px 0px 3px 0px !important; *@

            table tr:nth-child(even) {
@* background: #dddbd8; *@

            table tr:nth-child(odd) {
@* background: #d3d3d3; *@

            #table-daysheet,
            #table-billingAdmin,
            #table-HRM,
            #table-HRM-clinicList,
            #table-OLIS-logs,
            #table-OLIS-preload,
            #table-allergyInged-list,
            #table-allergy-list,
            #table-medic-interaction1,
            #table-classMapping,
            #tbl-patientmedications,
            #tbl_templateData,
            #tbl_performeter,
            #tbl_toBeBilled,
            #tbl_immun,
            #tbl_adminUsers,
            #edtFileList,
            #edtErrorList,
            #tbl_toBeBilled,
            #table-TestMaster,
            #table-adminPractice,
            #externalDocumentList,
            #claimList,
            #remittanceFileList,
            #scrollable-table,
            #tbl-prescribedsets,
            #tbl-hd-adm-list,
            .myTablepre-scrollable23 .myTable,
            .tbl-immun-init-s,
            .tbl-immun-immun-d,
            .tbl-immun-edit-s,
            .tbl-marked-seen,
            .tbl-Test-Description,
            .tbl-editCPP,
            .tbl-border {
@* border: 3px solid #f2f2f2 !important; *@
            }

            #tbl_templateData .txtEntry {
@* margin: 0px 3px 0px 3px !important; *@

            .div_header {
@* background-color: #f2f2f2; *@

            .active_nav {
@* background-color: #bcc6ca !important; *@

            .ui-icon {
@* background-color: #e9e9e9; *@

            .ui-icon,
            .ui-widget-content .ui-icon,
            .ui-datepicker-next-hover {
                /* background-image: url() !important; */
            }

            .ui-state-hover {
@* background-color: #e9e9e9 !important; *@
@* border: 0px !important; *@

            .ui-datepicker-next {
@* background-image: url(content/images/right.svg) !important; *@
@* background-repeat: no-repeat !important; *@
@* background-size: 50% !important; *@
@* background-position: 6px 8px !important; *@
            }

            .ui-datepicker-prev {
                /@*content:"\e079" !important;*/*@
                /*background-image: url(content/images/right.png) !important; */
@* background-image: url(content/images/left.svg) !important; *@
@* background-repeat: no-repeat !important; *@
@* background-size: 50% !important; *@
@* background-position: 6px 8px !important; *@

            .ui-datepicker .ui-datepicker-prev span,
            .ui-datepicker .ui-datepicker-next span {
@* margin-top: -1000px !important; *@

            #count-contact-tasks,
            #count-to-do {
@* height: 18px; *@
@* margin-top: -2px; *@
            }

            #nav_myTasks {
@* min-width: 116px; *@

            #nav_toDo {
@* min-width: 101px; *@

            .dropdown-menu {
                /* nav */
@* padding: 0px !important; *@

            .modal-header {
@* background-color: #f7f7f7 !important; *@
@* border-bottom: 1px solid #ebebeb !important; *@

            body,
            .btn-default,
            select,
            option,
            .__item a,
            .__item span,
            .modal-dialog {
@* color: #5a5959 !important; *@
            }

            .__item {
@* border-bottom: 1px solid #c4c4c4; *@

            .btn-primary {
@* color: #fff !important; *@

            select {
@* font-weight: normal !important; *@

            .custom-menu-link {
@* background-color: #fff !important; *@

            .div-medications {
@* min-height: 150px; *@

            .panel-heading {
@* height: 37px !important; *@

            #patmain-top-info-container,
            #patmain-top-info-vp .container {
@* margin-top: 10px; *@

            .div-medications ol {
@* padding: 0px; *@

            .panel {
                /@*background-color: unset !important;*/*@
@* border: 3px solid #f2f2f2; *@
@* background-color: transparent; *@
                /*IE*/
            }

            .panel-info {
@* border-color: #f2f2f2 !important; *@

            .panel-info>.panel-heading {
@* background-color: #f2f2f2 !important; *@
@* border-bottom: 1px solid #f2f2f2 !important; *@
@* border-color: #ebebeb !important; *@
            }

            label {
@* font-size: 13px; *@
@* font-weight: normal; *@
@* margin-bottom: 0px; *@
            }

            .f25 {
@* padding-right: 8px; *@

            #patient-info-container span {
@* font-weight: normal !important; *@

            #mmm {
@* top: 51px; *@
@* right: -251px; *@
@* width: 250px; *@
@* position: fixed; *@
@* z-index: 1022; *@
@* background-color: #808080; *@
@* overflow-x: hidden; *@
@* padding-top: 50px; *@
                /*  transition: 0.5s;  */
@* height: 100%; *@

            #mmm a {
@* padding: 8px 8px 8px 20px; *@
@* text-decoration: none; *@
@* display: block; *@
@* transition: .1s; *@
@* color: #f1f1f1; *@

            #mmm .closebtn {
@* position: absolute; *@
@* top: 0; *@
@* font-size: 36px; *@
            }

            #sidebar-right-open-contents {
@* top: 51px; *@
@* right: -251px; *@
@* width: 250px; *@
@* position: fixed; *@
@* z-index: 1022; *@
@* background-color: #808080; *@
@* overflow-x: hidden; *@
@* padding-top: 50px; *@
                /@* transition: 0.5s; *@/ @* height: 100%; *@

            #sidebar-right-open-contents a {
@* padding: 8px 8px 8px 20px; *@
@* text-decoration: none; *@
@* display: block; *@
@* transition: .1s; *@
@* color: #f1f1f1; *@

            #sidebar-right-open-contents .closebtn {
@* position: absolute; *@
@* top: 0; *@
@* font-size: 36px; *@
            }

            select {
@* border: 1px solid #ccc !important; *@

            #patient-info-container .ul-patient-menu a:hover {
@* background-color: #a2bfcb; *@

            #patient-info-container .td-patient .__776432 a {
@* color: #dad8d6 !important; *@

            #patient-info-container .td-patient .__776432 a {
@* color: #808080 !important; *@

            #patient-info-container .ul-patient-menu a:hover {
@* background-color: #a2bfcb; *@

            .billingcodes-div .form-group {
@* margin-right: 20px !important; *@

            .float-Left-Label {
@* float: left; *@
@* margin-right: 40px; *@

            .div-medications .glyphicon-refresh,
            .__456 .glyphicon-user,
            .__456998 .glyphicon-user,
            .btn-view-docschedule .glyphicon-sunglasses,
            #tbl-patientmedications .glyphicon-plus,
            #tbl-patientmedications .glyphicon-minus {
@* color: #a2bfcb !important; *@
            }

            .__456 .glyphicon-user {
@* margin-top: 2px; *@

            #patmain-top-info .container,
            #patmain-top-info-vp .container {
@* margin-top: 10px; *@

            .div-medications .glyphicon-refresh {
@* margin-left: 6px; *@

            .white-link {
@* color: black !important; *@
@* font-size: 9px; *@

            .dropdown-menu {
@* float: right; *@

            .text-primary {
@* color: #45410c; *@
                /* #186571 */

            .text-primary strong {
@* padding-right: 3px; *@
@* font-size: 20px; *@

            #sidebar-wrapper {
@* margin-left: -160px; *@
@* width: 160px; *@
@* background: #808080; *@
@* position: fixed; *@
@* height: 100%; *@
@* overflow-y: auto; *@
@* z-index: 1000; *@
@* transition: none !important; *@

            #wrapper {
@* transition: none !important; *@

            #bill-report-container .sidebar-nav li a {
@* color: #f1f1f1 !important; *@
@* display: block !important; *@
@* text-decoration: none !important; *@
@* padding-left: 16px !important; *@
            }

            #bill-report-container .high-lighted {
@* background-color: #a4aeb2; *@

            #bill-report-container #sidebar-wrapper {
@* margin-top: -25px !important; *@

            #bill-report-container #billModuleList {
@* margin-top: 45px !important; *@

            #bill-report-container #sidebar-wrapper li a:hover,
            #bill-report-container #sidebar-wrapper li a:visited {
@* background-color: #a2bfcb; *@

            .badgePad {
@* position: absolute; *@
@* left: 122px; *@
@* margin-top: 1px; *@
@* width: 30px; *@
@* margin-left: -1px; *@

            .ui-datepicker table {
@* margin: 0; *@

            .ui-datepicker tr:nth-child(even) {
@* background-color: #f6f6f6 !important; *@

            .ui-state-default,
            .ui-widget-content .ui-state-default,
            .ui-widget-header .ui-state-default,
            .ui-button,
            html .ui-button.ui-state-disabled:hover,
            html .ui-button.ui-state-disabled:active {
@* border: 0px !important; *@
            }

            .ui-state-default,
            .ui-timepicker-div,
            .ui-timepicker-div dl {
@* color: #707070 !important; *@
@* font-weight: normal !important; *@
            }

            OpeningStatement {
@* margin-top: -9px; *@
@* margin-bottom: 18px; *@

            .status-desc,
            .prereq-desc,
            .bottom-msg {
@* /*@position: absolute; bottom: 3px;@*/*@
            }

            .cpp-list,
            .vp-list {
@* margin: 0px 0 0 0; *@
@* padding: 0px; *@
            }

            .ui-autocomplete {
@* overflow-y: scroll !important; *@

            .rep-phrase-header,
            .cpp-header {
@* color: #444 !important; *@
                /@*f28383*/*@
@* font-weight: normal !important; *@
@* font-size: 14px !important; *@
            }

            .opening-box {
@* margin-top: -10px !important; *@
@* height: 88px !important; *@

            .customLbl {
@* display: inline-block; *@
@* min-width: 150px !important; *@
@* padding: 4px 0 5px 0; *@
@* background-color: #b9ceaf; *@
@* border: 0px !important; *@
@* font-size: 13px !important; *@
@* font-weight: normal !important; *@
@* color: #424242 !important; *@

            .menuText {
                /*font-size: 13px !important;*/
@* font-weight: normal !important; *@

            #divMeasurement {
@* margin-top: 5px; *@

            .btn-default:active:hover,
            .btn-default.active:hover,
            .open>.dropdown-toggle.btn-default:hover,
            .btn-default:active:focus,
            .btn-default.active:focus,
            .open>.dropdown-toggle.btn-default:focus,
            .btn-default:active.focus,
            .btn-default.active.focus,
            .open>.dropdown-toggle.btn-default.focus {
@* color: #fff !important; *@
@* background-color: #a2bfcb; *@
@* border-color: #8c8c8c; *@
            }

            .lbl-filter {
                /* float: left; */
                /@*width: 73px;*/*@
@* padding-top: 4px; *@
@* color: #f2f2f2; *@
@* font-size: 20px; *@
@* margin-top: -6px; *@
                /*padding-left: 0px;*/

            .__titles,
            .panel-heading {
@* color: #f28383 !important; *@
@* height: auto !important; *@
@* padding-top: 5px !important; *@
@* padding-bottom: 3px !important; *@
@* font-weight: normal !important; *@
@* padding-left: 6px; *@

            .panel-body {
@* padding: 6px !important; *@

            button:focus {
@* outline: 0 !important; *@

            .popover-title {
@* background-color: #f7f7f7 !important; *@
@* border-bottom: 1px solid #ebebeb !important; *@

            .popover-content {
@* color: #808080 !important; *@

            .dropdown-menu>.active>a,
            .dropdown-menu>.active>a:hover,
            .dropdown-menu>.active>a:focus {
@* color: #fff; *@
@* text-decoration: none; *@
@* background-color: #a2bfcb; *@
            }

            .table>thead>tr>th,
            .tbl-header-item>td {
@* border-bottom: 2px solid #ddd !important; *@
@* background-color: #f2f2f2; *@
                /*!important*/
@* color: #808080 !important; *@
            }

            .tbl-header-item>td {
@* vertical-align: text-top !important; *@
@* font-size: 13px !important; *@
@* font-weight: bold !important; *@
            }

            .table>thead>tr>th a {
@* color: #808080 !important; *@

            table {
@* border: 1px solid #e3e2e2 !important; *@

            #main-nav li.dropdown ul li a {
@* color: #8e8484 !important; *@

            #main-nav form {
@* margin-left: 0px; *@

            #nav_myTasks {
@* color: aqua; *@

            .module-name {
@* color: #ffcbcb; *@
                /*#ffcdb7;*/

            .overlay-glyph {
@* font-size: 44px !important; *@
@* margin-bottom: 10px; *@

            #scheduleNotes {
@* height: 10px !important; *@

            #schedule-container .schedule-details-wrapper .glyphicon-user {
@* font-size: 8px !important; *@

            #ui-id-1 {
@* z-index: 999999; *@

            .ui-dialog-titlebar {
                /* background-color: #f7f7f7 !important; */
@* border-bottom: 1px solid #ebebeb !important; *@
@* color: #808080 !important; *@
            }

            .ui-dialog-content,
            .ui-dialog-content a {
@* color: #808080 !important; *@

            .ui-dialog-titlebar-close {
@* background-color: unset !important; *@
                /@*#f7f7f7 !important;*/*@
@* background-image: url(content/images/close.svg) !important; *@
@* background-repeat: no-repeat !important; *@
@* background-size: 60% !important; *@
@* background-position: 3px 4px !important; *@
@* border: 0px !important; *@

@* -moz-opacity: 0.4; *@
@* opacity: 0.4; *@

            .ui-datepicker-next,
            .ui-datepicker-prev,
            .closebtn {

@* -moz-opacity: 0.4 !important; *@
@* opacity: 0.4 !important; *@
            }

            .ui-dialog-titlebar-close:hover,
            .ui-datepicker-next:hover,
            .ui-datepicker-prev:hover,
            .closebtn:hover {

@* -moz-opacity: 1 !important; *@
@* opacity: 1 !important; *@
            }

            .ui-datepicker-calendar th {
@* color: #808080; *@

            .ui-datepicker-week-end {
@* color: #a2bfcb !important; *@

            .ui-dialog-titlebar {
@* border: 0px !important; *@

            .ui-draggable {
@* box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48); *@
@* -moz-box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48); *@
@* -webkit-box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48); *@
            }

            .table>thead>tr>th {
@* border-bottom: 0px !important; *@

            .table {
@* margin-bottom: 0px !important; *@
                /*border: 0px !important;*/

            #edtFileList th,
            #claimList th {
@* vertical-align: text-top !important; *@

            cpp-list ol {
@* margin: 0px !important; *@
@* padding: 0px !important; *@

            .site-footer {
@* background: #333; *@
@* bottom: 0; *@
@* width: 100%; *@
@* position: fixed; *@

@* -moz-opacity: 0.95 !important; *@
@* opacity: 0.95 !important; *@
@* color: #fff; *@
@* height: 30px; *@
@* z-index: 7501; *@

            .site-footer a {
@* color: #f28383; *@

            .foItem {
@* line-height: 30px; *@

            .patient-info-line {
@* padding-top: 8px; *@
@* display: block; *@
@* position: fixed; *@
@* width: 100%; *@
@* z-index: 90; *@
@* background-color: #a28181; *@
                /* #e6e4e4 #eed1c7; */
@* color: #fff; *@
@* display: block; *@
@* position: fixed; *@
@* width: 100%; *@
@* z-index: 90; *@
@* left: 0px; *@
@* padding-left: 15px; *@
@* min-height: 36px; *@
@* border-bottom: 1px solid #ddd; *@
@* border-top: 0px !important; *@

            .patient-info-line-1 {
@* margin-top: -2px; *@

            .patient-info-line-2 {
@* margin-top: -10px; *@

            .table>thead>tr>th,
            .table>tbody>tr>th,
            .table>tfoot>tr>th,
            .table>thead>tr>td,
            .table>tbody>tr>td,
            .table>tfoot>tr>td {
@* padding: 2px 8px 2px 8px; *@
            }

            #cohortsList {
@* background-color: #fff; *@

            .validation-summary-errors ul {
@* list-style: none !important; *@

            input::-webkit-input-placeholder {
@* color: #c4c4c4 !important; *@

            input:-moz-placeholder {
                /@* Firefox 18- */*@
@* color: #c4c4c4 !important; *@

            input::-moz-placeholder {
                /*  Firefox 19+  */
@* color: #c4c4c4 !important; *@

            input:-ms-input-placeholder {
@* color: #c4c4c4 !important; *@

            .float_r {
@* float: right; *@

            .float_l {
@* float: left; *@

            .highlight,
            .highlight2 {
@* color: #f28383; *@

            .margin-left-5 {
@* margin-left: 5px; *@

            .margin-left-10 {
@* margin-left: 10px; *@

            .margin-left-15 {
@* margin-left: 15px; *@

            .margin-right-15 {
@* margin-right: 15px; *@

            .margin-right-20 {
@* margin-right: 20px; *@

            .btn-oma-guide {
@* width: 406px; *@

            #main-nav {
@* height: 50px !important; *@
@* display: none; *@

            .lbl-datestamp {
@* padding: 0px; *@
@* margin: 0px 5px 0px 0px; *@
@* height: 27px; *@
@* font-weight: normal !important; *@
            }

            .leftModal {
@* left: -37% !important; *@

            .nav-title {
@* margin-top: 6px; *@

            .nav-title a {
@* color: #fff; *@
@* text-decoration: none; *@
                /@*width: 188px !important;*@/ }

            .navbar-brand1,
            .module-name {
@* float: left; *@
@* font-size: 16px !important; *@
@* margin-top: 8px; *@
            }

            #nav_main_pnl {
@* margin-right: 0px !important; *@

            .navbar-header {
@* margin-top: 4px; *@

            .navbar-form {
@* border-top: unset !important; *@
@* border-bottom: unset !important; *@
@* -webkit-box-shadow: unset !important; *@
@* box-shadow: unset !important; *@
            }

            #forgotPasswordForm #Email {
@* width: 250px; *@

            #forgotChangePassword {
@* max-width: 512px; *@

            #loginForm h4 {
@* margin: 0; *@
@* padding: 0; *@

            #loginForm input {
@* max-width: 300px; *@

            #loginForm #Email,
            #loginForm #Password {
@* width: 250px; *@

            .margin-top-0 {
@* margin-top: 0px !important; *@

            .margin-top-less {
@* margin-top: -6px; *@

            .margin-top-more {
@* margin-top: 9px; *@

            #loginForm .field-validation-error {
@* margin-top: 3px !important; *@

            .ui-datepicker .ui-datepicker-next {
@* right: 2px !important; *@

            .ui-datepicker .ui-datepicker-prev {
@* left: 2px !important; *@

            .ui-state-disabled,
            .ui-widget-content .ui-state-disabled,
            .ui-widget-header .ui-state-disabled {
@* opacity: 1 !important; *@
            }

@* height: 25px !important; *@
@* padding: 5px 5px !important; *@
@* font-size: 11px !important; *@
@* line-height: 1.25 !important; *@
@* border: 1px solid #ccc !important; *@
@* color: #555 !important; *@

            .form-group-sm .form-control {
@* padding: 2px 0px 0px 5px !important; *@

            .label-primary {
@* background-color: #5d82a1 !important; *@
                /* #80959d */
@* color: #fff !important; *@
@* font-weight: normal; *@
            }

            #daysheet-wrapper .label {
@* font-weight: normal !important; *@

            .lbl_room {
@* font-size: 16px; *@

            .ui-datepicker-calendar tr:nth-child(odd) {
@* background-color: #f6f6f6 !important; *@

            .nav-side-sub-right {
                /@*margin-top: -40px;*@/ }

            .lbl-login-title {
@* font-size: 54px !important; *@
@* margin-bottom: 50px !important; *@
@* opacity: .8; *@

@* color: #424a56; *@
@* font-weight: bold; *@

            .navbar-nav {
@* margin: 0px; *@

            .navbar .divider-vertical {
@* height: unset !important; *@

            .pagination li a {
@* /*@width: 40px; *@
                text-align: center;@*/*@
@* color: #808080 !important; *@
            }

            .pagination>.active>a,
            .pagination>.active>a:focus,
            .pagination>.active>a:hover,
            .pagination>.active>span,
            .pagination>.active>span:focus,
            .pagination>.active>span:hover {
@* z-index: 3; *@
@* color: #fff; *@
@* cursor: default; *@
@* background-color: #a2bfcb !important; *@
@* border-color: #a2bfcb !important; *@

            .btn-default,
            .btn-default:hover,
            .btn-default:active,
            .btn-default:visited {
@* border-color: #eaeaea !important; *@
            }

            .appointment-tests .btn-default.active,
            .appointment-preconditions .btn-default.active {
@* border-color: #adadad !important; *@
@* color: #fff !important; *@
@* background: #5a5a5a !important; *@
@* outline: 0 !important; *@
            }

            .btn:active,
            .btn.active {
@* background-image: none; *@
@* outline: 0; *@
@* -webkit-box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0) !important; *@
@* box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0) !important; *@
            }

            .btn-default:hover {
@* background-color: #bfb8b8 !important; *@

            .btn-primary,
            .btn-primary:hover {
@* border-color: #eaeaea !important; *@

            button:focus {
@* outline: 0 !important; *@
            }

            .spacer-top-4 {
@* margin-top: 4px; *@

            .spacer-top-7 {
@* margin-top: 7px; *@

            .spacer-btm-7 {
@* margin-bottom: 7px !important; *@

            .spacer-top-10 {
@* margin-top: 10px; *@

            .spacer-top-15 {
@* margin-top: 15px; *@

            .spacer-top-35 {
@* margin-top: 35px; *@

            .spacer-top-50 {
@* margin-top: 50px; *@

            .nav>li>a:focus {
@* color: #a2bfcb; *@

            .nav>li>a:hover {
@* color: #b7b7b7 !important; *@
@* background-color: #6b6969 !important; *@

            .caret {
@* display: none; *@

            .dropdown-menu>li>a {
@* color: #8e8484 !important; *@

            #performeterList th,
            #edtFileList th,
            #edtErrorList th,
            #toBeBilledList th,
            #claimList th,
            #remittanceFileList th {
@* height: 24px; *@
@* vertical-align: middle !important; *@
            }

            .glyphicon-arrow-down:before {
@* content: "\e114" !important; *@

            .glyphicon-arrow-up:before {
@* content: "\e113" !important; *@

            .transparent {
@* opacity: 0 !important; *@

            .table>tbody>tr.active>td,
            .table>tbody>tr.active>th,
            .table>tbody>tr>td.active,
            .table>tbody>tr>th.active,
            .table>tfoot>tr.active>td,
            .table>tfoot>tr.active>th,
            .table>tfoot>tr>td.active,
            .table>tfoot>tr>th.active,
            .table>thead>tr.active>td,
            .table>thead>tr.active>th,
            .table>thead>tr>td.active,
            .table>thead>tr>th.active {
@* background-color: unset; *@
            }

            ._etb {
@* height: 80px; *@
@* width: 200px; *@

            .nav_hideUntilPageLoaded {
@* background-color: #333; *@

            textarea:focus,
            select:focus {
@* background-color: #fff; *@
            }

            #pnl_login {
@* margin-left: 120px; *@

            #btn_menu {
@* margin-top: -4px; *@
@* margin-bottom: 9px; *@

            .extendTextarea {
@* overflow: hidden; *@
@* min-height: 300px !important; *@
@* outline: none; *@
@* resize: none; *@
            }

            .txtReportPhrases {
                /*background-color: #ff8800;*/
                /*overflow: hidden;*/
@* min-height: 100px; *@
@* outline: none; *@
@* resize: none; *@

            /@*.pad15 {*@
@* padding: 0px 15px 0px 15px !important; *@
            }@*/*@
            #sidebar-right-open {
                /*color: #fff;*/
@* /*@top: 60px !important; *@ right: 20px !important; *@
@* z-index: 1021 !important; *@
                position: fixed !important;@*/*@
            }

            .__patient-top-pnl {
@* background-color: #d8e97f; *@
@* padding-top: 7px; *@
@* min-height: 45px; *@
            }

            #medicationTemplates {
@* float: right; *@

            .navbar-toggle {
@* padding: 9px 0px !important; *@

            .btn-default {
@* color: #333; *@
@* background-color: #fff; *@
@* border-color: #ccc; *@
            }

            .border-bottom {
@* border-bottom: 1px solid #EDEAEA; *@

            .allergies-code th {
@* color: #a94442; *@
@* background-color: #f2dede; *@

            .interactions-code th {
@* background-color: #fcf8e3 !important; *@

            .medication-code th {
@* background-color: #bed3dc; *@

            .modal-footer .btn+.btn {
@* margin-left: unset !important; *@

            .fixedWidth-btn {
@* width: 120px; *@

            .filter-by-patient {
@* float: right; *@

            @@media only screen and (max-width:768px) {
                .navbar-collapse {
@* padding-right: unset; *@
@* padding-left: unset; *@
                }

                .navbar-brand1 {
@* margin-left: 15px; *@
@* font-size: 28px !important; *@
@* margin-top: 12px; *@
                }

                #pnl_login {
@* margin-left: 0px !important; *@

                .lbl-datestamp {
@* font-size: 14px; *@

                .filter-by-patient {
@* float: left; *@

            @@media only screen and (max-width:976px) {
                .navbar-right {
@* float: unset !important; *@
                }

                .navbar-nav .open .dropdown-menu {
@* position: static; *@
@* float: none; *@
@* width: auto; *@
@* margin-top: 0; *@
@* background-color: transparent; *@
@* border: 0; *@
@* -webkit-box-shadow: none; *@
@* box-shadow: none; *@

                .navbar-nav .open .dropdown-menu>li>a {
@* line-height: 20px; *@

                .navbar-nav .open .dropdown-menu>li>a,
                .navbar-nav .open .dropdown-menu .dropdown-header {}

                .dropdown-menu>li>a {
@* display: block; *@
@* clear: both; *@
@* font-weight: normal; *@
@* line-height: 1.42857143; *@
@* color: #333; *@
@* white-space: nowrap; *@

                .navbar-header {
@* float: none; *@

                .navbar-toggle {
@* display: block; *@

                .navbar-collapse {
@* border-top: 1px solid transparent; *@
@* box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1); *@

                .navbar-collapse.collapse {
@* display: none !important; *@

                .navbar-nav {
@* float: none !important; *@

                .navbar-nav>li {
@* float: none; *@

                .navbar-nav>li>a {
@* padding-top: 10px; *@
@* padding-bottom: 10px; *@

                .navbar-text {
@* float: none; *@

                .navbar-collapse.collapse.in {
@* display: block !important; *@

                .collapsing {
@* overflow: hidden !important; *@

                .container-fluid>.navbar-collapse,
                .container>.navbar-collapse {
@* margin-right: -15px; *@
@* margin-left: -15px; *@
                }

                #nav_hideUntilPageLoaded1 {
@* background-color: #a2bfcb !important; *@
@* box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48); *@
@* -moz-box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48); *@
@* -webkit-box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.48); *@
                }

                .navbar-right~.navbar-right {
@* margin-right: 0; *@

            @@media only screen and (max-width:1280px) {

                .navbar-brand1,
                .module-name {
@* float: left; *@
@* font-size: 12px !important; *@
@* margin-top: 12px; *@
                }

                .lbl-login-title {
@* font-size: 39px !important; *@

                .__patient-top-pnl {
                    /* position: unset; */

                #medicationTemplates {
@* float: left; *@

            @@media only screen and (max-width:1322px) {
                #patientSearch {
@* width: 110px; *@
                }
            }

            @@media (min-width: 768px) {
                #divMeasurement .col-sm-2 {
@* width: 13.666667%; *@
                }
            }

            #divMeasurement .form-group {
@* margin-bottom: 10px !important; *@

            .td-edit-appointment .ul-popover-list {
@* min-height: 190px !important; *@

            .lbl-appointments {
@* margin-left: 15px; *@

            #divhistory #scrollable-table .tbl-header-item {
@* padding: 2px 0px 2px 0px; *@

            .vp-meas-box,

@* padding-left: 2px !important; *@
@* padding-right: 1px !important; *@

            #divMeasurement {
@* clear: both; *@

            #svgContainer {
@* background-color: #fff; *@

            #svgContainer #lgnd rect {
                /@*fill:#ff8800;*@/ }

            #svgContainer text {
@* fill: #333; *@
@* font-weight: normal; *@

            #segmentTable {
@* width: 100%; *@
@* border: 0px !important; *@

            .green {
@* color: #4b9e02 !important; *@

            .small,
            small {
@* font-size: unset !important; *@

            #schedule-container {
@* margin-top: 10px !important; *@

            .label-danger {
@* background-color: #d9534f; *@
@* font-size: 13px; *@
@* font-weight: normal; *@
@* padding: 1px; *@
            }

            h4 .custom-label {
@* font-size: 14px; *@
@* color: #444; *@
@* text-transform: uppercase; *@
            }

            #editPatientModal .col-md-2,
            #editPatientModal .col-md-3,
            #editPatientModal .col-md-4,
            #createPatientModal .col-md-2,
            #createPatientModal .col-md-3,
            #createPatientModal .col-md-4 {
@* height: 30px !important; *@
            }

            #editPatientModal hr,
            #createPatientModal hr {
@* margin-top: unset; *@
@* margin-bottom: 9px; *@
            }

            #editPatientModal .ui-draggable-handle,
            #createPatientModal .ui-draggable-handle {
@* cursor: pointer !important; *@

            #table-prescription {
@* width: 100%; *@

            #table-prescription td {
@* padding: 3px; *@

            #table-rx-body {
@* width: 100%; *@
@* border: 0px !important; *@

            .bold {
@* font-weight: bold; *@

            .meas-list li {
@* padding-left: 4px !important; *@

            .tbl-meas89>tbody>tr>td,
            .tbl-meas89>tbody>tr>th,
            .tbl-meas89>tfoot>tr>td,
            .tbl-meas89>tfoot>tr>th,
            .tbl-meas89>thead>tr>td,
            .tbl-meas89>thead>tr>th {
@* border-top: unset !important; *@
            }

            .tbl-meas89 tr:nth-child(odd) {
@* background: unset !important; *@

            ._mm86 {
@* float: left; *@
@* width: 46px; *@

            .daysheet-double-book {
@* background-color: orange; *@

            .modal-body .fax_p_p,

            .modal-body textarea,
            .modal-body select {
@* border: 1px solid #868686 !important; *@
            }

            .modal-body .c3-select {
@* border: 1px solid #868686 !important; *@

            .modal-body .c3-select span {
@* padding: 0 !important; *@
@* align-self: center; *@

            .btn-info-7 {
@* cursor: pointer; *@
@* color: #23527c; *@

            .cppBox {
@* min-height: 150px; *@
@* border: 1px solid #e2e2e2; *@
@* margin-bottom: 3px; *@
@* padding: 3px; *@
            }

            .__009857 tr td {
@* background: #D4CFCA !important; *@

            #div-vp-ms table {
@* border: 1px solid #e2e2e2 !important; *@

            .panel-title,
            .panel-title,
            .panel-info>.panel-heading,
            .rep-phrase-header,
            #div-vp-ms .custom-label,
            .billing-heading,
            .heading {
@* text-transform: uppercase !important; *@
                /* font-weight: bold !important; */
            }

            .heading {
@* color: #8598a0; *@
@* text-transform: uppercase; *@

            #div-vp-ms .custom-label {
@* color: #444 !important; *@

            .modal-sub-title {
@* text-transform: uppercase; *@
@* font-weight: bold; *@

            .txtBox,
            .largetxtBox {
@* padding-left: 3px !important; *@

            #div-pat-name {
@* height: 38px; *@

            #main-nav li.dropdown ul li a {
@* color: #4e4b4b !important; *@

            .app-status-item,
            .app-priority-item {
@* cursor: pointer; *@

            .pre-scrollable {
@* border: 2px solid #f2f2f2; *@

            .thin-line {
@* border: 1px solid #f2f2f2; *@

            .min-width-100 {
@* min-width: 100px !important; *@

            .alert-success {
@* background-color: #a4e22b; *@
@* color: #444; *@

            .glyphicon-pencil {
@* color: #5a5959 !important; *@
@* text-shadow: 0 0 3px #fff; *@

            #table-daysheet .glyphicon-pencil {
@* padding: 1px 5px !important; *@

            .tealcolor {
@* padding-left: 10px; *@
@* color: #444; *@
@* font-weight: bold; *@
            }

            .coralcolor {
@* color: #555 !important; *@

            #div-vp-rp .txtArea {
@* font-size: 12px !important; *@

            .btn-edit-ext-doctor,
            .btn-pay-method,
            .btn-add-test {
@* width: 40px; *@
@* padding-left: 4px; *@
            }

            .popover-title {
@* color: #5a5959; *@

            .schedule-day-header {
@* font-size: 9px !important; *@
@* font-weight: normal !important; *@

            #divMeasurement .label-primary {
@* font-size: 13px !important; *@

            .th-width-26 {
                /@*width: 37%;*/*@

            .hgt-24 {
@* height: 24px; *@

            .units {
                /* vertical-align: middle; */
@* overflow: hidden; *@
@* white-space: nowrap; *@
@* text-overflow: ellipsis; *@
@* width: 30px; *@
                /@* font-size: 11px; */*@
@* cursor: pointer; *@
@* background-color: inherit; *@
@* color: #4b9e02 !important; *@

            .med-templ-lst li a {
                /* margin-bottom: -6px; */
@* line-height: 1 !important; *@

            .tooltip-inner {
                /@* tooltip multline *@/ @* white-space: pre-wrap; *@

            .ctrl-eChart7 {
@* color: #23527c; *@
@* cursor: pointer; *@

            .ctrl-eChart7:hover,
            .lbl-open-pnl span:nth-child(2):hover {
@* text-decoration: underline !important; *@

            .cppBoxEdit:hover {
@* text-decoration: none; *@

            .ddl-matching-btn-sm {
@* line-height: 1.5; *@
@* border-radius: 3px; *@
@* line-height: 16.428571; *@
@* height: 32px; *@
@* margin-top: -1px; *@

            .rd {
@* background: transparent; *@
@* outline: 0; *@
@* cursor: default; *@
@* resize: none; *@
            }

            .smallLbl {
@* width: 55px; *@
@* display: inline-block; *@
@* margin-bottom: 4px; *@
            }

            .txtBox7 {
@* padding-left: 3px !important; *@
@* width: 50px !important; *@

            .modal-backdrop {
@* z-index: 1035 !important; *@

            .modal.in {
@* margin-top: 30px !important; *@

            #wrapper7 {
@* overflow-x: scroll !important; *@
@* margin-left: 2em !important; *@

            .headcol {
@* position: absolute !important; *@
@* width: 2em !important; *@
@* left: 5px !important; *@
            }

            .label {
@* padding: 1px 7px !important; *@

            .navbar-fixed-bottom,
            .navbar-fixed-top {
@* z-index: 1052 !important; *@

            .__H789MED {
@* min-height: 170px; *@

            #vp-modal-container .ctrl-eChart7 {
@* display: none !important; *@

            .__887034 #OfficeId {
@* margin-right: 27px; *@

            .ddl-max,
            .chk-discard {
@* display: none; *@

            #frm-vp {
@* margin-bottom: 126px; *@

            .hdr-lbl {
@* clear: both; *@
@* display: block; *@

            .btn-xs {
@* height: 23px !important; *@

            .row-hdr-span0 {
@* display: inherit; *@
@* border-top: 3px solid #b0acac; *@

            .row-hdr-span {
@* display: inherit; *@
@* border-top: 3px solid #b0acac; *@
@* color: #8598a0 !important; *@
                /* #a4a27b; */
            }

            .row-hdr-span2 {
@* display: inherit; *@
@* border-top: 1px solid #c5c1c1; *@
@* color: #8598a0 !important; *@
                /@*#a4a27b;*@/ /*border-bottom: 2px solid #b0acac;*/
            }

            .row-hdr-span3 {
@* display: inherit; *@
@* border-top: 3px solid #b0acac; *@

            .hdr {
@* font-size: 22px; *@

            .hdr-lbl-ht {
@* text-transform: uppercase; *@
@* color: #f28383; *@

            .patientName-cls {
@* font-size: 22px; *@

            .testName-cls {
@* font-size: 18px; *@

            .noChart-cls {
@* margin-bottom: 18px; *@

            .row-span {
@* border-top: 3px solid #b0acac; *@
                /* border-bottom: 1px solid #b0acac; */
@* display: inherit; *@
@* overflow-wrap: break-word; *@
@* word-wrap: break-word; *@
@* -ms-word-break: break-all; *@
                /@* This is the dangerous one in WebKit, as it breaks things wherever */*@
@* word-break: break-all; *@
                /*  Instead use this non-standard one:  */
@* word-break: break-word; *@
                /@* Adds a hyphen where the word breaks, if supported (No Blink) *@/ @* -ms-hyphens: auto; *@
@* -moz-hyphens: auto; *@
@* -webkit-hyphens: auto; *@
@* hyphens: auto; *@

            .row-span3 {
@* display: inherit; *@
@* border-top: 1px solid #c5c1c1; *@
                /*border-bottom: 2px solid #b0acac;*/
            }

            .truncate {
@* white-space: nowrap; *@
@* overflow: hidden; *@
@* text-overflow: ellipsis; *@
            }

            .wrap {
                /* width:5px; */
@* display: table; *@
@* white-space: pre-wrap; *@
                /@* css-3 */*@
@* white-space: -moz-pre-wrap; *@
                /*  Mozilla, since 1999  */
@* white-space: -pre-wrap; *@
                /@* Opera 4-6 *@/ @* white-space: -o-pre-wrap; *@
                /*  Opera 7  */
@* word-wrap: break-word; *@
                /@* Internet Explorer 5.5+ *@/ }

            .cppBox,
            .popover-content,
            .dont-break-out {
                /* These are technically the same, but use both */
@* overflow-wrap: break-word !important; *@
@* word-wrap: break-word !important; *@
@* -ms-word-break: break-all !important; *@
                /*  This is the dangerous one in WebKit, as it breaks things wherever  */
@* word-break: break-all !important; *@
                /@* Instead use this non-standard one: *@/ @* word-break: break-word !important; *@
                /*  Adds a hyphen where the word breaks, if supported (No Blink)  */
@* -ms-hyphens: auto !important; *@
@* -moz-hyphens: auto !important; *@
@* -webkit-hyphens: auto !important; *@
@* hyphens: auto !important; *@

            .fixedWidthfont,
            .fixedWidthChr {
@* font-family: Arial, sans-serif !important; *@
                /@*font-family:  'Cutive Mono' !important; /*'Cutive Mono', monospace*@/ */ white-space: pre-wrap;
            }

            .olis-heading {
@* color: #8598a0 !important; *@
@* text-transform: uppercase; *@

            .olis-heading-sub {
@* color: #8598a0 !important; *@
                /*  #d5a069; > #8fa322 | d9ff98 d9ff98 */

            .hdr-lbl-hg {
@* text-transform: uppercase; *@
@* font-size: 22px; *@
@* color: #f28383; *@
            }

            .hdr-lbl-ht {
@* text-transform: uppercase; *@
@* font-size: 22px; *@
@* color: #f28383; *@
            }

            .hdr-lbl-nl {
@* text-transform: initial; *@
@* color: #ededed; *@

            .strikeText {
@* text-decoration: line-through !important; *@

            .red {
@* color: red !important; *@

            .headerTitleOLISOBXZ {
@* color: cornflowerblue !important; *@

            .OLIS-high-light-text {
@* background: #F3F7A6; *@
@* font-weight: bold; *@

            #vp-modal-content footer {
@* display: none; *@

    </style>
    <link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
    @RenderSection("AwareServicesMicroUiLoaderSlot", required: false)
</head>

<body>
    <input type="hidden" name="hdSessionId" id="hdSessionId" value="@Context.Session.Id" />
    <div id="frm-builder-link-holder" data-frm-builder-link="@CerebrumFormBuilderLink" class="hidden"></div>
    <div id="overlayDiv"></div>
    <div id="overlayDiv2"></div>
    <div id="msgDiv">
        <div class="overlay-glyph glyphicon glyphicon-warning-sign "></div>
        <div class="overlay-msg"></div>
    </div>
    @{

        {

        }

        {

    <span id="c-return-url-holder" data-return-url="@Html.GetCurrentUrl()" class="hidden"></span>
    <nav id="main-nav" class="navbar navbar-fixed-top @navBGColorClass">

        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse"

                    <div id="btn_menu" class="icon-bar">Menu</div> <span class="icon-bar"></span> <span

                </button>
                <div class="float_l">
                    <a class="navbar-brand1" href="#" title="">&nbsp;</a>
                </div>
                <div class="module-name float_l">@moduleName&nbsp;</div>
            </div>
            <div class="navbar-collapse collapse " id="nav_hideUntilPageLoaded1">
                <ul class="nav navbar-nav pull-right" id="nav_main_pnl">
                    <li>
                        @Html.PartialAsync("_LoginPartialMain")
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div id="ajax-loader">
        <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
    </div>
    <div id="cb-modal-container"></div>
    <div id="dialog7" title="Patient E-Chart7">
        <div id="patientechartcontent7"></div>
    </div>
    @RenderSection("patientinfo", required: false)
    @RenderSection("toplinks", required: false)
    @RenderBody()

    <div id="cb-modal-container-bottom"></div>

            <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
        </div>
        <div id="cb-modal-container"></div>
        <div id="dialog7" title="Patient E-Chart7">
            <div id="patientechartcontent7"></div>
        </div>
    @RenderSection("scripts", required: false)
    @if (User.Identity.IsAuthenticated)
    {
        <script type="text/javascript">
            $(function () {
@* updateUserMenuCount(); *@

@* }); *@
        </script>

    <div class="body-content-footer-main text-center printNot">
        <div id="notification-placeholder" style-"z-index:7509"></div>
    </div>
    <footer class="site-footer printNot">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="float-Left-Label foItem hidden-xs">&copy; @DateTime.Now.Year - @copyright</div>
                    <div class="float-Left-Label foItem hidden-xs" id="ft-appName"></div>
                    <div class="float-Left-Label foItem mmn hidden-xs">ver.: @CerebrumVersion</div>
                    <div class="float-Left-Label foItem mmn hidden-xs">server: @CerebrumServer</div>
                    <div class="float-Left-Label foItem">Tech. support: <a id="ft-techSupportEmail" title="" href=""

                    <div id="div-uao-selected" class="float-Left-Label foItem">@uao</div>
                </div>
            </div>
        </div>
    </footer>

    @RenderSection("topscripts", required: false)

    <script src="/Scripts/jquery.easing.min.js" defer></script>
    <script src="/Scripts/handlebars.min.js"></script>
    <script type="text/javascript">
        @* *@
        //$('.hl-test-history')
        //    .popover({
        //        trigger: 'manual',
        //        html: true,
        //        placement: 'auto bottom'
        //    })
        //    .click(function (e) {
        //        if ($('#div-prev-tests-menu').length) {
        //            var box = $(this);
        //            var html = $('#div-prev-tests-menu').html();
        //            box.attr('data-content', html).popover('show');
        //        }
        //        e.preventDefault();
        //});
        *@ var messages = {
            invalidEmail: 'The Email field is not a valid e-mail address.',
            invalidPassword: 'The Password field is required',
            screenResolution: 'Your screen resolution is too small. Please use a display with minimum width of \'1034px\'.',
            OHIPExpired: 'OHIP card is expired',
            OHIPExpireToday: 'OHIP card will expire today'
@* }; *@

            email: function (arg) {

                if (!emailReg.test(arg)) {

                }
@* return isValidEmail; *@
            },
            str: function (val) {

                if (val.length < 1) {

                }
@* return isValidStr; *@

@* }; *@

            $.get(url, function (data) {
                $('#dialog7').dialog({
                    title: lastname + ', ' + firstname,
                    open: function (event, ui) {

                    }, create: function (event, ui) {
@* $(event.target).parent().css('position', 'fixed'); *@
                    }
@* }); *@
@* $('#patientechartcontent7').empty(); *@
@* $('#patientechartcontent7').append(data); *@
@* }); *@
@* }; *@

@* $('.navbar-brand1, .lbl-login-title').html(applicationNameFormatted + '&nbsp;').attr('title', applicationName); *@
@* $('#ft-appName').html(applicationNameFormatted); *@

@* $('#ft-techSupportEmail').html('@techSupportEmail').attr('title', '@techSupportEmail').attr('href', linkStr); *@

@* $('#overlay-msg').html(messages.screenResolution); *@
@* }; *@

@* $('.module-name').html(' - ' + arg); *@

@* }; *@

            if (arg.length > maxLength) {

            }
@* return ret; *@
@* }; *@

            if ($(window).width() < 1034) {
@* $('#overlayDiv,#msgDiv').show(); *@
@* $('body').css('overflow', 'hidden'); *@
            } else {
@* $('#overlayDiv,#msgDiv').hide(); *@
@* $('body').css('overflow', 'visible'); *@

@* }; *@

            if ((window.location.href).toLowerCase().indexOf('schedule') > -1 && (window.location.href).toLowerCase().indexOf('appointments') > -1 ||
                (window.location.href).toLowerCase().indexOf('measurements') > -1 && (window.location.href).toLowerCase().indexOf('appointmentid') > -1 ||
                (window.location.href).toLowerCase().indexOf('vp') > -1 && (window.location.href).toLowerCase().indexOf('appointmentid') > -1

            ) {
@* $('#nav_dataSheet').addClass('active_nav'); *@
            } else if ((window.location.href).toLowerCase().indexOf('contact') > -1) { //contactmanagers

@* $('#nav_myTasks').addClass('active_nav'); *@
                }
                //set page title as well
@* setHeaderTitle('My Tasks'); *@

            else if ((window.location.href).toLowerCase().indexOf('hrmmanagement') > -1 || (window.location.href).toLowerCase().indexOf('billingadmin') > -1) {
@* $('#nav_admin').addClass('active_nav'); *@

            else if ((window.location.href).toLowerCase().indexOf('manage') > -1 || (window.location.href).toLowerCase().indexOf('help') > -1) {
@* $('#nav_user').addClass('active_nav'); *@
            }
            else if ((window.location.href).toLowerCase().indexOf('/bill/') > -1) {
@* $('#nav_biling').addClass('active_nav'); *@
            }
            else if ((window.location.href).toLowerCase().indexOf('measurement') > -1 ||
                (window.location.href).toLowerCase().indexOf('externaldocument') > -1 ||
                (window.location.href).toLowerCase().indexOf('triage') > -1 ||
                (window.location.href).toLowerCase().indexOf('requisitionsearch') > -1
            ) {
@* $('#nav_toDo').addClass('active_nav'); *@
            }
@* }; *@

            $('.truncatedHeader').each(function (i, item) {
@* $(this).html(truncateText(($(item).html()).trim(), 25)); *@
@* }); *@
@* }; *@

            $('.lbl-name').each(function (i, item) {

@* $(this).html(arr[0].toUpperCase() + ', ' + arr[1]); *@
@* }); *@
@* }; *@

            //$('.ui-datepicker-next').addClass('glyphicon glyphicon-chevron-right');
            //$('.ui-datepicker-prev').addClass('glyphicon glyphicon-chevron-left');
@* }; *@

            if (paramValue) {
@* return paramValue; *@

            else {
@* return false; *@
            }
@* }; *@

            if (
                (window.location.href).toLowerCase().indexOf('assignment') > -1 &&
                (getQuerystring('externaldocument').toLowerCase()).length > -1 &&
                (getQuerystring('officeId').toLowerCase()).length > -1
            ) {
@* $('#createPatientModal, #editPatientModal').css('left', '-37% !important'); *@
            }
            else {
@* $('#createPatientModal, #editPatientModal').css('left', 'unset'); *@
            }
@* }; *@

            if (
                (window.location.href).toLowerCase().indexOf('account') > -1 && (window.location.href).toLowerCase().indexOf('forgotpassword') > -1 ||
                (window.location.href).toLowerCase().indexOf('account') > -1 && (window.location.href).toLowerCase().indexOf('login') > -1 ||
                (window.location.href).toLowerCase().indexOf('manage') > -1 && (window.location.href).toLowerCase().indexOf('changepassword') > -1
            ) {
@* $('#main-nav').hide(); *@
@* $('body').css('background-image', 'url(content/images/desk.jpg)'); *@
            }
            else {
@* $('#main-nav').show(); *@
@* $('body').css('background-color', mainBGcolor); *@

@* }; *@

@* return window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : ''); *@
@* }; *@

        $(document).on('click', '.td-edit-appointment a', function (e) {
            $('@*').each(function () {*@

                if (popover)
@* $(this).popover('hide'); // $(this).popover('destroy'); *@
@* }); *@
@* }); *@
@*{*@
            $('*@').each(function () { *@ var popover = $.data(this, 'bs.popover'); if (popover)
@* $(this).popover('hide');  // $(this).popover('destroy'); *@
@* }); *@
@* }); *@

@* setPageLabels(); *@
@* showBGimageIfLogin(); *@

        $(document).ready(function () {
            $('body').on('click', function (e) {
                if ($('.billingcodes-div').length > 0) {
                    $('.popover').each(function () {

                        if (!popover.$element.is(e.target)) {

@* popover.hide(); *@
                        }
@* }); *@

@* }); *@

@* truncateTableHeaders(); *@
@* highlightNavItem(); *@
            //formatName();
            //ifUploadReportsPage();

            //events
            $('.navbar-brand1').on('click', function (e) {

@* e.preventDefault(); *@
                if ((window.location.href).toLowerCase().indexOf('login') > -1 ||
                    (window.location.href).toLowerCase().indexOf('forgotpassword') > -1
                ) {

                }

@* }); *@

            $('#btn_returnToMain').on('click', function (e) {
@* $('.navbar-brand1').trigger('click'); *@
@* }); *@

            // $('html').click(function (e) {
            //if (menuVisible && $(e.target).attr('id') != 'mmm') {
            //    closeSidebar();
            //}
            // });

            $('#btn_menu').on('click', function (e) {

@* $('#overlayDiv2').show().fadeTo('easeInOutExpo', .9, function () { }); *@
                    //$('#overlayDiv2').show(function () { $('#overlayDiv2').fadeTo('fast', .9, function () { }) });
                }
                else {
@* $('#overlayDiv2').fadeTo('easeInOutExpo', 0, function () { $('#overlayDiv2').hide() }); *@

@* }); *@

            //extend modal height for Add/Edit patient
            //$('#cb-modal-container').on('show.bs.modal', function () {
            //    if ($(this).has('#createPatientModal').length > 0 || $(this).has('#editPatientModal').length > 0) {
            //        $(this).find('.modal-body').css({
            //            'max-height': $(window).height() - 234
            //        });
            //    }
            //    else {
            //        $(this).find('.modal-body').css('max-height', '');
            //    }
            //});
            //refresh upon successful patient edit
@* }); *@

        $(window).on('load', function () {
            if ($('.__009857').length > 0) {
                $('.hideOverflowUnits').each(function () {

@* $(this).html(''); *@
                    }
@* }); *@

                rel: 'stylesheet',
                type: 'text/css',
                href: '/Content/bootstrap-print2.css'
@* }); *@

                type: 'text/javascript',
                src: '/Scripts/cerebrum-print.js'
@* }); *@

@* css_link.appendTo('head'); *@
@* js_link.appendTo('head'); *@
@* }); *@

        $(window).resize(function () {

@* $('#btn_menu').trigger('click'); *@

@* $('#main-patient-search #patientSearch').autocomplete('close'); *@
@* }); *@

        $(window).scroll(function () {

            if (check) {
@* $('#main-patient-search #patientSearch').autocomplete('close'); *@
            }
@* }); *@

        function setDaysheetDoubleBookColor() {

@* var i, l; *@
            $('#table-daysheet .tr-row .td-status').each(function (index) {

@* console.log('practice doc: ' + item.practiceDoctor); *@
@* console.log('Row ID: ' + item.trRowId); *@

@* $('#' + item.trRowId).find('.app-time-color').first().css('background-color', doubleBookedColor); *@

@* $('#' + trRowId).find('.app-time-color').first().css('background-color', doubleBookedColor); *@

@* }); *@

    </script>
    <script type="text/javascript">
        if (!window.moment) {

            if (dateDiff > 0) {//expired

            }

            return {
                OHIPmsg: OHIPmsg,
                status: status

@* }; *@

        function ajaxCall(url, data, isFormData, callBack) {
            $.ajax({
                method: "POST",
                url: url,
                data: data,
                headers: { "RequestVerificationToken": requestVerificationToken },
                async: true,
                processData: isFormData ? false : undefined,
                contentType: isFormData ? false : undefined,
                beforeSend: function () { $("#ajax-loader").show(); },
                complete: function () { $("#ajax-loader").hide(); },
                success: function (result) { callBack(result); },
                error: function (xhr, thrownError) { checkAjaxError(url, xhr, thrownError); }
@* }); *@

    </script>
</body>

</html>
