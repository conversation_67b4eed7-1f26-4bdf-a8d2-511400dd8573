﻿using Cerebrum.BLL.Patient;
using Cerebrum.BLL.User;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum30.Security;
using log4net;
using Newtonsoft.Json;
using Ninject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using AwareMD.Cerebrum.Shared;
using Cerebrum30.Utility;
using Cerebrum30.Utility;
using Microsoft.ApplicationInsights.DataContracts;
using Cerebrum30.ApplicationInsights;
using Microsoft.Extensions.DependencyInjection;

namespace Cerebrum30.Filters
{
    public class BaseOnActionFilter : ActionFilterAttribute
    {
        /// <summary>
        /// Added for Redmine #13094
        /// </summary>
        public static string IpAddressHeader { get; } = System.Configuration.ConfigurationManager.AppSettings["IpAddressHeader"] ?? string.Empty;
        public PatientBLL _patientBLL { get; set; }
        public IUserCredentialBLL _userBll { get; set; }
        public log4net.ILog _log { get; set; }
        //readonly log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string messageNoPermission = "No permission to access this patient's information.";
        public BaseOnActionFilter()
        {
            // Services will be resolved from the service provider in OnActionExecuting
        }
        public BaseOnActionFilter(PatientBLL patientBLL, IUserCredentialBLL userBll, log4net.ILog log)
        {
            _patientBLL = patientBLL;
            _userBll = userBll;
            _log = log;
        }
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            // Resolve services from DI container if not already injected
            if (_log == null)
            {
                _log = filterContext.HttpContext.RequestServices.GetService<log4net.ILog>()
                    ?? log4net.LogManager.GetLogger(typeof(BaseOnActionFilter));
            }
            if (_patientBLL == null)
            {
                _patientBLL = filterContext.HttpContext.RequestServices.GetService<PatientBLL>();
            }
            if (_userBll == null)
            {
                _userBll = filterContext.HttpContext.RequestServices.GetService<IUserCredentialBLL>();
            }

            var request = filterContext.HttpContext.Request;
            var url = $"{request.Scheme}://{request.Host}{request.Path}{request.QueryString}";
            var ip = IPAddress(filterContext);
            var sessionId = filterContext.HttpContext.Session.Id;

            Helpers.BrowserInfo info = new Helpers.BrowserInfo();
            info.ip = ip;
            info.sessionId = sessionId;
            info.url = url;
            // TODO: Implement proper session storage for ASP.NET Core
            // filterContext.HttpContext.Session.SetString("BrowserData", JsonSerializer.Serialize(Helpers.BrowserData.Add(info, existingData)));

            if (ip.Equals("::1"))
            {
                try
                {
                    ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork).ToString();
                }
                catch { }
            }

            var queryparam = QueryString(filterContext);

            var logmsg = new
            {
                sessionId = sessionId,
                URL = url,
                HttpMethod = request.Method,
                Path = request.Path,
                PatientId = queryparam.Item1,
                Form = queryparam.Item2,
                userName = filterContext.HttpContext.User.Identity.Name,
                IPAddress = ip,
                LogOnUser = "", // request.LogonUserIdentity.Name - not available in ASP.NET Core
                Browser = "", // $"{request.Browser.Browser} {request.Browser.Version}" - not available in ASP.NET Core
                Port = $"{request.Host.Port}",
                IsAuthenticated = filterContext.HttpContext.User.Identity.IsAuthenticated,
                status = "",
                CreatedDateTime = DateTime.Now
            };

            try
            {
                var cerebrumUser = new CerebrumUser(filterContext.HttpContext.User as System.Security.Claims.ClaimsPrincipal);
                var requestTelemetry = filterContext.HttpContext.Items["Microsoft.ApplicationInsights.RequestTelemetry"] as RequestTelemetry;
                ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "c3.sessionId", sessionId, _log);
                ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "c3.practiceId", cerebrumUser.PracticeId.ToString(), _log);

                if (cerebrumUser.TenantId.HasValue)
                {
                    ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "tenant.id", cerebrumUser.TenantId.ToString(), _log);
                }
                ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "c3.userName", string.IsNullOrEmpty(logmsg.userName) ? logmsg.LogOnUser : logmsg.userName, _log);
                ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "c3.IPAddress", logmsg.IPAddress, _log);
                ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "c3.patientId", queryparam.Item1, _log);
                ApplicationInsightsTelemetryInitializer.SetRequestProperty(requestTelemetry, "c3.queryParams", logmsg.Form, _log);

                System.Diagnostics.Activity.Current?.AddBaggage("c3.userName", string.IsNullOrEmpty(logmsg.userName) ? logmsg.LogOnUser : logmsg.userName);
                System.Diagnostics.Activity.Current?.AddBaggage("c3.practiceId", cerebrumUser.PracticeId.ToString());
                if (cerebrumUser.TenantId.HasValue)
                {
                    System.Diagnostics.Activity.Current?.AddBaggage("tenant.id", cerebrumUser.TenantId.ToString());
                }

                _log.Page(
                    practiceId: cerebrumUser.PracticeId,
                    page: url,
                    ipaddress: ip,
                    user: string.IsNullOrEmpty(logmsg.userName) ? logmsg.LogOnUser : logmsg.userName,
                    message: JsonConvert.SerializeObject(logmsg),
                    loggedIn: logmsg.IsAuthenticated
                );

                if (!string.IsNullOrWhiteSpace(queryparam.Item1))
                {
                    int patientId = 0;
                    if (int.TryParse(queryparam.Item1, out patientId))
                    {
                        if (patientId != 0)
                        {
                            //string action = filterContext.ActionDescriptor.ActionName.ToLower();
                            bool hasPermission = false;
                            var userPatientAccessExits = cerebrumUser.HasPatientAccess(patientId);

                            hasPermission = userPatientAccessExits;

                            if (!userPatientAccessExits)
                            {
                                var dbPermission = _userBll.GetPatientAccess(cerebrumUser.UserId, patientId);

                                hasPermission = dbPermission == PatientAccesStatus.Access ? true : false;

                                if (hasPermission)
                                {
                                    cerebrumUser.UpdateAccessedPatientIds(patientId);
                                }
                            }

                            if (!hasPermission)
                            {
                                // TODO: ASP.NET Core migration - IsChildAction doesn't exist
                                if (filterContext.HttpContext.Request.IsAjaxRequest())
                                {
                                    filterContext.Result = new StatusCodeResult(403);
                                }
                                else
                                {
                                    ((Microsoft.AspNetCore.Mvc.Controller)filterContext.Controller).TempData["FilterMessage"] = messageNoPermission;
                                    filterContext.Result = new RedirectToRouteResult(new
                                    RouteValueDictionary(new { area = "", controller = "home", action = "nopermission" }));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_log != null) // putting this here for ticket 12545 Ninject have issue injecting the _log, we can remove this later when things getting stable
                {
                    _log.Error("BaseOnActionFilter ERROR - " + ex.ToString());
                }
                else
                {
                    throw new Exception("BaseOnActionFilter ERROR - _log should not be null", ex);
                }
            }


            base.OnActionExecuting(filterContext);
        }
        public string IPAddress(ActionExecutingContext filterContext)
        {
            var ip = GetUserIP(filterContext.HttpContext.Request);
            if (ip.Equals("::1"))
            {
                try
                {
                    ip = Dns.GetHostEntry(Dns.GetHostName()).AddressList.FirstOrDefault(ipa => ipa.AddressFamily == AddressFamily.InterNetwork).ToString();
                }
                catch { }
            }
            return ip;
        }

        public static string GetUserIP(System.Web.HttpRequest r)
        {
            var ip = Helper.GetClientIpByHeaderxForwarded(r);
            if (!string.IsNullOrEmpty(ip))
            {
                return ip;
            }

            ip = !string.IsNullOrWhiteSpace(r.ServerVariables[IpAddressHeader])
                ? r.ServerVariables[IpAddressHeader]
                : r.UserHostAddress;

            return GetUserIP(ip);
        }

        public static string GetUserIP(Microsoft.AspNetCore.Http.HttpRequest r)
        {
            return RequestUtils.GetIpAddress(r);
        }
        public static string GetUserIP(string ip)
        {
            if (!string.IsNullOrWhiteSpace(ip) && ip.Contains(","))
                ip = ip.Split(',').First().Trim();

            if (!string.IsNullOrWhiteSpace(ip) && IsValidIpAddress(ip))
            {
                return ip;
            }
            else
            {
                return "invalid";
            }
        }
        private static bool IsValidIpAddress(string ip)
        {
            IPAddress address;
            if (System.Net.IPAddress.TryParse(ip, out address))
            {
                switch (address.AddressFamily)
                {
                    case AddressFamily.InterNetwork:
                        return address.ToString() == ip;
                    default:
                        return true;
                }
            }
            else
            {
                return false;
            }
        }
        private Tuple<string, string> QueryString(ActionExecutingContext filterContext)
        {
            var r = filterContext.HttpContext.Request;
            string patientId = string.Empty;
            if (r.Method == "GET")
            {
                try
                {
                    var parameterNames = new List<string>() { "patientid", "patientrecordid", "pid" };
                    if (filterContext.ActionArguments.Keys.Any(p => parameterNames.Contains(p.ToLower()))) // action parameters first
                    {
                        var firstParam = filterContext.ActionArguments.Keys.FirstOrDefault(p => parameterNames.Contains(p.ToLower()));
                        patientId = Convert.ToString(filterContext.ActionArguments[firstParam]);


                    }
                    else // check query string
                    {
                        patientId = r.Query["patientid"];
                        if (string.IsNullOrWhiteSpace(patientId))
                            patientId = r.Query["patientrecordid"];
                        if (string.IsNullOrWhiteSpace(patientId))
                            patientId = r.Query["pid"];

                    }

                    if (String.IsNullOrWhiteSpace(patientId)) // check by appointmentId and appointmentTest too
                    {
                        var appParamNames = new List<string>() { "appointmentid", "appointmenttestid" };
                        var appIdStr = "";
                        var isAppId = false;

                        if (filterContext.ActionArguments.Keys.Any(p => appParamNames.Contains(p.ToLower()))) // action parameters first
                        {
                            var paramKey = "";

                            paramKey = filterContext.ActionArguments.Keys.FirstOrDefault(p => p.ToLower() == "appointmentid");
                            if (String.IsNullOrWhiteSpace(paramKey))
                            {
                                paramKey = filterContext.ActionArguments.Keys.FirstOrDefault(p => p.ToLower() == "appointmenttestid");
                            }
                            else
                            {
                                isAppId = true;
                            }

                            if (!String.IsNullOrWhiteSpace(paramKey))
                            {
                                appIdStr = Convert.ToString(filterContext.ActionArguments[paramKey]);
                            }
                        }
                        else // check query string
                        {
                            appIdStr = r.Query["appointmentid"];
                            if (string.IsNullOrWhiteSpace(appIdStr))
                            {
                                appIdStr = r.Query["appointmenttestid"];
                            }
                            else
                            {
                                isAppId = true;
                            }

                        }

                        if (!String.IsNullOrWhiteSpace(appIdStr))
                        {
                            int appId = 0;

                            if (int.TryParse(appIdStr, out appId))
                            {
                                //var patientBLL = new Cerebrum.BLL.Patient.PatientBLL();
                                int patId = _patientBLL.GetPatientIdByAppointmentInfo(appId, isAppId);

                                if (patId > 0)
                                {
                                    patientId = patId.ToString();
                                }
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    _log.Error($"{ex.Message} {ex.Source}");
                }
                return new Tuple<string, string>(patientId, r.QueryString.ToString());
            }
            else if (r.Method == "POST")
            {
                List<string> q = new List<string>();
                foreach (var key in r.Form.Keys)
                {
                    if (!string.IsNullOrWhiteSpace(r.Form[key]))
                        q.Add($"{key}={r.Form[key].ToString()}");
                }
                try
                {
                    patientId = r.Form["patientid"];
                    if (string.IsNullOrWhiteSpace(patientId))
                        patientId = r.Form["patientrecordid"];
                    if (string.IsNullOrWhiteSpace(patientId))
                        patientId = r.Form["pid"];
                }
                catch { }
                return new Tuple<string, string>(patientId, string.Join("|", q));
            }
            return new Tuple<string, string>("", "");
        }

    }
}