﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;

using Cerebrum30.Utility;
namespace Cerebrum30.Utility
{
    public class PDF_Writer
    {
        public string Create(
                            int appointmentID,
                            int patientID,
                            string fileName,
                            string reportPhrases,
                            string labResults,
                            string vsResults)
        {
            var document = new Document(PageSize.A4, 50, 50, 25, 25);

            string path = string.Format(
                                        "Areas/VP/Uploads/Reports/{0}_{1}/",
                                        appointmentID,
                                        patientID);

            // In ASP.NET Core, we need to use the web root path or content root path
            string contentRoot = HttpContextProvider.Current?.RequestServices?.GetService(typeof(Microsoft.Extensions.Hosting.IHostEnvironment)) is Microsoft.Extensions.Hosting.IHostEnvironment env ? env.ContentRootPath : Directory.GetCurrentDirectory();
            string absPath = Path.Combine(contentRoot, "wwwroot", path);
            string fullPath = Path.Combine(absPath, fileName);

            if (!Directory.Exists(absPath))
            {
                Directory.CreateDirectory(absPath);
            }

            var output = new FileStream(fullPath, FileMode.Create);
            var writer = PdfWriter.GetInstance(document, output);

            // Open the Document for writing
            document.Open();

            reportPhrases = reportPhrases.Replace("\\n", "|");
            vsResults = vsResults.Replace("\\n", "|");
            labResults = labResults.Replace("\\n", "|");

            foreach (var str in reportPhrases.Split("|".ToCharArray()))
            {
                document.Add(new Paragraph(str));
            }


            document.Add(new Chunk("\n"));

            foreach (var str in vsResults.Split("|".ToCharArray()))
            {
                document.Add(new Paragraph(str));
            }

            document.Add(new Chunk("\n"));

            foreach (var str in labResults.Split("|".ToCharArray()))
            {
                document.Add(new Paragraph(str));
            }

            //var reportPhrasesPara = new Paragraph(reportPhrases);
            //var vsResultsPara = new Paragraph(vsResults);
            //var labResultsPara = new Paragraph(labResults);

            //document.Add(reportPhrasesPara);
            //document.Add(vsResultsPara);
            //document.Add(labResultsPara);

            // Close the Document - this saves the document contents to the output stream
            document.Close();

            return fullPath;
        }


        public string Create(
                            int appointmentID,
                            int testID,
                            string fileName,
                            string reportPhrases,
                            string meas)

        {
            var document = new Document(PageSize.A4, 50, 50, 25, 25);

            string path = string.Format(
                                        "Areas/Measurements/Uploads/Reports/{0}_{1}/",
                                        appointmentID,
                                        testID);

            string contentRoot = HttpContextProvider.Current?.RequestServices?.GetService(typeof(IHostEnvironment)) is IHostEnvironment env ? env.ContentRootPath : Directory.GetCurrentDirectory();
            string absPath = Path.Combine(contentRoot, "wwwroot", path);
            string fullPath = Path.Combine(absPath, fileName);

            if (!Directory.Exists(absPath))
            {
                Directory.CreateDirectory(absPath);
            }

            if (File.Exists(fullPath))
            {
                File.Delete(fullPath);
            }

            var output = new FileStream(fullPath, FileMode.Create);

            var writer = PdfWriter.GetInstance(document, output);

            // Open the Document for writing
            document.Open();

            reportPhrases = reportPhrases.Replace("\\n", "|");
            meas = meas.Replace("\\n", "|");

            foreach (var str in reportPhrases.Split("|".ToCharArray()))
            {
                document.Add(new Paragraph(str));
            }

            document.Add(new Chunk("\n"));

            foreach (var str in meas.Split("|".ToCharArray()))
            {
                document.Add(new Paragraph(str));
            }

            //var reportPhrasesPara = new Paragraph(reportPhrases);
            //var vsResultsPara = new Paragraph(meas);

            //document.Add(reportPhrasesPara);
            //document.Add(vsResultsPara);


            // Close the Document - this saves the document contents to the output stream
            document.Close();
            document = null;

            writer.CloseStream = true;
            writer.Close();
            writer = null;

            output.Close();
            output.Dispose();
            output = null;

            return fullPath;
        }



        public void GetBase64()
        {


        }

        public string ConvertPDFToXML(string fileData, string fileName)
        {

            string xml = string.Format(
                                        @"<?xml version='1.0'?>
                                        <root xmlns:dt='urn:schemas-microsoft-com:datatypes'>
                                          <file dt:dt='bin.base64'>
                                          {0}
                                          </file>
                                          <name dt:dt='string'>/Newmarket555_Echocardiogram_Report~1_12_2016~169193~7281774112~9058305597.pdf</name>
                                        </root>", fileData);

            return xml;

        }

        [Obsolete("Do not use this method, we need to replace HttpWebRequest with HttpClientFactory")] // note by Mike L: I am not changing this one here because there is 0 references, so it is not being used
        public string PostXMLData(string destinationUrl, string requestXml)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(destinationUrl);
            byte[] bytes;
            bytes = System.Text.Encoding.ASCII.GetBytes(requestXml);
            request.ContentType = "text/xml; encoding='utf-8'";
            request.ContentLength = bytes.Length;
            request.Method = "POST";
            Stream requestStream = request.GetRequestStream();
            requestStream.Write(bytes, 0, bytes.Length);
            requestStream.Close();
            HttpWebResponse response;
            response = (HttpWebResponse)request.GetResponse();
            if (response.StatusCode == HttpStatusCode.OK)
            {
                Stream responseStream = response.GetResponseStream();
                string responseStr = new StreamReader(responseStream).ReadToEnd();
                return responseStr;
            }
            return null;
        }
    }
}
