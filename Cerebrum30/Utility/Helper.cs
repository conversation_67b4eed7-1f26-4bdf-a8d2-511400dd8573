﻿using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Identity;
using System.Security.Principal;
using System.Linq;
using Microsoft.AspNetCore.Http;
using HttpContext = Microsoft.AspNetCore.Http.HttpContext;
using System.IO;
using System.Configuration;
using System.Net.Mail;
using System.Net;
using Cerebrum30;
using System.Text;
using Cerebrum.Data;
using System.Net.Http;
using Cerebrum30.Infrastructure;
using HttpRequest = Microsoft.AspNetCore.Http.HttpRequest;   // <-- add this line

namespace Cerebrum30.Utility
{
    public class Helper
    {
        public static readonly string VP_TEST_NAME = "VP";
        public static readonly string REFERRAL_ROLE = "Referal Doctor";
        public static readonly string FRONT_DESK_ROLE = "Front Desk";
        public static readonly int VP_TEST_ID = 30;
        // Constant for storing the expiration date of the authentication token in session
        private const string AUTH_SERVER_TOKEN_EXPIRY_DATE_TIME = "AuthServerTokenSynchronousExpiryDateTime";
        private const string AUTH_SERVER_TOKEN = "AuthServerTokenSynchronous";
        private static readonly int AuthServerTokenExpiredInSeconds = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["AUTH_SERVER_TOKEN_EXPIRED_IN_SECONDS"]) == 0 ? 86400 : Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["AUTH_SERVER_TOKEN_EXPIRED_IN_SECONDS"]);
        private static readonly int AuthServerTokenBufferSeconds = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["AUTH_SERVER_TOKEN_BUFFER_SECONDS"]) == 0 ? 30 : Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["AUTH_SERVER_TOKEN_BUFFER_SECONDS"]);

        readonly static log4net.ILog _log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        static public int getEnumMin(Type t)
        {
            return Enum.GetValues(t).Cast<int>().Min();
        }
        static public int getEnumMax(Type t)
        {
            return Enum.GetValues(t).Cast<int>().Max();
        }

        public string GenerateToken(IHttpClientFactory httpClientFactory, int CallerUserID, int CallerPracticeID, string targetclienturl)
        {
            string retStr = string.Empty;

            string baseAddress = System.Configuration.ConfigurationManager.AppSettings["AUTH_SERVER"].ToString();
            //var clientConfig = GetClientConfigutaion();
            _log.Info($"Trying generate token for target URL: {targetclienturl}");
            string clientkey = System.Configuration.ConfigurationManager.AppSettings["clientkey"].ToString();
            string clientsecrect = System.Configuration.ConfigurationManager.AppSettings["clientsecrect"].ToString();
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            try
            {
                _log.Info($"GenerateToken Started - baseAddress: {baseAddress}");
                var client = httpClientFactory.CreateClient("download");
                var form = new Dictionary<string, string>
                   {
                       {"clientkey", clientkey},
                       {"clientsecrect", clientsecrect},
                       {"targetclienturl", targetclienturl},
                       {"CallerUserID", CallerUserID.ToString()},
                       {"CallerPracticeID", CallerPracticeID.ToString()}
                   };

                var tokenResponse = client.PostAsync(baseAddress + "/api/TokenAuthntication/GenerateToken", new FormUrlEncodedContent(form));
                var tokenResponse2 = tokenResponse.Result;
                if (tokenResponse2.StatusCode == HttpStatusCode.OK)
                {
                    _log.Info($"GenerateToken Succeed - baseUrl: {baseAddress}/api/TokenAuthntication/GenerateToken, Time: {stopwatch.ElapsedMilliseconds}");
                }
                else
                {
                    _log.Info($"GenerateToken Failed - baseUrl: {baseAddress}/api/TokenAuthntication/GenerateToken, Time: {stopwatch.ElapsedMilliseconds}, StatusCode: {tokenResponse2.StatusCode}");
                }
                string token = tokenResponse2.Content.ReadAsStringAsync().Result;
                retStr = token;
                return retStr;
            }
            catch (Exception ex)
            {
                _log.Error($"GenerateToken Error - baseAddress: {baseAddress}/api/TokenAuthntication/GenerateToken, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.Message}");
                if (ex.InnerException != null)
                {
                    _log.Error($"GenerateToken Error (Inner) - baseAddress: {baseAddress}/api/TokenAuthntication/GenerateToken, Time: {stopwatch.ElapsedMilliseconds}, ERROR: {ex.InnerException.Message}", ex.InnerException);
                }
                throw ex;
            }
        }
        public ParameterType GetParameterType(string term)
        {

            ParameterType result = ParameterType.NoData;

            term = term.ToUpper();

            switch (term)
            {

                case "APPOINTMENTID":

                    result = ParameterType.ApppointmentID;
                    break;

                case "DOCTORID":

                    result = ParameterType.DoctorID;
                    break;

                case "PATIENTID":


                    result = ParameterType.PatientID;
                    break;

                case "PATIENTRECORDID":

                    result = ParameterType.PatientRecordID;
                    break;

                default:
                    break;
            }

            return result;
        }

        public enum ParameterType
        {
            NoData = 0,
            ApppointmentID = 1,
            PatientID = 2,
            PatientRecordID = 2,
            DoctorID = 3
        }


        public bool TestByPractice(ParameterType dataType, int value, string userName)
        {
            int practiceID = (new Areas.Measurements.DataAccess.MeasurementRepository()).GetPracticeIDByUserName(userName);

            bool result = false;

            switch (dataType)
            {
                case ParameterType.NoData:


                    break;
                case ParameterType.ApppointmentID:

                    result = (new Areas.Measurements.DataAccess.MeasurementRepository()).TestAppointmentByPractice(value, practiceID);

                    break;
                case ParameterType.PatientID:

                    result = (new Areas.Measurements.DataAccess.MeasurementRepository()).TestPatientByPractice(value, practiceID);
                    break;

                //case ParameterType.PatientRecordID:

                //    break;
                case ParameterType.DoctorID:

                    result = (new Areas.Measurements.DataAccess.MeasurementRepository()).TestDoctorByPractice(value, practiceID);
                    break;
                default:
                    result = true;
                    break;
            }

            return result;
        }


        public static void WriteToLog(string errorMessage, Exception x = null)
        {
            _log.Error(errorMessage);
            try
            {
                // In ASP.NET Core, Server.MapPath is replaced with IWebHostEnvironment.WebRootPath
                string dir = Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "logs").Replace(@"/", @"\");
                bool bool_ = true;
                if (!Directory.Exists(dir))
                {
                    bool_ = CreateDirectory(dir);
                }

                if (bool_)
                {
                    using (StreamWriter w = File.AppendText(dir + @"/log.txt"))
                    {
                        Log(errorMessage, w, x);
                    }
                }

                //using (StreamWriter w = File.AppendText(HttpContextProvider.Current.Server.MapPath("~/logs/log.txt")))
                //{
                //    Log(errorMessage, w);
                //}
            }
            catch (Exception ex)
            {
                _log.Error($"{ex.Message} {ex.Source}");
            }

        }

        public static bool CheckIsDateCorrect(string sTime)
        {
            bool return_ = true;
            try
            {
                Convert.ToDateTime(sTime);
            }
            catch (FormatException)
            {
                return_ = false;
            }

            return return_;
        }

        public static bool CheckIsDateCorrect(DateTime sTime)
        {
            bool return_ = true;
            try
            {
                Convert.ToDateTime(sTime);
            }
            catch (FormatException)
            {
                return_ = false;
            }

            return return_;
        }
        private static void Log(string logMessage, TextWriter w, Exception x)
        {
            if (x == null)
                w.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd hh:mm:ss")} : {logMessage}");
            else
                w.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd hh:mm:ss")} : {logMessage} {ExceptionText(x)}");
        }

        private static string ExceptionText(Exception x)
        {
            if (x == null) return "";
            string txt = "Exception in:" + x.StackTrace;
            txt += (x.Message);
            while (x.InnerException != null)
            {
                txt += (x.InnerException.Message);
                x = x.InnerException;
            }
            return txt;
        }

        public static int GetTransferGroupSize()
        {
            int num = 0;
            var numObj = System.Configuration.ConfigurationManager.AppSettings["transGroupSize"];
            int.TryParse(numObj, out num);
            return num;
        }

        public static bool SendCodeByMail(string provider, string email, string code)
        {
            string SMTPTelus = System.Configuration.ConfigurationManager.AppSettings["SMTPTelus"];
            string SMTPpw_Telus = System.Configuration.ConfigurationManager.AppSettings["SMTPpw_Telus"];
            string SMTPun_Telus = System.Configuration.ConfigurationManager.AppSettings["SMTPun_Telus"];
            string SMTPGoogle = System.Configuration.ConfigurationManager.AppSettings["SMTPGoogle"];
            string SMTPpw_Googl = System.Configuration.ConfigurationManager.AppSettings["SMTPpw_Googl"];
            string SMTPun_Google = System.Configuration.ConfigurationManager.AppSettings["SMTPun_Google"];
            string SMTP_From = System.Configuration.ConfigurationManager.AppSettings["SMTP_From"];

            if (provider == "Email Code")
            {
                #region Telus Server
                //MailMessage mail = new MailMessage();
                //SmtpClient SmtpServer = new SmtpClient(SMTPTelus);
                //mail.From = new MailAddress(SMTP_From);
                //mail.To.Add(email);
                ////mail.CC.Add("<EMAIL>");
                //mail.Subject = "security code";
                //mail.Body = "Security code: " + code;
                //Helper.WriteToLog("Message sent:" + "Security code: " + code);
                //SmtpServer.Port = 2525;
                //SmtpServer.Credentials = new System.Net.NetworkCredential(SMTPun_Telus, SMTPpw_Telus);
                //SmtpServer.EnableSsl = false;
                //try
                //{
                //    SmtpServer.Send(mail);
                //    return true;
                //}
                //catch (Exception ex)
                //{
                //    System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                //    string methodName = st.GetFrame(0).GetMethod().Name;

                //    string Msg = methodName + " ### " + ex.Message + " ### ";
                //    if (ex.InnerException != null)
                //        Msg += ex.InnerException.Message + " ### SendCodeByMail " + provider;

                //    WriteToLog(Msg);
                //}
                #endregion

            }
            else if (provider == "Second Email Code")
            {
                #region Goodle Server
                var client1 = new SmtpClient(SMTPGoogle, 587);
                client1.UseDefaultCredentials = false;
                client1.Credentials = new NetworkCredential(SMTPun_Google, SMTPpw_Googl);
                client1.EnableSsl = false;
                try
                {
                    //email = "<EMAIL>";
                    client1.Send(SMTP_From, email, "security code", "Security code: " + code);
                    return true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                    string methodName = st.GetFrame(0).GetMethod().Name;

                    string Msg = methodName + " ### " + ex.Message + " ### ";
                    if (ex.InnerException != null)
                        Msg += ex.InnerException.Message + " ### SendCodeByMail " + provider;

                    WriteToLog(Msg);
                }
                #endregion
            }

            return false;
        }

        public static bool CreateDirectory(string path)
        {
            DirectoryInfo di = new DirectoryInfo(path);
            bool bool_ = false;
            try
            {
                if (di.Exists)
                {
                    bool_ = true;
                    di.Delete(true);
                }

                di.Create();
                bool_ = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message + " ### Helper.CreateDirectory";

                WriteToLog(Msg);
            }
            return bool_;
        }

        public static bool DeleteDirectory(string path)
        {
            DirectoryInfo di = new DirectoryInfo(path);
            try
            {
                di.Delete(true);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                string methodName = st.GetFrame(0).GetMethod().Name;

                string Msg = methodName + " ### " + ex.Message + " ### ";
                if (ex.InnerException != null)
                    Msg += ex.InnerException.Message + " ### Helper.DeleteDirectorie";

                WriteToLog(Msg);
            }
            return false;
        }

        /// <summary>
        /// send email via telus smtp server
        /// </summary>
        /// <param name="emailFrom">sender's email address </param>
        /// <param name="emailTo">receiver's email address</param>
        /// <param name="emailBody">emial body</param>
        /// <param name="subject">subject</param>
        /// <returns>return true if succ</returns>
        public static bool SendMail(string emailFrom, string emailTo, string emailBody, string subject, string attachmentPath = null)
        {
            string SMTPTelus = System.Configuration.ConfigurationManager.AppSettings["SMTPTelus"];
            string SMTPpw_Telus = System.Configuration.ConfigurationManager.AppSettings["SMTPpw_Telus"];
            string SMTPun_Telus = System.Configuration.ConfigurationManager.AppSettings["SMTPun_Telus"];

            using (MailMessage mail = new MailMessage())
            {
                SmtpClient SmtpServer = new SmtpClient(SMTPTelus);
                mail.From = new MailAddress(emailFrom);
                mail.To.Add(emailTo);
                //mail.CC.Add("<EMAIL>");
                mail.Subject = subject;
                mail.Body = emailBody;

                if (!string.IsNullOrEmpty(attachmentPath))
                {
                    mail.Attachments.Add(new Attachment(attachmentPath));
                }

                int port = 2525;
                // TODO: ASP.NET Core migration - ConfigurationManager doesn't exist, use IConfiguration
                string emailPort = null; // ConfigurationManager.AppSettings["SMTP_Telus_Port"];
                if (!string.IsNullOrWhiteSpace(emailPort))
                {
                    if (!int.TryParse(emailPort, out port))
                        port = 2525;
                }
                bool sslEnable = false;
                // TODO: ASP.NET Core migration - ConfigurationManager doesn't exist, use IConfiguration
                string sslEnableConfig = null; // ConfigurationManager.AppSettings["SMTPssl_Telus"];
                if (!string.IsNullOrWhiteSpace(sslEnableConfig) && sslEnableConfig.ToLower().Contains("on"))
                    sslEnable = true;

                SmtpServer.Port = port;
                SmtpServer.Credentials = new System.Net.NetworkCredential(SMTPun_Telus, SMTPpw_Telus);
                SmtpServer.EnableSsl = sslEnable;
                try
                {
                    SmtpServer.Send(mail);
                    return true;
                }

                catch (Exception ex)
                {
                    System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
                    string methodName = st.GetFrame(0).GetMethod().Name;

                    string Msg = methodName + " ### " + ex.Message + " ### ";
                    if (ex.InnerException != null)
                        Msg += ex.InnerException.Message + " ### SendMail ";

                    WriteToLog(Msg);
                }
            }

            return false;
        }

        public static string ReplaceNonPrintableCharacters(string s, char replaceWith)
        {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < s.Length; i++)
            {
                char c = s[i];
                byte b = (byte)c;
                if (b < 32)
                    result.Append(replaceWith);
                else
                    result.Append(c);
            }
            return result.ToString();
        }

        public static byte[] ImageToByteArray(System.Drawing.Image imageIn)
        {
            MemoryStream ms = new MemoryStream();
            imageIn.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
            imageIn.Dispose();
            return ms.ToArray();
        }

        public static string Environment()
        {
            string env = string.Empty;
            try
            {
                env = System.Configuration.ConfigurationManager.AppSettings["CerebrumEnvironment"].ToString();
            }
            catch
            {

            }
            return env;
        }
        public static bool IsProdEnvironment()
        {
            string env = Environment();
            if (string.IsNullOrEmpty(env))
            {
                return true;
            }
            else if (env.ToLower().Contains("production"))
            {
                return true;
            }

            return false;
        }
        public static void EraseOneIdSessionTokens(Microsoft.AspNetCore.Http.HttpContext httpContext)
        {
            var session = httpContext.Session;
            session.Remove("IsTokenRefreshPossible");
            session.Remove("TokenExpirationDate");
            session.Remove("RefreshTokenExpirationDate");
            session.Remove("Token2");

            session.Remove("AuthZId");
            session.Remove("Pcoi_url");
            session.Remove("Cms_url");
            session.Remove("FHIR_iss");
            session.Remove("Hub_url");
            session.Remove("Eforms_url");
            session.Remove("eReport_url");
            session.Remove("OTNeConsult_url");
            session.Remove("HPG_url");
        }

        public static string GenerateRequestId()
        {
            Random generator = new Random();
            var r = generator.Next(100000000, 999999999).ToString();
            return r;
        }
        public static string ToLowerAndTrim(string str)
        {
            if (string.IsNullOrWhiteSpace(str))
            {
                return "";
            }
            else
            {
                return str.ToLower().Trim();
            }
        }
        //https://stackoverflow.com/questions/735350/how-to-get-a-users-client-ip-address-in-asp-net
        public static string GetClientIpByHeaderxForwarded(HttpRequest request)
        {
            return GetIpFromString(request.Headers["X-Forwarded-For"]);
        }
        public static string GetClientIpByHeaderxForwarded()
        {
            var request = HttpContextProvider.Current.Request;
            return GetIpFromString(request.Headers["X-Forwarded-For"]);
        }
        private static string GetIpFromString(string xForwardedFor)
        {
            if (!String.IsNullOrEmpty(xForwardedFor))
            {
                var ip = xForwardedFor.Split(',')[0];
                if (!ip.Equals("::1"))
                {
                    return ip;
                }
            }
            return string.Empty;
        }
    }
}
static class IndentityInfo
{
    private static ApplicationUserManager _userManager;
    private static ApplicationRoleManager _roleManager;

    // TODO: Replace with dependency injection in ASP.NET Core
    // These static properties don't work with ASP.NET Core - use DI instead
    /*
    public static ApplicationUserManager UserManager
    {
        get
        {
            return _userManager ?? HttpContextProvider.Current.GetOwinContext().GetUserManager<ApplicationUserManager>();
        }
        private set
        {
            _userManager = value;
        }
    }

    public static ApplicationRoleManager RoleManager
    {
        get
        {
            return _roleManager ?? HttpContextProvider.Current.GetOwinContext().Get<ApplicationRoleManager>();
        }
        private set
        {
            _roleManager = value;
        }
    }
    */


    /// <summary>
    /// Get Practice Id of the logged user (Principal)
    /// </summary>
    /// <param name="identity"></param>
    /// <returns></returns>
    // TODO: Replace with proper ASP.NET Core Identity implementation using ClaimsPrincipal and DI
    public static int GetPracticeId(IIdentity identity)
    {
        // Temporary implementation - should be replaced with proper claims-based approach
        // For now, return a default practice ID to allow compilation
        // This needs to be implemented properly with ClaimsPrincipal and user claims
        return 1; // Default practice ID - NEEDS PROPER IMPLEMENTATION
    }

    /// <summary>
    /// Get permissions to the critical resources for the authenticated user
    /// </summary>
    /// <param name="user"></param>
    /// <param name="criticalResources"></param>
    /// <returns></returns>
    public static List<string> GetPermissions(IPrincipal user, List<string> criticalResources)
    {
        // TODO: Implement proper permission checking
        // For now, return all requested permissions as granted
        return criticalResources ?? new List<string>();
    }

    /// <summary>
    /// Get user by identity - temporary implementation for compilation
    /// </summary>
    /// <param name="identity"></param>
    /// <returns></returns>
    // TODO: Replace with proper ASP.NET Core Identity implementation using UserManager and DI
    public static Cerebrum.Data.ApplicationUser GetUserByIdentity(IIdentity identity)
    {
        // Temporary implementation - should be replaced with proper UserManager approach
        // For now, return a basic user object to allow compilation
        // This needs to be implemented properly with UserManager<ApplicationUser> via DI
        if (identity?.IsAuthenticated == true)
        {
            return new Cerebrum.Data.ApplicationUser
            {
                UserID = 1, // Default user ID - NEEDS PROPER IMPLEMENTATION
                UserName = identity.Name ?? "DefaultUser",
                FirstName = "Default",
                LastName = "User",
                Email = "<EMAIL>",
                CerebrumUserType = AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.Doctor // Default type
            };
        }
        return null;
    }

    /// <summary>
    /// Get permissions to the critical resources for the authenticated user
    /// </summary>
    /// <param name="user"></param>
    /// <param name="criticalresources"></param>
    /// <returns></returns>
    // TODO: Replace with ASP.NET Core Identity methods using ClaimsPrincipal
    /*
    public static List<string> GetPermissions(IPrincipal user, List<string> criticalresources)
    {
        CerebrumContext context = new CerebrumContext();
using Cerebrum30.Utility;
        string IdentityId = user.Identity.GetUserId();//GUID
        Cerebrum.Data.ApplicationUser au = context.Users.First(u => u.Id == IdentityId);
        List<string> userRolesIDs = (from ur in au.Roles select ur.RoleId).ToList();
        var allprs = from p in context.Permissions select p;
        List<string> userPermission = new List<string>();
        foreach (var id in userRolesIDs)
        {
            var ar = (Cerebrum.Data.ApplicationRole)(context.Roles.First(aRole => aRole.Id == id));
            var resultSet = (from rs in ar.RolePermissions
                             join itm in allprs on rs.PermissionId equals itm.Id
                             select itm.Name).ToList();
            foreach (var item in resultSet)
            {
                userPermission.Add(item);
            }
        }
        return (userPermission.Intersect(criticalresources)).ToList();
    }
    */

    /// <summary>
    /// Temporary UserManager property for compilation compatibility
    /// </summary>
    // TODO: Replace with proper dependency injection in ASP.NET Core
    public static ApplicationUserManager UserManager
    {
        get
        {
            // Temporary implementation - should be replaced with proper DI
            // For now, return null to allow compilation
            // This needs to be implemented properly with UserManager<ApplicationUser> via DI
            throw new NotImplementedException("UserManager access needs to be implemented with dependency injection in ASP.NET Core");
        }
    }

    /// <summary>
    /// Find permissions for user - temporary implementation for compilation
    /// </summary>
    /// <param name="user"></param>
    /// <param name="criticalresources"></param>
    /// <returns></returns>
    // TODO: Replace with ASP.NET Core Identity methods using ClaimsPrincipal and DI
    public static List<string> FindPermissions(System.Security.Principal.IPrincipal user, List<string> criticalresources)
    {
        // Temporary implementation - should be replaced with proper claims-based approach
        // For now, return empty list to allow compilation
        // This needs to be implemented properly with ClaimsPrincipal and role claims
        return new List<string>(); // Empty permissions - NEEDS PROPER IMPLEMENTATION
    }

    /// <summary>
    /// Find permissions for ApplicationUser - overload for compatibility
    /// </summary>
    /// <param name="user"></param>
    /// <param name="criticalResource"></param>
    /// <returns></returns>
    // TODO: Replace with ASP.NET Core Identity methods using ClaimsPrincipal and DI
    public static List<string> FindPermissions(Cerebrum.Data.ApplicationUser user, string criticalResource)
    {
        // Temporary implementation - should be replaced with proper claims-based approach
        // For now, return the critical resource if user is valid to allow compilation
        // This needs to be implemented properly with UserManager and role claims
        if (user != null && !string.IsNullOrEmpty(criticalResource))
        {
            return new List<string> { criticalResource }; // Return the requested permission - NEEDS PROPER IMPLEMENTATION
        }
        return new List<string>(); // Empty permissions - NEEDS PROPER IMPLEMENTATION
    }

    /// <summary>
    /// Find permissions for ApplicationUser - overload without critical resource
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    // TODO: Replace with ASP.NET Core Identity methods using ClaimsPrincipal and DI
    public static List<string> FindPermissions(Cerebrum.Data.ApplicationUser user)
    {
        // Temporary implementation - should be replaced with proper claims-based approach
        // For now, return default permissions based on user type to allow compilation
        // This needs to be implemented properly with UserManager and role claims
        if (user != null)
        {
            // Return default permissions based on user type
            switch (user.CerebrumUserType)
            {
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.Doctor:
                    return new List<string> { "VP", "CanDoTests" }; // Default doctor permissions
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.CardiologyTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.VascularTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.SETech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.GXTTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.EchoTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.HolterTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.ECGTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.NuclearTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.USTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.XRayTech:
                case AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.PulmonaryTech:
                    return new List<string> { "CanDoTests" }; // Default technician permissions
                default:
                    return new List<string> { "BasicAccess" }; // Default basic permissions
            }
        }
        return new List<string>(); // Empty permissions - NEEDS PROPER IMPLEMENTATION
    }

}
