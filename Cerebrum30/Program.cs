using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Cerebrum.Data;
using Cerebrum30.Infrastructure;
using Cerebrum30.Utility;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Builder;
using System.IO;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using AutoMapper;
using Microsoft.Extensions.FileProviders;
using Microsoft.AspNetCore.Http;

var builder = WebApplication.CreateBuilder(args);

// Add Entity Framework
builder.Services.AddDbContext<CerebrumContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("C3Context")));

// Add ASP.NET Core Identity
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 6;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequireLowercase = false;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
    options.Lockout.MaxFailedAccessAttempts = 10;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.RequireUniqueEmail = true;
    options.SignIn.RequireConfirmedEmail = false;
})
.AddEntityFrameworkStores<CerebrumContext>()
.AddDefaultTokenProviders();

// Configure cookie authentication
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(240); // Default login minutes
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/LogOff";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.SlidingExpiration = true;
});

// Add MVC services WITHOUT runtime compilation to force build-time compilation
builder.Services.AddControllersWithViews();

// Add other services
builder.Services.AddHttpContextAccessor();
builder.Services.AddHttpClient();

// Add repository dependencies
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentRepository, Cerebrum3.DataAccess.AppointmentRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentTypeRepository, Cerebrum3.DataAccess.AppointmentTypeRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentTestRepository, Cerebrum3.DataAccess.AppointmentTestRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IPracticeTestRepository, Cerebrum3.DataAccess.PracticeTestRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IOfficeRepository, Cerebrum3.DataAccess.OfficeRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.ITestRepository, Cerebrum3.DataAccess.TestRepository>();

// Add BLL dependencies for AccountController
builder.Services.AddScoped<Cerebrum.BLL.User.IUserCredentialBLL, Cerebrum.BLL.User.UserCredentialBLL>();
builder.Services.AddScoped<Cerebrum.BLL.Practice.IPracticeBLL, Cerebrum.BLL.Practice.PracticeBLL>();
builder.Services.AddScoped<Cerebrum.BLL.User.IUserPasswordService, Cerebrum.BLL.User.UserPasswordService>();

// Add BlacklistedPasswordFileService with file path from configuration
string blacklistedPasswordFilePath = Path.Combine(builder.Environment.ContentRootPath, "Content", "Resources", "common-password-blacklist.txt");
builder.Services.AddSingleton(provider => new Cerebrum.BLL.Utility.BlacklistedPasswordFileService(blacklistedPasswordFilePath));

// Add session support
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(240); // Match cookie timeout
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(Program));
builder.Services.AddSingleton<IMapper>(provider =>
{
    var config = new MapperConfiguration(cfg =>
    {
        Cerebrum30.AutomapperConfig.RegisterMapper(cfg);
    });
    return config.CreateMapper();
});

// Add logging services
builder.Services.AddLogging();
builder.Services.AddSingleton<log4net.ILog>(provider =>
    log4net.LogManager.GetLogger(typeof(Program)));

// Register BLL services
builder.Services.AddScoped<Cerebrum.BLL.Patient.PatientBLL>(provider =>
    new Cerebrum.BLL.Patient.PatientBLL(provider.GetRequiredService<CerebrumContext>()));
builder.Services.AddScoped<Cerebrum.BLL.User.IUserCredentialBLL, Cerebrum.BLL.User.UserCredentialBLL>(provider =>
    new Cerebrum.BLL.User.UserCredentialBLL(provider.GetRequiredService<CerebrumContext>()));

// Register Schedule/Daysheet services
builder.Services.AddScoped<Cerebrum30.Areas.Schedule.DataAccess.IUOWDaysheet, Cerebrum30.Areas.Schedule.DataAccess.UOWDaysheet>();
builder.Services.AddScoped<Cerebrum30.Areas.Schedule.DataAccess.IUOWAppointments, Cerebrum30.Areas.Schedule.DataAccess.UOWAppointments>();
builder.Services.AddScoped<Cerebrum30.Areas.Schedule.DataAccess.IUOWSchedule, Cerebrum30.Areas.Schedule.DataAccess.UOWSchedule>();

// Register VirtualVisit repositories and services
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository>();
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository>();
builder.Services.AddScoped<Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository>();
builder.Services.AddScoped<Cerebrum.VirtualVisit.Data.IUOWVirtualVisit<
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
    Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository>,
    Cerebrum30.Areas.VirtualVisit.DataAccess.UOWVirtualVisit<
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository>>();
builder.Services.AddScoped<Cerebrum.VirtualVisit.Seedwork.IVirtualVisitBLL>(provider =>
{
    var uow = provider.GetRequiredService<Cerebrum.VirtualVisit.Data.IUOWVirtualVisit<
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository>>();
    return new Cerebrum.VirtualVisit.VirtualVisitBLL<
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitRoomRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitInvitationRepository,
        Cerebrum30.DAL.DataAccess.Repositories.VirtualVisitLogRepository>(uow);
});

// Register Practice services and repositories
builder.Services.AddScoped<Cerebrum3.Infrastructure.IPracticeRepository, Cerebrum3.DataAccess.PracticeRepository>();
builder.Services.AddScoped<Cerebrum.BLL.Practice.IPracticeBLL, Cerebrum.BLL.Practice.PracticeBLL>();

// Register AppointmentPriorityConfiguration services and repositories
builder.Services.AddScoped<Cerebrum3.Infrastructure.IAppointmentPriorityRepository, Cerebrum3.DataAccess.AppointmentPriorityRepository>();
builder.Services.AddScoped<Cerebrum3.Infrastructure.IPracticeDefaultAppointmentPriorityRepository, Cerebrum3.DataAccess.PracticeDefaultAppointmentPriorityRepository>();
builder.Services.AddScoped<Cerebrum.BLL.Admin.IAppointmentPriorityConfigurationBLL, Cerebrum.BLL.Admin.AppointmentPriorityConfigurationBLL>();

// Register filters
builder.Services.AddScoped<Cerebrum30.Filters.BaseOnActionFilter>();

// Register compatibility layer services
builder.Services.AddScoped<Cerebrum30.Infrastructure.ApplicationUserManager>();
builder.Services.AddScoped<Cerebrum30.Infrastructure.ApplicationRoleManager>();
builder.Services.AddScoped<Cerebrum30.Infrastructure.ApplicationSignInManager>();

var app = builder.Build();
HttpContextProvider.Configure(app.Services.GetRequiredService<IHttpContextAccessor>());


// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseSession();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
