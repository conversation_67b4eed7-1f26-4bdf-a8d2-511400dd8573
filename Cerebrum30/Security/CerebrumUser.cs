﻿using Cerebrum.BLL.User;
using AwareMD.Cerebrum.Shared.Enums;
using Cerebrum.ViewModels.Econsult;
using Cerebrum.ViewModels.User;
using Microsoft.AspNetCore.Authentication;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Cerebrum30.Utility;

namespace Cerebrum30.Security
{
    public class CerebrumUser : ClaimsPrincipal
    {
        private List<VMUserRole> userRoles;
        private List<VMUserPermission> userPermissions;
        private List<VMUserOffice> userOffices;
        private List<int> userPatientIds;
        private string workListFilter = "WorkListFilter";
        public CerebrumUser(ClaimsPrincipal principal) : base(principal)
        {
        }

        #region Properties             

        public int PracticeId
        {
            get
            {
                if (HasCustomClaim("PracticeId"))
                {
                    return Convert.ToInt32(this.FindFirst("PracticeId").Value);
                }
                else
                {
                    return 0;
                }

            }

        }
        public Guid? TenantId
        {
            get
            {
                if (HasCustomClaim("TenantId"))
                {
                    return Guid.Parse(this.FindFirst("TenantId").Value);
                }
                return null;
            }
        }
        public int UserId
        {
            get
            {
                if (HasCustomClaim("UserId"))
                {
                    return Convert.ToInt32(this.FindFirst("UserId").Value);
                }
                else
                {
                    return 0;
                }

            }

        }
        public string UserName
        {
            get
            {
                if (HasCustomClaim(ClaimTypes.Name))
                {
                    return this.FindFirst(ClaimTypes.Name).Value;
                }
                else
                {
                    return "";
                }
            }
        }
        public string IpAddress
        {
            get
            {
                if (HasCustomClaim("IpAddress"))
                {
                    return this.FindFirst("IpAddress").Value;
                }
                else
                {
                    return "";
                }

            }
        }
        public string FirstName
        {
            get
            {
                if (HasCustomClaim("FirstName"))
                {
                    return this.FindFirst("FirstName").Value;
                }
                else
                {
                    return "";
                }

            }
        }
        public string LastName
        {
            get
            {
                if (HasCustomClaim("LastName"))
                {
                    return this.FindFirst("LastName").Value;
                }
                else
                {
                    return "";
                }

            }
        }
        public string FullName
        {
            get
            {
                if (HasCustomClaim("FullName"))
                {
                    return this.FindFirst("FullName").Value;
                }
                else
                {
                    return "";
                }

            }
        }
        public int PracticeDoctorId
        {
            get
            {
                if (HasCustomClaim("PracticeDoctorId"))
                {
                    return Convert.ToInt32(this.FindFirst("PracticeDoctorId").Value);
                }
                else
                {
                    return 0;
                }

            }
        }
        public SendOutReportMethod PracticeSendOutReportMethod
        {
            get
            {
                if (HasCustomClaim("SendOutReportMethod"))
                {
                    return (SendOutReportMethod)Convert.ToInt32(this.FindFirst("SendOutReportMethod").Value);
                }
                else
                {
                    return SendOutReportMethod.Regular;
                }
            }
        }
        public int ExternalDoctorId
        {
            get
            {
                if (HasCustomClaim("ExternalDoctorId"))
                {
                    return Convert.ToInt32(this.FindFirst("ExternalDoctorId").Value);
                }
                else
                {
                    return 0;
                }

            }
        }
        public string OfficeCprId
        {
            get
            {
                if (HasCustomClaim("OfficeCprId"))
                {
                    return this.FindFirst("OfficeCprId").Value;
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OfficeCprId", value.ToString());
            }
        }
        public string CPRID
        {
            get
            {
                if (HasCustomClaim("CPRID"))
                {
                    return this.FindFirst("CPRID").Value;
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("CPRID", value.ToString());
            }
        }
        public bool IsRegisteredWithPrescribeIT
        {
            get
            {
                if (HasCustomClaim("IsRegisteredWithPrescribeIT"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsRegisteredWithPrescribeIT").Value);
                }
                else
                {
                    return false;
                }
            }
            set
            {
                UpdateClaim("IsRegisteredWithPrescribeIT", value.ToString());
            }
        }
        public bool AutomaticPrescribeIT2FAEnabled
        {
            get
            {
                if (HasCustomClaim("AutomaticPrescribeIT2FAEnabled"))
                {
                    return Convert.ToBoolean(this.FindFirst("AutomaticPrescribeIT2FAEnabled").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("AutomaticPrescribeIT2FAEnabled", value.ToString());
            }
        }
        public bool WasShownPrescribeIT2FALogin
        {
            get
            {
                if (HasCustomClaim("WasShownPrescribeIT2FALogin"))
                {
                    return Convert.ToBoolean(this.FindFirst("WasShownPrescribeIT2FALogin").Value);
                }
                else
                {
                    return false;
                }
            }
            set
            {
                UpdateClaim("WasShownPrescribeIT2FALogin", value.ToString());
            }
        }
        public string DoctorCPSO
        {
            get
            {
                if (HasCustomClaim("DoctorCPSO"))
                {
                    return this.FindFirst("DoctorCPSO").Value;
                }
                else
                {
                    return "";
                }

            }
        }
        public string DoctorOHIP
        {
            get
            {
                if (HasCustomClaim("DoctorOHIP"))
                {
                    return this.FindFirst("DoctorOHIP").Value;
                }
                else
                {
                    return "";
                }

            }
        }
        public bool IsFamilyDoctorSpeciality
        {
            get
            {
                if (HasCustomClaim("IsFamilyDoctorSpeciality"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsFamilyDoctorSpeciality").Value);
                }
                else
                {
                    return false;
                }

            }
        }

        public bool IsPrescribeITActive
        {
            get
            {
                if (HasCustomClaim("IsPrescribeITActive"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsPrescribeITActive").Value);
                }
                else
                {
                    return false;
                }
            }
        }
        public bool IsPrescribeITenabled
        {
            get
            {
                if (HasCustomClaim("IsPrescribeITenabled"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsPrescribeITenabled").Value);
                }
                else
                {
                    return false; // default is false
                }
            }
            set
            {
                UpdateClaim("IsPrescribeITenabled", value.ToString());
            }
        }
        public string AccessToken
        {
            get
            {
                return GetAccessToken();
            }
        }

        public bool UserLoaded
        {
            get
            {
                if (HasCustomClaim("UserLoaded"))
                {
                    return Convert.ToBoolean(this.FindFirst("UserLoaded").Value);
                }
                else
                {
                    return false;
                }

            }
        }
        public int OfficeId  // selected office id  
        {
            get
            {
                if (HasCustomClaim("OfficeId"))
                {
                    return Convert.ToInt32(this.FindFirst("OfficeId").Value);
                }
                else
                {
                    return 0;
                }
            }
            set
            {
                UpdateClaim("OfficeId", value.ToString());
            }
        }

        public DateTime SelectedDate  // selected date of daysheet or schedule
        {
            get
            {
                if (HasCustomClaim("SelectedDate"))
                {
                    return Convert.ToDateTime(this.FindFirst("SelectedDate").Value);
                }
                else
                {
                    return System.DateTime.Now;
                }

            }
            set
            {
                UpdateClaim("SelectedDate", value.ToString());
            }
        }

        #region Daysheet Filters

        public string DSFilterTestGroups
        {
            get
            {
                if (HasCustomClaim("DSFilterTestGroups"))
                {
                    return this.FindFirst("DSFilterTestGroups").Value;
                }
                else
                {
                    return "0";
                }

            }
            set
            {
                UpdateClaim("DSFilterTestGroups", value.ToString());
            }
        }
        //public int DSFilterTestId
        //{
        //    get
        //    {
        //        if (HasCustomClaim("DSFilterTestId"))
        //        {
        //            return Convert.ToInt32(this.FindFirst("DSFilterTestId").Value);
        //        }
        //        else
        //        {
        //            return 0;
        //        }

        //    }
        //    set
        //    {
        //        UpdateClaim("DSFilterTestId", value.ToString());
        //    }
        //}
        public int DSFilterAppointmentStatusId
        {
            get
            {
                if (HasCustomClaim("DSFilterAppointmentStatusId"))
                {
                    return Convert.ToInt32(this.FindFirst("DSFilterAppointmentStatusId").Value);
                }
                else
                {
                    return 0;
                }

            }
            set
            {
                UpdateClaim("DSFilterAppointmentStatusId", value.ToString());
            }
        }
        public bool DSFilterExpected
        {
            get
            {
                if (HasCustomClaim("DSFilterExpected"))
                {
                    return Convert.ToBoolean(this.FindFirst("DSFilterExpected").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("DSFilterExpected", value.ToString());
            }
        }
        public bool DSFilterExcludeTestOnly
        {
            get
            {
                if (HasCustomClaim("DSFilterExcludeTestOnly"))
                {
                    return Convert.ToBoolean(this.FindFirst("DSFilterExcludeTestOnly").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("DSFilterExcludeTestOnly", value.ToString());
            }
        }
        public bool DSFilterExcludeCancelled
        {
            get
            {
                if (HasCustomClaim("DSFilterExcludeCancelled"))
                {
                    return Convert.ToBoolean(this.FindFirst("DSFilterExcludeCancelled").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("DSFilterExcludeCancelled", value.ToString());
            }
        }

        public bool DSFilterOnlyActionOnAbnormal
        {
            get
            {
                if (HasCustomClaim("DSFilterOnlyActionOnAbnormal"))
                {
                    return Convert.ToBoolean(this.FindFirst("DSFilterOnlyActionOnAbnormal").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("DSFilterOnlyActionOnAbnormal", value.ToString());
            }
        }

        public bool DSFilterShowOrders
        {
            get
            {
                if (HasCustomClaim("DSFilterShowOrders"))
                {
                    return Convert.ToBoolean(this.FindFirst("DSFilterShowOrders").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("DSFilterShowOrders", value.ToString());
            }
        }
        public string HFiDOC
        {
            get
            {
                if (HasCustomClaim("HFiDOC"))
                {
                    return this.FindFirst("HFiDOC").Value;
                }
                else
                {
                    return string.Empty; // default is true
                }

            }
            set
            {
                UpdateClaim("HFiDOC", value);
            }
        }

        public bool TAPP
        {
            get
            {
                if (HasCustomClaim("TAPP"))
                {
                    return Convert.ToBoolean(this.FindFirst("TAPP").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("TAPP", value.ToString());
            }
        }
        #endregion
        public bool DoInterActionsCheck
        {
            get
            {
                if (HasCustomClaim("DoInterActionsCheck"))
                {
                    return Convert.ToBoolean(this.FindFirst("DoInterActionsCheck").Value);
                }
                else
                {
                    return true; // default is true
                }

            }
            set
            {
                UpdateClaim("DoInterActionsCheck", value.ToString());
            }
        }

        public Cerebrum.ViewModels.Measurements.VMWorklistRequest WorkListFilterRequest
        {
            get
            {
                return GetWorkListFilterRequest();
            }
            set
            {
                UpdateClaim(workListFilter, JsonConvert.SerializeObject(value));
            }
        }

        public bool IsSuperAdmin
        {
            get { return HasRole($"Super Admin"); }
        }

        public bool IsAdmin
        {
            get { return HasRole($"Admin"); }
        }

        public string Language
        {
            get
            {
                if (HasCustomClaim("Language"))
                {
                    return this.FindFirst("Language").Value;
                }
                else
                {
                    return "en";
                }
            }
        }

        public bool IsPracticeAdmin
        {
            get { return HasRole($"Practice Admin"); }
        }

        public bool IsDoctor
        {
            get { return HasPermission("Doctor"); }
        }
        public bool IsNurse
        {
            get { return HasRole("Nurse"); }
        }
        public bool IsTrainee
        {
            get { return HasRole("Trainee"); }
        }

        public List<VMUserOffice> UserOffices
        {
            get { loadUserOffices(); return userOffices; }
            private set { }
        }
        public List<VMUserPermission> UserPermissions
        {
            get { loadUserPermissions(); return userPermissions; }
            private set { }
        }
        public bool IsAppointmentPriorityEnabled
        {
            get
            {
                if (HasCustomClaim("IsAppointmentPriorityEnabled"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsAppointmentPriorityEnabled").Value);
                }
                else
                {
                    return false;
                }
            }
        }

        public bool IsPracticeAppointmentPriorityEnabled
        {
            get
            {
                if (HasCustomClaim("IsPracticeAppointmentPriorityEnabled"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsPracticeAppointmentPriorityEnabled").Value);
                }
                else
                {
                    return false;
                }
            }
            set
            {
                UpdateClaim("IsPracticeAppointmentPriorityEnabled", value.ToString());
            }
        }

        public Guid? PatientFhirId
        {
            get
            {
                if (HasCustomClaim("PatientFhirId"))
                {
                    return Guid.Parse(this.FindFirst("PatientFhirId").Value);
                }
                else
                {
                    return null;
                }
            }
            set
            {
                UpdateClaim("PatientFhirId", value.ToString());
            }
        }
        #endregion

        #region Methods      

        /// <summary>
        /// This method may not be what you think it is. 
        /// It does not appear to be called to "load" the user in any 
        /// normal way, such as when they log in.
        /// </summary>
        public void Load()
        {
            var userIdGuid = GetUserIdGuid();
            if (!HasCustomClaim("UserLoaded"))
            {
                using (IUserCredentialBLL userBLL = new UserCredentialBLL(new Cerebrum.Data.CerebrumContext()))
                {
                    var user = userBLL.GetUser(userIdGuid);
                    if (user != null)
                    {
                        var ipAddress = Helper.GetClientIpByHeaderxForwarded();
                        if (string.IsNullOrEmpty(ipAddress))
                        {
                            // TODO: Fix HttpContext access for .NET 8
                            ipAddress = "127.0.0.1"; // HttpContextProvider.Current.Request.UserHostAddress;
                        }
                        AddClaim("PracticeId", user.PracticeId.ToString());
                        AddClaim("UserId", user.UserId.ToString());
                        AddClaim("FirstName", user.FirstName);
                        AddClaim("LastName", user.LastName);
                        AddClaim("FullName", user.FullName);
                        AddClaim("IpAddress", ipAddress);
                        AddClaim("OtnPractitionerId", "");
                        AddClaim("OtnUserId", "");
                        AddClaim("IsOtnDelegate", bool.FalseString);
                        AddClaim("IsOtnSpecialist", bool.FalseString);
                        AddClaim("OtnDelegatorPractitionerId", "");
                        AddClaim("OtnDelegatorUserId", "");
                        AddClaim("OtnCareTeam", "");
                        AddClaim("EchartPatientId", "0");
                        AddClaim("OtnDoctorName", "");
                        AddClaim("OneIdUaoId", "");
                        AddClaim("Language", user.Language == ApplicationLanguage.English ? "en" : "fr");


                        var sendOutReportMethod = userBLL.PracticeSendOutReportMethod(PracticeId);
                        AddClaim("SendOutReportMethod", Convert.ToString((int)sendOutReportMethod));

                        var practiceDoctor = userBLL.GetPracticeDoctor(PracticeId, UserId);
                        if (practiceDoctor != null)
                        {
                            bool isFamilyDoctorSpeciality = SpecialtyCodes.FamilyDoctor == practiceDoctor.Speciality;//| TeamMember.FamilyPractice;

                            AddClaim("PracticeDoctorId", practiceDoctor.PracticeDoctorId.ToString());
                            AddClaim("ExternalDoctorId", practiceDoctor.ExternalDoctorId.ToString());
                            AddClaim("DoctorCPSO", practiceDoctor.CPSO);
                            AddClaim("DoctorOHIP", practiceDoctor.OhipId);
                            AddClaim("IsFamilyDoctorSpeciality", isFamilyDoctorSpeciality.ToString());
                            //AddClaim("HFiDOC", userBLL.GetStudyDisplayName(practiceDoctor.PracticeDoctorId, "HF-iDOC"));

                        }

                        loadUserPermissions();
                        loadUserRoles();
                        loadUserOffices();

                        AddClaim("UserLoaded", "true");
                    }
                }
            }
        }

        public bool HasPermissionType(string permissionType)
        {
            loadUserPermissions();
            return userPermissions.Any(p => p.PermissionType.ToLower() == permissionType.ToLower() && p.PracticeId == PracticeId);
        }
        public bool HasPermission(string resource, string permissions, bool requireAll = false)
        {
            loadUserPermissions();
            string[] permissionList = permissions.ToLower().Split(',');
            resource = resource.ToLower();

            if (requireAll) // needs all of the permissions
            {
                var userPermList = userPermissions.Where(p => p.PermissionType.Trim().ToLower() == resource && p.PracticeId == PracticeId).Select(p => p.PermissionName.ToLower()).ToList();
                var hasAll = userPermList.Intersect(permissionList).Count() == permissionList.Count();
                return hasAll;
            }
            else
            {
                var anyper = userPermissions.Any(p => p.PermissionType.ToLower() == resource && p.PracticeId == PracticeId && permissionList.Contains(p.PermissionName.ToLower()));
                return anyper;
            }
        }
        public bool HasPermission(string permissions, bool requireAll = false)
        {
            loadUserPermissions();

            string[] permissionList = permissions.ToLower().Split(',');

            if (requireAll) // needs all of the permissions
            {
                var userPermList = userPermissions.Where(p => p.PracticeId == PracticeId).Select(p => p.PermissionName.Trim().ToLower()).ToList();
                var hasAll = userPermList.Intersect(permissionList).Count() == permissionList.Count();
                return hasAll;
            }
            else
            {
                return userPermissions.Any(p => permissionList.Contains(p.PermissionName.ToLower()));
            }

        }

        public List<string> GetPermissions(List<string> criticalresources)
        {
            loadUserPermissions();
            var userPermList = userPermissions.Select(p => p.PermissionName.Trim()).ToList();
            return (userPermList.Intersect(criticalresources)).ToList();
        }

        public bool HasRole(string roles, bool requireAll = false)
        {
            var rolesList = roles.ToLower().Split(',').ToList();
            string practic = this.PracticeId > 0 ? this.PracticeId.ToString() : "";
            rolesList = rolesList.Select(f => (f.Replace(f, $"{f.Trim()} {practic}")).Trim()).ToList();
            //if (userRoles == null)
            //{
            //    userRoles = userBLL.GetUserRoles(UserId);
            //}

            loadUserRoles();


            if (requireAll) // needs all of the roles
            {
                var userRoleList = userRoles.Select(p => p.Name.Trim().ToLower()).ToList();
                var hasAll = userRoleList.Intersect(rolesList).Count() == rolesList.Count();
                return hasAll;
            }
            else
            {
                bool rl = userRoles.Any(r => rolesList.Contains(r.Name.ToLower()));
                return rl;// userRoles.Any(r => rolesList.Contains(r.Name.ToLower()));
            }
        }
        public bool HasPatientAccess(int patientId)
        {
            loadUserPatientIds();
            var hasAccess = userPatientIds != null && userPatientIds.Contains(patientId);
            return hasAccess;
        }
        public bool HasOffice(int officeId)
        {
            //loadUserOffices();
            return UserOffices.Any(o => o.Id == officeId);
        }

        //public PatientAccesStatus GetPatientAccess(int patientId)
        //{            
        //    return userBLL.GetPatientAccess(UserId, patientId);            
        //}

        //public List<int> GetPatientAccessList(List<int> patientIds)
        //{
        //    var accessList = userBLL.GetPatientAccessList(UserId, patientIds);
        //    return accessList; // return only the patients that the user have access to
        //}
        //public bool HasPatientAccess(int patientId)
        //{
        //    var access = GetPatientAccess(patientId);
        //    if (access == PatientAccesStatus.Access)
        //    {
        //        return true;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}


        public bool HasCustomClaim(string claimKey)
        {
            var claim = this.Claims.Where(x => x.Type == claimKey).FirstOrDefault();

            if (claim != null)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool CanUserPunchInOutTimesheet()
        {
            var claim = this.Claims.Where(x => x.Type == "CanUserPunchTimesheet").FirstOrDefault();

            if (claim != null)
            {
                return bool.Parse(claim.Value);
            }
            else
            {
                return false;
            }
        }

        public bool AddClaim(string claimKey, string value)
        {

            var identity = this.Identity as ClaimsIdentity;
            identity.AddClaim(new Claim(claimKey, value));
            return true;

        }
        public bool UpdateAccessedPatientIds(int patientid)
        {
            string jsonPatients = this.FindFirst("UserPatients").Value;
            var existingPatientIds = JsonConvert.DeserializeObject<List<int>>(jsonPatients);
            if (!existingPatientIds.Contains(patientid))
            {
                existingPatientIds.Add(patientid);

                var jsonPatientIds = JsonConvert.SerializeObject(existingPatientIds);
                return UpdateClaim("UserPatients", jsonPatientIds);
            }
            return true;
        }
        public bool UpdateClaim(string claimKey, string value)
        {
            RemoveClaim(claimKey);

            //Adds the claim again:
            AddClaim(claimKey, value);
            ReAuth();
            return true;
        }
        public bool RemoveClaim(string claimKey)
        {
            var claim = this.Claims.Where(x => x.Type == claimKey).FirstOrDefault();
            if (claim == null)
            {
                return false;
            }
            else
            {
                var identity = this.Identity as ClaimsIdentity;
                identity.RemoveClaim(claim);
                return true;
            }

        }


        #endregion

        #region Private Methods
        private void ReAuth()
        {
            // TODO: GetOwinContext doesn't exist in ASP.NET Core - commenting out
            // var ctx = HttpContextProvider.Current.Request.GetOwinContext();
            // var authManager = ctx.Authentication;

            var identity = this.Identity as ClaimsIdentity;

            // TODO: authManager doesn't exist - commenting out
            // authManager.SignOut(identity.AuthenticationType);
            // authManager.SignIn(new AuthenticationProperties { IsPersistent = false }, identity);
        }
        public string GetUserIdGuid()
        {
            var userIdClaim = this.Claims
            .FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);

            return userIdClaim.Value;
        }
        private void loadUserPatientIds()
        {
            string jsonPatients = this.FindFirst("UserPatients").Value;
            userPatientIds = JsonConvert.DeserializeObject<List<int>>(jsonPatients);
        }
        private void loadUserPermissions()
        {
            string jsonPermissions = this.FindFirst("UserPermissions").Value;
            userPermissions = JsonConvert.DeserializeObject<List<VMUserPermission>>(jsonPermissions);
        }

        private void loadUserRoles()
        {
            var jsonRoles = this.FindFirst("UserRoles").Value;
            userRoles = JsonConvert.DeserializeObject<List<VMUserRole>>(jsonRoles);
        }

        private void loadUserOffices()
        {
            string jsonOffices = this.FindFirst("UserOffices").Value;
            userOffices = JsonConvert.DeserializeObject<List<VMUserOffice>>(jsonOffices);
        }

        private Cerebrum.ViewModels.Measurements.VMWorklistRequest GetWorkListFilterRequest()
        {
            var filterRequest = new Cerebrum.ViewModels.Measurements.VMWorklistRequest();
            var filtersStr = "";
            if (HasCustomClaim(workListFilter))
            {
                filtersStr = this.FindFirst(workListFilter).Value;
                filterRequest = JsonConvert.DeserializeObject<Cerebrum.ViewModels.Measurements.VMWorklistRequest>(filtersStr);
            }
            else
            {
                filtersStr = JsonConvert.SerializeObject(filterRequest);
                AddClaim(workListFilter, filtersStr);
            }
            return filterRequest;
        }

        private string GetAccessToken()
        {
            string token = this.FindFirst("AccessToken").Value;
            return token;
        }

        #endregion

        #region eConsult
        public string OtnPractitionerId
        {
            get
            {
                if (HasCustomClaim("OtnPractitionerId"))
                {
                    return Convert.ToString(FindFirst("OtnPractitionerId").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OtnPractitionerId", Convert.ToString(value));
            }
        }
        public string OtnUserId
        {
            get
            {
                if (HasCustomClaim("OtnUserId"))
                {
                    return Convert.ToString(FindFirst("OtnUserId").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OtnUserId", Convert.ToString(value));
            }
        }


        public bool IsOtnDelegate
        {
            get
            {
                if (HasCustomClaim("IsOtnDelegate"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsOtnDelegate").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("IsOtnDelegate", value.ToString());
            }
        }

        public bool IsOtnSpecialist
        {
            get
            {
                if (HasCustomClaim("IsOtnSpecialist"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsOtnSpecialist").Value);
                }
                else
                {
                    return false;
                }

            }
            set
            {
                UpdateClaim("IsOtnSpecialist", value.ToString());
            }
        }

        public string OtnDelegatorPractitionerId
        {
            get
            {
                if (HasCustomClaim("OtnDelegatorPractitionerId"))
                {
                    return Convert.ToString(FindFirst("OtnDelegatorPractitionerId").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OtnDelegatorPractitionerId", Convert.ToString(value));
            }
        }
        public string OtnDelegatorUserId
        {
            get
            {
                if (HasCustomClaim("OtnDelegatorUserId"))
                {
                    return Convert.ToString(FindFirst("OtnDelegatorUserId").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OtnDelegatorUserId", Convert.ToString(value));
            }
        }

        public int EchartPatientId
        {
            get
            {
                if (HasCustomClaim("EchartPatientId"))
                {
                    return Convert.ToInt32(this.FindFirst("EchartPatientId").Value);
                }
                else
                {
                    return 0;
                }
            }
            set
            {
                UpdateClaim("EchartPatientId", Convert.ToString(value));
            }
        }

        public string OtnDoctorName
        {
            get
            {
                if (HasCustomClaim("OtnDoctorName"))
                {
                    return Convert.ToString(FindFirst("OtnDoctorName").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OtnDoctorName", Convert.ToString(value));
            }
        }

        public VMCareTeam OtnCareTeam
        {
            get
            {
                if (HasCustomClaim("OtnCareTeam"))
                {
                    var jsonOtnCareTeam = this.FindFirst("OtnCareTeam").Value;
                    if (string.IsNullOrEmpty(jsonOtnCareTeam)) return new VMCareTeam();

                    return JsonConvert.DeserializeObject<VMCareTeam>(jsonOtnCareTeam);
                }
                else
                {
                    return new VMCareTeam();
                }
            }
            set
            {
                var jsonOtnCareTeam = JsonConvert.SerializeObject(value);
                UpdateClaim("OtnCareTeam", jsonOtnCareTeam);
            }
        }

        public string OneIdUaoId
        {
            get
            {
                if (HasCustomClaim("OneIdUaoId"))
                {
                    return Convert.ToString(FindFirst("OneIdUaoId").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OneIdUaoId", Convert.ToString(value));
            }
        }

        public string HubTopic
        {
            get
            {
                if (HasCustomClaim("HubTopic"))
                {
                    return Convert.ToString(FindFirst("HubTopic").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("HubTopic", Convert.ToString(value));
            }
        }

        public string OneIdUaoName
        {
            get
            {
                if (HasCustomClaim("OneIdUaoName"))
                {
                    return Convert.ToString(FindFirst("OneIdUaoName").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OneIdUaoName", Convert.ToString(value));
            }
        }

        public int UserUaoCount
        {
            get
            {
                if (HasCustomClaim("UserUaoCount"))
                {
                    return Convert.ToInt32(this.FindFirst("UserUaoCount").Value);
                }
                else
                {
                    return 0;
                }
            }
            set
            {
                UpdateClaim("UserUaoCount", Convert.ToString(value));
            }
        }

        public ServiceType ServiceType
        {
            get
            {
                if (HasCustomClaim("ServiceType"))
                {
                    return (ServiceType)Convert.ToInt32(this.FindFirst("ServiceType").Value);
                }
                else
                {
                    return ServiceType.None;
                }
            }
            set
            {
                var intValue = (int)(ServiceType)value;
                UpdateClaim("ServiceType", Convert.ToString(intValue));
            }
        }

        public bool IsUserLinkedToOneId
        {
            get
            {
                if (HasCustomClaim("IsUserLinkedToOneId"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsUserLinkedToOneId").Value);
                }
                else
                {
                    return false;
                }
            }
            set
            {
                UpdateClaim("IsUserLinkedToOneId", value.ToString());
            }
        }
        #endregion

        public bool IsShowDhdrDisclaimer
        {
            get
            {
                if (HasCustomClaim("IsShowDhdrDisclaimer"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsShowDhdrDisclaimer").Value);
                }
                else
                {
                    return false;
                }
            }
            set
            {
                UpdateClaim("IsShowDhdrDisclaimer", value.ToString());
            }
        }

        public bool IsDhdrEnabled
        {
            get
            {
                if (HasCustomClaim("IsDhdrEnabled"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsDhdrEnabled").Value);
                }
                else
                {
                    return false;
                }
            }
        }
        public bool IsEformsEnabled
        {
            get
            {
                if (HasCustomClaim("IsEformsEnabled"))
                {
                    return Convert.ToBoolean(this.FindFirst("IsEformsEnabled").Value);
                }
                else
                {
                    return false;
                }
            }
        }
        public string OntarioHealthClientId
        {
            get
            {
                if (HasCustomClaim("OntarioHealthClientId"))
                {
                    return Convert.ToString(FindFirst("OntarioHealthClientId").Value);
                }
                else
                {
                    return "";
                }
            }
            set
            {
                UpdateClaim("OntarioHealthClientId", Convert.ToString(value));
            }
        }

        public string IdpAccessToken
        {
            get
            {
                return HasCustomClaim("IdpAccessToken") ? Convert.ToString(this.FindFirst("IdpAccessToken").Value) : "";
            }
            set
            {
                UpdateClaim("IdpAccessToken", Convert.ToString(value));
            }
        }
    }
}


