<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Cerebrum30</RootNamespace>
    <AssemblyName>Cerebrum30</AssemblyName>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <!-- Configuration settings -->
    <Configuration Condition="'$(Configuration)' == ''">Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">AnyCPU</Platform>

    <!-- Build settings -->
    <OutputType>Exe</OutputType>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>

    <!-- Enable Razor view compilation at build time -->
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <RazorCompileOnBuild>true</RazorCompileOnBuild>
    <RazorCompileOnPublish>true</RazorCompileOnPublish>
    <MvcRazorCompileOnBuild>true</MvcRazorCompileOnBuild>
    <MvcRazorCompileOnPublish>true</MvcRazorCompileOnPublish>
    <MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
    <RazorLangVersion>Latest</RazorLangVersion>
    <GenerateRazorAssemblyAttributes>true</GenerateRazorAssemblyAttributes>
    <!-- Ensure cshtml files are included in compilation -->
    <EnableDefaultContentItems>true</EnableDefaultContentItems>

    <!-- Docker settings -->
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerLaunchUrl>http://{ServiceIPAddress}</DockerLaunchUrl>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>

    <!-- Application Insights -->
    <ApplicationInsightsResourceId>/subscriptions/11b4a421-1bf5-46fa-924f-ed2e5af66004/resourceGroups/rg-awmd-sandbox-local-shared-cace-01/providers/microsoft.insights/components/AppInsight-awmd-local-shared-cace-01</ApplicationInsightsResourceId>
  </PropertyGroup>

  <!-- Configuration-specific settings -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' == 'Uat'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- ASP.NET Core Package References -->
  <ItemGroup>
    <!-- Core ASP.NET Core packages - Most are built into .NET 8 framework -->
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <!-- Authentication.Cookies and SignalR are built into ASP.NET Core 8.0 -->

    <!-- Entity Framework Core -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11" />

    <!-- Identity -->
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.11" />

    <!-- Application Insights -->
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />

    <!-- AutoMapper -->
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />

    <!-- Auth0 -->
    <PackageReference Include="Auth0.AuthenticationApi" Version="7.25.1" />
    <PackageReference Include="Auth0.ManagementApi" Version="7.25.1" />

    <!-- FHIR STU3 (via Fhir_STU3 project reference) -->

    <!-- AwareMD packages - TODO: Add these packages when available -->
    <!-- <PackageReference Include="AwareMD.User.Api.Client" Version="1.0.10" /> -->
    <!-- <PackageReference Include="AwareMD.User.Dto" Version="1.0.7" /> -->

    <!-- Third-party packages -->
    <PackageReference Include="DotNetZip" Version="1.16.0" />
    <PackageReference Include="Starksoft.Aspen" Version="1.0.13" />

    <!-- Other packages -->
    <PackageReference Include="log4net" Version="2.0.17" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Svg" Version="3.4.7" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <!-- Project References -->
  <ItemGroup>
    <ProjectReference Include="..\Cerebrum.BLL\Cerebrum.BLL.csproj" />
    <ProjectReference Include="..\Cerebrum.ContactManager\Cerebrum.ContactManager.csproj" />
    <ProjectReference Include="..\Cerebrum.Data\Cerebrum.Data.csproj" />
    <ProjectReference Include="..\Cerebrum.ExternalDocument\Cerebrum.ExternalDocument.csproj" />
    <ProjectReference Include="..\Cerebrum.DTO\Cerebrum.DTO.csproj" />
    <ProjectReference Include="..\Cerebrum.ViewModels\Cerebrum.ViewModels.csproj" />
    <ProjectReference Include="..\Cerebrum.CDS\Cerebrum.CDS.csproj" />
    <ProjectReference Include="..\Cerebrum.CDM\Cerebrum.CDM.csproj" />
    <ProjectReference Include="..\Cerebrum.DHDR.Services\Cerebrum.DHDR.Services.csproj" />
    <ProjectReference Include="..\AwareMD.Cerebrum.Shared\AwareMD.Cerebrum.Shared.csproj" />
    <ProjectReference Include="..\AwareMD.DHDR.Dto\AwareMD.DHDR.Dto.csproj" />
    <ProjectReference Include="..\AwareMD.DHDR.Seedwork\AwareMD.DHDR.Seedwork.csproj" />
    <ProjectReference Include="..\AwareMD.WorkSheet.Dto\AwareMD.WorkSheet.Dto.csproj" />
    <ProjectReference Include="..\AwareMD.Eforms.Service\AwareMD.Eforms.Service.csproj" />
    <ProjectReference Include="..\Fhir_STU3\Fhir_STU3.csproj" />
    <ProjectReference Include="..\Cerebrum.VirtualVisit.Report\Cerebrum.VirtualVisit.Report.csproj" />
    <ProjectReference Include="..\Cerebrum.VirtualVisit\Cerebrum.VirtualVisit.csproj" />
    <ProjectReference Include="..\Cerebrum.VirtualVisit.Seedwork\Cerebrum.VirtualVisit.Seedwork.csproj" />
    <!-- CAPICOM dependencies fixed for .NET 8 - using modern cryptography APIs -->
    <ProjectReference Include="..\Cerebrum.Labs\Cerebrum.Labs.csproj" />
    <ProjectReference Include="..\Cerebrum.PrintLogBLL\Cerebrum.PrintLogBLL.csproj" />
    <ProjectReference Include="..\Cerebrum.Reminder\Cerebrum.Reminder.csproj" />
    <ProjectReference Include="..\Cerebrum.PrescribeIT\Cerebrum.PrescribeIT.csproj" />
  </ItemGroup>

  <!-- Exclude ASP.NET Framework specific files -->
  <ItemGroup>
    <Compile Remove="App_Start\NinjectWebCommon.cs" />
    <!-- Exclude original PDF file with iTextSharp issues and use stub instead -->
    <!-- <Compile Remove="DAL\DataAccessGB\Repositories\RepositoryForPatientChart.cs" /> -->
  </ItemGroup>

  <!-- Content files -->
  <ItemGroup>
    <Content Update="Web.config" CopyToOutputDirectory="PreserveNewest" />
    <Content Update="Web.Base.config" CopyToOutputDirectory="PreserveNewest" />
    <Content Update="Web.Base.Debug.config" CopyToOutputDirectory="PreserveNewest" />
    <Content Update="Web.Base.Release.config" CopyToOutputDirectory="PreserveNewest" />
    <Content Update="Web.Base.Uat.config" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <!-- Static files -->
  <ItemGroup>
    <Content Include="Content\**\*" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="Scripts\**\*" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="fonts\**\*" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <!-- Enable default Razor content items -->
  <PropertyGroup>
    <EnableDefaultRazorGenerateItems>true</EnableDefaultRazorGenerateItems>
    <EnableDefaultRazorTargetAssemblyInfoAttributes>true</EnableDefaultRazorTargetAssemblyInfoAttributes>
    <SuppressCheckForDuplicateFrameworkReferences>true</SuppressCheckForDuplicateFrameworkReferences>
  </PropertyGroup>

  <!-- Force Razor view compilation -->
  <Target Name="ForceRazorCompilation" BeforeTargets="Build" Condition="'$(EnableDefaultRazorGenerateItems)' == 'true'">
    <Message Text="Forcing Razor view compilation..." Importance="high" />
    <ItemGroup Condition="'$(EnableDefaultContentItems)' != 'false'">
      <Content Include="Views/**/*.cshtml" Condition="'%(Content.Extension)' == '.cshtml'" />
      <Content Include="Areas/**/Views/**/*.cshtml" Condition="'%(Content.Extension)' == '.cshtml'" />
    </ItemGroup>
  </Target>

</Project>
